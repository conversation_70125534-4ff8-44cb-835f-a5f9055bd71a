<cfcomponent extends="model.admin.admin" output="no">
	<cfset defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
		var local = structNew();
		arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = buildRightAssignments(this.siteResourceID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'));

		// build quick links -------------------------------------------------------------------------- ::
		this.link.list = buildCurrentLink(arguments.event,"list");
		this.link.viewInvoiceInfo = buildCurrentLink(arguments.event,"viewInvoiceInfo") & "&mode=direct";
		this.link.downloadInvoice = buildCurrentLink(arguments.event,"downloadInvoice") & "&mode=stream";
		this.link.downloadInvoiceBundle = buildCurrentLink(arguments.event,"downloadInvoiceBundle") & "&mode=stream";
		this.link.emailInvoice = buildCurrentLink(arguments.event,"emailInvoice") & "&mode=direct";
		this.link.editInvoice = buildCurrentLink(arguments.event,"editInvoice") & "&mode=direct";
		this.link.updateInvoice = buildCurrentLink(arguments.event,"updateInvoice") & "&mode=stream";
		this.link.moveInvoiceTransaction = buildCurrentLink(arguments.event,"moveInvoiceTransaction") & "&mode=direct";
		this.link.generateInvoiceBundle = buildCurrentLink(arguments.event,"generateInvoiceBundle") & "&mode=direct";
		this.link.massEmailInvoices = buildCurrentLink(arguments.event,"massEmailInvoices") & "&mode=direct";
		this.link.changeInvoiceDates = buildCurrentLink(arguments.event,"changeInvoiceDates") & "&mode=direct";
		this.link.message = buildCurrentLink(arguments.event,"message") & "&mode=direct";

		this.link.memSelectGotoLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list');
		this.link.grpSelectGotoLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups');

		local.invoiceAdminTool = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin');
		this.link.viewTransactionInfo = local.invoiceAdminTool & "&mca_ta=viewTransactionInfo&mode=direct";
		this.link.addPayment = local.invoiceAdminTool & "&mca_ta=addPayment&mode=direct";
		this.link.allocatePayment = local.invoiceAdminTool & "&mca_ta=allocatePayment&mode=direct";

		// method to run ------------------------------------------------------------------------------ ::
		local.methodToRun 	= this[arguments.event.getValue('mca_ta')];

		// pass the argument collection to the current method and execute it.
		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="list" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.objInvoice = CreateObject("component","invoice");

			local.invoiceListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=invoiceJSON&meth=getInvoiceList&mode=stream&srID=#this.siteResourceID#";
			local.sampleOption1ImportTemplate = buildCurrentLink(arguments.event,"sampleOption1ImportTemplate") & "&mode=stream";
			local.sampleOption2ImportTemplate = buildCurrentLink(arguments.event,"sampleOption2ImportTemplate") & "&mode=stream";
			local.setInvoicePayProfiles = buildCurrentLink(arguments.event,"setInvoicePayProfiles") & "&mode=direct";

			arguments.event.paramValue('profileID','');
			arguments.event.paramValue('statusID','1,3,5');
			arguments.event.paramValue('invoiceNumber','');
			arguments.event.paramValue('duedateStart','');
			arguments.event.paramValue('duedateEnd','');
			arguments.event.paramValue('cardOnFile','');
			arguments.event.paramValue('billeddateStart','');
			arguments.event.paramValue('billeddateEnd','');
			arguments.event.paramValue('dueAmtStart',0);
			arguments.event.paramValue('dueAmtEnd',0);
			arguments.event.paramValue('invAmtStart',0);
			arguments.event.paramValue('invAmtEnd',0);
			arguments.event.paramValue('trDetail','');
			arguments.event.paramValue('associatedMemberID','');
			arguments.event.paramValue('associatedGroupID','');

			arguments.event.setValue('dueAmtStart',val(ReReplace(arguments.event.getValue('dueAmtStart',0),'[^0-9\.]','','ALL')));
			arguments.event.setValue('dueAmtEnd',val(ReReplace(arguments.event.getValue('dueAmtEnd',0),'[^0-9\.]','','ALL')));
			arguments.event.setValue('invAmtStart',val(ReReplace(arguments.event.getValue('invAmtStart',0),'[^0-9\.]','','ALL')));
			arguments.event.setValue('invAmtEnd',val(ReReplace(arguments.event.getValue('invAmtEnd',0),'[^0-9\.]','','ALL')));

			local.urlString = "";
			local.urlString = local.urlString & '&statusID=' & arguments.event.getTrimValue('statusID') & '&chkAll=1';

			local.strETData = {
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				treeCode='ETINVOICES',
				title="Invoice Email Templates ", 
				intro="Here you manage email templates used by invoices.",
				gridext="#this.siteResourceID#_1",
				gridwidth=690,
				initGridOnLoad=false
			};
			local.strEmailTemplatesGrid = createObject("component","model.admin.emailTemplates.emailTemplateAdmin").manageEmailTemplates(strETData=local.strETData);

			local.memberTransationsLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');

			local.qryStatus = local.objInvoice.getInvoiceStatuses();
			local.qryInvoiceProfiles = local.objInvoice.getInvoiceProfiles(orgID=arguments.event.getValue('mc_siteinfo.orgid'));
			local.qryPayProfiles = local.objInvoice.getPaymentProfiles(siteID=arguments.event.getValue('mc_siteinfo.siteID'));
			local.qryPayProfilesAcctImport = local.objInvoice.getPaymentProfilesForAccountImport(siteID=arguments.event.getValue('mc_siteinfo.siteID'));
		</cfscript>
		
		<cfquery name="local.qryOrgData" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select invoiceNumPrefix
			from dbo.organizations
			where orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<!--- get the SRID and permissions of TransactionsAdmin. --->
		<cfset local.TransactionsAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TransactionsAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
		<cfset local.myRightsTransactionsAdmin = buildRightAssignments(local.TransactionsAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>

		<cfset local.showImpTemplate = true>
		<cfif arguments.event.getValue('tab','') eq 'import' and len(arguments.event.getValue('importFileName1',''))>
			<cfset local.showImpTemplate = false>
			<cfset local.impData = processAcctImportOpt1(event=arguments.event)>
		<cfelseif arguments.event.getValue('tab','') eq 'import' and len(arguments.event.getValue('importFileName2',''))>
			<cfset local.showImpTemplate = false>
			<cfset local.impData = processAcctImportOpt2(event=arguments.event)>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_invoices.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="moveInvoiceTransaction" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.formlink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('InvoiceAdmin') & "&mca_ta=saveMoveInvoiceTransaction&mode=stream";

		arguments.event.setValue('tid',int(val(arguments.event.getTrimValue('tid',0))));
		arguments.event.setValue('vid',int(val(arguments.event.getTrimValue('vid',0))));
		</cfscript>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.invoiceMoveTransaction') is not 1>
			<cflocation url="#this.link.message#&ec=MITNP" addtoken="no">
		</cfif>

		<cfquery name="local.qryTransaction" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">;

			select t.transactionID, i.invoiceID, i.fullInvoiceNumber as invoiceNumber, t.detail, t.amount,
				t.transactionDate,
				mAssign2.firstname
				+ case when o.hasMiddleName = 1 then isnull(' ' + nullif(mAssign2.middlename,''),'') else '' end
				+ ' ' + mAssign2.lastName
				+ case when o.hasSuffix = 1 then isnull(' ' + nullif(mAssign2.suffix,''),'') else '' end
				+ ' (' + mAssign2.membernumber + ')' as AssignedToMember,
				mTrans2.firstname
				+ case when o.hasMiddleName = 1 then isnull(' ' + nullif(mTrans2.middlename,''),'') else '' end
				+ ' ' + mTrans2.lastName
				+ case when o.hasSuffix = 1 then isnull(' ' + nullif(mTrans2.suffix,''),'') else '' end
				+ ' (' + mTrans2.membernumber + ')' as TransMember,
				rglCred.thePathExpanded as CREDITACCOUNT,
				rglCred.accountCode as CREDITACCOUNTCODE,
				ip.profileID as InvoiceProfileID,
				ip.profileName as InvoiceProfileName
			from dbo.tr_transactions as t
			inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
			inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
			inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
			inner join dbo.ams_members as mAssign on mAssign.memberid = i.assignedToMemberID
			inner join dbo.ams_members as mAssign2 on mAssign2.memberiD = mAssign.activeMemberID
			inner join dbo.ams_members as mTrans on mTrans.memberid = t.assignedToMemberID
			inner join dbo.ams_members as mTrans2 on mTrans2.memberiD = mTrans.activeMemberID
			inner join dbo.organizations as o on o.orgID = mAssign2.orgID
			INNER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as rglCred on rglCred.GLAccountID = t.creditGLAccountID
			where t.transactionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('tid')#">
			and i.invoiceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('vid')#">
			and o.orgID = @orgID
			and i.statusID = 1;
		</cfquery>
		<cfif local.qryTransaction.recordcount is 0>
			<cflocation url="#this.link.message#&ec=MITNF" addtoken="no">
		</cfif>

		<cfquery name="local.qryOpenInvoices" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">;

			select tmp.invoiceID, tmp.invoiceNumber, tmp.dateDue, tmp.memberName, sum(it2.cache_invoiceAmountAfterAdjustment) as InvAmt
			from (
				select i.invoiceID, i.fullInvoiceNumber as invoiceNumber, i.dateDue,
					m2.firstname
					+ case when o.hasMiddleName = 1 then isnull(' ' + nullif(m2.middlename,''),'') else '' end
					+ ' ' + m2.lastName
					+ case when o.hasSuffix = 1 then isnull(' ' + nullif(m2.suffix,''),'') else '' end
					+ ' (' + m2.membernumber + ')' as memberName
				from dbo.tr_invoices as i
				inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = i.assignedToMemberID
				inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberiD = m.activeMemberID
				inner join dbo.organizations as o on o.orgID = @orgID
				where i.orgID = @orgID
				and i.statusID = 1
				and i.invoiceProfileID = <cfqueryparam value="#local.qryTransaction.invoiceProfileID#" cfsqltype="CF_SQL_INTEGER">
				and i.invoiceID <> <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('vid')#">
			) as tmp
			left outer join dbo.tr_invoiceTransactions as it2 on it2.orgID = @orgID and it2.invoiceID = tmp.invoiceID
			group by tmp.invoiceID, tmp.invoiceNumber, tmp.dateDue, tmp.memberName
			order by tmp.invoiceNumber
		</cfquery>
		<cfif local.qryOpenInvoices.recordcount is 0>
			<cflocation url="#this.link.message#&ec=MITNOI" addtoken="no">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_moveInvoiceTransaction.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveMoveInvoiceTransaction" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<!--- init --->
		<cfset arguments.event.setValue('tid',int(val(arguments.event.getValue('tid',0))))>
		<cfset arguments.event.setValue('vid',int(val(arguments.event.getValue('vid',0))))>
		<cfset arguments.event.setValue('newvid',int(val(arguments.event.getValue('newvid',0))))>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="tr_moveInvoiceTransaction">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getValue('mc_siteinfo.orgid'))#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('tid')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('vid')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('newvid')#">
			</cfstoredproc>
			<cfset local.moved = true>
		<cfcatch type="any">
			<cfset local.moved = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfif local.moved>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<script language="javascript">
					if (top.closeMoveIT) {
						top.closeMoveIT(#arguments.event.getValue('vid')#);
						self.location.href = '#this.link.viewInvoiceInfo#&vid=#arguments.event.getValue('vid')#';
					} else {
						top.MCModalUtils.hideModal();
					}
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn returnAppStruct(local.data,"echo")>
		<cfelse>
			<cflocation url="#this.link.message#&ec=SMIT" addtoken="no">
		</cfif>
	</cffunction>

	<cffunction name="editInvoice" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfquery name="local.qryInvoice" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">,
					@invoiceID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getValue('vid',0))#">;

			select i.invoiceID, i.invoiceProfileID, i.fullInvoiceNumber as invoiceNumber,
				i.dateCreated, i.dateBilled, i.dateDue, i.MPProfileID as merchantProfileID, i.payProfileID, i.statusID, 
				istat.status as invoiceStatus, m2.memberid,
				m2.firstname
				+ case when o.hasMiddleName = 1 then isnull(' ' + nullif(m2.middlename,''),'') else '' end
				+ ' ' + m2.lastname
				+ case when o.hasSuffix = 1 then isnull(' ' + nullif(m2.suffix,''),'') else '' end
				+ ' (' + m2.membernumber + ')' as assignedTo,
				ip.profileName as invoiceProfileName, i.payProcessFee, i.processFeePercent
			from dbo.tr_invoices as i
			inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
			inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
			inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = i.assignedToMemberID
			inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = m.activeMemberID
			inner join dbo.organizations as o on o.orgID = m.orgID
			where o.orgID = @orgID
			and i.orgID = @orgID
			and i.invoiceID = @invoiceID
			and istat.status <> 'Paid';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryInvoice.recordcount is 0 and val(arguments.event.getValue('vid',0)) is 0 and arguments.event.getValue('mc_admintoolInfo.myRights.invoiceCreate') is not 1>
			<cflocation url="#this.link.message#&ec=CINP" addtoken="no">
		<cfelseif local.qryInvoice.recordcount is 1 and NOT (arguments.event.getValue('mc_admintoolInfo.myRights.invoiceEdit') is 1 OR (arguments.event.getValue('mc_admintoolInfo.myRights.invoiceClose') is 1 AND NOT listFindNoCase("Closed,Delinquent,Paid",local.qryInvoice.invoiceStatus)))>
			<cflocation url="#this.link.message#&ec=EINP" addtoken="no">
		<cfelseif local.qryInvoice.recordcount is 0 and val(arguments.event.getValue('vid',0)) gt 0>
			<cflocation url="#this.link.message#&ec=EINF" addtoken="no">
		<cfelseif local.qryInvoice.recordcount is 0 and val(arguments.event.getValue('vid',0)) is 0>
			<cfif queryAddRow(local.qryInvoice,1)>
				<cfset querySetCell(local.qryInvoice,"invoiceid",0)>
				<cfset querySetCell(local.qryInvoice,"invoiceprofileid",0)>
				<cfset querySetCell(local.qryInvoice,"invoiceprofilename",'')>
				<cfset querySetCell(local.qryInvoice,"invoiceNumber",0)>
				<cfset querySetCell(local.qryInvoice,"dateCreated",now())>
				<cfset querySetCell(local.qryInvoice,"dateBilled",now())>
				<cfset querySetCell(local.qryInvoice,"dateDue",now())>
				<cfset querySetCell(local.qryInvoice,"merchantProfileID",0)>
				<cfset querySetCell(local.qryInvoice,"payProfileID",0)>
				<cfset querySetCell(local.qryInvoice,"invoiceStatus","Open")>
				<cfset querySetCell(local.qryInvoice,"statusID",1)>
				<cfset querySetCell(local.qryInvoice,"memberid",val(arguments.event.getValue('mid',0)))>
				<cfif val(arguments.event.getValue('mid',0)) is 0>
					<cfset querySetCell(local.qryInvoice,"assignedTo","(No member selected)")>
				<cfelse>
					<cfquery name="local.qryGetMem" datasource="#application.dsn.membercentral.dsn#">
						select m2.firstname
							+ case when o.hasMiddleName = 1 then isnull(' ' + nullif(m2.middlename,''),'') else '' end
							+ ' ' + m2.lastname
							+ case when o.hasSuffix = 1 then isnull(' ' + nullif(m2.suffix,''),'') else '' end as membername,
							m2.membernumber
						from dbo.ams_members as m
						inner join dbo.ams_members as m2 on m2.memberid = m.activeMemberID
						inner join dbo.organizations as o on o.orgID = m.orgID
						where m.memberID = <cfqueryparam value="#val(arguments.event.getValue('mid',0))#" cfsqltype="CF_SQL_INTEGER">
						and o.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">
					</cfquery>
					<cfset querySetCell(local.qryInvoice,"assignedTo","#local.qryGetMem.membername# (#local.qryGetMem.membernumber#)")>
				</cfif>
				<cfset querySetCell(local.qryInvoice,"payProcessFee",0)>
				<cfset querySetCell(local.qryInvoice,"processFeePercent",0)>
			</cfif>
		</cfif>

		<cfset local.midToUse = val(local.qryInvoice.memberID)>
		<cfif local.midToUse eq 0>
			<!--- This may result in the value still being 0, but that's ok, because then we really don't have paymentProfiles --->
			<cfset local.midToUse = val(arguments.event.getValue('mid',0))>
		</cfif>

		<cfquery name="local.qryInvoiceProfiles" datasource="#application.dsn.membercentral.dsn#">
			select profileID, profileName
			from dbo.tr_invoiceProfiles
			where orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgid')#">
			and status <> 'D'
			order by profileName
		</cfquery>

		<cfstoredproc procedure="tr_getMerchantProfilesForInvoice" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryInvoice.invoiceID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
			<cfprocresult name="local.qryMerchantProfiles">
		</cfstoredproc>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryPayProfiles">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @orgID int, @siteID int, @bankDraftGatewayID int, @echeckGatewayID int, @memberID int;
			declare @tmpBankAccounts table (MPPpayProfileID int, siteID int);

			set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgid')#">;
			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
			set @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.midToUse#">;
			set @bankDraftGatewayID = dbo.fn_mp_getGatewayID('bankdraft');
			set @echeckGatewayID = dbo.fn_mp_getGatewayID('MCPayEcheck');

			insert into @tmpBankAccounts (MPPpayProfileID, siteID)
			select mpp.payProfileID, @siteID
			from dbo.tr_bankAccounts as b
			inner join dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = b.MPPpayProfileID
				and b.orgID = @orgID
				and mpp.memberID = @memberID
				and mpp.[status] = 'A';

			select mpp.payProfileID, mp.profileID, mp.profileName, mp.enableProcessingFeeDonation, 
				mp.processFeeDonationFeePercent, pfm.message as processFeeDonationFEMsg, mp.processFeeOtherPaymentsFELabel,
				case when len(isnull(mpp.nickname,'')) > 0 then mpp.nickname + ' (' + mpp.detail + ')' else mpp.detail end as detail
			from dbo.ams_memberPaymentProfiles as mpp
			inner join dbo.mp_profiles as mp on mp.profileID = mpp.profileID
				and mp.gatewayID not in (@bankDraftGatewayID,@echeckGatewayID)
				and mp.[status] in ('A','I')
			left outer join dbo.tr_solicitationMessages as pfm on pfm.messageID = mp.solicitationMessageID
			where mpp.memberID = @memberID
			and mpp.[status] = 'A'
				union all
			select distinct mpp.payProfileID, mp.profileID, mp.profileName, mp.enableProcessingFeeDonation, 
				mp.processFeeDonationFeePercent, '' as processFeeDonationFEMsg, mp.processFeeOtherPaymentsFELabel,
				case when len(isnull(mpp.nickname,'')) > 0 then mpp.nickname + ' (' + mpp.detail + ')' else mpp.detail end as detail
			from @tmpBankAccounts as b
			inner join dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = b.MPPpayProfileID
				and mpp.memberID = @memberID
				and mpp.[status] = 'A'
			inner join dbo.mp_profiles as mp on mp.siteID = b.siteID
				and mp.[status] in ('A','I')
				and mp.gatewayID in (@bankDraftGatewayID,@echeckGatewayID)
			order by profileName, detail, payProfileID desc;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryInvoiceAssocProfiles">
			select imp.profileID
			from dbo.tr_invoiceMerchantProfiles as imp
			inner join dbo.tr_invoices as i on i.invoiceID = imp.invoiceID
			where i.invoiceID = <cfqueryparam value="#val(arguments.event.getValue('vid',0))#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfset local.lockedMPProfileIDs = val(local.qryInvoice.merchantProfileID) ? local.qryInvoice.merchantProfileID : "">
		<cfset local.selectedInvMPProfileIDs = local.lockedMPProfileIDs>
		<cfif local.qryInvoiceAssocProfiles.recordCount>
			<cfset local.selectedInvMPProfileIDs = listAppend(local.selectedInvMPProfileIDs,valueList(local.qryInvoiceAssocProfiles.profileID))>
		</cfif>

		<cfset local.subCurrDate = DateFormat(now(), "yyyy-mm-dd")>
		<cfset local.subDueDate = DateFormat(local.qryInvoice.dateDue, "yyyy-mm-dd")>
		<cfif listFindNoCase("Pending,Closed,Delinquent",local.qryInvoice.invoiceStatus) AND local.qryPayProfiles.recordCount gt 0>
			<cfset local.offerCOF = true>
		<cfelse>
			<cfset local.offerCOF = false>
		</cfif>	

		<cfset local.invDue = 0>
		<cfset local.hasProcessFeePayProfiles = false>
		<cfset local.showProcessingFees = false>
		<cfif val(local.qryInvoice.invoiceID)>
			<cfquery name="local.qryProcessingFeePayProfiles" dbtype="query">
				SELECT TOP 1 profileID
				FROM [local].qryPayProfiles
				WHERE enableProcessingFeeDonation = 1
				AND processFeeDonationFeePercent > 0
			</cfquery>
			<cfset local.hasProcessFeePayProfiles = local.qryProcessingFeePayProfiles.recordCount GT 0>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryInvDue">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">,
					@invoiceID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryInvoice.invoiceID#">;
				
				SELECT SUM(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) as InvDue
				FROM dbo.tr_invoiceTransactions as it 
				inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID
					AND t.transactionID = it.transactionID
				WHERE it.orgID = @orgID 
				and it.invoiceID = @invoiceID;
						
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfset local.invDue = val(local.qryInvDue.InvDue)>
			<cfset local.showProcessingFees = local.hasProcessFeePayProfiles AND local.invDue GT 0>

			<cfset local.processFeeMsg = "">
			<cfset local.processFeeLabel = "">
			<cfif local.showProcessingFees AND val(local.qryInvoice.payProfileID)>
				<cfquery name="local.qryProcessFeeMsg" dbtype="query">
					SELECT processFeeDonationFEMsg, processFeeOtherPaymentsFELabel
					FROM [local].qryPayProfiles
					WHERE payProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryInvoice.payProfileID#">
					AND enableProcessingFeeDonation = 1
					AND processFeeDonationFeePercent > 0
				</cfquery>
				<cfset local.processingFee = NumberFormat(precisionEvaluate(local.invDue * val(local.qryInvoice.processFeePercent)/100),'0.00')>
				<cfset local.processFeeMsg = local.qryProcessFeeMsg.processFeeDonationFEMsg>
				<cfset local.processFeeLabel = replaceNoCase(local.qryProcessFeeMsg.processFeeOtherPaymentsFELabel,"{{AMOUNT}}",dollarFormat(local.processingFee))>
			</cfif>
		</cfif>

		<cfset local.qryInvoiceItemInfo = CreateObject("component","invoice").getInvoiceItemInfo(orgID=arguments.event.getValue('mc_siteinfo.orgID'), invoiceID=local.qryInvoice.invoiceID)>
		<cfset local.allowUpdateCOF = local.qryInvoiceItemInfo.allowUpdateCOF EQ 1>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_editInvoice.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="updateInvoice" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<!--- init --->
		<cfset arguments.event.setValue('vid',int(val(arguments.event.getValue('vid',0))))>
		<cfset arguments.event.setValue('assignedToMemberID',int(val(arguments.event.getValue('assignedToMemberID',0))))>
		<cfset arguments.event.setValue('invoiceProfileID',int(val(arguments.event.getValue('invoiceProfileID',0))))>
		<cfset arguments.event.setValue('statusID',int(val(arguments.event.getValue('statusID',0))))>
		<cfset local.invoiceAction = "">

		<!--- get invoice --->
		<cfquery name="local.qryInvoice" datasource="#application.dsn.membercentral.dsn#">
			select i.invoiceID, i.statusID, i.dateBilled, i.dateDue, ins.status
			from dbo.tr_invoices as i
			inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
			where i.invoiceID = <cfqueryparam value="#arguments.event.getValue('vid')#" cfsqltype="CF_SQL_INTEGER">
			and ins.status <> 'Paid'
		</cfquery>

		<cftry>
			<cfif local.qryInvoice.recordcount is 1 and (arguments.event.getValue('mc_admintoolInfo.myRights.invoiceEdit') is 1 OR (arguments.event.getValue('mc_admintoolInfo.myRights.invoiceClose') is 1 AND NOT listFindNoCase("Closed,Delinquent",local.qryInvoice.status)))>

				<cfif arguments.event.getValue('mc_admintoolInfo.myRights.invoiceEdit') is 1>
					<cfset arguments.event.setValue('dateBilled',dateformat(arguments.event.getTrimValue('dateBilled',local.qryInvoice.dateBilled),"m/d/yyyy"))>
					<cfset arguments.event.setValue('dateDue',dateformat(arguments.event.getTrimValue('dateDue',local.qryInvoice.dateDue),"m/d/yyyy"))>
				</cfif>

				<!--- if invoice will be closed/delinq and is now fully paid with active payments, mark it as paid --->
				<cfif listFind("3,5",arguments.event.getValue('statusID')) and listFindNoCase("Open,Pending,Closed,Delinquent",local.qryInvoice.status)>
					<cfquery name="local.qryAmtDue" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;

						declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">;

						select isnull(sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount),0) as amtDueNoPendingOnInvoice
						from dbo.tr_invoices as i
						left outer join dbo.tr_invoiceTransactions as it
							inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = it.transactionID and t.statusID <> 2
							on it.orgID = @orgID and it.invoiceid = i.invoiceid
						where i.orgID = @orgID
						and i.invoiceID = <cfqueryparam value="#local.qryInvoice.invoiceID#" cfsqltype="CF_SQL_INTEGER">;
					</cfquery>
					<cfif local.qryAmtDue.amtDueNoPendingOnInvoice is 0>
						<cfset arguments.event.setValue('statusID',4)>
					</cfif>
				</cfif>

				<cfset local.crdAssoc = arguments.event.getValue('crdAssoc','')>
				<cfif len(local.crdAssoc)>
					<cfset local.invMPProfileID = val(ListFirst(local.crdAssoc,'-'))>
					<cfset local.invMPPayProfileID = val(ListLast(local.crdAssoc,'-'))>
				<cfelse>
					<cfset local.invMPProfileID = 0>
					<cfset local.invMPPayProfileID = 0>
				</cfif>
				
				<cfset local.invoiceID = local.qryInvoice.invoiceID>
				<cfset local.invoiceAction = "Update">

				<cfset local.dateBilledChanged = dateCompare(arguments.event.getTrimValue('dateBilled'), local.qryInvoice.dateBilled, "d") is not 0>
				<cfset local.dateDueChanged = dateCompare(arguments.event.getTrimValue('dateDue'), local.qryInvoice.dateDue, "d") is not 0>

				<cfset local.qryInvoiceItemInfo = CreateObject("component","invoice").getInvoiceItemInfo(orgID=arguments.event.getValue('mc_siteinfo.orgID'), invoiceID=local.invoiceID)>
				<cfset local.allowUpdateCOF = local.qryInvoiceItemInfo.allowUpdateCOF EQ 1>

				<cfquery name="local.qryUpdate" datasource="#application.dsn.membercentral.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						IF OBJECT_ID('tempdb..##tmpAuditLog') IS NOT NULL 
							DROP TABLE ##tmpAuditLog;
						CREATE TABLE ##tmpAuditLog (auditCode varchar(10), msg varchar(max));

						declare @invoiceID int, @newBilledDate date, @oldBilledDate date, @newDueDate date, @oldDueDate date,
							@orgID int, @siteID int, @nowDate date, @existingPayProfileID int, @payProfileID int, @merchantProfileID int, 
							@existingPayProcessFee bit, @existingProcessFeePercent decimal(5,2), @payProcessFee bit, 
							@processFeePercent decimal(5,2), @invoiceNumber varchar(19), @enteredByMemberID int;
						set @enteredByMemberID = <cfqueryparam value="#val(session.cfcuser.memberdata.memberid)#" cfsqltype="CF_SQL_INTEGER">;
						set @invoiceID = <cfqueryparam value="#local.invoiceID#" cfsqltype="CF_SQL_INTEGER">;
						set @newBilledDate = <cfqueryparam value="#arguments.event.getTrimValue('dateBilled')#" cfsqltype="CF_SQL_DATE">;
						set @oldBilledDate = <cfqueryparam value="#local.qryInvoice.dateBilled#" cfsqltype="CF_SQL_DATE">;
						set @newDueDate = <cfqueryparam value="#arguments.event.getTrimValue('dateDue')#" cfsqltype="CF_SQL_DATE">;
						set @oldDueDate = <cfqueryparam value="#local.qryInvoice.dateDue#" cfsqltype="CF_SQL_DATE">;
						set @orgID = <cfqueryparam value="#arguments.event.getTrimValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;
						set @siteID = <cfqueryparam value="#arguments.event.getTrimValue('mc_siteInfo.siteID')#" cfsqltype="CF_SQL_INTEGER">;
						set @nowDate = getdate();

						<cfif arguments.event.getValue('mc_admintoolInfo.myRights.invoiceEdit') is 1 AND local.allowUpdateCOF>
							SELECT @existingPayProfileID = i.payProfileID, @existingPayProcessFee = i.payProcessFee, 
								@existingProcessFeePercent = i.processFeePercent,
								@invoiceNumber = i.fullInvoiceNumber
							FROM dbo.tr_invoices AS i
							INNER JOIN dbo.organizations AS o ON o.orgID = i.orgID
							WHERE i.invoiceID = @invoiceID;

							<cfif arguments.event.getValue('statusID') is 4>
								SET @payProfileID = NULL;
								SET @merchantProfileID = NULL;
							<cfelseif len(local.crdAssoc)>
								SET @payProfileID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.invMPPayProfileID#">,0);
								SET @merchantProfileID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.invMPProfileID#">,0);
							</cfif>

							SET @payProcessFee = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#val(arguments.event.getValue('payProcessFee',0))#">;

							IF @payProcessFee = 1 AND @payProfileID IS NOT NULL
								SELECT @processFeePercent = mp.processFeeDonationFeePercent
								FROM dbo.mp_profiles AS mp
								INNER JOIN dbo.ams_memberPaymentProfiles AS mpp on mpp.profileID = mp.profileID
								WHERE mpp.payProfileID = @payProfileID
								AND mp.enableProcessingFeeDonation = 1;

							IF ISNULL(@existingPayProfileID ,0) <> ISNULL(@payProfileID,0)
								INSERT INTO ##tmpAuditLog (auditCode, msg)
								SELECT 'INV', 'Pay Profile ' + 
										CASE WHEN mpp.payProfileID IS NOT NULL AND mpp2.payProfileID IS NOT NULL THEN 'changed from ' + mpp.detail + ' to ' + mpp2.detail + ' for'
											WHEN mpp.payProfileID IS NOT NULL AND mpp2.payProfileID IS NULL THEN mpp.detail + ' removed from'
											WHEN mpp.payProfileID IS NULL AND mpp2.payProfileID IS NOT NULL THEN mpp2.detail + ' associated to'
										END + ' Invoice ' + @invoiceNumber
								FROM dbo.tr_invoices as i
								LEFT OUTER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = i.payProfileID
								LEFT OUTER JOIN dbo.ams_memberPaymentProfiles as mpp2 on mpp2.payProfileID = @payProfileID
								WHERE i.invoiceID = @invoiceID;

							IF ISNULL(@existingPayProcessFee ,0) <> ISNULL(@payProcessFee,0)
								INSERT INTO ##tmpAuditLog (auditCode, msg)
								SELECT 'INV', 'Invoice ' + @invoiceNumber + ' Pay Processing Fees changed from ' + CASE WHEN ISNULL(@existingPayProcessFee ,0) = 1 THEN 'Yes' ELSE 'No' END  + ' to ' + CASE WHEN ISNULL(@payProcessFee,0) = 1 THEN 'Yes' ELSE 'No' END;
						
							IF ISNULL(@existingProcessFeePercent ,0) <> ISNULL(@processFeePercent,0)
								INSERT INTO ##tmpAuditLog (auditCode, msg)
								SELECT 'INV', 'Invoice ' + @invoiceNumber + ' Processing Fee Percentage changed from ' + CAST(ISNULL(@existingProcessFeePercent ,0) AS varchar(10)) + '% to ' + CAST(ISNULL(@processFeePercent ,0) AS varchar(10)) + '%';
						</cfif>

						BEGIN TRAN;
							update dbo.tr_invoices
							set assignedToMemberID = <cfqueryparam value="#arguments.event.getValue('assignedToMemberID')#" cfsqltype="CF_SQL_INTEGER">
								<cfif arguments.event.getValue('mc_admintoolInfo.myRights.invoiceEdit') is 1>
									<cfif local.dateBilledChanged>
										, dateBilled = @newBilledDate
									</cfif>
									<cfif local.dateDueChanged>
										, dateDue = @newDueDate
										, expectedPayDate = case when @newDueDate <= @nowDate then @nowDate else @newDueDate end
									</cfif>
									<cfif local.allowUpdateCOF>
										<cfif len(local.crdAssoc)>
											, payProfileID = @payProfileID
											, MPProfileID = @merchantProfileID
										<cfelseif arguments.event.getValue('statusID') is 4>
											, payProfileID = NULL
											, MPProfileID = NULL
										</cfif>
										, payProcessFee = ISNULL(@payProcessFee,0)
										, processFeePercent = @processFeePercent
									</cfif>
								</cfif>
								<cfif arguments.event.getValue('mc_admintoolInfo.myRights.invoiceClose') is 1 AND arguments.event.getValue('statusID') gt 0 AND listFindNoCase("Open,Pending,Closed,Delinquent",local.qryInvoice.status) and local.qryInvoice.statusID neq arguments.event.getValue('statusID')>
									, statusID = <cfqueryparam value="#arguments.event.getValue('statusID')#" cfsqltype="CF_SQL_INTEGER">
								</cfif>
							where invoiceID = @invoiceID;

							<cfif local.allowUpdateCOF>
								<!--- invoice / merchant profiles --->
								MERGE dbo.tr_invoiceMerchantProfiles AS Target
								USING (
									SELECT @invoiceID AS invoiceID, listItem as profileID
									FROM dbo.fn_intListToTable(<cfqueryparam value="#arguments.event.getValue('assocPayProfile','')#" cfsqltype="CF_SQL_VARCHAR">,',')
								) AS Source
								ON Target.invoiceID = Source.invoiceID AND Target.profileID = Source.profileID

								-- Insert new merchant profiles
								WHEN NOT MATCHED BY TARGET THEN
									INSERT (invoiceID, profileID)
									VALUES (Source.invoiceID, Source.profileID)

								-- Delete old merchant profiles for this invoiceID
								WHEN NOT MATCHED BY SOURCE AND Target.invoiceID = @invoiceID THEN
									DELETE;

								-- if there is an associated card on file, need to ensure that profile is added as well
								insert into dbo.tr_invoiceMerchantProfiles (invoiceID, profileID)
								select i.invoiceID, mp.profileID
								from dbo.tr_invoices as i
								inner join dbo.mp_profiles as mp on mp.profileID = i.MPProfileID and mp.[status] in ('A','I')
								where i.orgID = @orgID
								and i.invoiceID = @invoiceID
								and mp.profileID not in (select profileID from dbo.tr_invoiceMerchantProfiles where invoiceID = @invoiceID);
							</cfif>

							<cfif arguments.event.getValue('mc_admintoolInfo.myRights.invoiceClose') is 1 and arguments.event.getValue('statusID') is 4 and listFind("1,2",local.qryInvoice.statusID)>
								insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
								values (@invoiceID, getdate(), 3, #local.qryInvoice.statusID#, @enteredByMemberID);

								insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
								values (@invoiceID, getdate(), 4, 3, @enteredByMemberID);

							<cfelseif arguments.event.getValue('mc_admintoolInfo.myRights.invoiceClose') is 1 and arguments.event.getValue('statusID') gt 0 and listFind("1,2,3",local.qryInvoice.statusID) and local.qryInvoice.statusID neq arguments.event.getValue('statusID')>
								insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
								values (@invoiceID, getdate(), #arguments.event.getValue('statusID')#, #local.qryInvoice.statusID#, @enteredByMemberID);
							</cfif>

							<cfif local.dateBilledChanged>
								INSERT INTO dbo.tr_invoiceDateHistory (invoiceID, type, updateDate, newDate, oldDate, recordedByMemberID)
								VALUES (@invoiceID, 'B', getdate(), @newBilledDate, @oldBilledDate, @enteredByMemberID);
							</cfif>

							<cfif local.dateDueChanged>
								INSERT INTO dbo.tr_invoiceDateHistory (invoiceID, type, updateDate, newDate, oldDate, recordedByMemberID)
								VALUES (@invoiceID, 'D', getdate(), @newDueDate, @oldDueDate, @enteredByMemberID);

								-- add invoice transactions to limit checking table
								INSERT INTO platformQueue.dbo.queue_transactionLimitFlagging (transactionID, orgID, siteID)
								SELECT transactionID, @orgID, @siteID
								from dbo.tr_invoiceTransactions
								where orgID = @orgID
								and invoiceID = @invoiceID;
							</cfif>

							<!--- Check to see if change of due date should change the invoice from closed/delinq --->
							<cfif arguments.event.getValue('mc_admintoolInfo.myRights.invoiceEdit') is 1 and local.dateDueChanged>
								declare @ins_closed int, @ins_delinquent int, @currentStatusID int, @numDaysDelinquent int;
								select @ins_closed = statusID from dbo.tr_invoiceStatuses where [status] = 'Closed';
								select @ins_delinquent = statusID from dbo.tr_invoiceStatuses where [status] = 'Delinquent';

								select @currentStatusID = i.statusID, @numDaysDelinquent = inp.numDaysDelinquent
								from dbo.tr_invoices as i
								inner join dbo.tr_invoiceProfiles as inp on inp.profileID = i.invoiceProfileID
								where i.invoiceID = @invoiceID;

								-- any delinquent invoices that should not be based on inv profile setting should be closed
								-- any delinquent invoices that should not be based on duedate of inv profile setting should be closed
								IF @currentStatusID = @ins_delinquent 
									and (
										@numDaysDelinquent is null
										OR 
										(@numDaysDelinquent is not null and @newDueDate > DateAdd(DD,@numDaysDelinquent*-1,getdate()))
									)
								BEGIN
									UPDATE dbo.tr_invoices
									set statusID = @ins_closed
									where invoiceID = @invoiceID;

									insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
									values (@invoiceID, getdate(), @ins_closed, @ins_delinquent, @enteredByMemberID);
								END

								-- any closed invoices that that should be delinquent based on duedate of inv profile setting
								IF @currentStatusID = @ins_closed and @numDaysDelinquent is not null and @newDueDate <= DateAdd(DD,@numDaysDelinquent*-1,getdate()) BEGIN
									UPDATE dbo.tr_invoices
									set statusID = @ins_delinquent
									where invoiceID = @invoiceID;

									insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
									values (@invoiceID, getdate(), @ins_delinquent, @ins_closed, @enteredByMemberID);
								END
							</cfif>

							IF EXISTS (SELECT 1 FROM ##tmpAuditLog) BEGIN
								INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
								SELECT '{ "c":"auditLog", "d": {
									"AUDITCODE":"' + auditCode + '",
									"ORGID":' + cast(@orgID as varchar(10)) + ',
									"SITEID":' + cast(@siteID as varchar(10)) + ',
									"ACTORMEMBERID":' + cast(@enteredByMemberID as varchar(20)) + ',
									"ACTIONDATE":"' + convert(varchar(20),GETDATE(),120) + '",
									"MESSAGE":"' + replace(dbo.fn_cleanInvalidXMLChars(msg),'"','\"') + '" } }'
								FROM ##tmpAuditLog;
							END
						COMMIT TRAN;

						IF OBJECT_ID('tempdb..##tmpAuditLog') IS NOT NULL 
							DROP TABLE ##tmpAuditLog;

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>

			<cfelseif local.qryInvoice.recordcount is 0 and arguments.event.getValue('mc_admintoolInfo.myRights.invoiceCreate') is 1>
				<cfset arguments.event.setValue('dateBilled',dateformat(arguments.event.getTrimValue('dateBilled',now()),"m/d/yyyy"))>
				<cfset arguments.event.setValue('dateDue',dateformat(arguments.event.getTrimValue('dateDue',now()),"m/d/yyyy"))>

				<cfstoredproc procedure="tr_createInvoice" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('invoiceProfileID')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('assignedToMemberID')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.event.getTrimValue('dateBilled')# #timeformat(now(),'h:mm tt')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.event.getTrimValue('dateDue')# #timeformat(now(),'h:mm tt')#">
					<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.invoiceID">
					<cfprocparam type="Out" cfsqltype="CF_SQL_VARCHAR" variable="local.invoiceNumber">
				</cfstoredproc>

				<cfif len(arguments.event.getValue('assocPayProfile',''))>
					<cfquery name="local.qryAssocProfiles" datasource="#application.dsn.membercentral.dsn#">
						insert into dbo.tr_invoiceMerchantProfiles (invoiceID, profileID)
						select <cfqueryparam value="#local.invoiceID#" cfsqltype="CF_SQL_INTEGER">, listItem
						from dbo.fn_intListToTable(<cfqueryparam value="#arguments.event.getValue('assocPayProfile','')#" cfsqltype="CF_SQL_VARCHAR">,',');
					</cfquery>
				</cfif>

				<cfset local.invoiceAction = "Add">
			</cfif>

		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cflocation url="#this.link.message#&ec=UISV" addtoken="no">
		</cfcatch>
		</cftry>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				<!--- reload the invoice grid if it exists to reflect any changes made to invoice --->
				if (typeof top.doneEditInvoice === 'function') top.doneEditInvoice();

				<cfif structKeyExists(local,"invoiceID")>
					self.location.href = '#this.link.viewInvoiceInfo#&vid=#local.invoiceID#';
				<cfelse>
					top.MCModalUtils.hideModal();
				</cfif>
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="downloadInvoice" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.strInvoice = CreateObject("component","invoice").generateInvoice(siteID=arguments.event.getValue('mc_siteinfo.siteid'), invoiceID=arguments.event.getValue('vid'), tmpFolder=local.strFolder.folderPath, encryptFile=true, namedForBundle=false)>
		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath=local.strInvoice.invoicePath, displayName=ListLast(local.strInvoice.invoicePath,"/"), deleteSourceFile=1)>
		<cfsavecontent variable="local.data">
			<cfoutput>No invoice generated.</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="downloadInvoiceBundle" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfsetting requestTimeOut = "3000">

		<cfset local.qryInvoices = getInvoiceDataFromTransactionID(transactionID=arguments.event.getValue('tid',0))>
	
		<cfif local.qryInvoices.recordCount>
			<cfset local.strInvoiceBundle = createInvoiceBundle(siteID=arguments.event.getValue('mc_siteinfo.siteid'), invoiceIDList=valueList(local.qryInvoices.invoiceID))>
			<cfset application.objDocDownload.doDownloadDocument(sourceFilePath=local.strInvoiceBundle.invoicePath, displayName=local.strInvoiceBundle.displayName, deleteSourceFile=1)>
			<cfsavecontent variable="local.data">
				<cfoutput>No invoice generated.</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>That invoice was not found.</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getInvoiceDataFromTransactionID" access="private" output="false" returntype="query">
		<cfargument name="transactionID" type="numeric" required="true">

		<cfset var qryInvoiceData = "">

		<cfstoredproc procedure="tr_getInvoiceDataFromTransactionID" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.transactionID#">
			<cfprocresult name="qryInvoiceData" resultset="1">
		</cfstoredproc>

		<cfreturn qryInvoiceData>
	</cffunction>

	<cffunction name="createInvoiceBundle" access="private" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="invoiceIDList" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		
		<cfset local.objInvoice = CreateObject("component","invoice")>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix='invbundle')>			
		<cfloop list="#arguments.invoiceIDList#" index="local.invoiceID">
			<cfset local.objInvoice.generateInvoice(siteID=arguments.siteID, invoiceID=local.invoiceID, tmpFolder=local.strFolder.folderPath, encryptFile=false, namedForBundle=true)>
		</cfloop>

		<!--- merge and encrypt pdfs --->
		<cfset local.returnStruct.fileName = "InvoiceBundle.pdf">
		<cfset local.returnStruct.displayName = local.returnStruct.fileName>
		<cfset local.returnStruct.folderPath = local.strFolder.folderPath>
		<cfset local.returnStruct.invoicePath = "#local.returnStruct.folderPath#/#local.returnStruct.fileName#">
		<cfdirectory action="LIST" directory="#local.strFolder.folderPath#" name="local.qryPDFs" filter="Inv_*.pdf" sort="name">
		<cfset application.objCommon.mergePDFs(pdfPath=local.strFolder.folderPath, listofPDFs=valuelist(local.qryPDFs.name), finalOutPutFile=local.returnStruct.fileName)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="setInvoicePayProfiles" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.invoiceEdit') is not 1>
			<cflocation url="#this.link.message#&ec=SIPPNP" addtoken="no">
		</cfif>

		<!--- the pay profiles listed should be those active profiles that allowPayments. --->
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAllowedSitePayProfiles">
			select profileID, profileName
			from dbo.mp_profiles
			where siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
			and status = 'A'
			and allowPayments = 1
			order by profileName
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_setInvoicePayProfiles.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="doSetInvoicePayProfiles" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="duestartDate" type="string" required="yes">
		<cfargument name="dueendDate" type="string" required="yes">
		<cfargument name="billedstartDate" type="string" required="yes">
		<cfargument name="billedendDate" type="string" required="yes">
		<cfargument name="dueAmtStart" type="string" required="yes">
		<cfargument name="dueAmtEnd" type="string" required="yes">
		<cfargument name="invAmtStart" type="string" required="yes">
		<cfargument name="invAmtEnd" type="string" required="yes">
		<cfargument name="statuses" type="string" required="yes">
		<cfargument name="invoiceProfiles" type="string" required="yes">
		<cfargument name="invoiceNumber" type="string" required="yes">
		<cfargument name="trDetail" type="string" required="yes">
		<cfargument name="cardOnFile" type="string" required="yes">
		<cfargument name="associatedMemberID" type="string" required="yes">
		<cfargument name="associatedGroupID" type="string" required="yes">
		<cfargument name="linkedRecords" type="string" required="yes">
		<cfargument name="linkedRecordOptions" type="string" required="yes">
		<cfargument name="assocType" type="string" required="yes">
		<cfargument name="chkAll" type="boolean" required="yes">
		<cfargument name="invoiceIDList" type="string" required="yes">
		<cfargument name="notInvoiceIDList" type="string" required="yes">
		<cfargument name="f_assocPayProfile" type="string" required="yes">

		<cfscript>
			var local = structNew();
			if (not (len(arguments.statuses)))
				local.statuses = '1,2,3';
			else
				local.statuses = arguments.statuses;

			local.invoiceNumber = reReplace(arguments.invoiceNumber,'[^0-9]','','ALL');
			local.trDetail = trim(arguments.trDetail);
			local.invoiceProfiles = arguments.invoiceProfiles;
			local.payProfiles = arguments.f_assocPayProfile;
			local.associatedMemberID = arguments.associatedMemberID;
			local.associatedGroupID = arguments.associatedGroupID;

			local.dueAmtStart = '';
			local.dueAmtEnd = '';
			if (len(arguments.dueAmtStart))
				local.dueAmtStart = val(ReReplace(arguments.dueAmtStart,'[^0-9\.]','','ALL'));
			if (len(arguments.dueAmtEnd))
				local.dueAmtEnd = val(ReReplace(arguments.dueAmtEnd,'[^0-9\.]','','ALL'));
			
			local.invAmtStart = '';
			local.invAmtEnd = '';
			if (len(arguments.invAmtStart))
				local.invAmtStart = val(ReReplace(arguments.invAmtStart,'[^0-9\.]','','ALL'));
			if (len(arguments.invAmtEnd))
				local.invAmtEnd = val(ReReplace(arguments.invAmtEnd,'[^0-9\.]','','ALL'));

			local.returnStruct = structnew();
		</cfscript>

		<!--- card on file options --->
		<cfset local.cof0 = false>
		<cfset local.cofList = arguments.cardOnFile>
		<cfif len(local.cofList)>
			<cfset local.cod0Loc = listFind(local.cofList,0)>
			<cfif local.cod0Loc>
				<cfset local.cof0 = true>
				<cfset local.cofList = listDeleteAt(local.cofList,local.cod0Loc)>
			</cfif>
		</cfif>

		<cftry>
			<cfif not hasInvoiceAdminRights(siteID=arguments.mcproxy_siteID, resourceFunction="invoiceEdit")>
				<cfthrow message="Invalid Request">
			</cfif>

			<cfset local.runGetAssociated = false>
			<cfif len(trim(arguments.assocType)) and len(trim(arguments.linkedRecordOptions))>
				<cfset local.runGetAssociated = true>
			</cfif>

			<cfquery name="local.qryInvoices" datasource="#application.dsn.memberCentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					declare @orgID int = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">;

					IF OBJECT_ID('tempdb..##tblInv') IS NOT NULL
						DROP TABLE ##tblInv;
					CREATE TABLE ##tblInv (invoiceID int PRIMARY KEY);

					<cfif local.runGetAssociated>
						#getAssociatedWithQuery(associatedMemberID=arguments.associatedMemberID, associatedGroupID=arguments.associatedGroupID, 
							linkedRecords=arguments.linkedRecords, linkedRecordOptions=arguments.linkedRecordOptions, assocType=arguments.assocType)#
					</cfif>

					insert into ##tblInv (invoiceID)
					select i.invoiceID
					from dbo.tr_invoices as i
					inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
					inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = i.assignedToMemberID
					<cfif local.runGetAssociated>
						inner join ##tmpAWQInvoices as assocInv on assocInv.invoiceID = i.invoiceID
					</cfif>
					left outer join dbo.tr_invoiceTransactions as it 
						<cfif len(local.trDetail)>
							inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = it.transactionID
						</cfif>
						on it.orgID = @orgID and it.invoiceID = i.invoiceID
					where i.orgID = @orgID
					and istat.status <> 'paid'
					<cfif arguments.chkall is 0 and len(arguments.invoiceIDList) and arrayLen(reMatch("[^0-9,]",arguments.invoiceIDList)) is 0>
						and i.invoiceID in (#arguments.invoiceIDList#)
					<cfelse>
						<cfif arguments.chkall is 1 and len(arguments.notInvoiceIDList) and arrayLen(reMatch("[^0-9,]",arguments.notInvoiceIDList)) is 0>
							and i.invoiceID not in (#arguments.notInvoiceIDList#)
						</cfif>
						<cfif len(local.statuses)>
							and i.statusID in (<cfqueryparam value="#local.statuses#" cfsqltype="CF_SQL_INTEGER" list="yes">)
						</cfif>
						<cfif len(local.invoiceProfiles)>
							and i.invoiceProfileID in (<cfqueryparam value="#local.invoiceProfiles#" cfsqltype="CF_SQL_INTEGER" list="yes">)
						</cfif>
						<cfif len(local.invoiceNumber)>
							and i.invoiceNumber = <cfqueryparam value="#val(local.invoiceNumber)#" cfsqltype="CF_SQL_INTEGER">
						</cfif>
						<cfif len(local.trDetail)>
							and t.detail like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.trDetail#%">
						</cfif>
						<cfif len(arguments.duestartDate)>
							and i.dateDue >= <cfqueryparam value="#arguments.duestartDate#" cfsqltype="CF_SQL_DATE">
						</cfif>
						<cfif len(arguments.dueendDate)>
							and i.dateDue < <cfqueryparam value="#dateAdd('d',1,arguments.dueendDate)#" cfsqltype="CF_SQL_DATE">
						</cfif>
						<cfif len(arguments.billedstartDate)>
							and i.dateBilled >= <cfqueryparam value="#arguments.billedstartDate#" cfsqltype="CF_SQL_DATE">
						</cfif>
						<cfif len(arguments.billedendDate)>
							and i.dateBilled < <cfqueryparam value="#dateAdd('d',1,arguments.billedendDate)#" cfsqltype="CF_SQL_DATE">
						</cfif>
						<cfif len(arguments.cardOnFile)>
							and (
								<cfif local.cof0>
									i.payProfileID is null
								</cfif>
								<cfif local.cof0 and listLen(local.cofList)>
									or
								</cfif>
								<cfif listLen(local.cofList)>
									i.MPProfileID in (<cfqueryparam value="#local.cofList#" cfsqltype="CF_SQL_INTEGER" list="yes">)
								</cfif>
								)
						</cfif>
					</cfif>
					group by i.invoiceID
					having i.invoiceID = i.invoiceID
					<cfif len(local.dueAmtStart)>
						and sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) >= <cfqueryparam value="#local.dueAmtStart#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>
					<cfif len(local.dueAmtEnd)>
						and sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) <= <cfqueryparam value="#local.dueAmtEnd#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>
					<cfif len(local.invAmtStart)>
						and sum(it.cache_invoiceAmountAfterAdjustment) >= <cfqueryparam value="#local.invAmtStart#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>
					<cfif len(local.invAmtEnd)>
						and sum(it.cache_invoiceAmountAfterAdjustment) <= <cfqueryparam value="#local.invAmtEnd#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>;

					IF dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

					BEGIN TRAN;
						DELETE imp
						FROM dbo.tr_invoiceMerchantProfiles as imp
						INNER JOIN ##tblInv as tmp on tmp.invoiceID = imp.invoiceID;

						-- if any were checked, add them to the invoices
						<cfif listLen(local.payProfiles)>
							INSERT INTO dbo.tr_invoiceMerchantProfiles (invoiceID, profileID)
							SELECT tmp.invoiceID, mp.profileID
							FROM ##tblInv as tmp
							CROSS APPLY dbo.fn_intListToTable(<cfqueryparam value="#local.payProfiles#" cfsqltype="CF_SQL_VARCHAR">,',') as li
							INNER JOIN dbo.mp_profiles as mp on mp.profileID = li.listItem
							WHERE mp.[status] in ('A','I');
						</cfif>

						-- if there is an associated card on file for any invoice, need to ensure that profile is added as well
						insert into dbo.tr_invoiceMerchantProfiles (invoiceID, profileID)
						select i.invoiceID, mp.profileID
						from dbo.tr_invoices as i
						inner join ##tblInv as tmp on tmp.invoiceID = i.invoiceID
						inner join dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = i.payProfileID
						inner join dbo.mp_profiles as mp on mp.profileID = mpp.profileID
						where i.orgID = @orgID
						and mp.[status] in ('A','I')
							except 
						select invoiceID, profileID
						from dbo.tr_invoiceMerchantProfiles;
					COMMIT TRAN;

					IF OBJECT_ID('tempdb..##tblInv') IS NOT NULL
						DROP TABLE ##tblInv;
					<cfif local.runGetAssociated>
						IF OBJECT_ID('tempdb..##tmpAWQInvoices') is not null
							DROP TABLE ##tmpAWQInvoices;
					</cfif>

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					IF dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>		
	</cffunction>

	<cffunction name="changeInvoiceDates" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.invoiceEdit') is not 1>
			<cflocation url="#this.link.message#&ec=CIDNP" addtoken="no">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_changeInvoiceDates.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="doChangeInvoiceDates" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="duestartDate" type="string" required="yes">
		<cfargument name="dueendDate" type="string" required="yes">
		<cfargument name="billedstartDate" type="string" required="yes">
		<cfargument name="billedendDate" type="string" required="yes">
		<cfargument name="dueAmtStart" type="string" required="yes">
		<cfargument name="dueAmtEnd" type="string" required="yes">
		<cfargument name="invAmtStart" type="string" required="yes">
		<cfargument name="invAmtEnd" type="string" required="yes">
		<cfargument name="statuses" type="string" required="yes">
		<cfargument name="invoiceProfiles" type="string" required="yes">
		<cfargument name="invoiceNumber" type="string" required="yes">
		<cfargument name="trDetail" type="string" required="yes">
		<cfargument name="cardOnFile" type="string" required="yes">
		<cfargument name="associatedMemberID" type="string" required="yes">
		<cfargument name="associatedGroupID" type="string" required="yes">
		<cfargument name="linkedRecords" type="string" required="yes">
		<cfargument name="linkedRecordOptions" type="string" required="yes">
		<cfargument name="assocType" type="string" required="yes">
		<cfargument name="chkAll" type="boolean" required="yes">
		<cfargument name="invoiceIDList" type="string" required="yes">
		<cfargument name="notInvoiceIDList" type="string" required="yes">
		<cfargument name="f_wtc" type="numeric" required="yes">
		<cfargument name="f_dateDue" type="string" required="yes">
		<cfargument name="f_dateBilled" type="string" required="yes">

		<cfscript>
			var local = structNew();
			if (not (len(arguments.statuses)))
				local.statuses = '1,2,3,5';
			else
				local.statuses = arguments.statuses;

			local.invoiceNumber = reReplace(arguments.invoiceNumber,'[^0-9]','','ALL');
			local.trDetail = trim(arguments.trDetail);
			local.invoiceProfiles = arguments.invoiceProfiles;
			local.associatedMemberID = arguments.associatedMemberID;
			local.associatedGroupID = arguments.associatedGroupID;

			local.dueAmtStart = '';
			local.dueAmtEnd = '';
			if (len(arguments.dueAmtStart))
				local.dueAmtStart = val(ReReplace(arguments.dueAmtStart,'[^0-9\.]','','ALL'));
			if (len(arguments.dueAmtEnd))
				local.dueAmtEnd = val(ReReplace(arguments.dueAmtEnd,'[^0-9\.]','','ALL'));
			
			local.invAmtStart = '';
			local.invAmtEnd = '';
			if (len(arguments.invAmtStart))
				local.invAmtStart = val(ReReplace(arguments.invAmtStart,'[^0-9\.]','','ALL'));
			if (len(arguments.invAmtEnd))
				local.invAmtEnd = val(ReReplace(arguments.invAmtEnd,'[^0-9\.]','','ALL'));

			local.returnStruct = structnew();
		</cfscript>

		<!--- card on file options --->
		<cfset local.cof0 = false>
		<cfset local.cofList = arguments.cardOnFile>
		<cfif len(local.cofList)>
			<cfset local.cod0Loc = listFind(local.cofList,0)>
			<cfif local.cod0Loc>
				<cfset local.cof0 = true>
				<cfset local.cofList = listDeleteAt(local.cofList,local.cod0Loc)>
			</cfif>
		</cfif>

		<cftry>
			<cfif not hasInvoiceAdminRights(siteID=arguments.mcproxy_siteID, resourceFunction="invoiceEdit")>
				<cfthrow message="Invalid Request">
			</cfif>

			<cfif NOT listFind("1,2",arguments.f_wtc)>
				<cfthrow message="Unable to update invoice dates.">
			</cfif>

			<cfset local.runGetAssociated = false>
			<cfif len(trim(arguments.assocType)) and len(trim(arguments.linkedRecordOptions))>
				<cfset local.runGetAssociated = true>
			</cfif>

			<cfquery name="local.qryInvoices" datasource="#application.dsn.memberCentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					declare @orgID int, @newDate date, @nowDate date, @recordedByMemberID int;
					set @orgID = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">;
					<cfif arguments.f_wtc eq 1>
						set @newDate = <cfqueryparam value="#arguments.f_dateDue#" cfsqltype="CF_SQL_DATE">;
					<cfelse>
						set @newDate = <cfqueryparam value="#arguments.f_dateBilled#" cfsqltype="CF_SQL_DATE">;
					</cfif>
					set @nowDate = getdate();
					set @recordedByMemberID = <cfqueryparam value="#session.cfcuser.memberdata.memberID#" cfsqltype="CF_SQL_INTEGER">;

					IF OBJECT_ID('tempdb..##tblInv') IS NOT NULL
						DROP TABLE ##tblInv;
					IF OBJECT_ID('tempdb..##tblInvoices') IS NOT NULL
						DROP TABLE ##tblInvoices;
					CREATE TABLE ##tblInv (invoiceID int PRIMARY KEY);
					CREATE TABLE ##tblInvoices (invoiceID int PRIMARY KEY);

					<cfif local.runGetAssociated>
						#getAssociatedWithQuery(associatedMemberID=arguments.associatedMemberID, associatedGroupID=arguments.associatedGroupID,
							linkedRecords=arguments.linkedRecords, linkedRecordOptions=arguments.linkedRecordOptions, assocType=arguments.assocType)#
					</cfif>

					insert into ##tblInv (invoiceID)
					select i.invoiceID
					from dbo.tr_invoices as i
					inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
					inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = i.assignedToMemberID
					<cfif local.runGetAssociated>
						inner join ##tmpAWQInvoices as assocInv on assocInv.invoiceID = i.invoiceID
					</cfif>
					left outer join dbo.tr_invoiceTransactions as it 
					<cfif len(local.trDetail)>
						inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = it.transactionID
					</cfif>
					on it.orgID = @orgID and it.invoiceID = i.invoiceID
					where i.orgID = @orgID
					and istat.status <> 'paid'
					<cfif arguments.chkall is 0 and len(arguments.invoiceIDList) and arrayLen(reMatch("[^0-9,]",arguments.invoiceIDList)) is 0>
						and i.invoiceID in (#arguments.invoiceIDList#)
					<cfelse>
						<cfif arguments.chkall is 1 and len(arguments.notInvoiceIDList) and arrayLen(reMatch("[^0-9,]",arguments.notInvoiceIDList)) is 0>
							and i.invoiceID not in (#arguments.notInvoiceIDList#)
						</cfif>
						<cfif len(local.statuses)>
							and i.statusID in (<cfqueryparam value="#local.statuses#" cfsqltype="CF_SQL_INTEGER" list="yes">)
						</cfif>
						<cfif len(local.invoiceProfiles)>
							and i.invoiceProfileID in (<cfqueryparam value="#local.invoiceProfiles#" cfsqltype="CF_SQL_INTEGER" list="yes">)
						</cfif>
						<cfif len(local.invoiceNumber)>
							and i.invoiceNumber = <cfqueryparam value="#val(local.invoiceNumber)#" cfsqltype="CF_SQL_INTEGER">
						</cfif>
						<cfif len(local.trDetail)>
							and t.detail like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.trDetail#%">
						</cfif>
						<cfif len(arguments.duestartDate)>
							and i.dateDue >= <cfqueryparam value="#arguments.duestartDate#" cfsqltype="CF_SQL_DATE">
						</cfif>
						<cfif len(arguments.dueendDate)>
							and i.dateDue < <cfqueryparam value="#dateAdd('d',1,arguments.dueendDate)#" cfsqltype="CF_SQL_DATE">
						</cfif>
						<cfif len(arguments.billedstartDate)>
							and i.dateBilled >= <cfqueryparam value="#arguments.billedstartDate#" cfsqltype="CF_SQL_DATE">
						</cfif>
						<cfif len(arguments.billedendDate)>
							and i.dateBilled < <cfqueryparam value="#dateAdd('d',1,arguments.billedendDate)#" cfsqltype="CF_SQL_DATE">
						</cfif>
						<cfif arguments.f_wtc eq 1>
							and i.dateDue <> @newDate
						<cfelse>
							and i.dateBilled <> @newDate
						</cfif>
						<cfif len(arguments.cardOnFile)>
							and (
								<cfif local.cof0>
									i.payProfileID is null
								</cfif>
								<cfif local.cof0 and listLen(local.cofList)>
									or
								</cfif>
								<cfif listLen(local.cofList)>
									i.MPProfileID in (<cfqueryparam value="#local.cofList#" cfsqltype="CF_SQL_INTEGER" list="yes">)
								</cfif>
								)
						</cfif>
					</cfif>
					group by i.invoiceID
					having i.invoiceID = i.invoiceID
					<cfif len(local.dueAmtStart)>
						and sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) >= <cfqueryparam value="#local.dueAmtStart#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>
					<cfif len(local.dueAmtEnd)>
						and sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) <= <cfqueryparam value="#local.dueAmtEnd#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>
					<cfif len(local.invAmtStart)>
						and sum(it.cache_invoiceAmountAfterAdjustment) >= <cfqueryparam value="#local.invAmtStart#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>
					<cfif len(local.invAmtEnd)>
						and sum(it.cache_invoiceAmountAfterAdjustment) <= <cfqueryparam value="#local.invAmtEnd#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>;

					IF dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

					BEGIN TRAN;
						INSERT INTO dbo.tr_invoiceDateHistory (invoiceID, type, updateDate, newDate, oldDate, recordedByMemberID)
						select i.invoiceID, <cfif arguments.f_wtc eq 1>'D'<cfelse>'B'</cfif>, getdate(), @newDate,
							<cfif arguments.f_wtc eq 1>i.dateDue<cfelse>i.dateBilled</cfif>, @recordedByMemberID
						from dbo.tr_invoices as i
						inner join ##tblInv as tmp on tmp.invoiceID = i.invoiceID
						where i.orgID = @orgID;

						<cfif arguments.f_wtc eq 1>
							update i
							set i.dateDue = @newDate,
								i.expectedPayDate = case when @newDate <= @nowDate then @nowDate else @newDate end
							from dbo.tr_invoices as i
							inner join ##tblInv as tmp on tmp.invoiceID = i.invoiceID
							where i.orgID = @orgID;

							-- add invoice transactions to limit checking table
							INSERT INTO platformQueue.dbo.queue_transactionLimitFlagging (transactionID, orgID, siteID)
							SELECT it.transactionID, @orgID, <cfqueryparam value="#arguments.mcproxy_siteID#" cfsqltype="CF_SQL_INTEGER">
							from dbo.tr_invoiceTransactions as it
							inner join ##tblInv as tmp on tmp.invoiceID = it.invoiceID
							where it.orgID = @orgID;

							<!--- Check to see if change of due date should change the invoice from closed/delinq --->
							declare @ins_closed int, @ins_delinquent int;
							select @ins_closed = statusID from dbo.tr_invoiceStatuses where [status] = 'Closed';
							select @ins_delinquent = statusID from dbo.tr_invoiceStatuses where [status] = 'Delinquent';

							-- any delinquent invoices that should not be based on duedate of inv profile setting should be closed
							INSERT INTO ##tblInvoices (invoiceID)
							select i.invoiceID
							from dbo.tr_invoices as i
							inner join ##tblInv as tmp on tmp.invoiceID = i.invoiceID
							inner join dbo.tr_invoiceProfiles as inp on inp.profileID = i.invoiceProfileID
							where i.orgID = @orgID
							and i.statusID = @ins_delinquent
							and i.dateDue > DateAdd(DD,inp.numDaysDelinquent*-1,getdate());

							IF @@ROWCOUNT > 0 BEGIN
								UPDATE tr
								set tr.statusID = @ins_closed
								from dbo.tr_invoices as tr
								inner join ##tblInvoices as tbl on tbl.invoiceID = tr.invoiceID
								where tr.orgID = @orgID;

								insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
								select invoiceID, getdate(), @ins_closed, @ins_delinquent, @recordedByMemberID
								from ##tblInvoices;

								TRUNCATE TABLE ##tblInvoices;
							END

							-- any closed invoices that that should be delinquent based on duedate of inv profile setting
							INSERT INTO ##tblInvoices (invoiceID)
							select i.invoiceID
							from dbo.tr_invoices as i
							inner join ##tblInv as tmp on tmp.invoiceID = i.invoiceID
							inner join dbo.tr_invoiceProfiles as inp on inp.profileID = i.invoiceProfileID
							where i.orgID = @orgID 
							and i.statusID = @ins_closed
							and i.dateDue <= DateAdd(DD,inp.numDaysDelinquent*-1,getdate());

							IF @@ROWCOUNT > 0 BEGIN
								UPDATE tr
								set tr.statusID = @ins_delinquent
								from dbo.tr_invoices as tr
								inner join ##tblInvoices as tbl on tbl.invoiceID = tr.invoiceID
								where tr.orgID = @orgID;

								insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
								select invoiceID, getdate(), @ins_delinquent, @ins_closed, @recordedByMemberID
								from ##tblInvoices;

								TRUNCATE TABLE ##tblInvoices;
							END

						<cfelse>
							update i
							set i.dateBilled = @newDate
							from dbo.tr_invoices as i
							inner join ##tblInv as tmp on tmp.invoiceID = i.invoiceID;
						</cfif>
					COMMIT TRAN;

					IF OBJECT_ID('tempdb..##tblInv') IS NOT NULL
						DROP TABLE ##tblInv;
					IF OBJECT_ID('tempdb..##tblInvoices') IS NOT NULL
						DROP TABLE ##tblInvoices;
					<cfif local.runGetAssociated>
						IF OBJECT_ID('tempdb..##tmpAWQInvoices') is not null
							DROP TABLE ##tmpAWQInvoices;
					</cfif>

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					IF dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="generateInvoiceBundle" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFieldSetSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(
			siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="f_fsid", selectedFieldSetName='Invoices Download Standard',
			allowBlankOption=false, inlinePreviewSectionID="ibInstructionsContent")>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.invoiceDownload') is not 1>
			<cflocation url="#this.link.message#&ec=GIBNP" addtoken="no">
		</cfif>

		<cfquery name="local.qryInvoiceProfiles" datasource="#application.dsn.membercentral.dsn#">
			select profileID, profileName
			from dbo.tr_invoiceProfiles
			where orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgid')#">
			and status <> 'D'
			order by profileName
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_generateInvoiceBundle.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="doGenerateInvoiceBundle" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="duestartDate" type="string" required="yes">
		<cfargument name="dueendDate" type="string" required="yes">
		<cfargument name="billedstartDate" type="string" required="yes">
		<cfargument name="billedendDate" type="string" required="yes">
		<cfargument name="dueAmtStart" type="string" required="yes">
		<cfargument name="dueAmtEnd" type="string" required="yes">
		<cfargument name="invAmtStart" type="string" required="yes">
		<cfargument name="invAmtEnd" type="string" required="yes">
		<cfargument name="statuses" type="string" required="yes">
		<cfargument name="invoiceProfiles" type="string" required="yes">
		<cfargument name="invoiceNumber" type="string" required="yes">
		<cfargument name="trDetail" type="string" required="yes">
		<cfargument name="cardOnFile" type="string" required="yes">
		<cfargument name="associatedMemberID" type="string" required="yes">
		<cfargument name="f_wtp" type="numeric" required="yes">
		<cfargument name="f_sdf" type="string" required="yes">
		<cfargument name="f_ip" type="string" required="yes">
		<cfargument name="f_stmt" type="string" required="yes">
		<cfargument name="f_fsid" type="string" required="yes">
		<cfargument name="associatedGroupID" type="string" required="yes">
		<cfargument name="linkedRecords" type="string" required="yes">
		<cfargument name="linkedRecordOptions" type="string" required="yes">
		<cfargument name="assocType" type="string" required="yes">
		<cfargument name="chkall" type="boolean" required="yes">
		<cfargument name="incIDs" type="string" required="yes">
		<cfargument name="excIDs" type="string" required="yes">

		<cfscript>
			var local = structNew();

			if(not hasInvoiceAdminRights(siteID=arguments.mcproxy_siteID, resourceFunction="invoiceDownload")){
				return { "success":false };
			}

			local.maxInvoices = 250;
			if (not (len(arguments.statuses)))
				local.statuses = '1,3';
			else
				local.statuses = arguments.statuses;

			local.invoiceNumber = reReplace(arguments.invoiceNumber,'[^0-9]','','ALL');
			local.trDetail = trim(arguments.trDetail);
			local.invoiceProfiles = arguments.invoiceProfiles;

			local.dueAmtStart = '';
			local.dueAmtEnd = '';
			if (len(arguments.dueAmtStart))
				local.dueAmtStart = val(ReReplace(arguments.dueAmtStart,'[^0-9\.]','','ALL'));
			if (len(arguments.dueAmtEnd))
				local.dueAmtEnd = val(ReReplace(arguments.dueAmtEnd,'[^0-9\.]','','ALL'));
			
			local.invAmtStart = '';
			local.invAmtEnd = '';
			if (len(arguments.invAmtStart))
				local.invAmtStart = val(ReReplace(arguments.invAmtStart,'[^0-9\.]','','ALL'));
			if (len(arguments.invAmtEnd))
				local.invAmtEnd = val(ReReplace(arguments.invAmtEnd,'[^0-9\.]','','ALL'));
		</cfscript>

		<!--- card on file options --->
		<cfset local.cof0 = false>
		<cfset local.cofList = arguments.cardOnFile>
		<cfif len(local.cofList)>
			<cfset local.cod0Loc = listFind(local.cofList,0)>
			<cfif local.cod0Loc>
				<cfset local.cof0 = true>
				<cfset local.cofList = listDeleteAt(local.cofList,local.cod0Loc)>
			</cfif>
		</cfif>

		<cfsetting requestTimeOut = "2400">

		<!--- create temp folder for index and pdfs --->
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix="invbundle")>

		<!--- get fieldset --->
		<cfset local.resultsFieldsetID = val(arguments.f_fsid)>

		<cfset local.runGetAssociated = false>
		<cfif len(trim(arguments.assocType)) and len(trim(arguments.linkedRecordOptions))>
			<cfset local.runGetAssociated = true>
		</cfif>

		<cfquery name="local.qryInvoices" datasource="#application.dsn.memberCentral.dsn#" result="local.qryInvoicesResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @orgID int, @fieldSetID int, @outputFieldsXML xml;
				set @orgID = <cfqueryparam value="#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgID#" cfsqltype="CF_SQL_INTEGER">;
				set @fieldSetID = <cfqueryparam value="#local.resultsFieldsetID#" cfsqltype="CF_SQL_INTEGER">;

				IF OBJECT_ID('tempdb..##tmpINVIndex') IS NOT NULL
					DROP TABLE ##tmpINVIndex;
				IF OBJECT_ID('tempdb..##tmpINVIndexMembers') IS NOT NULL
					DROP TABLE ##tmpINVIndexMembers;
				IF OBJECT_ID('tempdb..##tmpINVIndexMembersForFS') IS NOT NULL
					DROP TABLE ##tmpINVIndexMembersForFS;
				IF OBJECT_ID('tempdb..##tmpINVIndexFinal') IS NOT NULL
					DROP TABLE ##tmpINVIndexFinal;
				CREATE TABLE ##tmpINVIndexMembers (MFSAutoID int IDENTITY(1,1) not null);
				CREATE TABLE ##tmpINVIndexMembersForFS (memberID int PRIMARY KEY);

				<cfif local.runGetAssociated>
					#getAssociatedWithQuery(associatedMemberID=arguments.associatedMemberID, associatedGroupID=arguments.associatedGroupID, 
						linkedRecords=arguments.linkedRecords, linkedRecordOptions=arguments.linkedRecordOptions, assocType=arguments.assocType)#
				</cfif>

				select tmp.invoiceid, tmp.invoiceProfileID, m.memberid, tmp.invoiceNumber as [Invoice Number], tmp.profileName as [Invoice Profile],
					tmp.dateBilled as [Date Billed], tmp.dateDue as [Date Due], tmp.statusID, dbo.fn_tr_showInvoicePayOnlineLink(tmp.invoiceid) as showLink,
					'#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).scheme#://#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).mainHostName#/invoices' as [Payment Link],
					tmp.invoiceCode as [Invoice Code], tmp.invAmt as [Invoice Amount], tmp.invDue as [Amount Due],
					m.firstname as [First Name], m.middlename as [Middle Name], m.lastname as [Last Name], m.Suffix,
					m.membernumber as [MemberNumber], m.Company
				into ##tmpINVIndex
				from (
					select i.invoiceID, i.invoiceProfileID, i.statusID, i.fullInvoiceNumber as invoiceNumber,
						ip.profileName, i.dateBilled, i.dateDue, i.invoiceCode,
						sum(it.cache_invoiceAmountAfterAdjustment) as InvAmt,
						sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) as InvDue,
						m.activeMemberid as memberID
					from dbo.tr_invoices as i
					inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
					inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
					inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = i.assignedToMemberID
					inner join dbo.organizations as o on o.orgID = @orgID
					<cfif local.runGetAssociated>
						inner join ##tmpAWQInvoices as assocInv on assocInv.invoiceID = i.invoiceID
					</cfif>
					left outer join dbo.tr_invoiceTransactions as it 
						<cfif len(local.trDetail)>
							inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = it.transactionID
						</cfif>
						on it.orgID = @orgID and it.invoiceID = i.invoiceID
					where i.orgID = @orgID
					and istat.status in ('Closed','Delinquent','Paid')
					<cfif arguments.chkall is 0 and len(arguments.incIDs) and arrayLen(reMatch("[^0-9,]",arguments.incIDs)) is 0>
						and i.invoiceID in (#arguments.incIDs#)
					<cfelse>
						<cfif arguments.chkall is 1 and len(arguments.excIDs) and arrayLen(reMatch("[^0-9,]",arguments.excIDs)) is 0>
							and i.invoiceID not in (#arguments.excIDs#)
						</cfif>
						<cfif len(local.statuses)>
							and i.statusID in (<cfqueryparam value="#local.statuses#" cfsqltype="CF_SQL_INTEGER" list="yes">)
						</cfif>
						<cfif len(local.invoiceProfiles)>
							and i.invoiceProfileID in (<cfqueryparam value="#local.invoiceProfiles#" cfsqltype="CF_SQL_INTEGER" list="yes">)
						</cfif>
						<cfif len(local.invoiceNumber)>
							and i.invoiceNumber = <cfqueryparam value="#val(local.invoiceNumber)#" cfsqltype="CF_SQL_INTEGER">
						</cfif>
						<cfif len(local.trDetail)>
							and t.detail like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.trDetail#%">
						</cfif>
						<cfif len(arguments.duestartDate)>
							and i.dateDue >= <cfqueryparam value="#arguments.duestartDate#" cfsqltype="CF_SQL_DATE">
						</cfif>
						<cfif len(arguments.dueendDate)>
							and i.dateDue < <cfqueryparam value="#dateAdd('d',1,arguments.dueendDate)#" cfsqltype="CF_SQL_DATE">
						</cfif>
						<cfif len(arguments.billedstartDate)>
							and i.dateBilled >= <cfqueryparam value="#arguments.billedstartDate#" cfsqltype="CF_SQL_DATE">
						</cfif>
						<cfif len(arguments.billedendDate)>
							and i.dateBilled < <cfqueryparam value="#dateAdd('d',1,arguments.billedendDate)#" cfsqltype="CF_SQL_DATE">
						</cfif>
						<cfif len(arguments.cardOnFile)>
							and (
								<cfif local.cof0>
									i.payProfileID is null
								</cfif>
								<cfif local.cof0 and listLen(local.cofList)>
									or
								</cfif>
								<cfif listLen(local.cofList)>
									i.MPProfileID in (<cfqueryparam value="#local.cofList#" cfsqltype="CF_SQL_INTEGER" list="yes">)
								</cfif>
								)
						</cfif>
					</cfif>
					group by i.invoiceID, i.invoiceProfileID, i.statusID, i.fullInvoiceNumber, ip.profileName, i.dateBilled, i.dateDue, i.invoiceCode, m.activeMemberid
					having i.invoiceCode = i.invoiceCode
					<cfif len(local.dueAmtStart)>
						and sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) >= <cfqueryparam value="#local.dueAmtStart#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>
					<cfif len(local.dueAmtEnd)>
						and sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) <= <cfqueryparam value="#local.dueAmtEnd#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>
					<cfif len(local.invAmtStart)>
						and sum(it.cache_invoiceAmountAfterAdjustment) >= <cfqueryparam value="#local.invAmtStart#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>
					<cfif len(local.invAmtEnd)>
						and sum(it.cache_invoiceAmountAfterAdjustment) <= <cfqueryparam value="#local.invAmtEnd#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>
				) as tmp
				inner join dbo.ams_members as m on m.memberid = tmp.memberID
				where m.orgID = @orgID;

				-- query result
				select invoiceid, memberid, MemberNumber
				from ##tmpINVIndex
				ORDER BY MemberNumber, InvoiceProfileID, [Invoice Number];

				<cfif listFind("2,3,4",arguments.f_wtp)>
					<cfif listFind("2,3",arguments.f_wtp)>
						IF (@@ROWCOUNT > 0 and @@ROWCOUNT <= #local.maxInvoices#) BEGIN
					<cfelse>
						IF (@@ROWCOUNT > 0) BEGIN
					</cfif>

						-- get fieldset data
						INSERT INTO ##tmpINVIndexMembersForFS (memberID)
						select distinct memberID
						from ##tmpINVIndex;

						EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@fieldsetID, @existingFields='',
							@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmpINVIndexMembersForFS', @membersResultTableName='##tmpINVIndexMembers',
							@linkedMembers=0, @mode='export', @outputFieldsXML=@outputFieldsXML OUTPUT;

						SELECT ROW_NUMBER() OVER(ORDER BY tmp.[MemberNumber], tmp.[InvoiceProfileID], tmp.[Invoice Number]) as row,
							tmp.[Invoice Number], tmp.[Invoice Profile],
							convert(varchar(10),tmp.[Date Billed],101) as [Date Billed],
							convert(varchar(10),tmp.[Date Due],101) as [Date Due],
							case when tmp.statusid in (3,5) and tmp.showLink = 1 then tmp.[Payment Link] else '''' end as [Payment Link],
							case when tmp.statusid in (3,5) and tmp.showLink = 1 then tmp.[Invoice Code] else '''' end as [Invoice Code],
							tmp.[Invoice Amount], tmp.[Amount Due],
							vw.*
						INTO ##tmpINVIndexFinal
						FROM ##tmpINVIndex as tmp
						INNER JOIN ##tmpINVIndexMembers as vw on vw.memberID = tmp.memberID;

						ALTER TABLE ##tmpINVIndexFinal DROP COLUMN memberID;

						DECLARE @selectsql varchar(max) = '
							SELECT *, row as mcCSVorder
							*FROM* ##tmpINVIndexFinal';
						EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/invoiceBundle.csv",'\')#', @returnColumns=0;
					END
				</cfif>

				IF OBJECT_ID('tempdb..##tmpINVIndex') IS NOT NULL
					DROP TABLE ##tmpINVIndex;
				IF OBJECT_ID('tempdb..##tmpINVIndexMembers') IS NOT NULL
					DROP TABLE ##tmpINVIndexMembers;
				IF OBJECT_ID('tempdb..##tmpINVIndexMembersForFS') IS NOT NULL
					DROP TABLE ##tmpINVIndexMembersForFS;
				IF OBJECT_ID('tempdb..##tmpINVIndexFinal') IS NOT NULL
					DROP TABLE ##tmpINVIndexFinal;
				<cfif local.runGetAssociated>
					IF OBJECT_ID('tempdb..##tmpAWQInvoices') is not null
						DROP TABLE ##tmpAWQInvoices;
				</cfif>

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
 
		<cfscript>
		local.returnStruct = structnew();
		local.returnStruct.data = structNew();
		local.returnStruct.data.invoiceCount = local.qryInvoices.recordCount;
		local.returnStruct.data.maxinvoices = local.maxInvoices;
		</cfscript>

		<cfif arguments.f_wtp eq 4 and local.qryInvoices.recordCount>
			<cfscript>
			local.returnStruct.data.maximumexceeded = false;
			local.returnStruct.data.rfp = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/invoiceBundle.csv", displayName="invoiceDownload.csv", deleteSourceFile=0);
			local.returnStruct.success = true;
			</cfscript>

		<cfelseif arguments.f_wtp neq 4 and local.qryInvoices.recordCount gt local.maxInvoices>
			<cfscript>
			local.returnStruct.data.maximumexceeded = true;
			local.returnStruct.success = true;
			</cfscript>

		<cfelseif arguments.f_wtp neq 4 and local.qryInvoices.recordCount>
			<cfset local.objInvoice = CreateObject("component","model.admin.transactions.invoice")>

			<!--- if wtp is 1,3 then we need to print statements --->
			<cfif listFind("1,3",arguments.f_wtp)>
				<cftry>
					<cfif arguments.f_sdf is 1>
						<cfset local.forceIP = arguments.f_ip>
					<cfelse>
						<cfset local.forceIP = 0>
					</cfif>

					<cfoutput query="local.qryInvoices" group="MemberID">
						<cfquery name="local.qryInvoicesMem" dbtype="query">
							select *
							from [local].qryInvoices
							where MemberID = #local.qryInvoices.MemberID#
						</cfquery>
						<cfset local.objInvoice.generateInvoiceStatement(qryInvoices=local.qryInvoicesMem, forceIP=local.forceIP, message=arguments.f_stmt, tmpFolder=local.strFolder.folderPath)>
					</cfoutput>
				<cfcatch type="Any">
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
				</cfcatch>
				</cftry>
			</cfif>

			<!--- if wtp is 2,3 then we need to print invoices --->
			<cfif listFind("2,3",arguments.f_wtp)>
				<cftry>
					<cfloop query="local.qryInvoices">
						<cfset local.objInvoice.generateInvoice(siteID=arguments.mcproxy_siteID, invoiceID=local.qryInvoices.invoiceID, tmpFolder=local.strFolder.folderPath, encryptFile=false, namedForBundle=true)>
					</cfloop>
				<cfcatch type="Any">
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
				</cfcatch>
				</cftry>
			</cfif>

			<!--- merge and encrypt pdfs --->
			<cfdirectory action="LIST" directory="#local.strFolder.folderPath#" name="local.qryPDFs" filter="Inv*.pdf" sort="name">
			<cfset application.objCommon.mergePDFs(local.strFolder.folderPath,valuelist(local.qryPDFs.name),"invoiceBundle.pdf")>

			<!--- zip pdf and csv --->
			<cfzip action="zip" file="#local.strFolder.folderPath#/invoiceBundleDownload.zip" source="#local.strFolder.folderPath#" filter="invoiceBundle.*" />

			<cfscript>
			local.returnStruct.data.maximumexceeded = false;
			local.returnStruct.data.rfp = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/invoiceBundleDownload.zip", displayName="invoiceBundleDownload.zip", deleteSourceFile=0);
			local.returnStruct.success = true;
			</cfscript>
		<cfelse>
			<cfscript>
			local.returnStruct.data.maximumexceeded = false;
			local.returnStruct.success = true;
			</cfscript>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doMassRefreshInvoiceMessages" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="duestartDate" type="string" required="yes">
		<cfargument name="dueendDate" type="string" required="yes">
		<cfargument name="billedstartDate" type="string" required="yes">
		<cfargument name="billedendDate" type="string" required="yes">
		<cfargument name="dueAmtStart" type="string" required="yes">
		<cfargument name="dueAmtEnd" type="string" required="yes">
		<cfargument name="invAmtStart" type="string" required="yes">
		<cfargument name="invAmtEnd" type="string" required="yes">
		<cfargument name="statuses" type="string" required="yes">
		<cfargument name="invoiceProfiles" type="string" required="yes">
		<cfargument name="invoiceNumber" type="string" required="yes">
		<cfargument name="trDetail" type="string" required="yes">
		<cfargument name="cardOnFile" type="string" required="yes">
		<cfargument name="associatedMemberID" type="string" required="yes">
		<cfargument name="associatedGroupID" type="string" required="yes">
		<cfargument name="linkedRecords" type="string" required="yes">
		<cfargument name="linkedRecordOptions" type="string" required="yes">
		<cfargument name="assocType" type="string" required="yes">
		<cfargument name="chkAll" type="boolean" required="yes">
		<cfargument name="invoiceIDList" type="string" required="yes">
		<cfargument name="notInvoiceIDList" type="string" required="yes">

		<cfscript>
			var local = structNew();

			if(not hasInvoiceAdminRights(siteID=arguments.mcproxy_siteID, resourceFunction="invoiceEdit"))
				throw(message="Invalid Request");

			local.maxInvoices = 1000;
			if (not (len(arguments.statuses)))
				local.statuses = '1,2,3,4';
			else
				local.statuses = arguments.statuses;

			local.invoiceNumber = reReplace(arguments.invoiceNumber,'[^0-9]','','ALL');
			local.trDetail = trim(arguments.trDetail);
			local.invoiceProfiles = arguments.invoiceProfiles;
			local.associatedMemberID = arguments.associatedMemberID;
			local.associatedGroupID = arguments.associatedGroupID;

			local.dueAmtStart = '';
			local.dueAmtEnd = '';
			if (len(arguments.dueAmtStart))
				local.dueAmtStart = val(ReReplace(arguments.dueAmtStart,'[^0-9\.]','','ALL'));
			if (len(arguments.dueAmtEnd))
				local.dueAmtEnd = val(ReReplace(arguments.dueAmtEnd,'[^0-9\.]','','ALL'));
				
			local.invAmtStart = '';
			local.invAmtEnd = '';
			if (len(arguments.invAmtStart))
				local.invAmtStart = val(ReReplace(arguments.invAmtStart,'[^0-9\.]','','ALL'));
			if (len(arguments.invAmtEnd))
				local.invAmtEnd = val(ReReplace(arguments.invAmtEnd,'[^0-9\.]','','ALL'));

			local.returnStruct = structnew();
			local.returnStruct.data = structNew();
			local.returnStruct.data.maxinvoices = local.maxInvoices;
		</cfscript>

		<!--- card on file options --->
		<cfset local.cof0 = false>
		<cfset local.cofList = arguments.cardOnFile>
		<cfif len(local.cofList)>
			<cfset local.cod0Loc = listFind(local.cofList,0)>
			<cfif local.cod0Loc>
				<cfset local.cof0 = true>
				<cfset local.cofList = listDeleteAt(local.cofList,local.cod0Loc)>
			</cfif>
		</cfif>

		<cfset local.runGetAssociated = false>
		<cfif len(trim(arguments.assocType)) and len(trim(arguments.linkedRecordOptions))>
			<cfset local.runGetAssociated = true>
		</cfif>

		<cfquery name="local.qryInvoices" datasource="#application.dsn.memberCentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @orgID int = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">;

				<cfif local.runGetAssociated>
					#getAssociatedWithQuery(associatedMemberID=arguments.associatedMemberID, associatedGroupID=arguments.associatedGroupID,
						linkedRecords=arguments.linkedRecords, linkedRecordOptions=arguments.linkedRecordOptions, assocType=arguments.assocType)#
				</cfif>

				select i.invoiceID
				from dbo.tr_invoices as i
				inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = i.assignedToMemberID
				<cfif local.runGetAssociated>
					inner join ##tmpAWQInvoices as assocInv on assocInv.invoiceID = i.invoiceID
				</cfif>
				left outer join dbo.tr_invoiceTransactions as it 
					<cfif len(local.trDetail)>
						inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = it.transactionID
					</cfif>
					on it.orgID = @orgID and it.invoiceID = i.invoiceID
				where i.orgID = @orgID
				<cfif arguments.chkall is 0 and len(arguments.invoiceIDList) and arrayLen(reMatch("[^0-9,]",arguments.invoiceIDList)) is 0>
					and i.invoiceID in (#arguments.invoiceIDList#)
				<cfelse>
					<cfif arguments.chkall is 1 and len(arguments.notInvoiceIDList) and arrayLen(reMatch("[^0-9,]",arguments.notInvoiceIDList)) is 0>
						and i.invoiceID not in (#arguments.notInvoiceIDList#)
					</cfif>
					<cfif len(local.statuses)>
						and i.statusID in (<cfqueryparam value="#local.statuses#" cfsqltype="CF_SQL_INTEGER" list="yes">)
					</cfif>
					<cfif len(local.invoiceProfiles)>
						and i.invoiceProfileID in (<cfqueryparam value="#local.invoiceProfiles#" cfsqltype="CF_SQL_INTEGER" list="yes">)
					</cfif>
					<cfif len(local.invoiceNumber)>
						and i.invoiceNumber = <cfqueryparam value="#val(local.invoiceNumber)#" cfsqltype="CF_SQL_INTEGER">
					</cfif>
					<cfif len(local.trDetail)>
						and t.detail like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.trDetail#%">
					</cfif>
					<cfif len(arguments.duestartDate)>
						and i.dateDue >= <cfqueryparam value="#arguments.duestartDate#" cfsqltype="CF_SQL_DATE">
					</cfif>
					<cfif len(arguments.dueendDate)>
						and i.dateDue < <cfqueryparam value="#dateAdd('d',1,arguments.dueendDate)#" cfsqltype="CF_SQL_DATE">
					</cfif>
					<cfif len(arguments.billedstartDate)>
						and i.dateBilled >= <cfqueryparam value="#arguments.billedstartDate#" cfsqltype="CF_SQL_DATE">
					</cfif>
					<cfif len(arguments.billedendDate)>
						and i.dateBilled < <cfqueryparam value="#dateAdd('d',1,arguments.billedendDate)#" cfsqltype="CF_SQL_DATE">
					</cfif>
					<cfif len(arguments.cardOnFile)>
						and (
							<cfif local.cof0>
								i.payProfileID is null
							</cfif>
							<cfif local.cof0 and listLen(local.cofList)>
								or
							</cfif>
							<cfif listLen(local.cofList)>
								i.MPProfileID in (<cfqueryparam value="#local.cofList#" cfsqltype="CF_SQL_INTEGER" list="yes">)
							</cfif>
							)
					</cfif>
				</cfif>
				group by i.invoiceID
				having i.invoiceID = i.invoiceID
				<cfif len(local.dueAmtStart)>
					and sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) >= <cfqueryparam value="#local.dueAmtStart#" cfsqltype="CF_SQL_DECIMAL" scale="2">
				</cfif>
				<cfif len(local.dueAmtEnd)>
					and sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) <= <cfqueryparam value="#local.dueAmtEnd#" cfsqltype="CF_SQL_DECIMAL" scale="2">
				</cfif>
				<cfif len(local.invAmtStart)>
					and sum(it.cache_invoiceAmountAfterAdjustment) >= <cfqueryparam value="#local.invAmtStart#" cfsqltype="CF_SQL_DECIMAL" scale="2">
				</cfif>
				<cfif len(local.invAmtEnd)>
					and sum(it.cache_invoiceAmountAfterAdjustment) <= <cfqueryparam value="#local.invAmtEnd#" cfsqltype="CF_SQL_DECIMAL" scale="2">
				</cfif>;

				<cfif local.runGetAssociated>
					IF OBJECT_ID('tempdb..##tmpAWQInvoices') is not null
						DROP TABLE ##tmpAWQInvoices;
				</cfif>

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.returnStruct.data.invoiceCount = local.qryInvoices.recordCount>

		<cfif local.qryInvoices.recordCount gt local.maxInvoices>
			<cfset local.returnStruct.data.maximumexceeded = true>
			<cfset local.returnStruct.success = true>
		<cfelse>
			<cfset local.returnStruct.data.maximumexceeded = false>
			<cftry>
				<cfstoredproc procedure="tr_refreshInvoiceMessages" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#ValueList(local.qryInvoices.invoiceID)#">
				</cfstoredproc>
				<cfset local.returnStruct.success = true>
			<cfcatch type="Any">
				<cfset local.returnStruct.success = false>
			</cfcatch>
			</cftry>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doMassCloseInvoices" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="duestartDate" type="string" required="yes">
		<cfargument name="dueendDate" type="string" required="yes">
		<cfargument name="billedstartDate" type="string" required="yes">
		<cfargument name="billedendDate" type="string" required="yes">
		<cfargument name="dueAmtStart" type="string" required="yes">
		<cfargument name="dueAmtEnd" type="string" required="yes">
		<cfargument name="invAmtStart" type="string" required="yes">
		<cfargument name="invAmtEnd" type="string" required="yes">
		<cfargument name="statuses" type="string" required="yes">
		<cfargument name="invoiceProfiles" type="string" required="yes">
		<cfargument name="invoiceNumber" type="string" required="yes">
		<cfargument name="trDetail" type="string" required="yes">
		<cfargument name="cardOnFile" type="string" required="yes">
		<cfargument name="associatedMemberID" type="string" required="yes">
		<cfargument name="associatedGroupID" type="string" required="yes">
		<cfargument name="linkedRecords" type="string" required="yes">
		<cfargument name="linkedRecordOptions" type="string" required="yes">
		<cfargument name="assocType" type="string" required="yes">
		<cfargument name="chkAll" type="boolean" required="yes">
		<cfargument name="invoiceIDList" type="string" required="yes">
		<cfargument name="notInvoiceIDList" type="string" required="yes">

		<cfscript>
			var local = structNew();

			if(not hasInvoiceAdminRights(siteID=arguments.mcproxy_siteID, resourceFunction="invoiceClose"))
				throw(message="Invalid Request");
			
			local.maxInvoices = 1000;
			if (not (len(arguments.statuses)))
				local.statuses = '1';
			else
				local.statuses = arguments.statuses;

			local.invoiceNumber = reReplace(arguments.invoiceNumber,'[^0-9]','','ALL');
			local.trDetail = trim(arguments.trDetail);
			local.invoiceProfiles = arguments.invoiceProfiles;
			local.associatedMemberID = arguments.associatedMemberID;
			local.associatedGroupID = arguments.associatedGroupID;

			local.dueAmtStart = '';
			local.dueAmtEnd = '';
			if (len(arguments.dueAmtStart))
				local.dueAmtStart = val(ReReplace(arguments.dueAmtStart,'[^0-9\.]','','ALL'));
			if (len(arguments.dueAmtEnd))
				local.dueAmtEnd = val(ReReplace(arguments.dueAmtEnd,'[^0-9\.]','','ALL'));
				
			local.invAmtStart = '';
			local.invAmtEnd = '';
			if (len(arguments.invAmtStart))
				local.invAmtStart = val(ReReplace(arguments.invAmtStart,'[^0-9\.]','','ALL'));
			if (len(arguments.invAmtEnd))
				local.invAmtEnd = val(ReReplace(arguments.invAmtEnd,'[^0-9\.]','','ALL'));

			local.returnStruct = structnew();
			local.returnStruct.data = structNew();
			local.returnStruct.data.maxinvoices = local.maxInvoices;
		</cfscript>

		<!--- card on file options --->
		<cfset local.cof0 = false>
		<cfset local.cofList = arguments.cardOnFile>
		<cfif len(local.cofList)>
			<cfset local.cod0Loc = listFind(local.cofList,0)>
			<cfif local.cod0Loc>
				<cfset local.cof0 = true>
				<cfset local.cofList = listDeleteAt(local.cofList,local.cod0Loc)>
			</cfif>
		</cfif>

		<cfset local.runGetAssociated = false>
		<cfif len(trim(arguments.assocType)) and len(trim(arguments.linkedRecordOptions))>
			<cfset local.runGetAssociated = true>
		</cfif>

		<cfquery name="local.qryInvoices" datasource="#application.dsn.memberCentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @orgID int = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">;

				<cfif local.runGetAssociated>
					#getAssociatedWithQuery(associatedMemberID=arguments.associatedMemberID, associatedGroupID=arguments.associatedGroupID,
						linkedRecords=arguments.linkedRecords, linkedRecordOptions=arguments.linkedRecordOptions, assocType=arguments.assocType)#
				</cfif>

				select i.invoiceID
				from dbo.tr_invoices as i
				inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
				inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = i.assignedToMemberID
				<cfif local.runGetAssociated>
					inner join ##tmpAWQInvoices as assocInv on assocInv.invoiceID = i.invoiceID
				</cfif>
				left outer join dbo.tr_invoiceTransactions as it 
					<cfif len(local.trDetail)>
						inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = it.transactionID
					</cfif>
					on it.orgID = @orgID and it.invoiceID = i.invoiceID
				where i.orgID = @orgID
				and istat.status not in ('Closed','Delinquent','Paid')
				<cfif arguments.chkall is 0 and len(arguments.invoiceIDList) and arrayLen(reMatch("[^0-9,]",arguments.invoiceIDList)) is 0>
					and i.invoiceID in (#arguments.invoiceIDList#)
				<cfelse>
					<cfif arguments.chkall is 1 and len(arguments.notInvoiceIDList) and arrayLen(reMatch("[^0-9,]",arguments.notInvoiceIDList)) is 0>
						and i.invoiceID not in (#arguments.notInvoiceIDList#)
					</cfif>
					<cfif len(local.statuses)>
						and i.statusID in (<cfqueryparam value="#local.statuses#" cfsqltype="CF_SQL_INTEGER" list="yes">)
					</cfif>
					<cfif len(local.invoiceProfiles)>
						and i.invoiceProfileID in (<cfqueryparam value="#local.invoiceProfiles#" cfsqltype="CF_SQL_INTEGER" list="yes">)
					</cfif>
					<cfif len(local.invoiceNumber)>
						and i.invoiceNumber = <cfqueryparam value="#val(local.invoiceNumber)#" cfsqltype="CF_SQL_INTEGER">
					</cfif>
					<cfif len(local.trDetail)>
						and t.detail like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.trDetail#%">
					</cfif>
					<cfif len(arguments.duestartDate)>
						and i.dateDue >= <cfqueryparam value="#arguments.duestartDate#" cfsqltype="CF_SQL_DATE">
					</cfif>
					<cfif len(arguments.dueendDate)>
						and i.dateDue < <cfqueryparam value="#dateAdd('d',1,arguments.dueendDate)#" cfsqltype="CF_SQL_DATE">
					</cfif>
					<cfif len(arguments.billedstartDate)>
						and i.dateBilled >= <cfqueryparam value="#arguments.billedstartDate#" cfsqltype="CF_SQL_DATE">
					</cfif>
					<cfif len(arguments.billedendDate)>
						and i.dateBilled < <cfqueryparam value="#dateAdd('d',1,arguments.billedendDate)#" cfsqltype="CF_SQL_DATE">
					</cfif>
					<cfif len(arguments.cardOnFile)>
						and (
							<cfif local.cof0>
								i.payProfileID is null
							</cfif>
							<cfif local.cof0 and listLen(local.cofList)>
								or
							</cfif>
							<cfif listLen(local.cofList)>
								i.MPProfileID in (<cfqueryparam value="#local.cofList#" cfsqltype="CF_SQL_INTEGER" list="yes">)
							</cfif>
							)
					</cfif>
				</cfif>
				group by i.invoiceID
				having i.invoiceID = i.invoiceID
				<cfif len(local.dueAmtStart)>
					and sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) >= <cfqueryparam value="#local.dueAmtStart#" cfsqltype="CF_SQL_DECIMAL" scale="2">
				</cfif>
				<cfif len(local.dueAmtEnd)>
					and sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) <= <cfqueryparam value="#local.dueAmtEnd#" cfsqltype="CF_SQL_DECIMAL" scale="2">
				</cfif>
				<cfif len(local.invAmtStart)>
					and sum(it.cache_invoiceAmountAfterAdjustment) >= <cfqueryparam value="#local.invAmtStart#" cfsqltype="CF_SQL_DECIMAL" scale="2">
				</cfif>
				<cfif len(local.invAmtEnd)>
					and sum(it.cache_invoiceAmountAfterAdjustment) <= <cfqueryparam value="#local.invAmtEnd#" cfsqltype="CF_SQL_DECIMAL" scale="2">
				</cfif>;

				<cfif local.runGetAssociated>
					IF OBJECT_ID('tempdb..##tmpAWQInvoices') is not null
						DROP TABLE ##tmpAWQInvoices;
				</cfif>

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.returnStruct.data.invoiceCount = local.qryInvoices.recordCount>

		<cfif local.qryInvoices.recordCount gt local.maxInvoices>
			<cfset local.returnStruct.data.maximumexceeded = true>
			<cfset local.returnStruct.success = true>
		<cfelse>
			<cfset local.returnStruct.data.maximumexceeded = false>
			<cftry>
				<cfstoredproc procedure="tr_closeInvoice" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
					<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#ValueList(local.qryInvoices.invoiceID)#">
				</cfstoredproc>
				<cfset local.returnStruct.success = true>
			<cfcatch type="Any">
				<cfset local.returnStruct.success = false>
			</cfcatch>
			</cftry>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doManuallyProcessPayments" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="duestartDate" type="string" required="yes">
		<cfargument name="dueendDate" type="string" required="yes">
		<cfargument name="billedstartDate" type="string" required="yes">
		<cfargument name="billedendDate" type="string" required="yes">
		<cfargument name="dueAmtStart" type="string" required="yes">
		<cfargument name="dueAmtEnd" type="string" required="yes">
		<cfargument name="invAmtStart" type="string" required="yes">
		<cfargument name="invAmtEnd" type="string" required="yes">
		<cfargument name="statuses" type="string" required="yes">
		<cfargument name="invoiceProfiles" type="string" required="yes">
		<cfargument name="invoiceNumber" type="string" required="yes">
		<cfargument name="trDetail" type="string" required="yes">
		<cfargument name="cardOnFile" type="string" required="yes">
		<cfargument name="associatedMemberID" type="string" required="yes">
		<cfargument name="associatedGroupID" type="string" required="yes">
		<cfargument name="linkedRecords" type="string" required="yes">
		<cfargument name="linkedRecordOptions" type="string" required="yes">
		<cfargument name="assocType" type="string" required="yes">
		<cfargument name="chkAll" type="boolean" required="yes">
		<cfargument name="invoiceIDList" type="string" required="yes">
		<cfargument name="notInvoiceIDList" type="string" required="yes">

		<cfscript>
			var local = structNew();
			if (not (len(arguments.statuses)))
				local.statuses = '1';
			else
				local.statuses = arguments.statuses;

			local.invoiceNumber = reReplace(arguments.invoiceNumber,'[^0-9]','','ALL');
			local.trDetail = trim(arguments.trDetail);
			local.invoiceProfiles = arguments.invoiceProfiles;
			local.associatedMemberID = arguments.associatedMemberID;
			local.associatedGroupID = arguments.associatedGroupID;

			local.dueAmtStart = '';
			local.dueAmtEnd = '';
			if (len(arguments.dueAmtStart))
				local.dueAmtStart = val(ReReplace(arguments.dueAmtStart,'[^0-9\.]','','ALL'));
			if (len(arguments.dueAmtEnd))
				local.dueAmtEnd = val(ReReplace(arguments.dueAmtEnd,'[^0-9\.]','','ALL'));
			
			local.invAmtStart = '';
			local.invAmtEnd = '';
			if (len(arguments.invAmtStart))
				local.invAmtStart = val(ReReplace(arguments.invAmtStart,'[^0-9\.]','','ALL'));
			if (len(arguments.invAmtEnd))
				local.invAmtEnd = val(ReReplace(arguments.invAmtEnd,'[^0-9\.]','','ALL'));

			local.returnStruct = structnew();
		</cfscript>

		<!--- card on file options --->
		<cfset local.cof0 = false>
		<cfset local.cofList = arguments.cardOnFile>
		<cfif len(local.cofList)>
			<cfset local.cod0Loc = listFind(local.cofList,0)>
			<cfif local.cod0Loc>
				<cfset local.cof0 = true>
				<cfset local.cofList = listDeleteAt(local.cofList,local.cod0Loc)>
			</cfif>
		</cfif>

		<cfset local.runGetAssociated = false>
		<cfif len(trim(arguments.assocType)) and len(trim(arguments.linkedRecordOptions))>
			<cfset local.runGetAssociated = true>
		</cfif>

		<cfquery name="local.qryManuallyProcessPayments" datasource="#application.dsn.memberCentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				
				IF OBJECT_ID('tempdb..##tmpInvoicesMPP') IS NOT NULL 
					DROP TABLE ##tmpInvoicesMPP;
				CREATE TABLE ##tmpInvoicesMPP (invoiceID int);

				DECLARE @orgID int = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">;

				<cfif local.runGetAssociated>
					#getAssociatedWithQuery(associatedMemberID=arguments.associatedMemberID, associatedGroupID=arguments.associatedGroupID,
						linkedRecords=arguments.linkedRecords, linkedRecordOptions=arguments.linkedRecordOptions, assocType=arguments.assocType)#
				</cfif>

				INSERT INTO ##tmpInvoicesMPP (invoiceID)
				select i.invoiceID
				from dbo.tr_invoices as i
				inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
				inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = i.assignedToMemberID
				<cfif local.runGetAssociated>
					inner join ##tmpAWQInvoices as assocInv on assocInv.invoiceID = i.invoiceID
				</cfif>
				left outer join dbo.tr_invoiceTransactions as it 
					<cfif len(local.trDetail)>
						inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = it.transactionID
					</cfif>
					on it.orgID = @orgID and it.invoiceID = i.invoiceID
				where i.orgID = @orgID
				and istat.status in ('Closed','Delinquent')
				<cfif arguments.chkall is 0 and len(arguments.invoiceIDList) and arrayLen(reMatch("[^0-9,]",arguments.invoiceIDList)) is 0>
					and i.invoiceID in (#arguments.invoiceIDList#)
				<cfelse>
					<cfif arguments.chkall is 1 and len(arguments.notInvoiceIDList) and arrayLen(reMatch("[^0-9,]",arguments.notInvoiceIDList)) is 0>
						and i.invoiceID not in (#arguments.notInvoiceIDList#)
					</cfif>
					<cfif len(local.statuses)>
						and i.statusID in (<cfqueryparam value="#local.statuses#" cfsqltype="CF_SQL_INTEGER" list="yes">)
					</cfif>
					<cfif len(local.invoiceProfiles)>
						and i.invoiceProfileID in (<cfqueryparam value="#local.invoiceProfiles#" cfsqltype="CF_SQL_INTEGER" list="yes">)
					</cfif>
					<cfif len(local.invoiceNumber)>
						and i.invoiceNumber = <cfqueryparam value="#val(local.invoiceNumber)#" cfsqltype="CF_SQL_INTEGER">
					</cfif>
					<cfif len(local.trDetail)>
						and t.detail like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.trDetail#%">
					</cfif>
					<cfif len(arguments.duestartDate)>
						and i.dateDue >= <cfqueryparam value="#arguments.duestartDate#" cfsqltype="CF_SQL_DATE">
					</cfif>
					<cfif len(arguments.dueendDate)>
						and i.dateDue < <cfqueryparam value="#dateAdd('d',1,arguments.dueendDate)#" cfsqltype="CF_SQL_DATE">
					</cfif>
					<cfif len(arguments.billedstartDate)>
						and i.dateBilled >= <cfqueryparam value="#arguments.billedstartDate#" cfsqltype="CF_SQL_DATE">
					</cfif>
					<cfif len(arguments.billedendDate)>
						and i.dateBilled < <cfqueryparam value="#dateAdd('d',1,arguments.billedendDate)#" cfsqltype="CF_SQL_DATE">
					</cfif>
					and i.payProfileID is not null
					<cfif listLen(local.cofList)>
						and i.MPProfileID in (<cfqueryparam value="#local.cofList#" cfsqltype="CF_SQL_INTEGER" list="yes">)
					</cfif>
				</cfif>
				group by i.invoiceID
				having i.invoiceID = i.invoiceID
				<cfif len(local.dueAmtStart)>
					and sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) >= <cfqueryparam value="#local.dueAmtStart#" cfsqltype="CF_SQL_DECIMAL" scale="2">
				</cfif>
				<cfif len(local.dueAmtEnd)>
					and sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) <= <cfqueryparam value="#local.dueAmtEnd#" cfsqltype="CF_SQL_DECIMAL" scale="2">
				</cfif>
				<cfif len(local.invAmtStart)>
					and sum(it.cache_invoiceAmountAfterAdjustment) >= <cfqueryparam value="#local.invAmtStart#" cfsqltype="CF_SQL_DECIMAL" scale="2">
				</cfif>
				<cfif len(local.invAmtEnd)>
					and sum(it.cache_invoiceAmountAfterAdjustment) <= <cfqueryparam value="#local.invAmtEnd#" cfsqltype="CF_SQL_DECIMAL" scale="2">
				</cfif>;

				IF EXISTS (select 1 from ##tmpInvoicesMPP)
					EXEC dbo.tr_manuallyProcessPayments @enteredByMemberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">;


				IF OBJECT_ID('tempdb..##tmpInvoicesMPP') IS NOT NULL 
					DROP TABLE ##tmpInvoicesMPP;
				<cfif local.runGetAssociated>
					IF OBJECT_ID('tempdb..##tmpAWQInvoices') is not null
						DROP TABLE ##tmpAWQInvoices;
				</cfif>
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
		
		<cfset local.returnStruct.success = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="viewInvoiceInfo" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfquery name="local.qryInvoiceCheck" datasource="#application.dsn.membercentral.dsn#">
			select i.invoiceID
			from dbo.tr_invoices as i
			inner join dbo.ams_members as m on m.memberid = i.assignedToMemberID
			where i.invoiceID = <cfqueryparam value="#arguments.event.getValue('vid',0)#" cfsqltype="CF_SQL_INTEGER">
			and m.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfif local.qryInvoiceCheck.recordcount is 0>
			<cflocation url="#this.link.message#&ec=VIINF" addtoken="no">
		</cfif>

		<cfstoredproc procedure="tr_viewInvoice" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryInvoiceCheck.invoiceID#">
			<cfprocresult name="local.qryInvoice" resultset="1">
			<cfprocresult name="local.qryInvoiceHistory" resultset="2">
		</cfstoredproc>

		<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="tr_invoiceData">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryInvoiceCheck.invoiceID#">
			<cfprocresult resultset="1" name="local.qryInvoiceMessages">
			<cfprocresult resultset="2" name="local.qrySection1Sales">
			<cfprocresult resultset="3" name="local.qrySection1Tax">
			<cfprocresult resultset="4" name="local.qrySection2Sales">
			<cfprocresult resultset="5" name="local.qrySection2Tax">
			<cfprocresult resultset="6" name="local.qrySection3Sales">
			<cfprocresult resultset="7" name="local.qrySection3Tax">
		</cfstoredproc>
		<cfquery name="local.qryS1STotal" dbtype="query">
			select sum(amount) as amt from [local].qrySection1Sales
		</cfquery>
		<cfquery name="local.qryS1TTotal" dbtype="query">
			select sum(amount) as amt from [local].qrySection1Tax
		</cfquery>
		<cfset local.section1Total = val(local.qryS1STotal.amt) + val(local.qryS1TTotal.amt)>

		<!--- get the SRID and permissions of TransactionsAdmin. --->
		<cfset local.TransactionsAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TransactionsAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
		<cfset local.myRightsTransactionsAdmin = buildRightAssignments(local.TransactionsAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>

		<cfif local.myRightsTransactionsAdmin.transAllocatePayment is 1>
			<cfset local.addPaymentEncString = CreateObject("component","transactionAdmin").generatePOForAddPayment(pmid=local.qryInvoice.assignedTomemberID, t="Invoice #local.qryInvoice.invoiceNumber#", ta=local.qryInvoice.InvDue, tmid=local.qryInvoice.assignedTomemberID, ad="v|#local.qryInvoice.invoiceid#")>
		</cfif>

		<cfset local.qryInvoiceItemInfo = CreateObject("component","invoice").getInvoiceItemInfo(orgID=arguments.event.getValue('mc_siteinfo.orgID'), invoiceID=local.qryInvoiceCheck.invoiceID)>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_invoiceInfo.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry!</h4>
				<div>
					<cfswitch expression="#arguments.event.getValue('ec','')#">
						<cfcase value="MITNF">That invoice transaction was not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: IA-MITNF</cfcase>
						<cfcase value="MITNOI">That invoice transaction could not be moved. There are no other open invoices that use the same Invoice Profile.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: IA-MITNOI</cfcase>
						<cfcase value="SMIT">There was an error moving the transaction to the selected invoice.<br/><br/>We have just notified MemberCentral Support of this issue.<br/>For reference, note this exact error code: IA-SMIT</cfcase>
						<cfcase value="EINF">That invoice was not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: IA-EINF</cfcase>
						<cfcase value="MITNP">You do not have the necessary permissions to move an invoice transaction.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfcase value="CINP">You do not have the necessary permissions to create an invoice.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfcase value="EINP">You do not have the necessary permissions to edit an invoice.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfcase value="CIDNP">You do not have the necessary permissions to mass change invoice dates.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfcase value="SIPPNP">You do not have the necessary permissions to mass change invoice pay profiles.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfcase value="GIBNP">You do not have the necessary permissions to download an invoice.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfcase value="PEINP">You do not have the necessary permissions to e-mail an invoice.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfcase value="UISV">The invoice could not be saved.<br/><br/>We have just notified MemberCentral Support of this issue.<br/>For reference, note this exact error code: IA-UISV</cfcase>
						<cfcase value="VIINF">That invoice could not be found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-VIINF</cfcase>
						<cfdefaultcase>An error has occurred. Contact MemberCentral for assistance.</cfdefaultcase>
					</cfswitch>
				</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getAssociatedWithQuery" access="public" output="false" returntype="string">
		<cfargument name="associatedMemberID" type="string" required="true">
		<cfargument name="associatedGroupID" type="string" required="true">
		<cfargument name="linkedRecords" type="string" required="true">
		<cfargument name="linkedRecordOptions" type="string" required="true">
		<cfargument name="assocType" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.associatedMemberID = val(arguments.associatedMemberID)>
		<cfset local.associatedGroupID = val(arguments.associatedGroupID)>
		<cfset local.linkedRecords = arguments.linkedRecords>
		<cfset local.linkedRecordOptions = arguments.linkedRecordOptions>
		<cfset local.assocType = arguments.assocType>

		<cfsavecontent variable="local.qryString">
			<cfoutput>
			IF OBJECT_ID('tempdb..##tmpAWQMembers') is not null
				DROP TABLE ##tmpAWQMembers;
			IF OBJECT_ID('tempdb..##tmpAWQInvoices') is not null
				DROP TABLE ##tmpAWQInvoices;
			CREATE TABLE ##tmpAWQMembers (memberID int PRIMARY KEY);
			CREATE TABLE ##tmpAWQInvoices (invoiceID int PRIMARY KEY);

			declare @memberID int = #local.associatedMemberID#, @groupID int = #local.associatedGroupID#;

			insert into ##tmpAWQMembers (memberID)
			<cfif local.assocType is "member">
				select @memberID as memberID;
			<cfelse>
				select memberID
				from dbo.cache_members_groups
				where orgID = @orgID
				and groupID = @groupID;
			</cfif>

			<cfif local.linkedRecords is "all">
				insert into ##tmpAWQMembers (memberID)
				select allchildMember.memberID
				from dbo.ams_members as m 
				inner join dbo.ams_recordRelationships as rr on rr.orgID = @orgID and rr.masterMemberID = m.memberID and rr.isActive = 1
				inner join dbo.ams_members as childMember on childMember.orgID = @orgID and rr.childMemberID = childMember.memberID
				inner join dbo.ams_members as allchildMember on allchildMember.orgID = @orgID and allchildMember.activeMemberID = childMember.memberID
				<cfif local.assocType is "group">
					inner join dbo.cache_members_groups as mg on mg.orgID = @orgID and mg.memberID = allchildMember.activeMemberID and mg.groupID = @groupID
				</cfif>
				where m.orgID = @orgID 
				and childMember.status <> 'D'
				<cfif local.assocType is "member">
					and m.memberID = @memberID
				</cfif>
					EXCEPT
				select memberID
				from ##tmpAWQMembers;
			</cfif>

			<cfif listFindNoCase(local.linkedRecordOptions,"assigned")>
				-- inv assigned to member
				insert into ##tmpAWQInvoices (invoiceID)
				select distinct i.invoiceID
				from dbo.tr_invoices as i 
				INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberid = i.assignedToMemberID
				INNER JOIN ##tmpAWQMembers as mct on mct.memberID = m.activeMemberID
				WHERE i.orgID = @orgID;
			</cfif>

			<cfif listFindNoCase(local.linkedRecordOptions,"trans")>
				-- trans on inv assigned to member
				insert into ##tmpAWQInvoices (invoiceID)
				select distinct it.invoiceID
				from dbo.tr_invoiceTransactions as it
				inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = it.transactionID and t.statusID not in (2,4)
				INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberid = t.assignedToMemberID
				INNER JOIN ##tmpAWQMembers as mct on mct.memberID = m.activeMemberID
				WHERE it.orgID = @orgID
					EXCEPT
				select invoiceID 
				from ##tmpAWQInvoices;
			</cfif>

			<cfif listFindNoCase(local.linkedRecordOptions,"payment")>
				-- payments assigned to member allocated to trans on inv
				insert into ##tmpAWQInvoices (invoiceID)
				select distinct it.invoiceID
				from dbo.cache_tr_allocations as alloc
				inner join dbo.tr_transactions as tAlloc on tAlloc.ownedByOrgID = @orgID and tAlloc.transactionID = alloc.transactionID_alloc and tAlloc.statusID = 1
				inner join dbo.tr_transactions as tSale on tSale.ownedByOrgID = @orgID and tSale.transactionID = alloc.transactionID_rev and tSale.statusID = 1
				inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = tSale.transactionID
				inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = alloc.assignedToMemberID_cash
				INNER JOIN ##tmpAWQMembers as mct on mct.memberID = m.activeMemberID
				where alloc.orgID = @orgID
					EXCEPT
				select invoiceID 
				from ##tmpAWQInvoices;
			</cfif>

			IF OBJECT_ID('tempdb..##tmpAWQMembers') is not null
				DROP TABLE ##tmpAWQMembers;
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.qryString>
	</cffunction>

	<!--- import data --->
	<cffunction name="sampleOption1ImportTemplate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = CreateObject("component","AccountingImport").generateOption1ImportTemplate()>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	<cffunction name="sampleOption2ImportTemplate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = CreateObject("component","AccountingImport").generateOption2ImportTemplate()>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	<cffunction name="processAcctImportOpt1" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objImport = CreateObject("component","AccountingImport")>

		<cfsetting requesttimeout="500">

		<cfset local.processResult = local.objImport.importOption1(event=arguments.event)>
		<cfset local.data = local.objImport.showImportResults(strResult=local.processResult, doAgainURL=this.link.list)>

		<cfreturn local.data>
	</cffunction>
	<cffunction name="processAcctImportOpt2" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objImport = CreateObject("component","AccountingImport")>

		<cfsetting requesttimeout="500">

		<cfset local.processResult = local.objImport.importOption2(event=arguments.event)>
		<cfset local.data = local.objImport.showImportResults(strResult=local.processResult, doAgainURL=this.link.list)>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="massEmailInvoices" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfset local.strFilters = structNew()>
		<cfset structInsert(local.strFilters, 'duestartDate', arguments.event.getValue('ds',''))>
		<cfset structInsert(local.strFilters, 'dueendDate', arguments.event.getValue('de',''))>
		<cfset structInsert(local.strFilters, 'dueAmtStart', arguments.event.getValue('as',''))>
		<cfset structInsert(local.strFilters, 'dueAmtEnd', arguments.event.getValue('ae',''))>
		<cfset structInsert(local.strFilters, 'invAmtStart', arguments.event.getValue('ias',''))>
		<cfset structInsert(local.strFilters, 'invAmtEnd', arguments.event.getValue('iae',''))>
		<cfset structInsert(local.strFilters, 'billedstartDate', arguments.event.getValue('bs',''))>
		<cfset structInsert(local.strFilters, 'billedendDate', arguments.event.getValue('be',''))>
		<cfset structInsert(local.strFilters, 'statuses', arguments.event.getValue('statusid',''))>
		<cfset structInsert(local.strFilters, 'invoiceProfiles', arguments.event.getValue('ip',''))>
		<cfset structInsert(local.strFilters, 'invoiceNumber', arguments.event.getValue('vn',''))>
		<cfset structInsert(local.strFilters, 'trDetail', arguments.event.getTrimValue('trd',''))>
		<cfset structInsert(local.strFilters, 'cardOnFile', arguments.event.getValue('cof',''))>
		<cfset structInsert(local.strFilters, 'associatedMemberID', arguments.event.getValue('am',''))>
		<cfset structInsert(local.strFilters, 'associatedGroupID', arguments.event.getValue('ag',''))>
		<cfset structInsert(local.strFilters, 'linkedRecords', arguments.event.getValue('lr',''))>
		<cfset structInsert(local.strFilters, 'linkedRecordOptions', arguments.event.getValue('ar',''))>
		<cfset structInsert(local.strFilters, 'assocType', arguments.event.getValue('at',''))>
		<cfset structInsert(local.strFilters, 'chkAll', arguments.event.getValue('chkall',0))>
		<cfset structInsert(local.strFilters, 'invoiceIDList', arguments.event.getValue('incids',''))>
		<cfset structInsert(local.strFilters, 'notInvoiceIDList', arguments.event.getValue('excids',''))>

		<cfset local.strResourceTitle = { resourceTitle='Invoices', resourceTitleDesc='E-mail links to <b>closed</b>, <b>delinquent</b>, or <b>paid</b> invoices based on selected filters.<br/>', 
			templateEditorLabel='<div class="d-flex"><span>Compose your message.</span><span class="ml-auto small text-dim"><i class="fa-regular fa-rectangle-list fa-lg"></i> We''ll automatically include a table of matching invoices below your message.</span></div>' }>

		<cfset local.argumentCollection = { siteID=arguments.event.getValue('mc_siteinfo.siteid'), siteCode=arguments.event.getValue('mc_siteinfo.sitecode'), resourceType='Invoices', 
			recipientType='Members', strResourceTitle=local.strResourceTitle, strFilters=local.strFilters, arrRecipientModes=arrayNew(1),
			mergeCodeInstructionsLink="#buildCurrentLink(arguments.event,'showMergeCodeInstructions')#&mode=stream",
			emailTemplateTreeCode="ETINVOICES" }>

		<cfset local.data = CreateObject("component","model.admin.common.modules.massEmails.massEmails").prepMassEmails(argumentCollection=local.argumentCollection)>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getInvoiceDataForEmailing" access="public" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="invoiceIDList" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>

		<cfset local.retStruct.qryData = getInvoiceDetailsFromInvoiceIDList(orgID=arguments.orgID, invoiceIDList=arguments.invoiceIDList)>
		<cfset local.retStruct.extendedLinkedMergeCode = "">
		<cfset local.retStruct.arrResTypeMergeCodes = arrayNew(1)>
		<cfset local.retStruct.invoiceList = getMemberInvoiceListDetails(qryMemberInvoiceDetails=local.retStruct.qryData)>
		
		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getFilteredInvoicesForEmailing" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = { itemIDList='', toolType='InvoiceAdmin', catTreeCode='ETINVOICES', extendedLinkedMergeCode='', extraMergeTagList='invoiceList', errorCode='' }>

		<cfset local.paramStruct = structNew()>
		<cfset local.paramStruct.duestartDate = arguments.event.getValue('duestartDate','')>
		<cfset local.paramStruct.dueendDate = arguments.event.getValue('dueendDate','')>
		<cfset local.paramStruct.dueAmtStart = arguments.event.getValue('dueAmtStart','')>
		<cfset local.paramStruct.dueAmtEnd = arguments.event.getValue('dueAmtEnd','')>
		<cfset local.paramStruct.invAmtStart = arguments.event.getValue('invAmtStart','')>
		<cfset local.paramStruct.invAmtEnd = arguments.event.getValue('invAmtEnd','')>
		<cfset local.paramStruct.billedstartDate = arguments.event.getValue('billedstartDate','')>
		<cfset local.paramStruct.billedendDate = arguments.event.getValue('billedendDate','')>
		<cfset local.paramStruct.statuses = arguments.event.getValue('statuses','')>
		<cfset local.paramStruct.invoiceProfiles = arguments.event.getValue('invoiceProfiles','')>
		<cfset local.paramStruct.invoiceNumber = arguments.event.getValue('invoiceNumber','')>
		<cfset local.paramStruct.trDetail = arguments.event.getTrimValue('trDetail','')>
		<cfset local.paramStruct.cardOnFile = arguments.event.getValue('cardOnFile','')>
		<cfset local.paramStruct.associatedMemberID = arguments.event.getValue('associatedMemberID','')>
		<cfset local.paramStruct.associatedGroupID = arguments.event.getValue('associatedGroupID','')>
		<cfset local.paramStruct.linkedRecords = arguments.event.getValue('linkedRecords','')>
		<cfset local.paramStruct.linkedRecordOptions = arguments.event.getValue('linkedRecordOptions','')>
		<cfset local.paramStruct.assocType = arguments.event.getValue('assocType','')>
		<cfset local.paramStruct.chkAll = arguments.event.getValue('chkAll',0)>
		<cfset local.paramStruct.invoiceIDList = arguments.event.getValue('invoiceIDList','')>
		<cfset local.paramStruct.notInvoiceIDList = arguments.event.getValue('notInvoiceIDList','')>

		<cfset local.retStruct.qryData = getFilteredInvoices(orgID=arguments.event.getValue('mc_siteinfo.orgID'), paramStruct=local.paramStruct)>

		<cfif local.retStruct.qryData.recordcount is 0>
			<cfset local.retStruct.errorCode = 'norecipient'>
			<cfreturn local.retStruct>
		<cfelse>
			<cfquery name="local.qryDistinctMembers" dbtype="query">
				select distinct memberID
				from [local].retStruct.qryData
			</cfquery>
			<cfset local.memberIDList = valueList(local.qryDistinctMembers.memberID)>
			<cfset local.retStruct.itemIDList = "#local.memberIDList#|#valueList(local.retStruct.qryData.invoiceID)#">
		</cfif>

		<cfquery name="local.qryMembersWithEmail" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @orgID int, @memberIDList varchar(max), @emailTagTypeID int;
			SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
			SET @emailTagTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('emailTagType',0)#">;
			SET @memberIDList = '#local.memberIDList#';

			select count(*) as membersWithEmail
			from dbo.ams_members as m 
			inner join dbo.fn_intListToTable(@memberIDList,',') as limitm on limitm.listitem = m.memberID 
			inner join dbo.ams_memberEmails as me on me.orgID = @orgID 
				and me.memberID = m.memberID 
				and me.email <> ''
			inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID 
				and metag.memberID = me.memberID 
				and metag.emailTypeID = me.emailTypeID
			inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID 
				and metagt.emailTagTypeID = metag.emailTagTypeID 
				and metagt.emailTagTypeID = @emailTagTypeID
			where m.status = 'A' 
			and m.orgID = @orgID;
		</cfquery>

		<!--- no email ids defined for all recipients --->
		<cfif val(local.qryMembersWithEmail.membersWithEmail) eq 0>
			<cfset local.retStruct.errorCode = 'noemailrecipient'>
			<cfreturn local.retStruct>
		</cfif>

		<cfset local.retStruct.qryInvoiceDetails = getInvoiceDetailsFromInvoiceIDList(orgID=arguments.event.getValue('mc_siteinfo.orgID'), invoiceIDList=valuelist(local.retStruct.qryData.invoiceID))>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getInvoiceDetailsFromInvoiceIDList" access="private" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="invoiceIDList" type="string" required="true">

		<cfset var qryInvoices = "">

		<cfquery name="qryInvoices" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @orgID int = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">;

			select i.invoiceID, i.invoiceProfileID, i.statusID, i.fullInvoiceNumber as invoiceNumber,
				ip.profileName, i.dateBilled, i.dateDue, i.invoiceCode,
				sum(it.cache_invoiceAmountAfterAdjustment) as InvAmt,
				sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) as InvDue,
				mActive.memberID, istat.status as invoiceStatus, 
				dbo.fn_tr_showInvoicePayOnlineLink(i.invoiceid) as showLink
			from dbo.tr_invoices as i
			inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
				and istat.status in ('Closed','Delinquent','Paid')
			inner join dbo.tr_invoiceProfiles as ip on ip.orgID = @orgID and ip.profileID = i.invoiceProfileID
			inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = i.assignedToMemberID
			inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID 
				and mActive.status IN ('A','I')
			inner join dbo.organizations as o on o.orgID = @orgID and o.orgID = m.orgID
			left outer join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceID = i.invoiceID
			where i.orgID = @orgID
			and i.invoiceID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="0#arguments.invoiceIDList#">)
			group by i.invoiceID, i.invoiceProfileID, i.statusID, i.fullInvoiceNumber, 
				ip.profileName, i.dateBilled, i.dateDue, i.invoiceCode, mActive.memberID, istat.status;
		</cfquery>

		<cfreturn qryInvoices>
	</cffunction>

	<cffunction name="parseContentWithMergeCodes" access="private" output="no" returntype="struct">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="content" type="string" required="yes">
		<cfargument name="subjectLine" type="string" required="yes">
		<cfargument name="qryInvoices" type="query" required="yes">

		<cfset var local = StructNew()>

		<cfset local.memberInfo = application.objMember.getMemberInfo(memberID=arguments.memberID)>

		<cfset arguments.content = urlDecode(arguments.content)>
		<cfset arguments.subjectLine = urlDecode(arguments.subjectLine)>

		<cfset local.qryMemberFields = application.objMergeCodes.getMergeViewFields(orgID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgID, 
			memberID=val(local.memberInfo.memberID), content="#arguments.content##arguments.subjectLine#")>

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).mainhostname>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
		</cfif>

		<cfset local.tempMemberData = { memberID=local.memberInfo.memberID, firstName=encodeForHTML(local.memberInfo.FirstName), middleName=encodeForHTML(local.memberInfo.MiddleName),
							 			lastName=encodeForHTML(local.memberInfo.LastName), company=encodeForHTML(local.memberInfo.Company), suffix=encodeForHTML(local.memberInfo.Suffix),
							 			prefix=encodeForHTML(local.memberInfo.Prefix), membernumber=local.memberInfo.membernumber, professionalSuffix=encodeForHTML(local.memberInfo.professionalSuffix), 
							 			orgcode=local.memberInfo.orgcode, siteID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID,
							 			hostname=local.thisHostname, useRemoteLogin=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).useRemoteLogin }>
		<cfloop array="#getMetaData(local.qryMemberFields)#" index="local.thisColumn">
			<cfif NOT StructKeyExists(local.tempMemberData,local.thisColumn.Name)>
				<cfset local.thisTempVal = local.qryMemberFields[local.thisColumn.Name][1]>
				<cfset structInsert(local.tempMemberData,local.thisColumn.Name,local.thisTempVal,true)>
			</cfif>	
		</cfloop>

		<cfif findNoCase("[[invoiceList]]", arguments.content)>
			<cfset arguments.content = replaceNoCase(arguments.content,"[[invoiceList]]", getMemberInvoiceListDetails(qryMemberInvoiceDetails=arguments.qryInvoices))>
		</cfif>

		<cfset local.strArgs = { content=arguments.content, memberdata=local.tempMemberData, orgcode=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgcode, sitecode=session.mcStruct.siteCode }>
		<cfset local.strMergedContent = application.objMergeCodes.processMergeCodes(argumentcollection=local.strArgs)>

		<cfset local.strArgs = { content=arguments.subjectLine, memberdata=local.tempMemberData, orgcode=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgcode, sitecode=session.mcStruct.siteCode }>
		<cfset local.strMergedSubjectContent = application.objMergeCodes.processMergeCodes(argumentcollection=local.strArgs)>

		<cfset local.strReturn = { content=local.strMergedContent.content, subjectLine=local.strMergedSubjectContent.content }>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getFilteredInvoices" access="private" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="paramStruct" type="struct" required="true">
		<cfargument name="memberID" type="numeric" required="false" default="0">

		<cfscript>
			var local = structNew();

			if (not (len(arguments.paramStruct.statuses)))
				local.statuses = '3,4,5';
			else
				local.statuses = arguments.paramStruct.statuses;

			local.invoiceNumber = reReplace(arguments.paramStruct.invoiceNumber,'[^0-9]','','ALL');
			local.trDetail = trim(arguments.paramStruct.trDetail);
			local.invoiceProfiles = arguments.paramStruct.invoiceProfiles;

			local.dueAmtStart = '';
			local.dueAmtEnd = '';
			if (len(arguments.paramStruct.dueAmtStart))
				local.dueAmtStart = val(ReReplace(arguments.paramStruct.dueAmtStart,'[^0-9\.]','','ALL'));
			if (len(arguments.paramStruct.dueAmtEnd))
				local.dueAmtEnd = val(ReReplace(arguments.paramStruct.dueAmtEnd,'[^0-9\.]','','ALL'));
			
			local.invAmtStart = '';
			local.invAmtEnd = '';
			if (len(arguments.paramStruct.invAmtStart))
				local.invAmtStart = val(ReReplace(arguments.paramStruct.invAmtStart,'[^0-9\.]','','ALL'));
			if (len(arguments.paramStruct.invAmtEnd))
				local.invAmtEnd = val(ReReplace(arguments.paramStruct.invAmtEnd,'[^0-9\.]','','ALL'));
		</cfscript>

		<!--- card on file options --->
		<cfset local.cof0 = false>
		<cfset local.cofList = arguments.paramStruct.cardOnFile>
		<cfif len(local.cofList)>
			<cfset local.cod0Loc = listFind(local.cofList,0)>
			<cfif local.cod0Loc>
				<cfset local.cof0 = true>
				<cfset local.cofList = listDeleteAt(local.cofList,local.cod0Loc)>
			</cfif>
		</cfif>

		<cfset local.runGetAssociated = false>
		<cfif len(trim(arguments.paramStruct.assocType)) and len(trim(arguments.paramStruct.linkedRecordOptions))>
			<cfset local.runGetAssociated = true>
		</cfif>

		<!--- filtered invoices --->
		<cfquery name="local.qryInvoices" datasource="#application.dsn.memberCentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @orgID int = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">;

				<cfif local.runGetAssociated>
					#getAssociatedWithQuery(associatedMemberID=arguments.paramStruct.associatedMemberID, associatedGroupID=arguments.paramStruct.associatedGroupID,
						linkedRecords=arguments.paramStruct.linkedRecords, linkedRecordOptions=arguments.paramStruct.linkedRecordOptions, assocType=arguments.paramStruct.assocType)#
				</cfif>

				select tmp.invoiceid, tmp.invoiceProfileID, m.memberid, tmp.invoiceNumber, tmp.profileName as invProfile,
					tmp.dateBilled, tmp.dateDue, tmp.statusID, dbo.fn_tr_showInvoicePayOnlineLink(tmp.invoiceid) as showLink,
					tmp.invoiceCode, tmp.invAmt, tmp.invDue, m.firstname, m.middlename, m.lastname, m.Suffix, m.membernumber, m.Company,
					tmp.invoiceStatus
				from (
					select i.invoiceID, i.invoiceProfileID, i.statusID, i.fullInvoiceNumber as invoiceNumber,
						ip.profileName, i.dateBilled, i.dateDue, i.invoiceCode,
						sum(it.cache_invoiceAmountAfterAdjustment) as InvAmt,
						sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) as InvDue,
						m.activeMemberid as memberID, istat.status as invoiceStatus
					from dbo.tr_invoices as i
					inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
					inner join dbo.tr_invoiceProfiles as ip on ip.orgID = @orgID and ip.profileID = i.invoiceProfileID
					inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = i.assignedToMemberID
					inner join dbo.organizations as o on o.orgID = m.orgID
					<cfif local.runGetAssociated>
						inner join ##tmpAWQInvoices as assocInv on assocInv.invoiceID = i.invoiceID
					</cfif>
					left outer join dbo.tr_invoiceTransactions as it 
						<cfif len(local.trDetail)>
							inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = it.transactionID
						</cfif>
						on it.orgID = @orgID and it.invoiceID = i.invoiceID
					where i.orgID = @orgID
					and istat.status in ('Closed','Delinquent','Paid')
					<cfif arguments.memberID gt 0>
						and m.memberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">
					</cfif>
					<cfif arguments.paramStruct.chkAll is 0 and len(arguments.paramStruct.invoiceIDList) and arrayLen(reMatch("[^0-9,]",arguments.paramStruct.invoiceIDList)) is 0>
						and i.invoiceID in (#arguments.paramStruct.invoiceIDList#)
					<cfelse>
						<cfif arguments.paramStruct.chkAll is 1 and len(arguments.paramStruct.notInvoiceIDList) and arrayLen(reMatch("[^0-9,]",arguments.paramStruct.notInvoiceIDList)) is 0>
							and i.invoiceID not in (#arguments.paramStruct.notInvoiceIDList#)
						</cfif>
						<cfif len(local.statuses)>
							and i.statusID in (<cfqueryparam value="#local.statuses#" cfsqltype="CF_SQL_INTEGER" list="yes">)
						</cfif>
						<cfif len(local.invoiceProfiles)>
							and i.invoiceProfileID in (<cfqueryparam value="#local.invoiceProfiles#" cfsqltype="CF_SQL_INTEGER" list="yes">)
						</cfif>
						<cfif len(local.invoiceNumber)>
							and i.invoiceNumber = <cfqueryparam value="#val(local.invoiceNumber)#" cfsqltype="CF_SQL_INTEGER">
						</cfif>
						<cfif len(local.trDetail)>
							and t.detail like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.trDetail#%">
						</cfif>
						<cfif len(arguments.paramStruct.duestartDate)>
							and i.dateDue >= <cfqueryparam value="#arguments.paramStruct.duestartDate#" cfsqltype="CF_SQL_DATE">
						</cfif>
						<cfif len(arguments.paramStruct.dueendDate)>
							and i.dateDue < <cfqueryparam value="#dateAdd('d',1,arguments.paramStruct.dueendDate)#" cfsqltype="CF_SQL_DATE">
						</cfif>
						<cfif len(arguments.paramStruct.billedstartDate)>
							and i.dateBilled >= <cfqueryparam value="#arguments.paramStruct.billedstartDate#" cfsqltype="CF_SQL_DATE">
						</cfif>
						<cfif len(arguments.paramStruct.billedendDate)>
							and i.dateBilled < <cfqueryparam value="#dateAdd('d',1,arguments.paramStruct.billedendDate)#" cfsqltype="CF_SQL_DATE">
						</cfif>
						<cfif len(arguments.paramStruct.cardOnFile)>
							and (
								<cfif local.cof0>
									i.payProfileID is null
								</cfif>
								<cfif local.cof0 and listLen(local.cofList)>
									or
								</cfif>
								<cfif listLen(local.cofList)>
									i.MPProfileID in (<cfqueryparam value="#local.cofList#" cfsqltype="CF_SQL_INTEGER" list="yes">)
								</cfif>
								)
						</cfif>
					</cfif>
					group by i.invoiceID, i.invoiceProfileID, i.statusID, i.fullInvoiceNumber, ip.profileName, i.dateBilled, i.dateDue, i.invoiceCode, m.activeMemberid, istat.status
					having i.invoiceCode = i.invoiceCode
					<cfif len(local.dueAmtStart)>
						and sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) >= <cfqueryparam value="#local.dueAmtStart#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>
					<cfif len(local.dueAmtEnd)>
						and sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) <= <cfqueryparam value="#local.dueAmtEnd#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>
					<cfif len(local.invAmtStart)>
						and sum(it.cache_invoiceAmountAfterAdjustment) >= <cfqueryparam value="#local.invAmtStart#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>
					<cfif len(local.invAmtEnd)>
						and sum(it.cache_invoiceAmountAfterAdjustment) <= <cfqueryparam value="#local.invAmtEnd#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>
				) as tmp
				inner join dbo.ams_members as m on m.memberid = tmp.memberID
				order by m.membernumber, tmp.invoiceProfileID, tmp.invoiceNumber;

				<cfif local.runGetAssociated>
					IF OBJECT_ID('tempdb..##tmpAWQInvoices') is not null
						DROP TABLE ##tmpAWQInvoices;
				</cfif>

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.qryInvoices>
	</cffunction>

	<cffunction name="getMemberInvoiceListDetails" access="public" output="false" returntype="string">
		<cfargument name="qryMemberInvoiceDetails" type="query" required="true">

		<cfset var local = structNew()>
		<cfset local.invoiceLink = "#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).scheme#://#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).mainhostname#/invoices">

		<cfsavecontent variable="local.memberInvoiceDetails">
			<cfoutput>
				<cfset local.totalDue = 0>
				<cfset local.invoiceList = "">
				<cfloop query="arguments.qryMemberInvoiceDetails">
					<cfset local.totalDue = local.totalDue + val(arguments.qryMemberInvoiceDetails.invDue)>
					<cfset local.stInvEnc = Replace(URLEncodedFormat(ToBase64(Encrypt("#arguments.qryMemberInvoiceDetails.invoicenumber#|#right(GetTickCount(),5)#|#arguments.qryMemberInvoiceDetails.invoiceCode#","M3mbeR_CenTR@l"))),"%","xPcmKx","ALL")>

					<cfset local.invoiceDownloadLink = '#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).scheme#://#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).mainhostname#/?pg=invoices&va=show&item=#local.stInvEnc#'>
					<cfset local.invPayOnlineLink = ''>

					<cfif listFindNoCase("Closed,Delinquent",arguments.qryMemberInvoiceDetails.invoiceStatus) and arguments.qryMemberInvoiceDetails.invDue gt 0 and arguments.qryMemberInvoiceDetails.showLink is 1>
						<cfset local.invPayOnlineLink = '#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).scheme#://#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).mainhostname#/invoices/#local.stInvEnc#'>
					</cfif>

					Invoice Number: #arguments.qryMemberInvoiceDetails.invoiceNumber#<br/>
					Invoice Amount Due: #dollarFormat(arguments.qryMemberInvoiceDetails.invDue)#<cfif arguments.qryMemberInvoiceDetails.invDue gt 0> on #DateFormat(arguments.qryMemberInvoiceDetails.dateDue,"m/d/yyyy")#</cfif><br/>
					<cfif arguments.qryMemberInvoiceDetails.invDue gt 0>
						Invoice Code: #arguments.qryMemberInvoiceDetails.invoiceCode# <br/>
					</cfif>
					<cfif arguments.qryMemberInvoiceDetails.recordcount is 1>
						<a href="#local.invoiceDownloadLink#">Click Here</a> to Download<cfif len(local.invPayOnlineLink)> or <a href="#local.invPayOnlineLink#">Pay Invoice Online</a></cfif>
					</cfif>

					<cfif arguments.qryMemberInvoiceDetails.recordcount gt 1>
						<cfset local.invoiceList = listAppend(local.invoiceList, "#arguments.qryMemberInvoiceDetails.invoiceNumber#|#right(GetTickCount(),5)#|#arguments.qryMemberInvoiceDetails.invoiceCode#")>
					</cfif>
					<br/>
				</cfloop>

				<cfif arguments.qryMemberInvoiceDetails.recordcount gt 1>
					<cfset local.stInvEnc = Replace(URLEncodedFormat(ToBase64(Encrypt("#local.invoiceList#","M3mbeR_CenTR@l"))),"%","xPcmKx","ALL")>
					<cfset local.multiInvoiceLink = '#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).scheme#://#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).mainhostname#/invoices/#local.stInvEnc#'>

					Total Due: #dollarFormat(local.totalDue)#<br/>
					<a href="#local.multiInvoiceLink#">Click Here</a> to view these invoices.
					<br/><br/><br/>
				<cfelse>
					<br/>
				</cfif>

				You may also visit <a href="#local.invoiceLink#">#local.invoiceLink#</a> and enter the invoice number and code for any outstanding invoice.
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.memberInvoiceDetails>
	</cffunction>

	<cffunction name="emailInvoice" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objET = CreateObject("component","model.admin.emailTemplates.emailTemplates")>
		<cfset local.qryEmailTemplates = local.objET.getCategoriesAndTemplatesForTree(siteID=arguments.event.getValue('mc_siteinfo.siteid'), treeCode="ETINVOICES")>
		<cfset local.qryEmailTemplateCategories = local.objET.getCategoriesForTree(siteID=arguments.event.getValue('mc_siteinfo.siteid'), treeCode="ETINVOICES")>
		<cfset local.formlink = buildCurrentLink(arguments.event,"doSendCustomEmailInvoice") & "&mode=stream">

		<cfswitch expression="#arguments.event.getValue('emailMode','single')#">
			<cfcase value="single">
				<cfset local.recipientEmailList = application.objMember.getMainEmail(memberID=arguments.event.getValue('mid',0)).email>
				<cfset local.qryInvoice = getInvoiceDetailsFromInvoiceID(orgID=arguments.event.getValue('mc_siteinfo.orgID'), invoiceID=arguments.event.getValue('vid',0))>
				
				<cfset local.strEmailData = { resourceTitle='Invoice', templateEditorLabel='Compose your message. <span class="fr" style="display:block;font-size:1.1em;"><i class="fa-regular fa-paperclip fa-lg"></i> We''ll automatically include a PDF attachment of the invoice.</span>',
												saveTemplateDesc='<b>Before we e-mail this invoice,</b> should we save this message as a template for future use?',
												attachmentName='#local.qryInvoice.invoicenumber# as PDF' }>

				<cfset local.strFormFields = structNew()>
				<cfset structInsert(local.strFormFields, 'emailMode', 'single')>
				<cfset structInsert(local.strFormFields, 'memberID', arguments.event.getValue('mid',0))>
				<cfset structInsert(local.strFormFields, 'invoiceIDList', arguments.event.getValue('vid',0))>
			</cfcase>
			<cfcase value="bundle">
				<cfset local.qryInvoices = getInvoiceDataFromTransactionID(transactionID=arguments.event.getValue('tid',0))>
				<cfset local.qryInvoiceDetails = getInvoiceDetailsFromInvoiceIDList(orgID=arguments.event.getValue('mc_siteinfo.orgID'), invoiceIDList=valueList(local.qryInvoices.invoiceID))>
				<cfset local.recipientEmailList = application.objMember.getMainEmail(memberID=val(local.qryInvoiceDetails.memberID)).email>
				
				<cfset local.strEmailData = { resourceTitle='Invoice(s)', templateEditorLabel='Compose your message. <span class="fr" style="display:block;font-size:1.1em;"><i class="fa-regular fa-paperclip fa-lg"></i> We''ll automatically include a PDF attachment of the invoices.</span>',
												saveTemplateDesc='<b>Before we e-mail these invoice(s),</b> should we save this message as a template for future use?',
												attachmentName='InvoiceBundle.pdf' }>

				<cfset local.strFormFields = structNew()>
				<cfset structInsert(local.strFormFields, 'emailMode', 'bundle')>
				<cfset structInsert(local.strFormFields, 'memberID', val(local.qryInvoiceDetails.memberID))>
				<cfset structInsert(local.strFormFields, 'invoiceIDList', valueList(local.qryInvoices.invoiceID))>
			</cfcase>
		</cfswitch>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_emailInvoice.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getPreviewCustomEmailInvoiceMessage" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="invoiceIDList" type="string" required="true">
		<cfargument name="templateContent" type="string" required="true">
		<cfargument name="subjectLine" type="string" required="true">
		<cfargument name="emailMode" type="string" required="true" hint="single or bundle">

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>

		<cftry>
			<cfif not hasInvoiceAdminRights(siteID=arguments.mcproxy_siteID, resourceFunction="invoiceEmail")>
				<cfthrow message="Invalid Request">
			</cfif>

			<cfset local.strParseCotent = getCustomEmailInvoiceParseContent(orgID=arguments.mcproxy_orgID, invoiceIDList=arguments.invoiceIDList, templateContent=arguments.templateContent, subjectLine=arguments.subjectLine, emailMode=arguments.emailMode)>
			<cfif not local.strParseCotent.success>
				<cfthrow message="Invoice not found">
			</cfif>

			<cfset local.parseContent = local.strParseCotent.parseContent>

			<cfset local.retStruct.templateDisp = application.objEmailWrapper.wrapMessage(emailTitle=application.objSiteInfo.getSiteInfo(arguments.mcproxy_siteCode).orgName, emailContent=local.parseContent.content, sitecode=arguments.mcproxy_siteCode)>
			<cfset local.retStruct.subjectline = urlEncodedFormat(local.parseContent.subjectLine)>
			<cfset local.retStruct.success = true>
		<cfcatch type="Any">
			<cfset local.retStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="sendCustomInvoiceTestEmailMessage" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="invoiceIDList" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="templateContent" type="string" required="true">
		<cfargument name="subjectLine" type="string" required="true">
		<cfargument name="emailFromName" type="string" required="true">
		<cfargument name="emailFrom" type="string" required="true">
		<cfargument name="emailMode" type="string" required="true" hint="single or bundle">

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.mcproxy_siteCode)>

		<cftry>
			<cfif not len(session.cfcuser.memberData.email) or not hasInvoiceAdminRights(siteID=arguments.mcproxy_siteID, resourceFunction="invoiceEmail")>
				<cfthrow message="Invalid Request">
			</cfif>

			<cfset local.strParseCotent = getCustomEmailInvoiceParseContent(orgID=arguments.mcproxy_orgID, invoiceIDList=arguments.invoiceIDList, templateContent=arguments.templateContent, subjectLine=arguments.subjectLine, emailMode=arguments.emailMode)>
			<cfif not local.strParseCotent.success>
				<cfthrow message="Invoice not found">
			</cfif>

			<cfset local.parseContent = local.strParseCotent.parseContent>
			<cfset local.emailTitle = local.mc_siteInfo.orgName>

			<cfswitch expression="#arguments.emailMode#">
				<cfcase value="single">
					<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.mcproxy_siteCode)>
					<cfset local.strInvoice = CreateObject("component","invoice").generateInvoice(siteID=local.mc_siteInfo.siteID, invoiceID=val(arguments.invoiceIDList), 
						tmpFolder=local.strFolder.folderPath, encryptFile=true, namedForBundle=false)>
				</cfcase>
				<cfcase value="bundle">
					<cfset local.strInvoice = createInvoiceBundle(siteID=local.mc_siteInfo.siteID, invoiceIDList=arguments.invoiceIDList)>
				</cfcase>
			</cfswitch>
			
			<cfif not FileExists(local.strInvoice.invoicePath)>
				<cfthrow message="File not found">
			</cfif>

			<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name=arguments.emailFromName, email=local.mc_siteinfo.networkEmailFrom },
				emailto=[ { name:'', email:session.cfcuser.memberData.email } ],
				emailreplyto=arguments.emailFrom,
				emailsubject="TEST: #local.parseContent.subjectLine#",
				emailtitle=local.emailTitle,
				emailhtmlcontent=local.parseContent.content,
				emailAttachments=[{ file=local.strInvoice.fileName, folderpath=local.strInvoice.folderPath }],
				siteID=local.mc_siteinfo.siteID,
				memberID=arguments.memberID,
				messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EMAILINV"),
				sendingSiteResourceID=local.mc_siteInfo.siteSiteResourceID,
				isTestMessage=1
			)>

			<cfset local.retStruct.success = true>
		<cfcatch type="Any">
			<cfset local.retStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getCustomEmailInvoiceParseContent" access="private" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="invoiceIDList" type="string" required="true">
		<cfargument name="templateContent" type="string" required="true">
		<cfargument name="subjectLine" type="string" required="true">
		<cfargument name="emailMode" type="string" required="true" hint="single or bundle">

		<cfset var local = structNew()>
		<cfset local.retStruct = { "success":true, "parseContent":"" }>

		<cfswitch expression="#arguments.emailMode#">
			<cfcase value="single">
				<cfset local.qryInvoices = getInvoiceDetailsFromInvoiceID(orgID=arguments.orgID, invoiceID=val(arguments.invoiceIDList))>
			</cfcase>
			<cfcase value="bundle">
				<cfset local.qryInvoices = getInvoiceDetailsFromInvoiceIDList(orgID=arguments.orgID, invoiceIDList=arguments.invoiceIDList)>
			</cfcase>
		</cfswitch>
				
		<cfif local.qryInvoices.recordcount is 0>
			<cfset local.retStruct.success = false>
		<cfelse>
			<!--- message prep --->
			<cfset local.retStruct.parseContent = parseContentWithMergeCodes(memberID=local.qryInvoices.memberID, content=arguments.templateContent, subjectLine=arguments.subjectLine, qryInvoices=local.qryInvoices)>
		</cfif>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getInvoiceDetailsFromInvoiceID" access="private" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="invoiceID" type="numeric" required="true">

		<cfset var qryInvoice = "">

		<cfquery name="qryInvoice" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @orgID int = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">;

			select i.invoiceID, i.invoiceProfileID, i.statusID, i.fullInvoiceNumber as invoiceNumber,
				ip.profileName, i.dateBilled, i.dateDue, i.invoiceCode,
				sum(it.cache_invoiceAmountAfterAdjustment) as InvAmt,
				sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) as InvDue,
				mActive.memberID, istat.status as invoiceStatus,
				dbo.fn_tr_showInvoicePayOnlineLink(i.invoiceid) as showLink
			from dbo.tr_invoices as i
			inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
				and istat.status in ('Closed','Delinquent','Paid')
			inner join dbo.tr_invoiceProfiles as ip on ip.orgID = @orgID and ip.profileID = i.invoiceProfileID
			inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = i.assignedToMemberID
			inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
				and mActive.status IN ('A','I')
			inner join dbo.organizations as o on o.orgID = @orgID and o.orgID = m.orgID
			left outer join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceID = i.invoiceID
			where i.orgID = @orgID
			and i.invoiceID = <cfqueryparam value="#arguments.invoiceID#" cfsqltype="CF_SQL_INTEGER">
			group by i.invoiceID, i.invoiceProfileID, i.statusID, i.fullInvoiceNumber, 
				ip.profileName, i.dateBilled, i.dateDue, i.invoiceCode, mActive.memberID, istat.status;
		</cfquery>

		<cfreturn qryInvoice>
	</cffunction>

	<cffunction name="doSendCustomEmailInvoice" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfswitch expression="#arguments.event.getValue('emailMode','')#">
			<cfcase value="single">
				<cfset local.qryInvoices = getInvoiceDetailsFromInvoiceID(orgID=arguments.event.getValue('mc_siteinfo.orgID'), invoiceID=val(arguments.event.getValue('invoiceIDList',0)))>
			</cfcase>
			<cfcase value="bundle">
				<cfset local.qryInvoices = getInvoiceDetailsFromInvoiceIDList(orgID=arguments.event.getValue('mc_siteinfo.orgID'), invoiceIDList=arguments.event.getValue('invoiceIDList',''))>
			</cfcase>
		</cfswitch>

		<cfif local.qryInvoices.recordcount is 0>
			<cfset local.data = '<h4>No Recipients Found</h4><div>Please check the filtered recipients.</div>'>
			<cfreturn returnAppStruct(local.data,"echo")>
		<cfelse>
			<cfquery name="local.qryDistinctMembers" dbtype="query">
				select distinct memberID
				from [local].qryInvoices
			</cfquery>
			<cfset local.memberIDList = valueList(local.qryDistinctMembers.memberID)>
			<cfset local.invoiceIDList = valueList(local.qryInvoices.invoiceID)>
		</cfif>

		<!--- message prep --->
		<cfset local.templateContent = application.objResourceRenderer.qualifyAllLinks(content=arguments.event.getTrimValue('templateContent',''), siteid=arguments.event.getValue('mc_siteInfo.siteid'))>	
		<cfset local.emailTitle = "#arguments.event.getValue('mc_siteinfo.orgName')#">
		<cfset local.emailSubject = arguments.event.getTrimValue('emailSubject','')>
		<cfset local.recipientEmailList = arguments.event.getTrimValue('recipientEmailList','')>
		<cfset local.emailTemplateCategoryID = arguments.event.getTrimValue('selCategory',0)>
		<cfset local.templateAndSubjectContentToParse = "#local.emailSubject##local.templateContent#">
		<cfset local.emailContentWrapper = application.objEmailWrapper.wrapMessage(emailTitle=local.emailTitle, emailContent=local.templateContent, sitecode=arguments.event.getValue('mc_siteInfo.sitecode'))>

		<cfif len(local.recipientEmailList)>
			<cfloop list="#local.recipientEmailList#" index="local.thisEmail" delimiters=";">
				<cfif NOT (len(local.thisEmail) AND isValid("regex",local.thisEmail,application.regEx.email))>
					<cfthrow message="Invalid recipient email address.">
					<cfbreak>
				</cfif>
			</cfloop>
		<cfelse>
			<cfthrow message="Invalid recipient email address.">
		</cfif>

		<cfif application.MCEnvironment neq "production"> 
			<cfset local.deliveryReportEmail = "<EMAIL>">
		<cfelseif len(session.cfcuser.memberData.email)>
			<cfset local.deliveryReportEmail = session.cfcuser.memberData.email>
		<cfelse>
			<cfset local.deliveryReportEmail = ''>
		</cfif>

		<!--- if including merge codes that require coldfusion, we need to mark recipient as not ready so we can insert the field below --->
		<cfset local.hasExtendedMergeCodes = false>
		<cfset local.strRecipientExtMergeTags = application.objMergeCodes.detectExtendedMergeCodes(siteID=arguments.event.getValue('mc_siteinfo.siteid'), rawContent=local.templateAndSubjectContentToParse, extraMergeTagList='')>
		<cfif local.strRecipientExtMergeTags.contentHasMergeCodes>
			<cfset local.hasExtendedMergeCodes = true>
		</cfif>

		<!--- generating invoice --->
		<cfswitch expression="#arguments.event.getValue('emailMode','')#">
			<cfcase value="single">
				<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.siteCode'))>
				<cfset local.strInvoice = CreateObject("component","invoice").generateInvoice(siteID=arguments.event.getValue('mc_siteInfo.siteid'), invoiceID=val(local.qryInvoices.invoiceID), 
												tmpFolder=local.strFolder.folderPath, encryptFile=true, namedForBundle=false)>
				<cfset local.strInvoice.folderPath = local.strFolder.folderPath>
			</cfcase>
			<cfcase value="bundle">
				<cfset local.strInvoice = createInvoiceBundle(siteID=arguments.event.getValue('mc_siteInfo.siteid'), invoiceIDList=local.invoiceIDList)>
			</cfcase>
		</cfswitch>

		<!--- create message and recipients with merge codes --->
		<cftry>
			<cfif arguments.event.getValue('saveTemplateOption',0) is 1 and local.emailTemplateCategoryID is 0>
				<cfset local.EmailTemplateAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EmailTemplateAdmin', siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
			</cfif>

			<cfquery name="local.qryEmailInvoiceRecipients" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @siteID int, @resourceTypeID int, @parentSiteResourceID int, @emailSubject varchar(200),
						@templateName varchar(300), @rawContent varchar(max), @contentID int, @siteResourceID int, @toolType varchar(200),
						@memberID int, @contentVersionID int, @controllingSiteResourceID int, @categoryTreeID int, @categoryTreeCode varchar(20),
						@categoryTreeName varchar(100), @emailTemplateCategoryID int, @emailTemplateID int, @emailFromName varchar(200), @emailFrom varchar(200),
						@emailContentWrapper varchar(max), @memberIDList varchar(max), @invoiceIDList varchar(max), @deliveryReportEmail varchar(200),
						@categoryName varchar(200), @sendOnDate datetime, @recipientEmailList varchar(max);

					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteid')#">;
					SET @emailSubject = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.emailSubject#">;
					SET @rawContent = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.templateContent#">;
					SET @emailFromName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('emailFromName','')#">;
					SET @emailFrom = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('emailReplyTo','')#">;
					SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
					SET @emailContentWrapper = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.emailContentWrapper#">;
					SET @memberIDList = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.memberIDList#">;
					SET @invoiceIDList = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.invoiceIDList#">;
					SET @recipientEmailList = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.recipientEmailList#">,'');
					SET @toolType = 'InvoiceAdmin';

					<cfif len(local.deliveryReportEmail)>
						SET @deliveryReportEmail = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.deliveryReportEmail#">;
					</cfif>

					<cfif arguments.event.getValue('massEmailScheduling','') eq 'later' and len(arguments.event.getValue('emailDateScheduled',''))>
						SET @sendOnDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#parseDateTime(replace(arguments.event.getValue('emailDateScheduled'),' - ',' '))#">;
					<cfelse>
						SET @sendOnDate = getDate();
					</cfif>
				
					SET @resourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedContent');
				
					SELECT @parentSiteResourceID = st.siteResourceID 
					FROM dbo.admin_tooltypes tt
					INNER JOIN dbo.admin_siteTools st ON st.toolTypeID = tt.toolTypeID
						AND st.siteID = @siteID
						AND tt.toolType = @toolType
					INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = st.siteResourceID
						AND sr.siteResourceStatusID = 1;

					BEGIN TRAN;
						<!--- email invoice without using template --->
						<cfif arguments.event.getValue('saveTemplateOption',0) is 0>
							EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@resourceTypeID, @parentSiteResourceID=@parentSiteResourceID, 
								@siteResourceStatusID=1, @isHTML=1, @languageID=1, @isActive=1, @contentTitle=@emailSubject, @contentDesc='', 
								@rawContent=@rawContent, @memberID=@memberID, @contentID=@contentID OUTPUT, @siteResourceID=@siteResourceID OUTPUT;

							select top 1 @contentVersionID = cv.contentVersionID
							from dbo.cms_contentLanguages as cl
							inner join dbo.cms_contentVersions as cv on cv.siteID = @siteID
								and cv.contentID = @contentID
								and cv.contentLanguageID = cl.contentLanguageID 
								and cv.isActive = 1
							where cl.siteID = @siteID
							and cl.contentID = @contentID
							and cl.languageID = 1;

						<!--- create a new email template --->
						<cfelseif arguments.event.getValue('saveTemplateOption',0) is 1>
							<cfif local.emailTemplateCategoryID is 0>
								set @categoryTreeCode = 'ETINVOICES';
								set @controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.EmailTemplateAdminSRID#">;
								set @categoryName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('newCategoryName','')#">;

								select @categoryTreeName = categoryTreeName 
								from dbo.cms_categoryTrees 
								where controllingSiteResourceID = @controllingSiteResourceID 
								and categoryTreeCode = @categoryTreeCode;

								select @categoryTreeID = dbo.fn_getCategoryTreeIDForSiteResourceIDandTree(@controllingSiteResourceID,@categoryTreeName);

								EXEC dbo.cms_createCategory @categoryTreeID=@categoryTreeID, @categoryName=@categoryName, @categoryDesc='', @categoryCode='', 
									@parentCategoryID=NULL, @contributorMemberID=@memberID, @categoryID=@emailTemplateCategoryID OUTPUT;
							<cfelse>
								set @emailTemplateCategoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.emailTemplateCategoryID#">;
							</cfif>

							SET @templateName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('templateName','')#">;
							
							EXEC dbo.et_createEmailTemplate @templateTypeCode='ckeditor', @templateName=@templateName, @templateDescription='', 
								@categoryID=@emailTemplateCategoryID, @rawContent=@rawContent, @subjectLine=@emailSubject, 
								@emailFromName=@emailFromName, @emailFrom=@emailFrom, @createdByMemberID=@memberID, 
								@siteID=@siteID, @templateID=@emailTemplateID OUTPUT;

							select top 1 @contentVersionID = cv.contentVersionID
							from dbo.et_emailTemplates as et
							inner join dbo.cms_contentLanguages as cl on cl.siteID = @siteID
								and cl.contentID = et.contentID 
								and cl.languageID = 1
							inner join dbo.cms_contentVersions as cv on cv.siteID = @siteID
								and cv.contentID = cl.contentID
								and cv.contentLanguageID = cl.contentLanguageID 
								and cv.isActive = 1
							where et.templateID = @emailTemplateID;

						<!--- update existing email template --->
						<cfelseif arguments.event.getValue('saveTemplateOption',0) is 2>
							set @emailTemplateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fEmailTemplateID',0)#">;

							update dbo.et_emailTemplates
							set subjectLine = @emailSubject,
								emailFromName = @emailFromName,
								emailFrom = @emailFrom
							where templateID = @emailTemplateID;

							select @contentID = contentID 
							from dbo.et_emailTemplates
							where templateID = @emailTemplateID;

							EXEC dbo.cms_updateContent @contentID=@contentID, @languageID=1, @isHTML=1, @contentTitle=@emailSubject, 
								@contentDesc='', @rawcontent=@rawcontent, @memberID=@memberID;

							select top 1 @contentVersionID = cv.contentVersionID
							from dbo.cms_contentLanguages as cl
							inner join dbo.cms_contentVersions as cv on cv.siteID = @siteID
								and cv.contentID = @contentID
								and cv.contentLanguageID = cl.contentLanguageID 
								and cv.isActive = 1
							where cl.siteID = @siteID
							and cl.contentID = @contentID
							and cl.languageID = 1;

						</cfif>
					COMMIT TRAN;

					-- insert and return email recipients
					EXEC dbo.ams_emailInvoices @siteID=@siteID, @memberIDList=@memberIDList, @invoiceIDList=@invoiceIDList, @messageToParse=@rawContent, 
						@messageWrapper=@emailContentWrapper, @emailTagTypeID=NULL, @emailFromName=@emailFromName, @emailReplyTo=@emailFrom, @emailSubject=@emailSubject, 
						@contentVersionID=@contentVersionID, @recordedByMemberID=@memberID, @deliveryReportEmail=@deliveryReportEmail, @overrideEmailList=@recipientEmailList, 
						@sendOnDate=@sendOnDate, @markRecipientAsReady=0, @consentListIDs=null;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		<cfcatch type="Any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("No recipients for message", cfcatch.detail)>
				<cfset local.errorCode = 'noemailrecipient'>
			<cfelse>
				<cfset local.errorCode = ''>
			</cfif>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfreturn returnAppStruct(showMessage(errorCode=local.errorCode),"echo")>
		</cfcatch>
		</cftry>

		<!--- insert email attachments --->
		<cfif FileExists(local.strInvoice.invoicePath)>
			<cftry>

				<cfset local.pathFromS3Uploader = replacenocase(local.strInvoice.invoicePath,application.paths.SharedTempNoWeb.path,application.paths.SharedTempNoWeb.pathS3Uploader)>
				<cfset local.pathFromS3Uploader = replace(local.pathFromS3Uploader,'/','\','all')>

				<cfquery name="local.qryInsertEmailAttachments" datasource="#application.dsn.platformMail.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						DECLARE @fileName varchar(400), @localDirectory varchar(400), @s3keyMod varchar(4), @messageStatusIDQueued int,
							@objectKey varchar(400), @filePathForS3Upload varchar(400), @attachmentID int, @s3bucketName varchar(100), 
							@s3UploadReadyStatusID int, @nowDate datetime = GETDATE();
						DECLARE @tmpRecipients TABLE (recipientID int);

						SET @fileName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strInvoice.displayName#">;
						SET @localDirectory = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strInvoice.folderPath#">;
						SET @filePathForS3Upload = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.pathFromS3Uploader#">;
						SET @s3bucketName = 'platformmail-membercentral-com';

						SELECT @messageStatusIDQueued = statusID 
						FROM dbo.email_statuses 
						WHERE statusCode = 'Q';

						EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Upload', @queueStatus='readyToProcess', @queueStatusID=@s3UploadReadyStatusID OUTPUT;

						INSERT INTO @tmpRecipients (recipientID)
						select listitem
						from memberCentral.dbo.fn_intListToTable('0#valueList(local.qryEmailInvoiceRecipients.recipientID)#',',');

						BEGIN TRAN;
							EXEC dbo.email_insertAttachment @fileName=@fileName, @localDirectory=@localDirectory, @attachmentID=@attachmentID OUTPUT;

							INSERT INTO dbo.email_messageRecipientAttachments (recipientID, attachmentID)
							select recipientID, @attachmentID
							from @tmpRecipients;

							<!--- insert to s3 upload queue --->
							SET @s3keyMod = FORMAT(@attachmentID % 1000, '0000');
							SET @objectKey = LOWER('#application.MCEnvironment#/outgoing/' + @s3keyMod + '/' + cast(@attachmentID as varchar(10)) + '/' + @fileName);

							IF NOT EXISTS (select 1 from platformQueue.dbo.queue_S3Upload where s3bucketName = @s3bucketName and objectKey = @objectKey)
								INSERT INTO platformQueue.dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated)
								VALUES (@s3UploadReadyStatusID, @s3bucketName, @objectKey, @filePathForS3Upload, 0, @nowDate, @nowDate);

							<cfif NOT local.hasExtendedMergeCodes>
								UPDATE mrh 
								SET mrh.emailStatusID = @messageStatusIDQueued
								FROM dbo.email_messageRecipientHistory as mrh
								INNER JOIN @tmpRecipients as tmp on tmp.recipientID = mrh.recipientID;
							</cfif>
						COMMIT TRAN;

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfreturn returnAppStruct(showMessage(errorCode=''),"echo")>
			</cfcatch>
			</cftry>
		</cfif>

		<cfif local.hasExtendedMergeCodes>
			<cftry>
				<cfset replaceDetectedExtendedMergeCodes(event=arguments.event, qryRecipients=local.qryEmailInvoiceRecipients, strRecipientExtMergeTags=local.strRecipientExtMergeTags)>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfreturn returnAppStruct(showMessage(errorCode=''),"echo")>
			</cfcatch>
			</cftry>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">
					top.MCModalUtils.hideModal();
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="replaceDetectedExtendedMergeCodes" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="qryRecipients" type="query" required="true">
		<cfargument name="strRecipientExtMergeTags" type="struct" required="true">

		<cfset var local = structNew()>

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = arguments.event.getValue('mc_siteinfo.mainHostName')>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
		</cfif>

		<cfloop query="arguments.qryRecipients">
			<cfset local.strMergeTagArgs = { siteID=arguments.event.getValue('mc_siteinfo.siteid'), orgcode=arguments.event.getValue('mc_siteinfo.orgcode'),
				recipientID=arguments.qryRecipients.recipientID, messageID=arguments.qryRecipients.messageID, recipientMemberID=arguments.qryRecipients.memberID,
				memberID=arguments.qryRecipients.memberID, membernumber=arguments.qryRecipients.membernumber, hostname=local.thisHostname,
				useRemoteLogin=arguments.event.getValue('mc_siteinfo.useRemoteLogin'), strRecipientExtMergeTags=arguments.strRecipientExtMergeTags }>
			<cfset application.objMergeCodes.replaceExtendedMergeCodes(argumentCollection=local.strMergeTagArgs)>

			<cfset setRecipientAsReady(siteID=arguments.event.getValue('mc_siteinfo.siteid'),messageID=arguments.qryRecipients.messageID,recipientID=arguments.qryRecipients.recipientID)>
		</cfloop>
	</cffunction>

	<cffunction name="setRecipientAsReady" access="private" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="messageID" type="numeric" required="true">
		<cfargument name="recipientID" type="numeric" required="true">

		<cfset var qryEmailTemplateData = "">

		<cfquery datasource="#application.dsn.platformMail.dsn#" name="qryEmailTemplateData">
			exec dbo.email_setMessageRecipientHistoryStatus
				@siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">,
				@messageID = <cfqueryparam value="#arguments.messageID#" cfsqltype="CF_SQL_INTEGER">,
				@recipientID = <cfqueryparam value="#arguments.recipientID#" cfsqltype="CF_SQL_INTEGER">,
				@statusCode = <cfqueryparam value="Q" cfsqltype="CF_SQL_VARCHAR">,
				@updateDate = <cfqueryparam value="0" cfsqltype="CF_SQL_BIT">
		</cfquery>
	</cffunction>

	<cffunction name="hasInvoiceAdminRights" access="public" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="resourceFunction" type="string" required="yes">

		<cfset var local = structNew()>

		<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='InvoiceAdmin', siteID=arguments.siteID)>
		<cfset local.tmpRights = buildRightAssignments(siteResourceID=local.siteResourceID, memberID=session.cfcuser.memberdata.memberid, siteID=arguments.siteID)>

		<cfreturn structKeyExists(local.tmpRights, arguments.resourceFunction) AND local.tmpRights[arguments.resourceFunction] eq 1>
	</cffunction>

	<cffunction name="showMessage" access="package" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfswitch expression="#arguments.errorCode#">
			<cfcase value="noemailrecipient">
				<cfset local.message = '<h4>No Recipients with Defined Emails</h4><div>No filtered recipients had email addresses defined, so we were not able to send this message.</div>'>
			</cfcase>
			<cfdefaultcase>
				<cfset local.message = "<b>An error occurred. Try again or contact support for assistance.</b">
			</cfdefaultcase>
		</cfswitch>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				<div style="padding:10px;">#JSStringFormat(local.message)#</div>
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

</cfcomponent>