ALTER PROC dbo.queue_paperStatements_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int, @downloadModeColumnID int;
	EXEC dbo.queue_getQueueTypeID @queueType='PaperStatements', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (detailID int PRIMARY KEY, itemID int);

	-- dequeue. 
	WITH itemIDs AS (
		select distinct qp.itemID
		from dbo.queue_paperStatementsDetail qd
		INNER JOIN dbo.queue_paperStatements as qp ON qp.itemID = qd.itemID
		where qd.queueStatusID  = @statusReady
			except
		select distinct qp.itemID
		from dbo.queue_paperStatementsDetail qd
		INNER JOIN dbo.queue_paperStatements as qp ON qp.itemID = qd.itemID
		where qd.queueStatusID  <> @statusReady
	)
	UPDATE qid
	SET qid.queueStatusID  = @statusGrabbed,
		qid.dateUpdated = GETDATE()
		OUTPUT INSERTED.detailID, tmpIDs.itemID
		INTO #tmpNotify
	FROM itemIDs as tmpIDs
	INNER JOIN dbo.queue_paperStatements as qi on qi.itemID = tmpIDs.itemID
	INNER JOIN dbo.queue_paperStatementsDetail as qid on qid.itemID = qi.itemID
		and qid.queueStatusID = @statusReady;

	select qp.itemID, me.email as reportEmail, s.siteName, s.siteID, s.siteCode, o.orgCode, mActive.memberID, 
		mActive.firstname, mActive.lastname, mActive.memberNumber, qp.downloadMode
	from #tmpNotify as ig
	INNER JOIN dbo.queue_paperStatements as qp ON qp.itemID = ig.itemID
	INNER JOIN membercentral.dbo.sites s on s.siteID = qp.siteID
	INNER JOIN membercentral.dbo.organizations o on s.orgID = o.orgID
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID in (qp.orgID,1) 
		and m.memberID = qp.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID = m.orgID 
		and mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = m.orgID and metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m.orgID 
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = m.orgID
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	group by qp.itemID, me.email, s.siteName, s.siteID, s.siteCode, o.orgCode, mActive.memberID, 
		mActive.firstname, mActive.lastname, mActive.memberNumber, qp.downloadMode
	order by qp.itemID;
	
	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO