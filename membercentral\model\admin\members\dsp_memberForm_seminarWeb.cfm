<cfif structKeyExists(local.tmpMemSetRights,"ViewSeminarWeb") and local.tmpMemSetRights.ViewSeminarWeb neq 1>
	<cflocation url="#buildCurrentLink(arguments.event,"message")#&message=1" addtoken="no">
</cfif>
<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>
<cfsavecontent variable="local.registrantJS">
	<cfoutput>
	<script type="text/javascript" src="/assets/admin/javascript/seminarweb.js#local.assetCachingKey#"></script>
	<script>
		var #ToScript(arguments.event.getValue('mc_siteInfo.siteid'),"sw_siteid")#;
		var #ToScript(arguments.event.getValue('mc_siteInfo.sitecode'),"sw_sitecode")#;
		<cfif len(arguments.event.getValue('mc_siteInfo.swlBrand'))>
			var #ToScript(local.SWLRegistrantsLink,'link_swlregistrantslist')#
			var SWLRegistrantsListTable, sw_showCreditCol = 1, sw_showAttendanceCol = 1;
			var #ToScript(local.editSWLProgram,'link_editswlprogram')#;
            var #ToScript(local.sendSWLMaterialsLink,'link_viewswlmaterials')#;
            var #ToScript(local.sendSWLReplayLink,'link_replaylink')#;
			var #ToScript(local.viewSWLProgressLink,'link_viewswlprogress')#;
		</cfif>
		<cfif len(arguments.event.getValue('mc_siteInfo.swodBrand'))>
			var SWODRegistrantsListTable;
			var #ToScript(local.SWODRegistrantsLink,'link_swodregistrantslist')#
			var #ToScript(local.editSWODProgram,'link_editswodprogram')#
			var #ToScript(local.viewSWODProgressLink,'link_viewswodprogress')#
		</cfif>
		<cfif len(arguments.event.getValue('mc_siteInfo.swcpBrand'))>
			var #ToScript(local.SWCPRegistrantsLink,'link_swcpregistrantslist')#
			var #ToScript(local.editSWCPProgram,'link_editswcpprogram')#
			var #ToScript(local.viewSWCPProgressLink,'link_viewswcpprogress')#
			var #ToScript(local.viewSWCPProgressDetailLink,'link_viewswcpdetailprogress')#
		</cfif>
		var sw_itemtype, sw_reggridmode = 'reggrid';
		var #ToScript(local.manageSWCreditLink,'link_managecredit')#;
		var #ToScript(local.viewSWCertificateLink,'link_viewcertificate')#;
		var #ToScript(local.viewSWCommunicationLink,'link_viewcommunication')#;
		var #ToScript(local.resendSWInstructionsLink,'link_resendinstructions')#;
		var #ToScript(local.changeRegistrantPriceLink,'link_changeregprice')#
		var #ToScript(local.removeEnrollmentLink,'link_removeenrollment')#;

		var #ToScript(local.addSWPaymentLink,'link_addswpayment')#;
		var #ToScript(local.myRightsTransactionsAdmin.transAllocatePayment,'sw_hastransallocaterights')#;
		<cfif local.myRightsTransactionsAdmin.transAllocatePayment is 1>
			var #ToScript(local.allocateSWPaymentLink,'link_allocateswpayment')#;
		</cfif>

		$(document).ready(function(){		
			<cfif len(arguments.event.getValue('mc_siteInfo.swlBrand'))>
				initSWLRegistrants();
			</cfif>
			<cfif len(arguments.event.getValue('mc_siteInfo.swodBrand'))>
				initSWODRegistrants();
			</cfif>
			<cfif len(arguments.event.getValue('mc_siteInfo.swcpBrand'))>
				initSWCPRegistrants();
			</cfif>

			loadSWTab();
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.registrantJS)#">

<cfoutput>
<cfif len(arguments.event.getValue('mc_siteInfo.swlBrand'))>
	<div class="row my-3">
		<div class="col-xl-12">
			<div class="card card-box mb-1">
				<div class="card-header py-1 bg-light">
					<div class="card-header--title font-weight-bold font-size-lg">
						#local.SWLheading#
					</div>
				</div>
				<div class="card-body pb-3">
					<div class="toolButtonBar">
						<div><a href="javascript:filterSWLProgramRegistrations();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter registrations."><i class="fa-regular fa-users-gear"></i> Filter Registrations</a></div>
					</div>

					<div id="divSWLRegFilterForm" class="row my-3" style="display:none;">
						<div class="col-xl-12">
							<div class="card card-box mb-1">
								<div class="card-header py-1 bg-light">
									<div class="card-header--title font-weight-bold font-size-md">
										Filter Registrations
									</div>
								</div>
								<div class="card-body pb-3">
									<form name="frmSWLRegFilter" id="frmSWLRegFilter">
										<div class="row">
											<div class="col-xl-6 col-lg-12">
												<div class="form-row">
													<div class="col">
														<div class="form-label-group mb-2">
															<div class="input-group dateFieldHolder">
																<input type="text" name="rSWLDateFrom" id="rSWLDateFrom" value="" class="form-control dateControl">
																<div class="input-group-append">
																	<span class="input-group-text cursor-pointer calendar-button" data-target="rSWLDateFrom"><i class="fa-solid fa-calendar"></i></span>
																	<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('rSWLDateFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
																</div>
																<label for="rSWLDateFrom">Registered From</label>
															</div>
														</div>
													</div>
													<div class="col">
														<div class="form-label-group mb-2">
															<div class="input-group dateFieldHolder">
																<input type="text" name="rSWLDateTo" id="rSWLDateTo" value="" class="form-control dateControl">
																<div class="input-group-append">
																	<span class="input-group-text cursor-pointer calendar-button" data-target="rSWLDateTo"><i class="fa-solid fa-calendar"></i></span>
																	<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('rSWLDateTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
																</div>
																<label for="rSWLDateTo">Registered To</label>
															</div>
														</div>
													</div>
												</div>
											</div>
											<div class="col-xl-6 col-lg-12">
												<div class="form-row">
													<div class="col">
														<div class="form-label-group mb-2">
															<div class="input-group">
																<select name="rSWLAttended" id="rSWLAttended" class="form-control">
																	<option value="">Attended or Not Attended</option>
																	<option value="1">Attended</option>
																	<option value="0">Not Attended</option>
																</select>
																<label for="rSWLAttended">Attended Status</label>
															</div>
														</div>
													</div>
													<div class="col">
														<div class="form-label-group mb-2">
															<div class="input-group">
																<select name="rSWLCredits" id="rCredits" class="form-control">
																	<option value="">Awarded Credits or No Credits</option>						
																	<option value="1">Awarded Credits</option>
																	<option value="0">Awarded no credits</option>
																</select>
																<label for="rSWLCredits">Credits Status</label>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
										<div class="row">
											<div class="col-xl-6 col-lg-12">
												<div class="form-label-group mb-2">
													<div class="input-group">
														<select name="rSWLHideDeleted" id="rSWLHideDeleted" class="form-control">
															<option value="1">Hide Deleted Registrations</option>
															<option value="0">Show Deleted Registrations</option>
														</select>
														<label for="rSWLHideDeleted">Deleted Registration</label>
													</div>
												</div>
											</div>
											<div class="col-xl-6 col-lg-12 text-right align-self-center">
												<button type="button" name="btnClearSWLRegFilters" class="btn btn-sm btn-secondary" onclick="resetSWRegFilters('swl');">Clear Filters</button>
												<button type="button" name="btnFilterSWLRegGrid" class="btn btn-sm btn-primary" onclick="dofilterSWLProgramRegistrations();">
													<i class="fa-light fa-filter"></i> Filter Registrations
												</button>
											</div>
										</div>
									</form>
								</div>
							</div>
						</div>
					</div>

					<table id="SWLRegistrantsListTable" class="table table-sm table-striped table-bordered" style="width:100%">
						<thead>
							<tr>
								<th>Program</th>
								<th>Registered</th>
								<th>Attended</th>
								<th>Credits</th>
								<th>Billed</th>
								<th>Due</th>
								<th>Actions</th>
							</tr>
						</thead>
					</table>
				</div>
			</div>
		</div>
	</div>
</cfif>

<cfif len(arguments.event.getValue('mc_siteInfo.swodBrand'))>
	<div class="row my-3">
		<div class="col-xl-12">
			<div class="card card-box mb-1">
				<div class="card-header py-1 bg-light">
					<div class="card-header--title font-weight-bold font-size-lg">
						#local.SWODheading#
					</div>
				</div>
				<div class="card-body pb-3">
					<div class="toolButtonBar">
						<div><a href="javascript:filterSWODProgramRegistrations();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter registrations."><i class="fa-regular fa-users-gear"></i> Filter Registrations</a></div>
					</div>

					<div id="divSWODRegFilterForm" class="row my-3" style="display:none;">
						<div class="col-xl-12">
							<div class="card card-box mb-1">
								<div class="card-header py-1 bg-light">
									<div class="card-header--title font-weight-bold font-size-md">
										Filter Registrations
									</div>
								</div>
								<div class="card-body pb-3">
									<form name="frmSWODRegFilter" id="frmSWODRegFilter">
										<div class="row">
											<div class="col-xl-6 col-lg-12">
												<div class="form-row">
													<div class="col">
														<div class="form-label-group mb-2">
															<div class="input-group dateFieldHolder">
																<input type="text" name="rSWODDateFrom" id="rSWODDateFrom" value="" class="form-control dateControl">
																<div class="input-group-append">
																	<span class="input-group-text cursor-pointer calendar-button" data-target="rSWODDateFrom"><i class="fa-solid fa-calendar"></i></span>
																	<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('rSWODDateFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
																</div>
																<label for="rSWODDateFrom">Registered From</label>
															</div>
														</div>
													</div>
													<div class="col">
														<div class="form-label-group mb-2">
															<div class="input-group dateFieldHolder">
																<input type="text" name="rSWODDateTo" id="rSWODDateTo" value="" class="form-control dateControl">
																<div class="input-group-append">
																	<span class="input-group-text cursor-pointer calendar-button" data-target="rSWODDateTo"><i class="fa-solid fa-calendar"></i></span>
																	<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('rSWODDateTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
																</div>
																<label for="rSWODDateTo">Registered To</label>
															</div>
														</div>
													</div>
												</div>
											</div>
											<div class="col-xl-6 col-lg-12">
												<div class="form-row">
													<div class="col">
														<div class="form-label-group mb-2">
															<select name="fSWODCompleted" id="fSWODCompleted" class="form-control">
																<option value="">Complete or Incomplete Programs</option>
																<option value="C">Completed Programs Only</option>
																<option value="I">Incomplete Programs Only</option>
															</select>
															<label for="fSWODCompleted">Complete Status</label>
														</div>
													</div>
													<div class="col">
														<div class="form-label-group mb-2">
															<select name="rSWODHideDeleted" id="rSWODHideDeleted" class="form-control">
																<option value="1">Hide Deleted Registrations</option>
																<option value="0">Show Deleted Registrations</option>
															</select>
															<label for="rSWODHideDeleted">Deleted Registration</label>
														</div>
													</div>
												</div>
											</div>
										</div>
										<div class="row">
											<div class="col-xl-6 col-lg-12">
												<div class="form-row">
													<div class="col">
														<div class="form-label-group mb-2">
															<div class="input-group dateFieldHolder">
																<input type="text" name="cSWODDateFrom" id="cSWODDateFrom" value="" class="form-control dateControl">
																<div class="input-group-append">
																	<span class="input-group-text cursor-pointer calendar-button" data-target="cSWODDateFrom"><i class="fa-solid fa-calendar"></i></span>
																	<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('cSWODDateFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
																</div>
																<label for="cSWODDateFrom">Completed From</label>
															</div>
														</div>
													</div>
													<div class="col">
														<div class="form-label-group mb-2">
															<div class="input-group dateFieldHolder">
																<input type="text" name="cSWODDateTo" id="cSWODDateTo" value="" class="form-control dateControl">
																<div class="input-group-append">
																	<span class="input-group-text cursor-pointer calendar-button" data-target="cSWODDateTo"><i class="fa-solid fa-calendar"></i></span>
																	<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('cSWODDateTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
																</div>
																<label for="cSWODDateTo">Completed To</label>
															</div>
														</div>
													</div>
												</div>
											</div>
											<div class="col-xl-6 col-lg-12 text-right align-self-center">
												<button type="button" name="btnClearSWLRegFilters" class="btn btn-sm btn-secondary" onclick="resetSWRegFilters('swod');">Clear Filters</button>
												<button type="button" name="btnFilterSWODRegGrid" class="btn btn-sm btn-primary" onclick="doFilterSWODProgramRegistrations();">
													<i class="fa-light fa-filter"></i> Filter Registrations
												</button>
											</div>	
										</div>
									</form>
								</div>
							</div>
						</div>
					</div>

					<table id="SWODRegistrantsListTable" class="table table-sm table-striped table-bordered" style="width:100%">
						<thead>
							<tr>
								<th>Program</th>
								<th>Registered</th>
								<th>Credits</th>
								<th>Billed</th>
								<th>Due</th>
								<th>Actions</th>
							</tr>
						</thead>
					</table>
				</div>
			</div>
		</div>
	</div>
</cfif>

<cfif len(arguments.event.getValue('mc_siteInfo.swcpBrand'))>
	<div class="card card-box my-3">
		<div class="card-header py-1 bg-light">
			<div class="card-header--title font-weight-bold font-size-lg">
				#local.SWCPheading#
			</div>
		</div>
		<div class="card-body pb-3">
			<table id="SWCPRegistrantsListTable" class="table table-sm table-striped table-bordered" style="width:100%">
				<thead>
					<tr>
						<th>Program</th>
						<th>Progress</th>
						<th>Tools</th>
					</tr>
				</thead>
			</table>
		</div>
	</div>
</cfif>
</cfoutput>