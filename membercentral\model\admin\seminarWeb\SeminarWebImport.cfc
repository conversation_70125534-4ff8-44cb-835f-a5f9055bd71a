<cfcomponent output="false">
	<!--- SWOD Import --->
	<cffunction name="generateSWODImportTemplate" access="package" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = "The sample import file could not be generated. Contact MemberCentral for assistance.">
		
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix='sw')>
		<cfset local.reportFileName = "SWODImportTemplate.csv">

		<cfset local.arrFields = [
			["ProgramCode",""], ["Featured",""], ["SeminarTitle","Req"], ["SeminarSubtitle",""], ["Description","Req"],
			["Objective1",""], ["Objective2",""], ["Objective3",""], ["Objective4",""], ["Objective5",""], ["IntroductoryText",""],["CompletionText",""],
			["OrigPublished","Req"], ["PlayerMode","Req"], ["PlayerQATab","Req"], ["BlankPlayer","Req"], 
			["CompleteTime","Req"], ["Certificate","Req"], ["SellInCatalog","Req"], ["StartSale","Req"], ["EndSale","Req"], 
			["RateName1 ",""], ["RateAmount1",""], ["RateName2",""], ["RateAmount2",""], ["RateName3",""], ["RateAmount3",""], 
			["RateName4",""], ["RateAmount4",""], ["RateName5",""], ["RateAmount5",""], 
			["Credit","Req"], ["Subjects",""], ["AutoCreateTitle","Req"], ["Evaluation",""]
		]>

		<cfquery name="local.qryExport" datasource="#Application.dsn.membercentral.dsn#" result="local.qryExportResult">
			set nocount on;

			-- create temp table
			IF OBJECT_ID('tempdb..##tmpSWODTemplate') IS NOT NULL 
				DROP TABLE ##tmpSWODTemplate;
			CREATE TABLE ##tmpSWODTemplate (
				autoID int
				<cfloop array="#local.arrFields#" index="local.thisCol">
					, #local.thisCol[1]# varchar(30)
				</cfloop>
			);

			insert into ##tmpSWODTemplate (
				<cfloop array="#local.arrFields#" item="local.thisCol" index="local.index">
					#local.thisCol[1]#<cfif local.index lt arrayLen(local.arrFields)>,</cfif>
				</cfloop>
			)
			values (
				<cfloop array="#local.arrFields#" item="local.thisCol" index="local.index">
					'#local.thisCol[2]#'<cfif local.index lt arrayLen(local.arrFields)>,</cfif>
				</cfloop>
			);

			DECLARE @selectsql varchar(max) = '
				SELECT
					<cfloop array="#local.arrFields#" item="local.thisCol" index="local.index">
						#local.thisCol[1]#,
					</cfloop>
					ROW_NUMBER() OVER(order by #local.arrFields[1][1]#) as mcCSVorder 
				*FROM* ##tmpSWODTemplate';
			EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#', @returnColumns=0;

			IF OBJECT_ID('tempdb..##tmpSWODTemplate') IS NOT NULL 
				DROP TABLE ##tmpSWODTemplate;
		</cfquery>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="importSWODPrograms" access="package" output="false" returntype="struct">
		<cfargument name="event" type="any" required="yes">

		<cfscript>
		var local = structNew();
		local.objImport = CreateObject("component","model.admin.common.modules.import.import");

		local.ovAction = arguments.event.getValue('frmOvAction','s');
		</cfscript>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.errorCode = 999>
		<cfset local.returnStruct.errorInfo = StructNew()>

		<!--- upload file --->
		<cfset local.uploadResult = local.objImport.uploadFile(sitecode=arguments.event.getValue('mc_siteinfo.sitecode'), formFieldName='importFileName')>
		<cfif local.uploadResult.isErr>
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errorCode = 1>
			<cfset structInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.uploadResult.errMsg)>
			<cfreturn local.returnStruct>
		</cfif>

		<cfif local.uploadResult.ext eq "xls">
			<cfset local.parseResult = local.objImport.parseXLS(strFilePath=local.uploadResult.uploadedFile)>
			<cfset local.returnStruct.success = local.parseResult.success>
			<cfset local.csvFile = replaceNoCase(local.uploadResult.uploadedFile, ".xls", ".csv")>
			<cfif NOT local.returnStruct.success>
				<cfset local.returnStruct.errorCode = local.parseResult.errorCode>
				<cfset local.returnStruct.errorInfo = local.parseResult.errorInfo>
				<cfreturn local.returnStruct>
			</cfif>

			<!--- if only one sheet --->
			<cfif arrayLen(local.parseResult.arrSheets) is 1>
				<cfset local.lstDateColumns = 'OrigPublished,StartSale,EndSale'>
				<cfset local.parseResult = local.objImport.parseXLSSheet(strFilePath=local.uploadResult.uploadedFile,strFilePathCSV=local.csvFile,sheetIndex=0,lstDateColumns=local.lstDateColumns)>
				<cfset local.returnStruct.success = local.parseResult.success>
				<cfif NOT local.returnStruct.success>
					<cfset local.returnStruct.errorCode = local.parseResult.errorCode>
					<cfset local.returnStruct.errorInfo = local.parseResult.errorInfo>
					<cfreturn local.returnStruct>
				</cfif>
			<cfelse>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errorCode = 7>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,'The uploaded Excel file contains #arrayLen(local.parseResult.arrSheets)# sheets. Edit the file to contain only one sheet and try again.')>
				<cfreturn local.returnStruct>
			</cfif>
			<!--- Files was successfully read in --->
			<cfset local.uploadResult.uploadedFile = local.csvFile>
		</cfif>

		<!--- parse CSVs --->
		<cfset local.parseResult = local.objImport.parseCSV(stFilePath=local.uploadResult.uploadedFile, stFilePathTmp="#local.uploadResult.uploadedFilePath#/#local.uploadResult.uploadFilenameWithoutExt#Parsed.csv")>

		<cfif local.parseResult.isErr>
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errorCode = 2>
			<cfset structInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.parseResult.errMsg)>
			<cfreturn local.returnStruct>
		<cfelse>
			<cfset local.uploadResult.columnNames = ToString(local.parseResult.strTableColumnNames)>
		</cfif>

		<!--- import files --->
		<cfset local.importToSQLResult = importSWODToSQL(strImportFile=local.uploadResult, columnNames=local.uploadResult.columnNames, 
			siteID=arguments.event.getValue('mc_siteinfo.siteID'), ovAction=local.ovAction)>
 		<cfif local.importToSQLResult.isErr>
			<cfset local.returnStruct.success = false>
			<cfif isDefined("local.importToSQLResult.importResultXML")>
				<cfset local.returnStruct.errorCode = 5>
				<cfset structInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.importToSQLResult.importResultXML)>
			<cfelse>
				<cfset local.returnStruct.errorCode = 4>
				<cfset structInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.importToSQLResult.errMsg)>
			</cfif>
			<cfreturn local.returnStruct>
		</cfif>		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="importSWODToSQL" access="private" output="false" returntype="struct">
		<cfargument name="strImportFile" type="struct" required="yes">
		<cfargument name="columnNames" type="string" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="ovAction" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.rs = { isErr=0, errMsg='', errCount=0 } >

		<cftry>
	        <cfquery name="local.qryImport" datasource="#application.dsn.membercentral.dsn#" result="local.qryImportResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
				
					declare @siteID int, @importResult xml, @recordedByMemberID int, @ovAction char(1);
					set @recordedByMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcUser.memberData.memberID#">;
					set @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;
					set @ovAction = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.ovAction#">;

					-- bulk insert from file
					BEGIN TRY
						IF OBJECT_ID('tempdb..##mc_SWODImport') IS NOT NULL 
							DROP TABLE ##mc_SWODImport;
						CREATE TABLE ##mc_SWODImport (
							<cfloop list="#arguments.columnnames#" index="local.thisCol" delimiters="#chr(7)#">
								[#local.thisCol#] varchar(max)<cfif local.thisCol neq listLast(arguments.columnnames,chr(7))>, </cfif>
							</cfloop>
						);
						BULK INSERT ##mc_SWODImport 
							FROM '#arguments.strImportFile.uploadedFilePathUNC#\#arguments.strImportFile.uploadFilenameWithoutExt#Parsed.csv' 
							WITH (FIELDTERMINATOR='#chr(7)#', FIRSTROW=2);
					END TRY
					BEGIN CATCH
						select @importResult = '<import><errors><error msg="Unable to import the file for processing." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH

					-- import file
					BEGIN TRY
						set @importResult = null;
						EXEC seminarWeb.dbo.swod_importSeminars @siteID=@siteID, @recordedByMemberID=@recordedByMemberID, @ovAction=@ovAction, @importResult=@importResult OUTPUT;
					END TRY
					BEGIN CATCH
						select @importResult = '<import><errors><error msg="Unable to process the import file." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH
				
					on_done:
					declare @errCount int;
					select @errCount = @importResult.value('count(/import/errors/error)','int');
					SELECT @importResult as importResult, @errCount as errCount;

					IF OBJECT_ID('tempdb..##mc_SWODImport') IS NOT NULL 
						DROP TABLE ##mc_SWODImport;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

            <cfset local.rs.importResultXML = xmlparse(local.qryImport.importResult)>
            <cfset local.rs.errCount = local.qryImport.errCount>
            <cfif local.rs.errCount gt 0>
            	<cfset local.rs.isErr = 1>
            </cfif>

			<cfcatch type="Any">
				<cfset local.rs.isErr = 1>
				<cfset local.rs.errMsg = "There was a problem importing the files. Try the upload again or contact us for assistance.">
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			</cfcatch>
		</cftry>
		<cfreturn local.rs>
	</cffunction>

	<cffunction name="showImportResults" access="package" output="false" returntype="string">
		<cfargument name="strResult" type="struct" required="yes">
		<cfargument name="doAgainURL" type="string" required="yes">

		<cfscript>
			var local = structNew();
			local.data = '';
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<h4>Import Results</h4>

			<cfif NOT arguments.strResult.success>
				<div class="alert alert-danger">
					<div class="font-weight-bold mb-3">The import was stopped and requires your attention.</div>
					<cfif arguments.strResult.errorCode is 5>
						<cfset local.arrErrors = XMLSearch(arguments.strResult.errorInfo[arguments.strResult.errorCode],"/import/errors/error")>
						<div>
						<cfif arrayLen(local.arrErrors) gt 200>
							<b>Only the first 200 errors are shown.</b><br/><br/>
						</cfif>
						<cfset local.thisErrNum = 0>
						<cfloop array="#local.arrErrors#" index="local.thisErr">
							<cfset local.thisErrNum = local.thisErrNum + 1>
							#local.thisErr.xmlAttributes.msg#<br/>
							<cfif local.thisErrNum is 200>
								<cfbreak>
							</cfif>
						</cfloop>
						</div>
					<cfelse>
						<div>#arguments.strResult.errorInfo[arguments.strResult.errorCode]#</div>
					</cfif>
					<button name="btnDoOver" class="btn btn-sm btn-secondary mt-3" type="button" onClick="self.location.href='#arguments.doAgainURL#&tab=import';">Try upload again</button>
				</div>
			<cfelse>
				<p class="font-weight-bold">Import Has Been Scheduled</p>
				<div>
					The import of the uploaded file has been scheduled and will begin shortly.<br/>
					You will be sent an e-mail with the results of the import.<br/><br/>
					Please wait until you receive the emailed report before contacting Support with any questions.
				</div>
				<button class="btn btn-sm btn-secondary mt-3" name="btnDoOver" type="button" onClick="self.location.href='#arguments.doAgainURL#&tab=import';">Upload another file</button>
			</cfif>
			</cfoutput>
		</cfsavecontent>
			
		<cfreturn local.data>
	</cffunction>
</cfcomponent>