CREATE VIEW [dbo].[BlitzWhoLog_Deltas] AS 
WITH MaxQueryDuration AS 
( 
    SELECT 
        MIN([ID]) AS [MinID], 
		MAX([ID]) AS [MaxID] 
    FROM [dbo].[BlitzWhoLog]
    GROUP BY [ServerName], 
    [session_id], 
    [database_name], 
    [request_time], 
    [start_time], 
    [sql_handle] 
) 
SELECT 
    [ID], 
    [ServerName], 
    [CheckDate], 
    [elapsed_time], 
    [session_id], 
    [database_name], 
    [query_text_snippet], 
    [query_plan], 
    [live_query_plan], 
    [query_cost], 
    [status], 
    [wait_info], 
    [wait_resource], 
    [top_session_waits], 
    [blocking_session_id], 
    [open_transaction_count], 
    [is_implicit_transaction], 
    [nt_domain], 
    [host_name], 
    [login_name], 
    [nt_user_name], 
    [program_name], 
    [fix_parameter_sniffing], 
    [client_interface_name], 
    [login_time], 
    [start_time], 
    [request_time], 
    [request_cpu_time], 
    [degree_of_parallelism], 
    [request_logical_reads], 
    [Logical_Reads_MB], 
    [request_writes], 
    [Logical_Writes_MB], 
    [request_physical_reads], 
    [Physical_reads_MB], 
    [session_cpu], 
    [session_logical_reads], 
    [session_logical_reads_MB], 
    [session_physical_reads], 
    [session_physical_reads_MB], 
    [session_writes], 
    [session_writes_MB], 
    [tempdb_allocations_mb], 
    [memory_usage], 
    [estimated_completion_time], 
    [percent_complete], 
    [deadlock_priority], 
    [transaction_isolation_level], 
    [last_dop], 
    [min_dop], 
    [max_dop], 
    [last_grant_kb], 
    [min_grant_kb], 
    [max_grant_kb], 
    [last_used_grant_kb], 
    [min_used_grant_kb], 
    [max_used_grant_kb], 
    [last_ideal_grant_kb], 
    [min_ideal_grant_kb], 
    [max_ideal_grant_kb], 
    [last_reserved_threads], 
    [min_reserved_threads], 
    [max_reserved_threads], 
    [last_used_threads], 
    [min_used_threads], 
    [max_used_threads], 
    [grant_time], 
    [requested_memory_kb], 
    [grant_memory_kb], 
    [is_request_granted], 
    [required_memory_kb], 
    [query_memory_grant_used_memory_kb], 
    [ideal_memory_kb], 
    [is_small], 
    [timeout_sec], 
    [resource_semaphore_id], 
    [wait_order], 
    [wait_time_ms], 
    [next_candidate_for_memory_grant], 
    [target_memory_kb], 
    [max_target_memory_kb], 
    [total_memory_kb], 
    [available_memory_kb], 
    [granted_memory_kb], 
    [query_resource_semaphore_used_memory_kb], 
    [grantee_count], 
    [waiter_count], 
    [timeout_error_count], 
    [forced_grant_count], 
    [workload_group_name], 
    [resource_pool_name], 
    [context_info], 
    [query_hash], 
    [query_plan_hash], 
    [sql_handle], 
    [plan_handle], 
    [statement_start_offset], 
    [statement_end_offset] 
    FROM 
        ( 
            SELECT 
                   [ID], 
			       [ServerName], 
			       [CheckDate], 
			       [elapsed_time], 
			       [session_id], 
			       [database_name], 
			       /* Truncate the query text to aid performance of painting the rows in SSMS */ 
			       CAST([query_text] AS NVARCHAR(1000)) AS [query_text_snippet], 
			       [query_plan], 
			       [live_query_plan], 
			       [query_cost], 
			       [status], 
			       [wait_info], 
			       [wait_resource], 
			       [top_session_waits], 
			       [blocking_session_id], 
			       [open_transaction_count], 
			       [is_implicit_transaction], 
			       [nt_domain], 
			       [host_name], 
			       [login_name], 
			       [nt_user_name], 
			       [program_name], 
			       [fix_parameter_sniffing], 
			       [client_interface_name], 
			       [login_time], 
			       [start_time], 
			       [request_time], 
			       [request_cpu_time], 
			       [degree_of_parallelism], 
			       [request_logical_reads], 
			       ((CAST([request_logical_reads] AS DECIMAL(38,2))* 8)/ 1024) [Logical_Reads_MB], 
			       [request_writes], 
			       ((CAST([request_writes] AS DECIMAL(38,2))* 8)/ 1024) [Logical_Writes_MB], 
			       [request_physical_reads], 
			       ((CAST([request_physical_reads] AS DECIMAL(38,2))* 8)/ 1024) [Physical_reads_MB], 
			       [session_cpu], 
			       [session_logical_reads], 
			       ((CAST([session_logical_reads] AS DECIMAL(38,2))* 8)/ 1024) [session_logical_reads_MB], 
			       [session_physical_reads], 
			       ((CAST([session_physical_reads] AS DECIMAL(38,2))* 8)/ 1024) [session_physical_reads_MB], 
			       [session_writes], 
			       ((CAST([session_writes] AS DECIMAL(38,2))* 8)/ 1024) [session_writes_MB], 
			       [tempdb_allocations_mb], 
			       [memory_usage], 
			       [estimated_completion_time], 
			       [percent_complete], 
			       [deadlock_priority], 
			       [transaction_isolation_level], 
			       [last_dop], 
			       [min_dop], 
			       [max_dop], 
			       [last_grant_kb], 
			       [min_grant_kb], 
			       [max_grant_kb], 
			       [last_used_grant_kb], 
			       [min_used_grant_kb], 
			       [max_used_grant_kb], 
			       [last_ideal_grant_kb], 
			       [min_ideal_grant_kb], 
			       [max_ideal_grant_kb], 
			       [last_reserved_threads], 
			       [min_reserved_threads], 
			       [max_reserved_threads], 
			       [last_used_threads], 
			       [min_used_threads], 
			       [max_used_threads], 
			       [grant_time], 
			       [requested_memory_kb], 
			       [grant_memory_kb], 
			       [is_request_granted], 
			       [required_memory_kb], 
			       [query_memory_grant_used_memory_kb], 
			       [ideal_memory_kb], 
			       [is_small], 
			       [timeout_sec], 
			       [resource_semaphore_id], 
			       [wait_order], 
			       [wait_time_ms], 
			       [next_candidate_for_memory_grant], 
			       [target_memory_kb], 
			       [max_target_memory_kb], 
			       [total_memory_kb], 
			       [available_memory_kb], 
			       [granted_memory_kb], 
			       [query_resource_semaphore_used_memory_kb], 
			       [grantee_count], 
			       [waiter_count], 
			       [timeout_error_count], 
			       [forced_grant_count], 
			       [workload_group_name], 
			       [resource_pool_name], 
			       [context_info], 
			       [query_hash], 
			       [query_plan_hash], 
			       [sql_handle], 
			       [plan_handle], 
			       [statement_start_offset], 
			       [statement_end_offset] 
            FROM [dbo].[BlitzWhoLog]
        ) AS [BlitzWho] 
INNER JOIN [MaxQueryDuration] ON [BlitzWho].[ID] = [MaxQueryDuration].[MaxID];
GO
