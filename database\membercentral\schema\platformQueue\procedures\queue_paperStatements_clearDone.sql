ALTER PROC dbo.queue_paperStatements_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusDone int;
	EXEC dbo.queue_getStatusIDbyType @queueType='PaperStatements', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;

	IF OBJECT_ID('tempdb..#tmpItemIDs') IS NOT NULL 
		DROP TABLE #tmpItemIDs;
	CREATE TABLE #tmpItemIDs (itemID int PRIMARY KEY);

	INSERT INTO #tmpItemIDs (itemID)
	select distinct qid.itemID
	from dbo.queue_paperStatementsDetail qi
	inner join dbo.queue_paperStatements as qid ON qid.itemID = qi.itemID
	where qi.queueStatusID = @statusDone
		except
	select distinct qid.itemID
	from dbo.queue_paperStatementsDetail as qi
	inner join dbo.queue_paperStatements as qid ON qid.itemID = qi.itemID
	where qi.queueStatusID <> @statusDone;
		
	IF @@ROWCOUNT = 0
		GOTO on_done;

	BEGIN TRAN;
		DELETE from dbo.queue_paperStatementsDetail
		where itemID in (
			select qi.itemID
			FROM dbo.queue_paperStatementsDetail as qi
			inner join dbo.queue_paperStatements as qid on qid.itemID = qi.itemID
			INNER JOIN #tmpItemIDs as tmp on tmp.itemID = qid.itemID
			WHERE qi.queueStatusID = @statusDone
		);

		DELETE from dbo.queue_paperStatements
		where itemID not in (select itemID from dbo.queue_paperStatementsDetail);
	COMMIT TRAN;

	on_done:
	IF OBJECT_ID('tempdb..#tmpItemIDs') IS NOT NULL 
		DROP TABLE #tmpItemIDs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO