<cfcomponent output="false">

	<cfset variables.apiURL = "https://api.zebra.com">
	<cfset variables.previewURL = "http://api.labelary.com">
	<cfset variables.apiKey = "wU6T6ACWwGGk8xWvSWhBqhdotRsHHJRw">
	<cfset variables.apiHost = "api.zebra.com">
	<cfset variables.PREVIEW_WIDTH = 576>
	
	<cffunction name="PrintPreview" access="public" output="no" returntype="struct">
		<cfargument name="width" type="string" required="true">
		<cfargument name="height" type="string" required="true">
		<cfargument name="ZPL" type="string" required="true">

		<cfset var local = structnew()>

		<cftry>
			<cfset local.apiResponse = callAPI(mode="preview", apiQS="/v1/printers/12dpmm/labels/#arguments.width#x#arguments.height#/0", payload=arguments.ZPL)>
			<cfif local.apiResponse.statusCode eq 200>
				<cfset local.apiResponse.success = true>
			<cfelse>
				<cfset local.apiResponse.success = false>
				<cfset local.apiResponse.errorMessage = "Print Preview call failed">
				<cfif isStruct(local.apiResponse.arrResult)>
					<cfset local.apiResponse.errorMessage = local.apiResponse.arrResult.DeveloperMessage>
				</cfif>
				<cfset local.apiResponse.device = structnew()>
				<cfset structDelete(local.apiResponse, "ARRRESULT")>
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.apiResponse>
	</cffunction>	

	<cffunction name="PrintBadge" access="public" output="no" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="deviceID" type="numeric" required="true">
		<cfargument name="serialNumber" type="string" required="true">
		<cfargument name="ZPL" type="string" required="true">

		<cfset var local = structnew()>
		<cfset local.success = false>
		
		<cftry>
			<cfset local.apiResponse = callAPI(mode="print", apiQS="/v2/devices/printers/send", payload=arguments.ZPL, serialNumber=arguments.serialNumber)>			
			<cfif local.apiResponse.statusCode eq 200 and isStruct(local.apiResponse.arrResult)>
				<cfset local.success = true>
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>	

	<cffunction name="callAPI" access="private" output="no" returntype="struct">
		<cfargument name="mode" type="string" required="true">
		<cfargument name="apiQS" type="string" required="true">
		<cfargument name="payload" type="string" required="false" default="">
		<cfargument name="serialNumber" type="string" required="false" default="">
		
		<cfset var local = structnew()>
		<cfset local.returnStruct = structnew()>

		<cftry>
			<cfif arguments.mode eq "print">
				<cfset local.requestURL = "#variables.apiURL##arguments.apiQS#">

				<cfset local.zplInTemp = "#createUUID()##createUUID()#.txt">
				<cfset local.returnStruct.zplInPath = "#application.paths.RAIDTemp.path##local.zplInTemp#">
				<cffile action="write" charset="us-ascii" file="#local.returnStruct.zplInPath#" output="#arguments.payload#">

				<cfhttp url="#local.requestURL#" method="post" userAgent="Membercentral.com" throwonerror="yes" result="local.APIResult" multipart="yes" multipartType="form-data">
					<cfhttpparam type="HEADER" name="Content-Type" value="text/plain">
					<cfhttpparam type="HEADER" name="apikey" value="#variables.apiKey#">
					<cfhttpparam type="HEADER" name="host" value="#variables.apiHost#">
					<cfhttpparam type="formField" name="sn" value="#arguments.serialNumber#">					
					<cfhttpparam type="file" name="zpl_file" file="#local.returnStruct.zplInPath#" mimetype="text/plain">
				</cfhttp>

				<cfset local.returnStruct.statusCode = trim(local.APIResult.responseheader.status_code)>
				<cfset local.apiFileContent = "">
				<cfset local.returnStruct.arrResult = arrayNew(1)>

			<cfelseif arguments.mode eq "preview">
				<cfset local.requestURL = "#variables.previewURL##arguments.apiQS#">
				<cfhttp url="#local.requestURL#" method="post" userAgent="Membercentral.com" throwonerror="yes" result="local.APIResult">
					<cfhttpparam type="HEADER" name="Accept" value="image/png">
					<cfhttpparam type="HEADER" name="Content-Type" value="application/x-www-form-urlencoded">
					<cfhttpparam type="BODY" value="#arguments.payload#" mimetype="text/plain">
				</cfhttp>

				<cfset local.returnStruct.statusCode = trim(local.APIResult.responseheader.status_code)>
				<cfif local.APIResult.status_code EQ 200>
					<cfset local.apiFileContent = ImageNew(local.APIResult.filecontent)>

					<cfset ImageResize(local.apiFileContent, variables.PREVIEW_WIDTH)>
					<cfset local.returnStruct.imagePreview = local.apiFileContent>
					<cfset local.imgInTemp = "#createUUID()##createUUID()#.png">
					<cfset local.returnStruct.imagePath = "#application.paths.RAIDTemp.path##local.imgInTemp#">
					<cfset ImageWrite(local.apiFileContent,local.returnStruct.imagePath)>

				<cfelse>
					<cfset local.apiFileContent = deserializeJSON(local.APIResult.filecontent)>
					<cfset local.returnStruct.arrResult = local.apiFileContent>
				</cfif>
			</cfif>

			<cfset local.strLog = { 
				request = { 
					method="POST", 
					endpoint=local.requestURL,
					bodycontent=arguments.payload
				}, response = {
					bodycontent=local.apiFileContent,
					headers=local.APIResult.responseheader,
					statuscode=local.APIResult.status_code
				}}>
			<cfset logAPICall(strCall=local.strLog)>

			<cfif local.returnStruct.statusCode eq 200>
				<cfset local.returnStruct.success = true>
			<cfelse>
				<cfset local.returnStruct.success = false>
			</cfif>			

		<cfcatch type="any">
			<cfset local.args = arguments>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.statusCode = local.APIResult.status_code>
			<cfif len(local.APIResult.filecontent)>
				<cfset local.returnStruct.arrResult = deserializeJSON(local.APIResult.filecontent)>			
			<cfelse>
				<cfset local.returnStruct.arrResult = arrayNew(1)>
			</cfif>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="logAPICall" output="false" access="private" returntype="void">
		<cfargument name="strCall" type="struct" required="true">

		<cfset var local = structnew()>

		<cftry>
			<cfset local.strRequest = {
				"c":"Zebra",
				"d": {
					"request": {
						"method":arguments.strCall.request.method,
						"endpoint":arguments.strCall.request.endpoint,
						"bodycontent":arguments.strCall.request.bodycontent
					},
					"response": {
						"bodycontent":arguments.strCall.response.bodycontent,
						"headers":arguments.strCall.response.headers,
						"statuscode":arguments.strCall.response.statuscode
					},
					"timestamp":now()
				}
			}>
	
			<cfquery name="local.qryInsertMongoQueue" datasource="#application.dsn.membercentral.dsn#">
				INSERT INTO platformQueue.dbo.queue_mongo (msgjson) 
				VALUES (<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#serializeJSON(local.strRequest)#">)
			</cfquery>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
		</cfcatch>
		</cftry>
	</cffunction>

</cfcomponent>
