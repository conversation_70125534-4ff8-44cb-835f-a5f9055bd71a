CREATE PROC dbo.et_copyEmailTemplate
@templateID varchar(10),
@createdByMemberID int,
@siteID int,
@newTemplateID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @templateTypeID int, @appCreatedContentResourceTypeID int, @parentSiteResourceID int, 
		@contentID int, @contentSiteResourceID int, @templateName varchar(200), @templateDescription varchar(max),
		@categoryID int, @rawContent varchar(max), @subjectLine varchar(200), @emailFromName varchar(200), 
		@emailFrom varchar(200);	
 
	set @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');
	select @parentSiteResourceID = dbo.fn_getSiteResourceIDForResourceType('EmailTemplateAdmin', @siteID);

	select @templateName = 'Copy of '+et.templateName, @templateTypeID = et.templateTypeID,
		@templateDescription = et.templateDescription, @categoryID = et.categoryID, 
		@subjectLine = et.subjectLine, @emailFrom = et.emailFrom, @emailFromName = et.emailFromName, 
		@rawContent = content.rawContent
	from dbo.et_emailTemplates et
	CROSS APPLY dbo.fn_getContent(et.contentID,1) as content
	WHERE et.templateID = @templateID;
 
	BEGIN TRAN;
		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID,
			@parentSiteResourceID=@parentSiteResourceID, @siteResourceStatusID=1, @isHTML=1,
			@languageID=1, @isActive=1, @contentTitle=@templateName, @contentDesc='', @rawContent=@rawContent,
			@memberID=@createdByMemberID, @contentID=@contentID OUTPUT, @siteResourceID=@contentSiteResourceID OUTPUT;

		INSERT INTO dbo.et_emailTemplates (templateTypeID, templateName, categoryID, templateDescription, contentID, subjectLine, 
			emailFrom, emailFromName, [status], createdByMemberID)
		VALUES (@templateTypeID, @templateName, @categoryID, @templateDescription, @contentID, @subjectLine, 
			@emailFrom, @emailFromName, 'A', @createdByMemberID);

		SELECT @newTemplateID = SCOPE_IDENTITY();
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
