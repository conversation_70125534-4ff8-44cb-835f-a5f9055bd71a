----------------------------------------------------------------------------------------------------
/* Query #50: PASSED */
ALTER PROCEDURE [dbo].[sw_LogFileStats]
(
	@LogFileStatsDelay VARCHAR(128) = NULL
)
AS
BEGIN
    IF @LogFileStatsDelay IS NULL 
    BEGIN
        SET @LogFileStatsDelay = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'LogFileStatsDelay')
    END

    IF OBJECT_ID('tempdb..#LogFileStats1') IS NOT NULL
    BEGIN
        DROP TABLE #LogFileStats1
    END

    IF OBJECT_ID('tempdb..#LogFileStats2') IS NOT NULL
    BEGIN
        DROP TABLE #LogFileStats2
    END

    CREATE TABLE #LogFileStats1 
    (
        [DatabaseID] smallint NOT NULL,
        [FileID] smallint NOT NULL,
        [Counter_ms] bigint NOT NULL,
        [Reads] bigint NOT NULL,
        [BytesRead] bigint NOT NULL,
        [ReadWait] bigint NOT NULL,
        [Writes] bigint NOT NULL,
        [BytesWritten] bigint NOT NULL,
        [WriteWait] bigint NOT NULL,
        [Size] bigint NOT NULL
    )

    CREATE TABLE #LogFileStats2
    (
        [DatabaseID] smallint NOT NULL,
        [FileID] smallint NOT NULL,
        [Counter_ms] bigint NOT NULL,
        [Reads] bigint NOT NULL,
        [BytesRead] bigint NOT NULL,
        [ReadWait] bigint NOT NULL,
        [Writes] bigint NOT NULL,
        [BytesWritten] bigint NOT NULL,
        [WriteWait] bigint NOT NULL,
    )

    INSERT INTO #LogFileStats1
    SELECT [database_id], [file_id], [sample_ms], [num_of_reads], [num_of_bytes_read], [io_stall_read_ms], [num_of_writes], [num_of_bytes_written], [io_stall_write_ms], [size_on_disk_bytes] FROM sys.dm_io_virtual_file_stats(NULL, NULL)

    WAITFOR DELAY @LogFileStatsDelay

    INSERT INTO #LogFileStats2
    SELECT [database_id], [file_id], [sample_ms], [num_of_reads], [num_of_bytes_read], [io_stall_read_ms], [num_of_writes], [num_of_bytes_written], [io_stall_write_ms] FROM sys.dm_io_virtual_file_stats(NULL, NULL)

    INSERT INTO [SQLWatchmen].[dbo].[FileStatsLog] ([DateTime], [Database], [File], [Duration_ms], [Reads], [BytesRead], [ReadWait_ms], [ReadLatency_ms], [Writes], [BytesWritten], [WriteWait_ms], [WriteLatency_ms], [Size_bytes])
    SELECT
    SYSDATETIME(),
    DB_NAME(one.[DatabaseID]),
    mf.[physical_name],
    two.[Counter_ms] - one.[Counter_ms],
    two.[Reads] - one.[Reads],
    two.[BytesRead] - one.[BytesRead],
    two.[ReadWait] - one.[ReadWait],
    CASE WHEN ((two.[Reads] - one.[Reads]) = 0) 
        THEN NULL ELSE (two.[ReadWait] - one.[ReadWait]) / (two.[Reads] - one.[Reads]) END,
    two.[Writes] - one.[Writes],
    two.[BytesWritten] - one.[BytesWritten],
    two.[WriteWait] - one.[WriteWait],
    CASE WHEN ((two.[Writes] - one.[Writes]) = 0)
        THEN NULL ELSE (two.[WriteWait] - one.[WriteWait]) / (two.[Writes] - one.[Writes]) END,
    one.Size
    FROM #LogFileStats1 AS one
    JOIN #LogFileStats2 AS two ON one.[DatabaseID] = two.[DatabaseID] AND one.[FileID] = two.[FileID]
    JOIN sys.master_files AS mf ON mf.[database_id] = one.[DatabaseID] AND mf.[file_id] = one.[FileID]

    IF OBJECT_ID('tempdb..#LogFileStats1') IS NOT NULL
    BEGIN
        DROP TABLE #LogFileStats1
    END

    IF OBJECT_ID('tempdb..#LogFileStats2') IS NOT NULL
    BEGIN
        DROP TABLE #LogFileStats2
    END

END
GO
