ALTER PROC dbo.email_sendMessageSupport
@errorSubject VARCHAR(400),
@errorTitle VARCHAR(400),
@messageContent VARCHAR(MAX),
@forDev bit


AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @siteID INT, @orgSysMemberID INT, @messageTypeID INT, @sendingSiteResourceID INT, @tier VARCHAR(12),
		@messageID INT, @recipientIDList VARCHAR(max);

	SET @siteID = membercentral.dbo.fn_getSiteIDFromSiteCode('MC');
	SET @orgSysMemberID = membercentral.dbo.fn_ams_getOrgSystemMemberID(1);
	SELECT @messageTypeID = messageTypeID FROM dbo.email_messageTypes WHERE messageTypeCode = 'SUPPORTISSUE';
	SELECT @sendingSiteResourceID = siteResourceID from membercentral.dbo.sites where siteID = @siteID;
	SELECT @tier = tier FROM membercentral.dbo.fn_getServerSettings();
	
	SET @errorSubject = quotename(@tier) + ' ' + @errorSubject;
	IF @forDev = 1
		SET @errorSubject = @errorSubject + '  @dev@';

	EXEC dbo.email_SendMessage @fromName='MemberCentral', @fromEmail='<EMAIL>', 
		@toEmailList='<EMAIL>', @replyToEmail='', @subject=@errorSubject, 
		@title=@errorTitle, @messageContent=@messageContent, @attachmentsList=NULL, @siteID=@siteID, 
		@memberID=@orgSysMemberID, @messageTypeID=@messageTypeID, @sendingSiteResourceID=@sendingSiteResourceID, 
		@referenceType=NULL, @referenceID=NULL, @doWrapEmail=1, @environmentName=@tier, 
		@messageID=@messageID OUTPUT, @recipientIDList=@recipientIDList OUTPUT;		

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO