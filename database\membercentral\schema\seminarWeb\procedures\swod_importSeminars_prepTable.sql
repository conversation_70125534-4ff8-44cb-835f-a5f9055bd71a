ALTER PROC dbo.swod_importSeminars_prepTable
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET @importResult = NULL;

	-- *********************************
	-- ensure all required columns exist 
	-- *********************************
	BEGIN TRY
		-- this will get the columns that are required
		IF OBJECT_ID('tempdb..#tblSWODReqCols') IS NOT NULL
			DROP TABLE #tblSWODReqCols;
		CREATE TABLE #tblSWODReqCols (COLUMN_NAME sysname);

		INSERT INTO #tblSWODReqCols
		SELECT 'rowID' UNION ALL
		SELECT 'SeminarTitle' UNION ALL
		SELECT 'Description' UNION ALL
		SELECT 'OrigPublished' UNION ALL
		SELECT 'PlayerMode' UNION ALL
		SELECT 'PlayerQATab' UNION ALL
		SELECT 'BlankPlayer' UNION ALL
		SELECT 'CompleteTime' UNION ALL
		SELECT 'Certificate' UNION ALL
		SELECT 'SellInCatalog' UNION ALL
		SELECT 'StartSale' UNION ALL
		SELECT 'EndSale' UNION ALL
		SELECT 'Credit' UNION ALL
		SELECT 'AutoCreateTitle';

		-- this will get the columns that are actually in the import
		IF OBJECT_ID('tempdb..#tblSWODImportCols') IS NOT NULL 
			DROP TABLE #tblSWODImportCols;
		CREATE TABLE #tblSWODImportCols (ORDINAL_POSITION int, COLUMN_NAME sysname);

		INSERT INTO #tblSWODImportCols
		SELECT column_id, [name] 
		FROM tempdb.sys.columns 
		WHERE object_id = OBJECT_ID('tempdb..#mc_SWODImport');

		INSERT INTO #tblSWODErrors (msg)
		SELECT 'The required column ' + org.column_name + ' is missing from your data.'
		FROM #tblSWODReqCols as org
		LEFT OUTER JOIN #tblSWODImportCols as imp on imp.column_name = org.column_name
		WHERE imp.ORDINAL_POSITION IS NULL;
		
		IF @@ROWCOUNT > 0 GOTO on_done;

		DELETE FROM #tblSWODImportCols
		WHERE column_name in (SELECT COLUMN_NAME FROM #tblSWODReqCols);

		IF OBJECT_ID('tempdb..#tblSWODReqCols') IS NOT NULL 
			DROP TABLE #tblSWODReqCols;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSWODErrors (msg)
		VALUES ('Unable to validate file contains all required columns.');

		GOTO on_done;
	END CATCH

	-- **********
	-- prep table
	-- **********
	BEGIN TRY
		-- add holding columns
		ALTER TABLE #mc_SWODImport ADD MCSeminarID int NULL, MCLayoutID int NULL, itemUID uniqueidentifier NOT NULL DEFAULT(NEWID()), MCCategoryIDList varchar(max), MCFormIDList varchar(max);

		-- ensure rowID is an int
		ALTER TABLE #mc_SWODImport ALTER COLUMN RowID int not null;

		-- add missing columns
		IF NOT EXISTS (SELECT ORDINAL_POSITION FROM #tblSWODImportCols WHERE column_name = 'ProgramCode') BEGIN
			ALTER TABLE #mc_SWODImport ADD ProgramCode varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('ProgramCode');
		END ELSE
			DELETE FROM #tblSWODImportCols WHERE column_name = 'ProgramCode';

		IF NOT EXISTS (SELECT ORDINAL_POSITION FROM #tblSWODImportCols WHERE column_name = 'Featured') BEGIN
			ALTER TABLE #mc_SWODImport ADD Featured bit NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Featured');
		END ELSE
			DELETE FROM #tblSWODImportCols WHERE column_name = 'Featured';

		IF NOT EXISTS (SELECT ORDINAL_POSITION FROM #tblSWODImportCols WHERE column_name = 'SeminarSubtitle') BEGIN
			ALTER TABLE #mc_SWODImport ADD SeminarSubtitle varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('SeminarSubtitle');
		END ELSE
			DELETE FROM #tblSWODImportCols WHERE column_name = 'SeminarSubtitle';

		IF NOT EXISTS (SELECT ORDINAL_POSITION FROM #tblSWODImportCols WHERE column_name = 'Objective1') BEGIN
			ALTER TABLE #mc_SWODImport ADD Objective1 varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Objective1');
		END ELSE
			DELETE FROM #tblSWODImportCols WHERE column_name = 'Objective1';

		IF NOT EXISTS (SELECT ORDINAL_POSITION FROM #tblSWODImportCols WHERE column_name = 'Objective2') BEGIN
			ALTER TABLE #mc_SWODImport ADD Objective2 varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Objective2');
		END ELSE
			DELETE FROM #tblSWODImportCols WHERE column_name = 'Objective2';

		IF NOT EXISTS (SELECT ORDINAL_POSITION FROM #tblSWODImportCols WHERE column_name = 'Objective3') BEGIN
			ALTER TABLE #mc_SWODImport ADD Objective3 varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Objective3');
		END ELSE
			DELETE FROM #tblSWODImportCols WHERE column_name = 'Objective3';

		IF NOT EXISTS (SELECT ORDINAL_POSITION FROM #tblSWODImportCols WHERE column_name = 'Objective4') BEGIN
			ALTER TABLE #mc_SWODImport ADD Objective4 varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Objective4');
		END ELSE
			DELETE FROM #tblSWODImportCols WHERE column_name = 'Objective4';

		IF NOT EXISTS (SELECT ORDINAL_POSITION FROM #tblSWODImportCols WHERE column_name = 'Objective5') BEGIN
			ALTER TABLE #mc_SWODImport ADD Objective5 varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Objective5');
		END ELSE
			DELETE FROM #tblSWODImportCols WHERE column_name = 'Objective5';

		IF NOT EXISTS (SELECT ORDINAL_POSITION FROM #tblSWODImportCols WHERE column_name = 'IntroductoryText') BEGIN
			ALTER TABLE #mc_SWODImport ADD IntroductoryText varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('IntroductoryText');
		END ELSE
			DELETE FROM #tblSWODImportCols WHERE column_name = 'IntroductoryText';

		IF NOT EXISTS (SELECT ORDINAL_POSITION FROM #tblSWODImportCols WHERE column_name = 'CompletionText') BEGIN
			ALTER TABLE #mc_SWODImport ADD CompletionText varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('CompletionText');
		END ELSE
			DELETE FROM #tblSWODImportCols WHERE column_name = 'CompletionText';

		IF NOT EXISTS (SELECT ORDINAL_POSITION FROM #tblSWODImportCols WHERE column_name = 'RateName1') BEGIN
			ALTER TABLE #mc_SWODImport ADD RateName1 varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('RateName1');
		END ELSE
			DELETE FROM #tblSWODImportCols WHERE column_name = 'RateName1';

		IF NOT EXISTS (SELECT ORDINAL_POSITION FROM #tblSWODImportCols WHERE column_name = 'RateAmount1') BEGIN
			ALTER TABLE #mc_SWODImport ADD RateAmount1 decimal(9,2) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('RateAmount1');
		END ELSE
			DELETE FROM #tblSWODImportCols WHERE column_name = 'RateAmount1';

		IF NOT EXISTS (SELECT ORDINAL_POSITION FROM #tblSWODImportCols WHERE column_name = 'RateName2') BEGIN
			ALTER TABLE #mc_SWODImport ADD RateName2 varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('RateName2');
		END ELSE
			DELETE FROM #tblSWODImportCols WHERE column_name = 'RateName2';

		IF NOT EXISTS (SELECT ORDINAL_POSITION FROM #tblSWODImportCols WHERE column_name = 'RateAmount2') BEGIN
			ALTER TABLE #mc_SWODImport ADD RateAmount2 decimal(9,2) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('RateAmount2');
		END ELSE
			DELETE FROM #tblSWODImportCols WHERE column_name = 'RateAmount2';

			IF NOT EXISTS (SELECT ORDINAL_POSITION FROM #tblSWODImportCols WHERE column_name = 'RateName3') BEGIN
			ALTER TABLE #mc_SWODImport ADD RateName3 varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('RateName3');
		END ELSE
			DELETE FROM #tblSWODImportCols WHERE column_name = 'RateName3';

		IF NOT EXISTS (SELECT ORDINAL_POSITION FROM #tblSWODImportCols WHERE column_name = 'RateAmount3') BEGIN
			ALTER TABLE #mc_SWODImport ADD RateAmount3 decimal(9,2) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('RateAmount3');
		END ELSE
			DELETE FROM #tblSWODImportCols WHERE column_name = 'RateAmount3';

		IF NOT EXISTS (SELECT ORDINAL_POSITION FROM #tblSWODImportCols WHERE column_name = 'RateName4') BEGIN
			ALTER TABLE #mc_SWODImport ADD RateName4 varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('RateName4');
		END ELSE
			DELETE FROM #tblSWODImportCols WHERE column_name = 'RateName4';

		IF NOT EXISTS (SELECT ORDINAL_POSITION FROM #tblSWODImportCols WHERE column_name = 'RateAmount4') BEGIN
			ALTER TABLE #mc_SWODImport ADD RateAmount4 decimal(9,2) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('RateAmount4');
		END ELSE
			DELETE FROM #tblSWODImportCols WHERE column_name = 'RateAmount4';

		IF NOT EXISTS (SELECT ORDINAL_POSITION FROM #tblSWODImportCols WHERE column_name = 'RateName5') BEGIN
			ALTER TABLE #mc_SWODImport ADD RateName5 varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('RateName5');
		END ELSE
			DELETE FROM #tblSWODImportCols WHERE column_name = 'RateName5';

		IF NOT EXISTS (SELECT ORDINAL_POSITION FROM #tblSWODImportCols WHERE column_name = 'RateAmount5') BEGIN
			ALTER TABLE #mc_SWODImport ADD RateAmount5 decimal(9,2) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('RateAmount5');
		END ELSE
			DELETE FROM #tblSWODImportCols WHERE column_name = 'RateAmount5';

		IF NOT EXISTS (SELECT ORDINAL_POSITION FROM #tblSWODImportCols WHERE column_name = 'Subjects') BEGIN
			ALTER TABLE #mc_SWODImport ADD Subjects varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Subjects');
		END ELSE
			DELETE FROM #tblSWODImportCols WHERE column_name = 'Subjects';
			
		IF NOT EXISTS (SELECT ORDINAL_POSITION FROM #tblSWODImportCols WHERE column_name = 'Evaluation') BEGIN
			ALTER TABLE #mc_SWODImport ADD Evaluation varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Evaluation');
		END ELSE
			DELETE FROM #tblSWODImportCols WHERE column_name = 'Evaluation';
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSWODErrors (msg)
		VALUES ('Unable to prepare import table by adding missing columns.');

		INSERT INTO #tblSWODErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- *************
	-- extra columns 
	-- *************
	BEGIN TRY
		-- extra columns should stop import to prevent accidental misnamed columns
		IF EXISTS (select column_name from #tblSWODImportCols) BEGIN
			INSERT INTO #tblSWODErrors (msg)
			SELECT TOP 100 PERCENT 'The imported file contains the extra column ' + cast(column_name as varchar(300)) + '. Remove this column from your file.'
			FROM #tblSWODImportCols
			ORDER BY column_name;
		END
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSWODErrors (msg)
		VALUES ('Unable to validate import file for extra columns.');

		INSERT INTO #tblSWODErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- *************************
	-- prepare table for unpivot
	-- *************************
	IF NOT EXISTS (select top 1 * from #tblSWODErrors) BEGIN
		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN ProgramCode varchar(250) NULL;
			ALTER TABLE #mc_SWODImport ALTER COLUMN SeminarTitle varchar(250) NULL;
			ALTER TABLE #mc_SWODImport ALTER COLUMN SeminarSubtitle varchar(250) NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			VALUES ('Unable to prepare data for unpivot.');

			INSERT INTO #tblSWODErrors (msg)
			VALUES (left(error_message(),300));

			GOTO on_done;
		END CATCH
	END

	-- ************************
	-- generate result xml file
	-- ************************
	on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((SELECT top 100 PERCENT membercentral.dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblSWODErrors
			order by rowID
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);

	IF OBJECT_ID('tempdb..#tblSWODReqCols') IS NOT NULL
		DROP TABLE #tblSWODReqCols;
	IF OBJECT_ID('tempdb..#tblSWODImportCols') IS NOT NULL
		DROP TABLE #tblSWODImportCols;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
