----------------------------------------------------------------------------------------------------
/* Query #31: PASSED */
ALTER PROCEDURE [dbo].[sw_AlertFailedLogins] 
(
	@FailedLoginThreshold int = NULL,
	@FailedLoginAlertInterval int = NULL,
	@MailProfileName varchar(128) = NULL,
	@AlertEmail varchar(128) = NULL,
	@Company varchar(128) = NULL,
	@Configuration varchar(128) = NULL
)
AS
BEGIN
	SET NOCOUNT ON

	IF @FailedLoginAlertInterval IS NULL
	BEGIN
		SELECT @FailedLoginAlertInterval = CAST([Value] AS int) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'FailedLoginAlertInterval'
	END

    DECLARE @DateTimeCutoff datetime = DATEADD(minute, -@FailedLoginAlertInterval, GETDATE())

	IF (SELECT TOP 1 [DateTime] FROM [SQLWatchmen].[dbo].[AlertLog] WHERE [Alert] = 'Failed Logins' AND [DateTime] > @DateTimeCutoff) IS NOT NULL
	BEGIN
		RETURN
	END

	IF @FailedLoginThreshold IS NULL
	BEGIN
		SELECT @FailedLoginThreshold = CAST([Value] AS int) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'FailedLoginThreshold'
	END

	IF OBJECT_ID('tempdb..#FailedLoginAlert') IS NOT NULL
	BEGIN
		DROP TABLE #FailedLoginAlert
	END

	CREATE TABLE #FailedLoginAlert
	(
		[LogDate] datetime,
		[ProcessInfo] nvarchar(256),
		[Text] nvarchar(MAX)
	)

	INSERT INTO #FailedLoginAlert
	EXEC sp_readerrorlog 0 , 1, 'Login failed' 

	DECLARE @FailedLoginCount int 
	SELECT @FailedLoginCount = COUNT(*) FROM #FailedLoginAlert WHERE [LogDate] > @DateTimeCutoff

	IF @FailedLoginCount > @FailedLoginThreshold
	BEGIN	
		IF @MailProfileName IS NULL
		BEGIN
			SELECT @MailProfileName = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'MailProfileName'
		END

		IF @AlertEmail IS NULL
		BEGIN
			SELECT @AlertEmail = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'AlertEmail'
		END

		IF @Company IS NULL
		BEGIN
			SELECT @Company = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Company'
		END

		IF @Configuration IS NULL
		BEGIN
			SELECT @Configuration = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Configuration'
		END

		DECLARE @Subject varchar(255)
		DECLARE @Body varchar(8000)

		SET @Subject = 'Excessive Failed Logins Detected | ' + @Configuration + ' | ' + @Company

		SET @Body = 'There have been ' + CAST(@FailedLoginCount AS varchar) + ' failed logins in the past ' + CAST(@FailedLoginAlertInterval AS varchar) + CHAR(13) + CHAR(10)
            + 'Distinct failures are listed below:' + CHAR(13) + CHAR(10)
        SELECT @Body += x.[Text] + CHAR(13) + CHAR(10) FROM (SELECT DISTINCT [Text] FROM #FailedLoginAlert) AS x

		EXEC msdb.dbo.sp_send_dbmail
			@profile_name = @MailProfileName,
			@recipients = @AlertEmail,
			@subject = @Subject,
			@body = @Body

		INSERT INTO [SQLWatchmen].[dbo].[AlertLog] ([Alert]) VALUES ('Failed Logins')

	END

	IF OBJECT_ID('tempdb..#FailedLoginAlert') IS NOT NULL
	BEGIN
		DROP TABLE #FailedLoginAlert
	END

END
GO
