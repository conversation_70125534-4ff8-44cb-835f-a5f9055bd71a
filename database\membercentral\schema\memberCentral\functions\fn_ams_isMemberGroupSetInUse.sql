CREATE FUNCTION dbo.fn_ams_isMemberGroupSetInUse (@groupSetID INT)
RETURNS bit
AS
BEGIN

	DECLARE @inUse bit = 0, @count int;

	set @count = 0;
	select @count = count(classificationid) FROM dbo.ams_memberDirectoryClassifications WHERE groupSetID = @groupSetID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END


	set @count = 0;
	select @count = count(classificationid) FROM dbo.ams_classifications WHERE groupSetID = @groupSetID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END
	

	set @count = 0;
	select @count = count(o.objectID)
	from dbo.rpt_dashboardObjectTypes as ot
	inner join dbo.rpt_dashboardObjects as o on o.objectTypeID = ot.objectTypeID
	where ot.useGroupsetFilter = 1
	and o.objectDataXML.value('(/obj/gsid)[1]','int') = @groupSetID;

	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END


	set @count = 0;
	select @count = count(sr.reportID)
	from dbo.rpt_SavedReports as sr
	CROSS APPLY sr.otherXML.nodes('/report/groupsets/groupset') as G(gs)
	INNER JOIN dbo.ams_memberGroupSets as mgs on mgs.uid = G.gs.value('@uid','uniqueidentifier')
		and mgs.groupSetID = @groupSetID;

	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END


	on_done:
	RETURN @inUse;

END
GO
