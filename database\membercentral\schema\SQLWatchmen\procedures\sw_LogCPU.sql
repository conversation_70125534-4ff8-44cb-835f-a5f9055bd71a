----------------------------------------------------------------------------------------------------
/* Query #49: PASSED */
ALTER PROCEDURE [dbo].[sw_LogCPU] 
AS
BEGIN
	SET NOCOUNT ON

	DECLARE @ts_now bigint = (SELECT cpu_ticks/(cpu_ticks/ms_ticks) FROM sys.dm_os_sys_info)
	INSERT INTO [SQLWatchmen].[dbo].[CPULog] ([DateTime], [SQL], [Other], [Idle])
	SELECT TOP(1) 
	DATEADD(ms, -1 * (@ts_now - [timestamp]), SYSDATETIME()), 
	[SQL],
	100 - [Idle] - [SQL],
	Idle
	FROM (
	SELECT 
	record.value('(./Record/SchedulerMonitorEvent/SystemHealth/SystemIdle)[1]', 'int') AS [Idle],
	record.value('(./Record/SchedulerMonitorEvent/SystemHealth/ProcessUtilization)[1]', 'int') AS [SQL], 
	[timestamp]
	FROM (
	SELECT [timestamp], CONVERT(xml, record) AS [record]
	FROM sys.dm_os_ring_buffers
	WHERE ring_buffer_type = N'RING_BUFFER_SCHEDULER_MONITOR'
	AND record LIKE N'%<SystemHealth>%') AS x ) AS y

END
GO
