ALTER PROC dbo.sms_markRecipientBatch
@batchSize int,
@restrictToMessageID int,
@workerUUID uniqueIdentifier OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @readyStatusID int, @grabbedStatusID int, @processingStatusID int,
		@cancelledStatusID int, @recipientID int, @batchSizeMultiplier int = 12, @realbatchSize int, @nowDate datetime = getdate();

	DECLARE @tblRecipients TABLE(
		recipientID int PRIMARY KEY,
		siteID int NOT NULL,
		messageID int NOT NULL,
		memberID int NOT NULL,
		toPhoneNumber varchar(50) NOT NULL,
		deliveryStatusID int NOT NULL,
		INDEX ix_messageRecipients NONCLUSTERED (messageID, recipientID)
	);

	DECLARE @tblMessages TABLE(
		messageID int NOT NULL,
		messagingServiceID int,
		siteID int NOT NULL,
		siteCode varchar(10) NOT NULL,
		messageContent varchar(max) NOT NULL);

	DECLARE @tblActiveMessagingServices TABLE (
		siteID int NOT NULL,
		messagingServiceID int NOT NULL,
		messagingServiceSID varchar(300),
		subuserID int,
		subuserSID varchar(300),
		subuserAuthToken varchar(300),
		providerID int
	);

	DECLARE @tblRecipientOptOuts TABLE(recipientID int PRIMARY KEY);

	SELECT @readyStatusID = statusID FROM dbo.sms_messageDeliveryStatuses WHERE [status] = 'readyForProcessing';
	SELECT @grabbedStatusID = statusID FROM dbo.sms_messageDeliveryStatuses WHERE [status] = 'grabbedForProcessing';
	SELECT @cancelledStatusID = statusID FROM dbo.sms_messageDeliveryStatuses WHERE [status] = 'cancelledBeforeSending';
	SET @workerUUID = newID();
	SET @realbatchSize = @batchSize * @batchSizeMultiplier;

	-- any mail to send? Skip if we're sending a specific sms
    IF @restrictToMessageID IS NULL BEGIN
        SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
        SELECT TOP 1 @recipientID = mr.recipientID 
        FROM dbo.sms_messageRecipients as mr
        INNER JOIN dbo.sms_messages as m on mr.siteID = m.siteID
			AND m.[status] = 'A'
			AND m.sendOnDate < @nowDate
        	AND m.messageID = mr.messageID
        WHERE mr.deliveryStatusID = @readyStatusID;

        SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

        IF @recipientID IS NULL 
            GOTO on_done;
    END

	-- mark recipients
	UPDATE r 
	SET r.deliveryStatusID = @grabbedStatusID,
		r.dateLastUpdated = @nowDate
		OUTPUT inserted.recipientID, inserted.siteID, inserted.messageID, inserted.memberID, inserted.toPhoneNumber, inserted.deliveryStatusID
		INTO @tblRecipients
	FROM dbo.sms_messageRecipients AS r
	INNER JOIN (
		SELECT TOP (@realbatchSize) recipientID
		FROM dbo.sms_messageRecipients AS mr
		INNER JOIN dbo.sms_messages AS m ON mr.siteID = m.siteID
			AND m.[status] = 'A'
			AND m.sendOnDate < @nowDate
			AND m.messageID = mr.messageID
		WHERE mr.deliveryStatusID = @readyStatusID
		AND mr.messageID = ISNULL(@restrictToMessageID,mr.messageID)
		ORDER BY mr.dateEntered
	) AS temp ON temp.recipientID = r.recipientID;

	-- get messages
	INSERT INTO @tblMessages (messageID, messagingServiceID, siteID, siteCode, messageContent)
	SELECT m.messageID, m.messagingServiceID, m.siteID, s.siteCode, mc.content
	FROM (SELECT DISTINCT siteID, messageID FROM @tblRecipients) AS tmpM
	INNER JOIN dbo.sms_messages AS m ON tmpM.siteID = m.siteID
		AND m.[status] = 'A'
		AND m.messageID = tmpM.messageID 
	INNER JOIN dbo.sms_messageContent AS mc ON mc.messageID = m.messageID
	INNER JOIN membercentral.dbo.sites AS s ON s.siteID = m.siteID;

	-- get activeMessageServices
	INSERT INTO @tblActiveMessagingServices (messagingServiceID, siteID, messagingServiceSID, subuserID, subuserSID, subuserAuthToken, providerID)
	SELECT DISTINCT sms.messagingServiceID, tm.siteID, sms.[sid], su.subuserID, su.username, su.[password], su.providerID
	FROM @tblMessages AS tm
	INNER JOIN dbo.sms_subuserMessagingServices AS sms ON sms.messagingServiceID = tm.messagingServiceID
		AND sms.isActive = 1
	INNER JOIN dbo.sms_subusers AS su ON su.subuserID = sms.subuserID
		AND su.isActive = 1;

	-- enforce opt-outs
	INSERT INTO @tblRecipientOptOuts (recipientID)
	SELECT DISTINCT r.recipientID
	FROM @tblRecipients AS r
	INNER JOIN @tblMessages AS m ON m.messageID = r.messageID
	INNER JOIN dbo.sms_messagingServiceOptOuts AS o ON o.siteID = m.siteID
		AND o.messagingServiceID = m.messagingServiceID
		AND o.phonenumberE164 = r.toPhoneNumber;

	IF EXISTS (SELECT 1 FROM @tblRecipientOptOuts) BEGIN
		UPDATE r
		SET deliveryStatusID = @cancelledStatusID,
			dateLastUpdated = @nowDate
		FROM dbo.sms_messageRecipients AS r
		INNER JOIN @tblRecipientOptOuts AS mo ON mo.recipientID = r.recipientID
		WHERE r.deliveryStatusID = @grabbedStatusID;
	END

	-- remove any opted out recipients from temp table so they are not returned to the worker
	IF EXISTS (SELECT 1 FROM @tblRecipientOptOuts) BEGIN
		DELETE tmpR
		FROM @tblRecipientOptOuts AS opt
		INNER JOIN @tblRecipients AS tmpR ON opt.recipientID = tmpR.recipientID
	END

	INSERT INTO dbo.sms_messageRecipientDeliveryQueue(recipientID, messageID, siteID, messagingServiceID, deliveryStatusID, dateLastUpdated, batchID, batchStartDate)
	SELECT DISTINCT r.recipientID, m.messageID, m.siteID, m.messagingServiceID, r.deliveryStatusID, @nowDate, @workerUUID, @nowDate
	FROM @tblRecipients AS r
	INNER JOIN @tblMessages AS m ON m.messageID = r.messageID;

	-- qryMessages
	SELECT messageID, messagingServiceID, siteID, siteCode, messageContent
	FROM @tblMessages
	ORDER BY messageID;
	
	-- qryRecipients
	SELECT recipientID, siteID, messageID, memberID, toPhoneNumber
	FROM @tblRecipients;

	-- qryMessagingServices
	SELECT messagingServiceID, siteID, messagingServiceSID, subuserID, subuserSID, subuserAuthToken, providerID
	FROM @tblActiveMessagingServices;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO