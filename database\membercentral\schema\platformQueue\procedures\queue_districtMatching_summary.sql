CREATE PROC dbo.queue_districtMatching_summary

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	insert into #tmpQueueDataCount (queueType, queueStatus, numItems, minDateAdded, offerDelete)
	select 'districtMatching', 'readyToProcess', count(qi.itemID), min(qi.dateAdded), 1
	from dbo.queue_districtMatching as qi
	having count(qi.itemID) > 0;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO