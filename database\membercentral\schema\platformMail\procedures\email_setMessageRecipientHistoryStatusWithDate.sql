CREATE PROC dbo.email_setMessageRecipientHistoryStatusWithDate
@siteID int,
@messageID int,
@recipientID int,
@statusID int,
@updateDate bit

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @currentDate datetime = GETDATE();

	update mrh
	set mrh.emailStatusID = @statusID,
		mrh.dateLastUpdated = @currentDate
	from dbo.email_messageRecipientHistory mrh
	inner join dbo.email_statusApprovedFlow flow on mrh.siteID = @siteID
		and flow.currentStatusID = mrh.emailStatusID
		and flow.allowedNewStatusID = @statusID
	where mrh.messageID = @messageID	   
	and mrh.recipientID = @recipientID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
