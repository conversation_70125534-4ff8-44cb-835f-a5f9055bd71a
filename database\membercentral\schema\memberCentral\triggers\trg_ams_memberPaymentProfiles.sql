ALTER TRIGGER trg_ams_memberPaymentProfiles ON dbo.ams_memberPaymentProfiles
AFTER UPDATE 
AS 

SET NOCOUNT ON
BEGIN TRY

	IF NOT EXISTS (SELECT * FROM inserted) RETURN	

	IF UPDATE(lastUpdatedDate) or UPDATE(failedSinceDate) or UPDATE(failedLastDate) or UPDATE(expiration) or UPDATE(cardTypeID) or UPDATE([status]) BEGIN
		DECLARE @anyChangeForInv int, @anyChangeForCC int, @orgID int, @dateForNull datetime = '1/1/1900';
		DECLARE @tblProfilesToRunForInv TABLE (orgID int, payProfileID int);
		DECLARE @tblProfilesToRunForCC TABLE (orgID int, payProfileID int);
		DECLARE @tblOrgIDs TABLE (orgID int);
		
		-- did any of my tracking fields change for any of the rows updated? card must be linked to invoice.
		INSERT INTO @tblProfilesToRunForInv (orgID, payProfileID)
		SELECT inv.orgID, i.payProfileID	
		FROM Inserted as i 
		INNER JOIN Deleted as d ON i.payProfileID = d.payProfileID 
		inner join dbo.tr_invoices as inv on inv.payProfileID = i.payProfileID
		WHERE isnull(i.lastUpdatedDate,@dateForNull) != isnull(d.lastUpdatedDate,@dateForNull)
		OR isnull(i.failedSinceDate,@dateForNull) != isnull(d.failedSinceDate,@dateForNull)
		OR isnull(i.failedLastDate,@dateForNull) != isnull(d.failedLastDate,@dateForNull);

		-- did any of the tracking fields (cc exp conditions) change for any of the rows updated? card should be of gateway type AuthorizeCCCIM
		INSERT INTO @tblProfilesToRunForCC (orgID, payProfileID)
		SELECT DISTINCT s.orgID, mpp.payProfileID
		FROM Inserted AS i
		INNER JOIN Deleted AS d ON i.payProfileID = d.payProfileID
		INNER JOIN dbo.ams_memberPaymentProfiles AS mpp ON mpp.payProfileID = i.payProfileID
		INNER JOIN dbo.mp_profiles AS mp ON mp.profileID = mpp.profileID
		INNER JOIN dbo.sites AS s ON s.siteID = mp.siteID
		INNER JOIN dbo.mp_gateways AS g ON g.gatewayID = mp.gatewayID
			AND g.gatewayType = 'AuthorizeCCCIM'
		WHERE ISNULL(i.expiration,@dateForNull) != ISNULL(d.expiration,@dateForNull)
		OR ISNULL(i.lastUpdatedDate,@dateForNull) != ISNULL(d.lastUpdatedDate,@dateForNull)
		OR ISNULL(i.failedLastDate,@dateForNull) != ISNULL(d.failedLastDate,@dateForNull)
		OR ISNULL(i.cardTypeID,0) != ISNULL(d.cardTypeID,0)
		OR (i.[status] != d.[status] AND i.[status] = 'D');

		SELECT @anyChangeForInv = COUNT(*) from @tblProfilesToRunForInv;
		SELECT @anyChangeForCC = COUNT(*) from @tblProfilesToRunForCC;
		IF @anyChangeForInv > 0 OR @anyChangeForCC > 0 BEGIN
			
			-- handle any conditions that need updating
			BEGIN TRY
				SET XACT_ABORT OFF;	

				IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
					DROP TABLE #tblMCQRun;
				CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

				INSERT INTO @tblOrgIDs (orgID)
				SELECT orgID
				FROM @tblProfilesToRunForInv
					UNION
				SELECT orgID
				FROM @tblProfilesToRunForCC;

				select @orgID = min(orgID) from @tblOrgIDs;
				while @orgID is not null begin
					
					-- m.status <> 'D' is required here because in member merges we update COF to set the lastUpdatedDate, 
					-- which runs this trigger for the deleted members which causes them to be added to the cache_members_conditions tables and that is not allowed.
					INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
					SELECT distinct @orgID, m.activeMemberID, c.conditionID 
					from dbo.ams_virtualGroupConditions as c WITH(NOLOCK)
					inner join @tblProfilesToRunForInv as ptr on ptr.orgID = @orgID
					inner join Inserted as i on i.payProfileID = ptr.payProfileID
					inner join dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID and m.memberID = i.memberID and m.status <> 'D'
					where c.orgID = @orgID
					and c.fieldcode = 'acct_inv'
					and c.conditionTypeID = 1;

					-- cc exp conditions
					INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
					SELECT DISTINCT @orgID, mpp.memberID, c.conditionID
					FROM dbo.ams_virtualGroupConditions AS c
					INNER JOIN dbo.ams_virtualGroupConditionValues AS cv ON cv.conditionID = c.conditionID
					INNER JOIN dbo.ams_memberPaymentProfiles AS mpp on CAST(mpp.profileID as varchar(20)) = cv.conditionValue
					INNER JOIN @tblProfilesToRunForCC as tmp on tmp.orgID = @orgID
						AND tmp.payProfileID = mpp.payProfileID
					INNER JOIN dbo.ams_virtualGroupConditionKeys AS k ON k.conditionKeyID = cv.conditionKeyID
						AND k.conditionKey = 'acctCCProf'
					INNER JOIN dbo.ams_members as m WITH(NOLOCK) on m.orgID = @orgID
						AND m.memberID = mpp.memberID
						AND m.[status] <> 'D'
					WHERE c.orgID = @orgID
					AND c.fieldCode = 'acct_cc';
					
					EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

					TRUNCATE TABLE #tblMCQRun;

					select @orgID = min(orgID) from @tblOrgIDs where orgID > @orgID;
				end
				
				IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
					DROP TABLE #tblMCQRun;

				SET XACT_ABORT ON;
			END TRY
			BEGIN CATCH
				SET XACT_ABORT ON;
				EXEC dbo.up_MCErrorHandler @raise=0, @email=1;
			END CATCH

		END
	END

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO
