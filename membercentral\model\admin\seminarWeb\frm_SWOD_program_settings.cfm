<cfsavecontent variable="local.settingsJS">
	<cfoutput>
	<script language="JavaScript">
		<cfif local.hasManageSWFormRights>
			var SW_pretest_FormsTable, SW_posttest_FormsTable, SW_evaluation_FormsTable;
			var #toScript(local.SWFormsListLink,'link_SWFormsList')#;
			var #toScript(local.editFormLink,'link_editSWForm')#;
		</cfif>
		function showIntroMessageText(){
			if ($('##toggleIntroMessageText').is(':checked')) {
				$('##introMessageTextHolder').removeClass("d-none");
			} else {
				$('##introMessageTextHolder').addClass("d-none");
				$('##introMessageText').val('');
			}
		}
		function showEndOfSeminarText(){
			if ($('##toggleEndOfSeminarText').is(':checked')) {
				$('##endOfSeminarTextHolder').removeClass("d-none");
			} else {
				$('##endOfSeminarTextHolder').addClass("d-none");
				$('##endOfSeminarText').val('');
			}
		}
		function saveSWODProgramSettings(callback) {
			mca_hideAlert('err_settings');
			var saveResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					$('##frmSWODProgramSettings,##divSWProgramSettingsSaveLoading').toggle();
					if(!$("##program-settings .card-header:first ##saveResponse").length)
						$("##program-settings .card-header:first .card-header--title").after('<span id="saveResponse"></span>');
					$('##program-settings .card-header:first ##saveResponse').html('<span class="badge badge-success">SAVED SUCCESSFULLY</span>').addClass('text-success').show().fadeOut(10000);
					if(programAdded)
						$('##nextButton').prop('disabled',false);
					else
						$('##program-settings .card-footer .save-button').prop('disabled',false);
					if (callback) {
						callback();
					}
				} else {
					var arrReq = [];
					arrReq.push(r.err && r.err.length ? r.err : 'We were unable to save sw program settings.');
					$('##err_settings').html(arrReq.join('<br/>')).removeClass('d-none');
					$('html,body').animate({scrollTop: $('##err_settings').offset().top-120},500);
					if(programAdded)
						$('##nextButton').prop('disabled',false);
					else
						$('##program-settings .card-footer .save-button').prop('disabled',false);
				}
			};

			var arrReq = [];
			if($("##toggleIntroMessageText:checked").length == 1 && !$('##introMessageText').val().length)
				arrReq.push('Enter Program Introduction Message.');
			if($("##toggleEndOfSeminarText:checked").length == 1 && !$('##endOfSeminarText').val().length)
				arrReq.push('Enter Program Completion Text.');
			<cfif local.isPublisher>
				if($('##togglePrerequisiteSeminar').prop('checked') && !$('##preReq').val().length)
					arrReq.push('Select Prerequisite Seminar if you want to enable it');
				if($("##enableCustomField:checked").length == 1 && $('##customFieldHolder .dataTable .dataTables_empty').length > 0)
					arrReq.push('Add custom field for At the time of registration.');
				if($("##preTestRequired:checked").length == 1 && $('##SW_pretest_FormsTable .dataTables_empty').length > 0)
					arrReq.push('Select pre-test for When Registrant Begins Program.');
				if($("##examRequired:checked").length == 1 && $('##SW_posttest_FormsTable .dataTables_empty').length > 0)
					arrReq.push('Select exam for Before Registrant Completes Program.');
				if($("##evaluationRequired:checked").length == 1 && $('##SW_evaluation_FormsTable .dataTables_empty').length > 0)
					arrReq.push('Select evaluation for Before Registrant Completes Program.');
				if($("##offerQA:checked").length == 1 && $('##swod_moderator_table .dataTables_empty').length > 0)
					arrReq.push('Add Moderators to receive Q&A from registrants.');
				if($("##toggleLink:checked").length == 1 && $('##SWLinksTable .dataTables_empty').length > 0)
					arrReq.push('Add External Links for the program.');
			</cfif>

			if (arrReq.length) {
				$('##err_settings').html(arrReq.join('<br/>')).removeClass('d-none');
				$('html,body').animate({scrollTop: $('##err_settings').offset().top-120},500);
				if(programAdded)
					$('##nextButton').prop('disabled',false);
				else
					$('##program-settings .card-footer .save-button').prop('disabled',false);
				return false;
			}

			$('##frmSWODProgramSettings,##divSWProgramSettingsSaveLoading').toggle();
			
			var objParams = { 
				seminarID:$('##seminarID').val(),
				introMessageText:$('##introMessageText').val(),
				endofSeminartext:$('##endOfSeminarText').val()
				<cfif local.isPublisher>
					,offerQA:$("##offerQA:checked").length,
					blankOnInactivity:$("##blankOnInactivity:checked").length,
					offerCertificate:$("##offercertificate:checked").length,
					preReq:($("##togglePrerequisiteSeminar:checked").length == 0 ? 0 : ($('##preReq').val() === '' ? 0 : parseInt($('##preReq').val(), 10)))
				</cfif>
			};

			TS_AJX('ADMINSWOD','saveSWODProgramSettings',objParams,saveResult,saveResult,20000,saveResult);
		}
		<cfif local.isPublisher>
			function showPrerequisiteSeminar() {
				if ($('##togglePrerequisiteSeminar').is(':checked')) 
					$('##prerequisiteSeminarHolder').removeClass("d-none");
				else {
					$('##prerequisiteSeminarHolder').addClass("d-none");
					$('##preReq').val('');
				}
			}
			function showCustomFieldSettings(){
				if ($('##enableCustomField').is(':checked')) {
					$('##customFieldHolder').removeClass("d-none");
				} else {
					$('##customFieldHolder').addClass("d-none");
				}
			}
			function showPreTestRequiredSettings(){
				if ($('##preTestRequired').is(':checked')) {
					$('##preTestRequiredHolder').removeClass("d-none");
				} else {
					$('##preTestRequiredHolder').addClass("d-none");
				}
			}
			function showExamRequiredSettings(){
				if ($('##examRequired').is(':checked')) {
					$('##examRequiredHolder').removeClass("d-none");
				} else {
					$('##examRequiredHolder').addClass("d-none");
				}
			}
			function showEvaluationRequiredSettings(){
				if ($('##evaluationRequired').is(':checked')) {
					$('##evaluationRequiredHolder').removeClass("d-none");
				} else {
					$('##evaluationRequiredHolder').addClass("d-none");
				}
			}
			function onAddSWProgramAuthormoderator() {
				if (!$('##offerQA').prop('disabled')) 
					$('##offerQA').prop('disabled', true);
			}
			function onRemoveSWProgramAuthorModerator() {
				if ($('##swod_moderator_table tbody tr').length === 1) {
                    $('##moderatorsHolder').addClass("d-none");
					if ($('##offerQA').prop('disabled')) 
						$('##offerQA').prop('disabled', false);
					$('##offerQA').prop('checked', false); 
                }
			}
			function showModerators(){
				if ($('##offerQA').is(':checked')) {
					$('##moderatorsHolder').removeClass("d-none");
					$('##offerQA').val("1");
					$.fn.DataTable.isDataTable('##swod_moderator_table') ? swod_moderator_table.draw() : initSWODModerators();
				} else {
					$('##moderatorsHolder').addClass("d-none");
					$('##offerQA').val("0");
				}
			}
			function toggleBlankOnInactivity() {
				if ($('##blankOnInactivity').is(':checked')) 
					$('##blankOnInactivity').val("1");
				else
					$('##blankOnInactivity').val("0");
			}
			function showLinks(){
				if ($('##toggleLink').is(':checked')) {
					$('##linksHolder').removeClass("d-none");
				} else {
					$('##linksHolder').addClass("d-none");
				}
			}
			function toggleOffercertificate() {
				if ($('##offercertificate').is(':checked')) 
					$('##offercertificate').val("1");
				else
					$('##offercertificate').val("0");
			}
			<cfif local.hasEditRights>
				<cfloop array="#local.arrSWODEnrollmentFields#" index="local.thisGrid">
					function mccf_#local.thisGrid.gridExt#_drawCallBack(row, data, dataIndex) {
						let totalRows = window['mccf_#local.thisGrid.gridExt#_table'].data().count();
						
						if (totalRows) {
							if($("##enableCustomField:checked").length == 0) {
								$("##enableCustomField").prop('checked',true);
							}
							$('##enableCustomField').prop('disabled',true);
						} else {
							$('##enableCustomField').prop('disabled',false);
						}
						showCustomFieldSettings();
						
						if (isSWProgramLocked())
							$('##enableCustomField').prop('disabled',true); 
					}
				</cfloop>
			</cfif>
		</cfif>

		function initSWODSettings() {
			<cfif local.isPublisher>
				<cfif local.hasEditRights>
					<cfloop array="#local.arrSWODEnrollmentFields#" index="local.thisGrid">
						if($.fn.DataTable.isDataTable('##mccf_'+'#local.thisGrid.gridExt#'+'_table'))
							mccf_reloadFieldsTable('#local.thisGrid.gridExt#');	
						else mccf_initFieldsTable('#local.thisGrid.gridExt#');
					</cfloop>
				</cfif>
				<cfif local.hasManageSWFormRights>
					if(!$.fn.DataTable.isDataTable('##SW_pretest_FormsTable')) 
						initSWForms('SWOD','preTest');
					if(!$.fn.DataTable.isDataTable('##SW_posttest_FormsTable')) 
						initSWForms('SWOD','postTest');
					if(!$.fn.DataTable.isDataTable('##SW_evaluation_FormsTable')) 
						initSWForms('SWOD','evaluation');
				</cfif>
				$.fn.DataTable.isDataTable('##SWLinksTable') ? SWLinksTable.draw() : initSWLinksTable();
				if ($('##enableCustomField').is(':checked')) 
					$('##customFieldHolder').removeClass("d-none");
				else 
					$('##customFieldHolder').addClass("d-none");
				if ($('##moderatorsHolder').is(':visible')) {
					$.fn.DataTable.isDataTable('##swod_moderator_table') ? swod_moderator_table.draw() : initSWODModerators();
					$('##offerQA').prop('disabled', true);
				}
				if ($('##blankOnInactivity').is(':checked')) 
					$('##blankOnInactivity').val("1");
				else
					$('##blankOnInactivity').val("0");
				if ($('##offercertificate').is(':checked')) 
					$('##offercertificate').val("1");
				else
					$('##offercertificate').val("0");
			</cfif>
		}
	</script>
	<cfif local.hasEditRights and local.isPublisher>
		#local.strSWODEnrollmentFieldsGrid.js#
	</cfif>
	<style>
		.custom-control-input:disabled~.custom-control-label {
			color: ##3b3e66;
		}
    </style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.settingsJS#">    

<cfoutput>
	<div id="err_settings" class="alert alert-danger mb-2 mt-2 d-none"></div>
	
	<form name="frmSWODProgramSettings" id="frmSWODProgramSettings" method="post" onsubmit="saveSWODProgramSettings();" autocomplete="off">
		<input type="hidden" name="seminarID" id="seminarID" value="#local.qrySeminar.seminarID#">
		
		<cfif local.hasEditRights>
			<div id="prePurchaseHolder" class="mt-2">
				<div class="card card-box mb-1">
					<div class="card-header bg-light">
						<div class="card-header--title">
							<b>Before Registrant Purchases Program</b>
						</div>
					</div>
					<div class="card-body pb-3">
						<div class="form-group">
							<div class="custom-control custom-switch">
								<input type="checkbox" name="togglePrerequisiteSeminar" id="togglePrerequisiteSeminar" onclick="showPrerequisiteSeminar()" class="custom-control-input" <cfif LEN(local.qrySeminar.preReq)> checked="checked"</cfif>>
								<label class="custom-control-label" for="togglePrerequisiteSeminar">
									Prerequisite: Require another program to be completed before the registrant can purchase this program.
								</label>
						
								<div class="form-row mt-2 <cfif !LEN(local.qrySeminar.preReq)>d-none</cfif>" id="prerequisiteSeminarHolder">
									<div class="col">
										<div class="form-group">
											<div class="form-label-group">
												<cfif local.qrySWODSeminars.recordcount>
													<select name="preReq" id="preReq" class="form-control">
														<option value=""></option>
														<cfoutput query="local.qrySWODSeminars" group="isPublished">
															<optgroup label="#iif(local.qrySWODSeminars.isPublished is 1,DE('Active Seminars'),DE('Inactive Seminars'))#">
																<cfoutput>
																	<option value="#local.qrySWODSeminars.seminarID#" <cfif local.qrySeminar.preReq is local.qrySWODSeminars.seminarID>selected</cfif>>#left(local.qrySWODSeminars.seminarName,100)#<cfif len(local.qrySWODSeminars.seminarName) gt 100>...</cfif></option>
																</cfoutput>
															</optgroup>
														</cfoutput>
													</select>
													<label for="preReq">Prerequisite Seminar</label>
												<cfelse>
													(no other On Demand seminars have been created)
												</cfif>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			
			<div id="atRegistrationHolder" class="mt-2">
				<div class="card card-box mb-1">
					<div class="card-header bg-light">
						<div class="card-header--title">
							<b>At Time of Registration</b>
						</div>
					</div>
					<div class="card-body pb-3">
						<div class="form-group mb-2">
							<div class="custom-control custom-switch">
								<input type="checkbox" name="enableCustomField" id="enableCustomField" onclick="showCustomFieldSettings();" class="custom-control-input">
								<label class="custom-control-label text-body" for="enableCustomField">
									Custom Fields: Add custom registration fields to collect information or require questions/statements to be answered by the registrant.
									<br/><i>Note: Registrants on this site who respond to custom fields can be downloaded from the Registrants tab by clicking "Download Registrants."</i>
								</label>
								
								<div class="form-group my-2" id="customFieldHolder">
									#local.strSWODEnrollmentFieldsGrid.html#
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</cfif>

		<cfif local.hasManageSWFormRights OR local.hasEditRights OR !local.isPublisher>
			<div id="registrationBeginsProgramHolder" class="mt-2">
				<div class="card card-box mb-1">
					<div class="card-header bg-light">
						<div class="card-header--title">
							<b>When Registrant Begins Program</b>
						</div>
					</div>
					<div class="card-body pb-3">
						<cfif local.hasManageSWFormRights>
							<div class="form-group mb-3">
								<div class="custom-control custom-switch">
									<input type="checkbox" name="preTestRequired" id="preTestRequired" onclick="showPreTestRequiredSettings();" class="custom-control-input">
									<label class="custom-control-label text-body" for="preTestRequired">
										Pre-Test: Require attendees to complete one or more pre-tests.
									</label>
									
									<div class="my-2" id="preTestRequiredHolder">
										<div id="err_addpretest" class="alert alert-danger mb-2 d-none"></div>
										<div class="form-label-group">
											<div class="input-group">
												<select name="fPreTest" id="fPreTest" class="form-control">
													<option value=""></option>
													<cfloop query="local.qryOrgForms">
														<option value="#local.qryOrgForms.formID#">#left(local.qryOrgForms.formTitle,100)#</option>
													</cfloop>
												</select>
												<div class="input-group-append">
													<button type="button" name="btnAddPreTest" id="btnAddPreTest" class="btn input-group-text" onclick="doAddSWPreTest();">Add Pre-Test</button>
												</div>
												<label for="fPreTest">Select Custom Pre-Test for this Program</label>
											</div>
										</div>
										<table id="SW_pretest_FormsTable" class="table table-sm table-striped table-bordered" style="width:100%">
										<thead>
											<tr>
												<th id="columnid"></th>
												<th>Selected Pre-Tests</th>
												<th>Actions</th>
											</tr>
										</thead>
										</table>
									</div>
								</div>
							</div>
						</cfif>
						<cfif local.hasEditRights OR !local.isPublisher>
							<div class="form-group mb-3">
								<div class="custom-control custom-switch">
									<input type="checkbox" name="toggleIntroMessageText" id="toggleIntroMessageText" onclick="showIntroMessageText();" class="custom-control-input" <cfif local.isPublisher AND len(local.qrySeminar.introMessageText)> checked="checked"<cfelseif !local.isPublisher AND LEN(local.qrySeminarSyndicateObj.introMessageText)>checked="checked"</cfif>>
									<label class="custom-control-label" for="toggleIntroMessageText">
										Program Introduction Message: Show a custom message to the registrant that shows in their program outline.
									</label>
							
									<div class="form-group <cfif local.isPublisher AND !len(local.qrySeminar.introMessageText)>d-none<cfelseif !local.isPublisher AND !len(local.qrySeminarSyndicateObj.introMessageText)>d-none</cfif> pt-2" id="introMessageTextHolder">
										<div class="form-label-group">
											<textarea name="introMessageText" id="introMessageText" class="form-control form-control-sm" rows="4"><cfif local.isPublisher>#local.qrySeminar.introMessageText#<cfelse>#trim(local.qrySeminarSyndicateObj.introMessageText)#</cfif></textarea>
											<label for="introMessageText">Introductory Message</label>
										</div>
									</div>
								</div>
							</div>
						</cfif>
						<cfif local.hasEditRights>
							<div class="form-group mb-3">
								<div class="custom-control custom-switch">
									<input type="checkbox" name="offerQA" id="offerQA" onclick="showModerators();" class="custom-control-input" <cfif local.qrySeminar.offerQA OR local.qryAuthors.recordCount> checked="checked" value="1"<cfelse>value="0"</cfif>>
									<label class="custom-control-label" for="offerQA">
										Q&A: Allow registrants to submit program questions to a designated moderator.
									</label>
							
									<div class="form-group <cfif !local.qrySeminar.offerQA AND !local.qryAuthors.recordCount>d-none</cfif> pt-2" id="moderatorsHolder">
										<div class="alert alert-info">
											Moderators will receive Q&A questions from members.
										</div>
										<div class="toolButtonBar">
											<div><a href="##" onclick="addSWProgramAuthor('moderator');return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to add moderator."><i class="fa-regular fa-circle-plus"></i> Add Moderator</a></div>
										</div>
										<table id="swod_moderator_table" class="table table-sm table-striped table-bordered" style="width:100%">
											<thead>
												<tr>
													<th id="columnid"></th>
													<th>Moderator</th>
													<th>Tools</th>
												</tr>
											</thead>
										</table>
										
										<script type="text/html" id="mc_addSWProgramAuthorForm_moderator">
											<form name="frmAddSWProgramAuthor_moderator" id="frmAddSWProgramAuthor_moderator">
												<div id="err_addswprogramauthor_moderator" class="alert alert-danger mb-2 d-none"></div>
												<div class="form-label-group">
													<select name="fSWAuthor_moderator" id="fSWAuthor_moderator" class="custom-select"></select>
													<label for="fSWAuthor_moderator">Choose Moderator</label>
												</div>
											</form>
										</script>
									</div>
								</div>
							</div>
							<div class="form-group mb-3">
								<div class="custom-control custom-switch">
									<input type="checkbox" name="blankOnInactivity" id="blankOnInactivity" onClick="toggleBlankOnInactivity()" class="custom-control-input" <cfif local.qrySeminar.blankOnInactivity> checked="checked"</cfif>>
									<label class="custom-control-label" for="blankOnInactivity">
										Inactivity: Pause the program if the registrant clicks outside of the program player.
									</label>
								</div>
							</div>
							<div class="form-group mb-3">
								<div class="custom-control custom-switch">
									<input type="checkbox" name="toggleLink" id="toggleLink" onclick="showLinks();" class="custom-control-input">
									<label class="custom-control-label" for="toggleLink">
										Other Resources: Offer registrants links to external sites inside the program player.
									</label>
							
									<div class="form-group pt-2" id="linksHolder">
										<div class="toolButtonBar">
											<div><a href="javascript:editSWLink(0);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to add link"><i class="fa-regular fa-circle-plus"></i> Add Link</a></div>
										</div>
										<table id="SWLinksTable" class="table table-sm table-striped table-bordered" style="width:100%">
											<thead>
												<tr>
													<th id="columnid"></th>
													<th>Link</th>
													<th>Actions</th>
												</tr>
											</thead>
										</table>
									</div>
								</div>
							</div>
						</cfif>
					</div>
				</div>
			</div>
		</cfif>

		<cfif local.hasManageSWFormRights>
			<div id="beforeRegistrationCompleteHolder" class="mt-2">
				<div class="card card-box mb-1">
					<div class="card-header bg-light">
						<div class="card-header--title">
							<b>Before Registrant Completes Program</b>
						</div>
					</div>
					<div class="card-body pb-3">
						<div class="form-group mb-2">
							<div class="form-group mb-3">
								<div class="custom-control custom-switch">
									<input type="checkbox" name="examRequired" id="examRequired" onclick="showExamRequiredSettings();" class="custom-control-input">
									<label class="custom-control-label text-body" for="examRequired">
										Exam: Require attendees to complete one or more exams.
									</label>
									
									<div class="my-2" id="examRequiredHolder">
										<div id="err_addexam" class="alert alert-danger mb-2 d-none"></div>
										<div class="form-label-group">
											<div class="input-group">
												<select name="fExam" id="fExam" class="form-control">
													<option value=""></option>
													<cfloop query="local.qryOrgForms">
														<option value="#local.qryOrgForms.formID#">#left(local.qryOrgForms.formTitle,100)#</option>
													</cfloop>
												</select>
												<div class="input-group-append">
													<button type="button" name="btnAddExam" id="btnAddExam" class="btn input-group-text" onclick="doAddSWExam();">Add Exam</button>
												</div>
												<label for="fExam">Select Custom Exam for this Program</label>
											</div>
										</div>
										<table id="SW_posttest_FormsTable" class="table table-sm table-striped table-bordered" style="width:100%">
											<thead>
												<tr>
													<th id="columnid"></th>
													<th>Selected Exams</th>
													<th>Actions</th>
												</tr>
											</thead>
										</table>
									</div>
								</div>
							</div>
						</div>

						<div class="form-group mb-2">
							<div class="form-group mb-3">
								<div class="custom-control custom-switch">
									<input type="checkbox" name="evaluationRequired" id="evaluationRequired" onclick="showEvaluationRequiredSettings();" class="custom-control-input">
									<label class="custom-control-label text-body" for="evaluationRequired">
										Evaluation: Require attendees to complete one or more evaluations.
									</label>
									
									<div class="my-2" id="evaluationRequiredHolder">
										<div id="err_addevaluation" class="alert alert-danger mb-2 d-none"></div>
										<div class="form-label-group">
											<div class="input-group">
												<select name="fEvaluation" id="fEvaluation" class="form-control">
													<option value=""></option>
													<cfloop query="local.qryOrgSurveyForms">
														<option value="#local.qryOrgSurveyForms.formID#">#left(local.qryOrgSurveyForms.formTitle,100)#</option>
													</cfloop>
												</select>
												<div class="input-group-append">
													<button type="button" name="btnAddEvaluation" id="btnAddEvaluation" class="btn input-group-text" onclick="doAddSWEvaluation();">Add Evaluation</button>
												</div>
												<label for="fExam">Select Custom Evaluation for this Program</label>
											</div>
										</div>
										<table id="SW_evaluation_FormsTable" class="table table-sm table-bordered" style="width:100%">
											<thead>
												<tr>
													<th id="columnid"></th>
													<th>Selected Evaluations</th>
													<th>Actions</th>
												</tr>
											</thead>
										</table>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</cfif>

		<cfif local.hasEditRights OR !local.isPublisher>
			<div id="beforeRegistrationCompleteHolder" class="mt-2">
				<div class="card card-box mb-1">
					<div class="card-header bg-light">
						<div class="card-header--title">
							<b>After Registrant Completes Program</b>
						</div>
					</div>
					<div class="card-body pb-3">
						<div class="form-group mb-3">
							<div class="custom-control custom-switch">
								<input type="checkbox" name="toggleEndOfSeminarText" id="toggleEndOfSeminarText" onclick="showEndOfSeminarText();" class="custom-control-input" <cfif local.isPublisher AND len(local.qrySeminar.endOfSeminarText)> checked="checked"<cfelseif !local.isPublisher AND len(local.qrySeminarSyndicateObj.endOfSeminarText)>checked="checked"</cfif>>
								<label class="custom-control-label" for="toggleEndOfSeminarText">
									Program Completion Message: Show a custom message to the registrant after completing the program.
								</label>
						
								<div class="form-group <cfif local.isPublisher AND !len(local.qrySeminar.endOfSeminarText)>d-none<cfelseif !local.isPublisher AND !len(local.qrySeminarSyndicateObj.endOfSeminarText)>d-none</cfif> pt-2" id="endOfSeminarTextHolder">
									<div class="form-label-group">
										<textarea name="endOfSeminarText" id="endOfSeminarText" class="form-control form-control-sm" rows="4"><cfif local.isPublisher>#local.qrySeminar.endOfSeminarText#<cfelse>#local.qrySeminarSyndicateObj.endOfSeminarText#</cfif></textarea>
										<label for="endOfSeminarText">Completion Text</label>
									</div>
								</div>
							</div>
						</div>
						<cfif local.hasEditRights>
							<div class="form-group mb-3">
								<div class="custom-control custom-switch">
									<input type="checkbox" name="offercertificate" id="offercertificate" onClick="toggleOffercertificate()" class="custom-control-input" <cfif local.qrySeminar.offercertificate> checked="checked"</cfif>>
									<label class="custom-control-label" for="offercertificate">
										Certificate: Award the registrant a certificate after completing the program.
									</label>
								</div>
							</div>
						</cfif>
					</div>
				</div>
			</div>
		</cfif>
		<div class="text-right mb-2">
			<button type="button" name="btnSaveSWODProgramSettings" class="btn btn-sm btn-primary d-none" onclick="saveSWODProgramSettings();">Save Settings</button>
		</div>
	</form>
	
	<div id="divSWProgramSettingsSaveLoading" style="display:none;">
		<div class="text-center">
			<br/>
			<i class="fa-light fa-circle-notch fa-spin fa-3x"></i>
			<br/><br/>
			<b>Please wait while we save these program settings.</b>
			<br/>
		</div>
	</div>
</cfoutput>