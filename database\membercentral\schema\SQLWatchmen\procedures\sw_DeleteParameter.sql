----------------------------------------------------------------------------------------------------
/* Query #46: PASSED */
ALTER PROCEDURE [dbo].[sw_DeleteParameter]
(
    @Parameter VARCHAR(128) = NULL,
    @Commit BIT = 0,
    @Help BIT = 0
)
AS
BEGIN

    IF @Parameter IS NULL OR @Help = 1
    BEGIN
        PRINT 
            'Stored Procedure sw_DeleteParameter' + CHAR(13) +
            '-------------------------------------------------------------------------------' + CHAR(13) +
            'Purpose: ' + CHAR(13) +
            CHAR(9) + 'To remove a parameter from the Parameters table.' + CHAR(13) +
            'Parameters: ' + CHAR(13) +
            CHAR(9) + '@Parameter VARCHAR(128) - The name of the parameter to delete.' + CHAR(13) +
            CHAR(9) + '@Commit BIT - Set to 1 to perform the delete.' + CHAR(13) +
            CHAR(9) + '@Help BIT - Set to 1 to display this help message.' + CHAR(13) + 
            'Note: ' + CHAR(13) +
            CHAR(9) + 'Please ensure the parameter is not in use before deleting it.'
        RETURN
    END

    IF @Commit = 1
    BEGIN
        DELETE FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = @Parameter
    END
    ELSE
    BEGIN
        SELECT 'Current Value', * FROM [dbo].[Parameters] WHERE [Parameter] = @Parameter
        SELECT 'sw_DeleteParameter @Help = 1 for more information'
    END

END
GO
