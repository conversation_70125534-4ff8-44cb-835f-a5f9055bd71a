ALTER PROCEDURE dbo.ref_createSMSTemplate
@templateTypeCode varchar(10),
@templateName varchar(200),
@templateDescription varchar(max),
@rawContent varchar(max),
@languageID int,
@isDefault int,
@isActive int,
@createdByMemberID int,
@siteID int,
@strArea varchar(25),
@templateID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @templateTypeID int, @appCreatedContentResourceTypeID int, 
		@parentSiteResourceID int, @contentID int, @contentSiteResourceID int;

	SELECT @templateTypeID = templateTypeID
	FROM dbo.et_emailTemplateTypes
	WHERE templateTypeCode = @templateTypeCode;
 
	select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');
	select @parentSiteResourceID = dbo.fn_getSiteResourceIDForResourceType('ReferralsAdmin', @siteID);
 
	BEGIN TRAN;
		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID,
			@parentSiteResourceID=@parentSiteResourceID, @siteResourceStatusID=1, @isHTML=0,
			@languageID=1, @isActive=1, @contentTitle=@templateName, @contentDesc='', @rawContent=@rawContent,
			@memberID=@createdByMemberID, @contentID=@contentID OUTPUT, @siteResourceID=@contentSiteResourceID OUTPUT;

		INSERT INTO dbo.ref_smsTemplates (templateTypeID, templateName, templateDescription, contentID, 
			languageID, isActive, isDefault, createdByMemberID,dateCreated,siteID,area)
		VALUES(@templateTypeID, @templateName, @templateDescription, @contentID, @languageID, 
			@isActive, @isDefault, @createdByMemberID,GETDATE(),@siteID,@strArea);

		SELECT @templateID = SCOPE_IDENTITY();
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO