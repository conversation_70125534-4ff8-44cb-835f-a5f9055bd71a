----------------------------------------------------------------------------------------------------
/* Query #48: PASSED */
ALTER PROCEDURE [dbo].[sw_LogBlocking]
(
	@LogBlockingThreshold smallint = NULL
)
AS
BEGIN
	SET NOCOUNT ON

	IF @LogBlockingThreshold IS NULL
	BEGIN
		SELECT @LogBlockingThreshold = CAST([Value] AS smallint) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'LogBlockingThreshold'
	END

	DECLARE @LogBlockingThresholdms INT = @LogBlockingThreshold * 60000
	DECLARE @LogBlockingThresholdS INT = @LogBlockingThreshold * 60

	IF (SELECT COUNT(*) FROM sys.dm_exec_requests WHERE blocking_session_id <> 0 AND blocking_session_id IS NOT NULL AND wait_time > @LogBlockingThresholdms) = 0
	BEGIN
		RETURN
	END

	IF OBJECT_ID('tempdb..#BlockingLogMain') IS NOT NULL 
	BEGIN
		DROP TABLE #BlockingLogMain
	END

	IF OBJECT_ID('tempdb..#BlockingLogSessions') IS NOT NULL 
	BEGIN
		DROP TABLE #BlockingLogSessions
	END
	
	CREATE TABLE #BlockingLogMain 
	(
		[SessionID] SMALLINT NULL,
		[RequestID] INT NULL,
		[StartTime] DATETIME NULL,
		[HostName] NVARCHAR(128),
		[ProgramName] NVARCHAR(128),
		[ClientInterfaceName] NVARCHAR(32),
		[LoginName] NVARCHAR(128)NULL,
		[Status] NVARCHAR(30) NULL, 
		[Command] NVARCHAR(32) NULL,
		[ShortQueryText] NVARCHAR(512),
		[Database] NVARCHAR(128) NULL,
		[BlockingSessionID] SMALLINT,
		[WaitType] NVARCHAR(60) NULL,
		[WaitTimeSeconds] FLOAT NULL,
		[WaitResource] NVARCHAR(256) NULL,
		[TotalTimeSeconds] FLOAT NULL
	)

	CREATE TABLE #BlockingLogSessions 
	(
		SessionID SMALLINT NOT NULL
	)

	INSERT INTO #BlockingLogMain ([SessionID], [RequestID], [StartTime], [HostName], [ProgramName], [ClientInterfaceName], [LoginName], [Status], [Command], [ShortQueryText], [Database], [BlockingSessionID], [WaitType], [WaitTimeSeconds], [WaitResource], [TotalTimeSeconds])
	SELECT
	s.session_id,
	r.request_id,
	r.start_time,
	s.host_name,
	s.program_name,
	s.client_interface_name,
	s.login_name,
	r.status,
	r.command,
	LEFT(RTRIM(LTRIM(t.text)), 512) AS [ShortQueryText],
	DB_NAME(r.database_id) AS [Database],
	r.blocking_session_id,
	r.wait_type,
	CAST(r.wait_time AS FLOAT)/1000 AS [WaitTimeSeconds],
	r.wait_resource,
	CAST(r.total_elapsed_time AS FLOAT)/1000 AS [TotalTimeSeconds]
	FROM sys.dm_exec_sessions AS s
	LEFT JOIN sys.dm_exec_requests AS r on r.session_id = s.session_id
	OUTER APPLY sys.dm_exec_sql_text(r.sql_handle) AS t
	
	/*Blocked Sessions*/
	INSERT INTO #BlockingLogSessions (SessionID)
	SELECT SessionID FROM #BlockingLogMain WHERE BlockingSessionID <> 0 AND BlockingSessionID IS NOT NULL

	/*Blocking Sessions*/
	INSERT INTO #BlockingLogSessions (SessionID)
	SELECT BlockingSessionID FROM #BlockingLogMain WHERE BlockingSessionID <> 0 AND BlockingSessionID IS NOT NULL

	INSERT INTO [SQLWatchmen].[dbo].[BlockingLog] ([SessionID], [RequestID], [StartTime], [HostName], [ProgramName], [ClientInterfaceName], [LoginName], [Status], [Command], [ShortQueryText], [Database], [BlockingSessionID], [WaitType], [WaitTimeSeconds], [WaitResource], [TotalTimeSeconds])
	SELECT * FROM #BlockingLogMain WHERE SessionID IN (SELECT DISTINCT SessionID FROM #BlockingLogSessions)

	IF OBJECT_ID('tempdb..#BlockingLogMain') IS NOT NULL 
	BEGIN
		DROP TABLE #BlockingLogMain
	END

	IF OBJECT_ID('tempdb..#BlockingLogSessions') IS NOT NULL 
	BEGIN
		DROP TABLE #BlockingLogSessions
	END

END
GO
