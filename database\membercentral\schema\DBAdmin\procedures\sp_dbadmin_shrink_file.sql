USE [DBAdmin]
GO
/****** Object:  StoredProcedure [dbo].[sp_dbadmin_shrink_file]    Script Date: 7/16/2025 1:53:04 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

ALTER PROCEDURE [dbo].[sp_dbadmin_shrink_file]	
AS
BEGIN
	
	SET NOCOUNT ON;

    DECLARE @sql NVARCHAR(MAX);

    -- Shrink file in the first database
    SET @sql = N'USE [tempdb];
                 DBCC SHRINKFILE (2 ,500);';
    EXEC sp_executesql @sql;

	SET @sql = N'USE [platformQueue];
                 DBCC SHRINKFILE (N''platformQueue_log'' ,25);';
    EXEC sp_executesql @sql;

	SET @sql = N'USE [memberCentral];
                 DBCC SHRINKFILE (N''memberCentral_log'', 1000);';
    EXEC sp_executesql @sql;

	SET @sql = N'USE [platformMail];
                 DBCC SHRINKFILE (N''platformMail_log'', 75);';
    EXEC sp_executesql @sql;

	SET @sql = N'USE [platformStatsMC];
                 DBCC SHRINKFILE (2 , 2000);';
    EXEC sp_executesql @sql;

	SET @sql = N'USE [platformStats];
                 DBCC SHRINKFILE (2 , 1000);';
    EXEC sp_executesql @sql;

END
