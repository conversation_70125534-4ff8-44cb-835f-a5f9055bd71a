----------------------------------------------------------------------------------------------------
/* Query #35: PASSED */
ALTER PROCEDURE [dbo].[sw_AlertLongRunningJobs] 
(
	@LongRunningJobsAlertMinimumDurationThreshold SMALLINT = NULL,
	@LongRunningJobsAlertPercentDurationThreshold FLOAT = NULL,
	@LongRunningJobsAlertInterval SMALLINT = NULL,
	@LongRunningJobsAlertExcludedJobs NVARCHAR(128) = NULL,
	@MailProfileName varchar(128) = NULL,
	@AlertEmail varchar(128) = NULL,
	@Company varchar(128) = NULL,
	@Configuration varchar(128) = NULL
)
AS
BEGIN
	SET NOCOUNT ON

	IF @LongRunningJobsAlertInterval IS NULL
	BEGIN
		SET @LongRunningJobsAlertInterval = (SELECT CAST([Value] AS SMALLINT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'LongRunningJobsAlertInterval') 
	END

	IF DATEDIFF(minute, GETDATE(), (SELECT MAX([DateTime]) FROM [SQLWatchmen].[dbo].[AlertLog] WHERE [Alert] = 'Long Running Jobs')) < @LongRunningJobsAlertInterval
	BEGIN
		RETURN
	END

	IF @LongRunningJobsAlertMinimumDurationThreshold IS NULL
	BEGIN
		SET @LongRunningJobsAlertMinimumDurationThreshold = (SELECT CAST([Value] AS SMALLINT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'LongRunningJobsAlertMinimumDurationThreshold') 
	END

	IF @LongRunningJobsAlertPercentDurationThreshold IS NULL
	BEGIN
		SET @LongRunningJobsAlertPercentDurationThreshold = (SELECT CAST([Value] AS FLOAT)/100.0 FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'LongRunningJobsAlertPercentDurationThreshold') 
	END

	IF @LongRunningJobsAlertExcludedJobs IS NULL
	BEGIN
		SET @LongRunningJobsAlertExcludedJobs = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'LongRunningJobsAlertExcludedJobs')
	END	

	IF OBJECT_ID('tempdb..#AlertLongRunningJobs') IS NOT NULL
	BEGIN
		DROP TABLE #AlertLongRunningJobs
	END

	CREATE TABLE #AlertLongRunningJobs
	(
		[JobName] NVARCHAR(128) NOT NULL,
		[StartDateTime] DATETIME NOT NULL,
		[CurrentRunTime] INT NOT NULL,
		[AverageRunTime] INT NOT NULL
	)

	INSERT INTO #AlertLongRunningJobs
	SELECT 
	sj.name, 
	sja.start_execution_date, 
	DATEDIFF(minute, sja.start_execution_date, GETDATE()) AS [Run Time (minutes)], 
	sjh.AverageDuration
	FROM msdb.dbo.sysjobs sj
	JOIN msdb.dbo.sysjobactivity sja ON sj.job_id = sja.job_id
	JOIN 
	(SELECT job_id, 
	AVG(CASE LEN(CAST(run_duration AS NVARCHAR))
		WHEN 1 THEN 0
		WHEN 2 THEN 0
		WHEN 3 THEN CAST(SUBSTRING(CAST(run_duration AS NVARCHAR), 1, 1) AS INT)
		WHEN 4 THEN CAST(SUBSTRING(CAST(run_duration AS NVARCHAR), 1, 2) AS INT)
		WHEN 5 THEN (CAST(SUBSTRING(CAST(run_duration AS NVARCHAR), 1, 1) AS INT) * 60) + CAST(SUBSTRING(CAST(run_duration AS NVARCHAR), 2, 2) AS INT)
		WHEN 6 THEN (CAST(SUBSTRING(CAST(run_duration AS NVARCHAR), 1, 2) AS INT) * 60) + CAST(SUBSTRING(CAST(run_duration AS NVARCHAR), 3, 2) AS INT)
		END) 
	AS [AverageDuration] 
	FROM msdb.dbo.sysjobhistory 
	GROUP BY job_id) 
	sjh ON sj.job_id = sjh.job_id
	WHERE session_id = (SELECT MAX(session_id) FROM msdb.dbo.sysjobactivity)
	AND sja.start_execution_date IS NOT NULL
	AND sja.stop_execution_date IS NULL

	IF (SELECT TOP 1 [JobName] FROM #AlertLongRunningJobs 
	WHERE [CurrentRunTime] > @LongRunningJobsAlertMinimumDurationThreshold 
	AND [CurrentRunTime] > ([AverageRunTime] * @LongRunningJobsAlertPercentDurationThreshold)
	AND (',' + @LongRunningJobsAlertExcludedJobs + ',' NOT LIKE '%,' + [JobName] + ',%')) IS NOT NULL
	BEGIN
		IF @MailProfileName IS NULL
		BEGIN
			SET @MailProfileName = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'MailProfileName')
		END

		IF @AlertEmail IS NULL
		BEGIN
			SET @AlertEmail = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'AlertEmail')
		END

		IF @Company IS NULL
		BEGIN
			SET @Company = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Company')
		END

		IF @Configuration IS NULL
		BEGIN
			SET @Configuration = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Configuration')
		END

		DECLARE @Subject NVARCHAR(256)
		DECLARE @Body NVARCHAR(MAX)
		
		SET @Subject = 'Long Running Job Detected | ' + @Configuration + ' | ' + @Company

		SET @Body = 'At least one job has been running for a minimum of ' + CAST(@LongRunningJobsAlertMinimumDurationThreshold AS VARCHAR) + ' minutes and for a duration of at least ' + CAST((@LongRunningJobsAlertPercentDurationThreshold * 100) AS VARCHAR) + '% of the job''s average duration.' + CHAR(13) + CHAR(10)
			+ 'Please see details listed below: ' + CHAR(13) + CHAR(10)

		SELECT @Body += 'Job Name: ' + [JobName] +  ' | ' + 'Start Date/Time: ' + CAST([StartDateTime] AS VARCHAR) + ' | ' + 'Current Run Time (minutes): ' + CAST([CurrentRunTime] AS VARCHAR) + ' | ' + 'Average Run Time (minutes): ' + CAST([AverageRunTime] AS VARCHAR) + CHAR(13) + CHAR(10)
			FROM #AlertLongRunningJobs WHERE [CurrentRunTime] > @LongRunningJobsAlertMinimumDurationThreshold AND [CurrentRunTime] > ([AverageRunTime] * @LongRunningJobsAlertPercentDurationThreshold) AND (',' + @LongRunningJobsAlertExcludedJobs + ',' NOT LIKE '%,' + [JobName] + ',%')

		EXEC msdb.dbo.sp_send_dbmail
			@profile_name = @MailProfileName,
			@recipients = @AlertEmail,
			@subject = @Subject,
			@body = @Body

		INSERT INTO [SQLWatchmen].[dbo].[AlertLog] ([Alert]) VALUES ('Long Running Jobs')
	END

	IF OBJECT_ID('tempdb..#AlertLongRunningJobs') IS NOT NULL
	BEGIN
		DROP TABLE #AlertLongRunningJobs
	END

END
GO
