<cfoutput>
<h5 class="p-2">#local.qryMemberData.firstName# #local.qryMemberData.lastName#</h5>
<table class="table table-sm table-borderless border mt-3 p-2">
    <thead>
    <tr>
		<th></th>
        <th width="78%">Seminar</th>
        <th width="20%">Status</th>
    </tr>
    </thead>
    <tbody>
    <cfif local.qryItems.recordCount>
        <cfloop query="local.qryItems">
            <tr>
				<td><cfif len(local.qryItems.dateCompleted)><i class="fa-regular fa-square-check fa-lg"></i></cfif></td>
                <td>
					<a href="javascript:top.viewSWCPProgressDetail(#local.qryItems.seminarid#,#local.qryItems.enrollmentid#,#local.programID#,#local.depomemberdataID#)">#encodeForHTML(local.qryItems.seminarname)#</a>
				</td>
                <td class="text-nowrap">
					<cfif len(local.qryItems.dateEnrolled) is 0>
						Not Enrolled
					<cfelseif len(local.qryItems.dateCompleted) is 0>
						<cfif len(local.qryItems.SWODID) and local.qryItems.isPublished and local.qryItems.preReqFulfilled>
							In progress
						<cfelseif len(local.qryItems.SWODID) and local.qryItems.isPublished>
							Awaiting Prereqs
						<cfelseif len(local.qryItems.SWLID) and local.qryItems.isPublished and now() lt local.qryItems.dateStart>
							Not yet begun
						<cfelseif len(local.qryItems.SWLID) and local.qryItems.isPublished>
							Did not attend
						<cfelse>
							Not available
						</cfif>
					<cfelse>
						<cfif local.qryItems.passed and local.qryItems.offerCertificate>
							Completed
						<cfelseif local.qryItems.passed and not local.qryItems.offerCertificate>
							Completed
						<cfelseif not local.qryItems.passed>
							Failed
						</cfif>
					</cfif>
				</td>
            </tr>
        </cfloop>
    <cfelse>
        <tr>
            <td colspan="2" class="p-3">No records found.</td>
        </tr>
    </cfif>
    </tbody>
</table>
</cfoutput>