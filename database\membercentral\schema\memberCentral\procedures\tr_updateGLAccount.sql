ALTER PROC dbo.tr_updateGLAccount
@orgID int,
@GLAccountID int,
@accountName varchar(200),
@accountCode varchar(200),
@parentGLAccountID int,
@invoiceProfileID int,
@invoiceContentID int,
@deferredGLAccountID int,
@bypassDeferredCleanup bit,
@salesTaxProfileID int,
@salesTaxTaxJarCategoryID int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @GLAccountTypeID int, @accountNameDef varchar(200), @parentGLAccountIDDef int, @thePath varchar(max), @theParentPath varchar(max);

	-- null account code if no length
	IF @accountCode is not null and len(@accountCode) = 0
		SET @accountCode = null;

	-- null deferredGLAccountID if 0
	IF @deferredGLAccountID = 0
		SET @deferredGLAccountID = null;

	select @GLAccountTypeID = accountTypeID from dbo.tr_GLAccounts where GLAccountID = @GLAccountID;

	-- null invoiceProfileID if not revenue
	IF @GLAccountTypeID <> 3
		SELECT @invoiceProfileID = null;

	SET @accountName = REPLACE(@accountName,'"','');

	-- cant have multiple accounts with same accountName at same level
	-- cant have multiple accounts with same accountCode
	IF (@parentGLAccountID is null AND EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountName and parentGLAccountID is null and accountTypeID = @GLAccountTypeID and GLAccountID <> @GLAccountID and status <> 'D'))
		OR (@parentGLAccountID is not null and EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountName and parentGLAccountID = @parentGLAccountID and accountTypeID = @GLAccountTypeID and GLAccountID <> @GLAccountID and status <> 'D')) 
		OR (@accountCode is not null and EXISTS (select GLAccountID from dbo.tr_GLAccounts where orgID = @orgID and accountCode = @accountCode and GLAccountID <> @GLAccountID and status <> 'D'))
		OR (@GLAccountTypeID = 3 and @invoiceProfileID is null)
		RAISERROR('this account would violate distinct rules', 16, 1);

	IF @parentGLAccountID IS NOT NULL BEGIN
		select @thePath = rgl.thePath
		from dbo.tr_GLAccounts as gl
		inner join dbo.fn_getRecursiveGLAccounts(@orgID) as rgl on rgl.GLAccountID = gl.GLAccountID
		where gl.GLAccountID = @GLAccountID
		and gl.orgID = @orgID;

		select @theParentPath = rgl.thePath
		from dbo.tr_GLAccounts as gl
		inner join dbo.fn_getRecursiveGLAccounts(@orgID) as rgl on rgl.GLAccountID = gl.GLAccountID
		where gl.GLAccountID = @parentGLAccountID
		and gl.orgID = @orgID;

		IF LEFT(@theParentPath,LEN(@thePath)+1) = @thePath + '.'
			RAISERROR('Invalid Parent GLAccount', 16, 1);
	END

	BEGIN TRAN;
		-- create new deferred account if necessary
		IF @deferredGLAccountID = -1 BEGIN
			select @parentGLAccountIDDef = GLAccountID
				from dbo.tr_GLAccounts 
				where orgID = @orgID
				and AccountTypeID = 5 
				and (
					(@GLAccountTypeID = 3 and GLCode = 'DEFERREDREVENUE')
					OR
					(@GLAccountTypeID = 5 and GLCode = 'DEFERREDTAX')
				)
				and isSystemAccount = 1;

			SET @accountNameDef = @accountName + ' Deferred';

			IF EXISTS (select GLAccountID FROM dbo.tr_GLAccounts where orgID = @orgID and accountName = @accountNameDef and parentGLAccountID = @parentGLAccountIDDef and status <> 'D')
				SET @accountNameDef = @accountName + ' ' + cast(@GLAccountID as varchar(10)) + ' Deferred';

			EXEC dbo.tr_createGLAccount @orgID=@orgID, @AccountTypeID=5, @accountName=@accountNameDef, @accountCode=null, @GLCode=null, 
				@parentGLAccountID=@parentGLAccountIDDef, @invoiceProfileID=null, @isSystemAccount=1, @invoiceContentID=null, 
				@deferredGLAccountID=null, @salesTaxProfileID=null, @salesTaxTaxJarCategoryID=null, @recordedByMemberID=@recordedByMemberID, @GLAccountID=@deferredGLAccountID OUTPUT;
		END

		UPDATE dbo.tr_GLAccounts
		SET accountName = @accountName, 
			accountCode = @accountCode, 
			parentGLAccountID = nullIf(@parentGLAccountID,0),
			invoiceProfileID = @invoiceProfileID,
			invoiceContentID = @invoiceContentID,
			deferredGLAccountID = @deferredGLAccountID,
			salesTaxProfileID = @salesTaxProfileID,
			salesTaxTaxJarCategoryID = nullIf(@salesTaxTaxJarCategoryID,0)
		WHERE GLAccountID = @GLAccountID
		and orgID = @orgID;

		-- cleanup orphaned deferred accounts		
		IF @bypassDeferredCleanup = 0
			EXEC dbo.tr_deleteUnlinkedDeferredAccounts @orgID=@orgID, @recordedByMemberID=@recordedByMemberID;

		INSERT INTO platformQueue.dbo.queue_mongo (msgjson) 
		SELECT '{ "c":"auditLog", "d": { 
			"AUDITCODE":"ACCT", 
			"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ', 
			"SITEID":0, 
			"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
			"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
			"MESSAGE":"' + REPLACE(dbo.fn_cleanInvalidXMLChars('GL Account [' + @accountName + '] updated.'),'"','\"') + '" } }';
	COMMIT TRAN;
 
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
