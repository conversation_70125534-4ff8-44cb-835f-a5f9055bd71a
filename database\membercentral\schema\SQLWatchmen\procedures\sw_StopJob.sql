----------------------------------------------------------------------------------------------------
/* Query #55: PASSED */
ALTER PROCEDURE [dbo].[sw_StopJob]
(
	@JobName NVARCHAR(128) = NULL,
	@ElapsedTimeCutoff SMALLINT = 0
)
AS
BEGIN
	SET NOCOUNT ON

	IF @JobName IS NULL
	BEGIN
		PRINT 'Please Provide a Job Name using @JobName'
		RETURN
	END

	DECLARE @MinutesElapsed INT = (SELECT DATEDIFF(minute, sja.start_execution_date, GETDATE())
	FROM msdb.dbo.sysjobactivity AS sja
	INNER JOIN msdb.dbo.sysjobs AS sj ON sja.job_id = sj.job_id
	WHERE sja.start_execution_date IS NOT NULL
	AND sja.stop_execution_date IS NULL
	AND sj.name = @JobName 
	AND session_id = (SELECT MAX(session_id) FROM msdb.dbo.sysjobactivity))

	IF @MinutesElapsed IS NULL
	BEGIN
		PRINT 'Job Not Running'
		RETURN
	END
	ELSE IF @MinutesElapsed < @ElapsedTimeCutoff 
	BEGIN
		PRINT CAST(@MinutesElapsed AS VARCHAR) + ' Minutes Elapsed'
		PRINT 'Cutoff Threshold: ' + CAST(@ElapsedTimeCutoff AS VARCHAR) 
		RETURN
	END
	ELSE
	BEGIN
		PRINT 'Kill Job' + CHAR(13) +
		CAST(@MinutesElapsed AS VARCHAR) + ' Minutes Elapsed'
		PRINT 'Cutoff Threshold: ' + CAST(@ElapsedTimeCutoff AS VARCHAR)
		EXEC msdb.dbo.sp_stop_job @job_name = @JobName
	END
	
END
GO
