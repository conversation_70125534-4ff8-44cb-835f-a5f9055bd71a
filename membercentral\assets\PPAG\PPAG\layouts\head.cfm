<cfset local.strMenus = application.objCMS.getPageMenus(event=event)>
<cfoutput>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>#event.getValue('mc_pageDefinition.pagetitle',event.getValue('mc_siteInfo.sitename'))#</title>
	#application.objCMS.getBootstrapHeadHTML()#
	
	<link rel="icon" href="/images/favicon.ico" type="image/x-icon">
	<link rel="shortcut icon" href="/images/favicon.ico">
		
	<cfif event.getValue('mc_pageDefinition.layoutMode','normal') NEQ "direct">
		<cfif len(event.getValue('mc_pageDefinition.pagekeywords',''))>
			<meta name="keywords" content="#event.getValue('mc_pageDefinition.pagekeywords','')#">
		</cfif>
		<cfif len(event.getValue('mc_pageDefinition.pageDescription',''))>
			<meta name="description" content="#event.getValue('mc_pageDefinition.pageDescription','')#">
		</cfif>				
		
		<link rel="stylesheet" href="/css/dropDown.css" />
		<script src="/javascript/modernizr.custom.js"></script> 		
		<link rel="stylesheet" href="/css/styles.css" />
		<link rel="stylesheet" href="/css/styles_ref.css" />

		#application.objCMS.getFontAwesomeHTML(includeVersion4Support=false)#
		
		<link rel="stylesheet" type="text/css" href="/assets/common/javascript/jQueryAddons/mmenu/jquery.mmenu.all.css" />
		<script type="text/javascript" src="/assets/common/javascript/jQueryAddons/mmenu/jquery.mmenu.min.all.js"></script>
		<script type="text/javascript" src="/assets/common/javascript/hideaddrbar.js"></script>
		
		<script type="text/javascript" src="/assets/common/javascript/commonBootstrapMobile.js"></script>
		
	</cfif>
	
	<!-- Style -->	     
	<link rel="stylesheet" type="text/css" href="/css/styles_ref.css" />
	<script src="/javascript/modernizr.custom.js"></script> 
	<link rel="stylesheet" type="text/css" href="/css/styles.css" />
	<link rel="stylesheet" type="text/css" href="/css/main.css" />
	<!-- Responsive -->
	<link rel="stylesheet" type="text/css" href="/css/responsive.css" />
	
	<style type="text/css">
		##mmenuPageWrapper{padding-top:0px !important;}
		.Footer-link h2 {margin-top:15px !important;}
	
	<!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
	<!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
	<!--[if lt IE 9]>
	      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
	      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
	    <![endif]-->	
		
		<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		  ##noRightsLoggedOut{display:none;}
			##noRightsLoggedIn{display:block;}
  		<cfelse>
			##noRightsLoggedOut{display:block;}
			##noRightsLoggedIn{display:none;}
	  </cfif>	
	</style>
	
	#application.objCMS.getResponsiveHeadHTML()#
</cfoutput>