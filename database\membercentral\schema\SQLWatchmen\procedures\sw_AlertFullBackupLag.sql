ALTER PROCEDURE [dbo].[sw_AlertFullBackupLag]
(
	@FullBackupLagAlertThreshold INT = NULL,
	@FullBackupLagExcludedDatabases NVARCHAR(MAX) = NULL,
	@MailProfileName NVARCHAR(MAX) = NULL,
	@AlertEmail NVARCHAR(MAX) = NULL,
	@Company NVARCHAR(MAX) = NULL,
	@Configuration NVARCHAR(MAX) = NULL
)
AS
BEGIN
	SET NOCOUNT ON;

    IF @FullBackupLagAlertThreshold IS NULL
    BEGIN
        SET @FullBackupLagAlertThreshold = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'FullBackupLagAlertThreshold')
    END

    IF @FullBackupLagExcludedDatabases IS NULL
    BEGIN
        SET @FullBackupLagExcludedDatabases = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'FullBackupLagExcludedDatabases')
    END

    IF OBJECT_ID('tempdb..#FullBackupLagAlert') IS NOT NULL
    BEGIN
        DROP TABLE #FullBackupLagAlert
    END

    CREATE TABLE #FullBackupLagAlert
    (
        [Database] NVARCHAR(128) NOT NULL,
        [LastFullBackupDate] DATETIME NOT NULL,
        [BackupAgeHours] INT NOT NULL
    );

    WITH lastFullBackup AS (
        SELECT
            [database_name],
            MAX([backup_finish_date]) AS [LastFullBackupDate]
        FROM [msdb].[dbo].[backupset]
        WHERE [type] = 'D'
        GROUP BY [database_name]
    )
    INSERT INTO #FullBackupLagAlert ([Database], [LastFullBackupDate], [BackupAgeHours])
    SELECT 
        [d].[name] AS [Database],
        COALESCE([lfb].[LastFullBackupDate], '1900-01-01 00:00:00.000') AS [LastFullBackupDate],
        DATEDIFF(HOUR, COALESCE([lfb].[LastFullBackupDate], '1900-01-01 00:00:00.000'), GETDATE()) AS [BackupAgeHours]
    FROM [master].[sys].[databases] AS [d]
    LEFT JOIN [lastFullBackup] AS [lfb] ON 
        [d].[name] = [lfb].[database_name]
    WHERE 
        [d].[database_id] <> 2
        AND (',' + @FullBackupLagExcludedDatabases + ',' NOT LIKE '%,' + [d].[name] + ',%');

    IF EXISTS (SELECT * FROM #FullBackupLagAlert WHERE [BackupAgeHours] > @FullBackupLagAlertThreshold)
    BEGIN

        IF @MailProfileName IS NULL
		BEGIN
			SELECT @MailProfileName = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'MailProfileName'
		END

		IF @AlertEmail IS NULL
		BEGIN
			SELECT @AlertEmail = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'AlertEmail'
		END

		IF @Company IS NULL
		BEGIN
			SELECT @Company = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Company'
		END

		IF @Configuration IS NULL
		BEGIN
			SELECT @Configuration = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Configuration'
		END

        DECLARE @Subject NVARCHAR(255)
        DECLARE @Message NVARCHAR(MAX)

        SET @Subject = 'Full Backup Lag Alert'
        SET @Message = 'The following databases have not had a full backup in over ' + CAST(@FullBackupLagAlertThreshold AS NVARCHAR) + ' hours:' + CHAR(13) + CHAR(10) + CHAR(13) + CHAR(10)

        SELECT 
            @Message += [Database] + ' | ' + CAST([BackupAgeHours] AS NVARCHAR) + ' hours' + CHAR(13) + CHAR(10)
        FROM #FullBackupLagAlert
        WHERE [BackupAgeHours] > @FullBackupLagAlertThreshold

        INSERT INTO [SQLWatchmen].[dbo].[AlertLog] ([Alert]) VALUES ('FullBackupLag')

        EXEC msdb.dbo.sp_send_dbmail 
            @profile_name = @MailProfileName,
            @recipients = @AlertEmail,
            @subject = @Subject,
            @body = @Message

    END

    IF OBJECT_ID('tempdb..#FullBackupLagAlert') IS NOT NULL
    BEGIN
        DROP TABLE #FullBackupLagAlert
    END

END
GO
