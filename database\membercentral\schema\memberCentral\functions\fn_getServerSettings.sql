CREATE FUNCTION dbo.fn_getServerSettings()
RETURNS TABLE 
AS
RETURN (

	select 
		tier = 
			case
			when @@SERVERNAME IN ('MCDEVAG1') then 'Development'
			when @@SERVERNAME IN ('MCBETAAG1') then 'Beta'
			else 'Production'
			end,
		siteHostnameEnvironmentList = 
			case
			when @@SERVERNAME IN ('MCDEVAG1') then 'localDevelopment,test,development,newtest,newdevelopment'
			when @@SERVERNAME IN ('MCBETAAG1') then 'beta,newbeta'
			else 'production'
			end,
		backendTempPath =
			case
			when @@SERVERNAME IN ('MCDEVAG1') then '\\mcdev01\backendroot\temp\'
			when @@SERVERNAME IN ('MCBETAAG1') then '\\mcbeta01\backendroot\temp\'
			else '\\mcfile01\platformtemp\'
			end,
		siteDocumentsPath =
			case
			when @@SERVERNAME IN ('MCDEVAG1') then '\\mcdev01\siteDocuments\'
			when @@SERVERNAME IN ('MCBETAAG1') then '\\mcbeta01\siteDocuments\'
			else '\\mcfile01\platform\siteDocuments\'
			end,
		userAssetsPath =
			case
			when @@SERVERNAME IN ('MCDEVAG1') then '\\mcdev01\wwwroot\membercentral\userassets\'
			when @@SERVERNAME IN ('MCBETAAG1') then '\\mcbeta01\wwwroot\membercentral\userassets\'
			else '\\mcfile01\mcuserassets\'
			end,
		seminarwebfiles =
			case
			when @@SERVERNAME IN ('MCDEVAG1') then '\\mcdev01\seminarwebfiles\'
			when @@SERVERNAME IN ('MCBETAAG1') then '\\mcbeta01\seminarwebfiles\'
			else '\\mcfile01\seminarwebfiles\'
			end,
		depositionTextPath =
			case
			when @@SERVERNAME IN ('MCDEVAG1') then '\\mcdev01\tlasites\depodocuments\'
			when @@SERVERNAME IN ('MCBETAAG1') then '\\mcbeta01\tlasites\depodocuments\'
			else '\\mcfile01\depos\txt\'
			end,
		sharedTempNoWebPath =
			case
			when @@SERVERNAME IN ('MCDEVAG1','MCBETAAG1') then '/app/backendroot/temp/'
			else '/app/platformtemp/'
			end,
		sharedTempNoWebS3UploaderPath =
			case
			when @@SERVERNAME IN ('MCDEVAG1','MCBETAAG1') then 'c:\backendroot\temp\'
			else '/srv/sanstorage/isilion/databasebackups/samba/platformtemp/'
			end,
		mcapiSecret = '8e91806SW3d544eTS4490a00MCdf3TLd',
		regexNameFields = '([<>%$^~!{}\[\]]+|(\/\*)+|(http)+)',
		regexCompany = '([<>{}]+|(\/\*)+|(http)+)',
		regexAddrAttn = '([<>%$^~!{}\[\]]+|(\/\*)+|(http)+)', 
		regexAddrFields = '([<>%$^~{}]+|(\/\*)+|(http)+)', 
		regexaddrZip = '[^a-zA-Z0-9 \-]', 
		regexPhone = '([<>%$^~{}]+|(\/\*)+|(http)+)',
		regexMergeCode = '(?<=\[\[)([^,\]]+)(?=,?([^\]]+)?\]\])'

)
GO
