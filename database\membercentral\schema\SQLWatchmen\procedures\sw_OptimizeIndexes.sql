----------------------------------------------------------------------------------------------------
/* Query #52: PASSED */
ALTER PROCEDURE [dbo].[sw_OptimizeIndexes]
(
    @OptimizeIndexesDatabases nvarchar(max) = NULL,
    @OptimizeIndexesFragmentationLow nvarchar(max) = NULL,
    @OptimizeIndexesFragmentationMedium nvarchar(max) = NULL,
    @OptimizeIndexesFragmentationHigh nvarchar(max) = NULL,
    @OptimizeIndexesFragmentationLevel1 int = NULL,
    @OptimizeIndexesFragmentationLevel2 int = NULL,
    @OptimizeIndexesMinNumberOfPages int = NULL,
    @OptimizeIndexesMaxNumberOfPages int = NULL,
    @OptimizeIndexesSortInTempdb nvarchar(max) = NULL,
    @OptimizeIndexesMaxDOP int = NULL,
    @OptimizeIndexesStatisticsModificationLevel int = NULL,
    @OptimizeIndexesStatisticsSample int = NULL,
    @OptimizeIndexesTimeLimit int = NULL,
    @OptimizeIndexesWaitAtLowPriorityMaxDuration int = NULL,
    @OptimizeIndexesWaitAtLowPriorityAbortAfterWait nvarchar(max) = NULL,
    @OptimizeIndexesResumable nvarchar(max) = NULL,
    @OptimizeIndexesDatabaseOrder nvarchar(max) = NULL
)
AS
BEGIN
    IF @OptimizeIndexesDatabases IS NULL
    BEGIN
        SET @OptimizeIndexesDatabases = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'OptimizeIndexesDatabases')
    END

    IF @OptimizeIndexesFragmentationLow IS NULL
    BEGIN
        SET @OptimizeIndexesFragmentationLow = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'OptimizeIndexesFragmentationLow')
    END

    IF @OptimizeIndexesFragmentationMedium IS NULL
    BEGIN
        SET @OptimizeIndexesFragmentationMedium = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'OptimizeIndexesFragmentationMedium')
    END

    IF @OptimizeIndexesFragmentationHigh IS NULL
    BEGIN
        SET @OptimizeIndexesFragmentationHigh = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'OptimizeIndexesFragmentationHigh')
    END

    IF @OptimizeIndexesFragmentationLevel1 IS NULL
    BEGIN
        SET @OptimizeIndexesFragmentationLevel1 = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'OptimizeIndexesFragmentationLevel1')
    END

    IF @OptimizeIndexesFragmentationLevel2 IS NULL
    BEGIN
        SET @OptimizeIndexesFragmentationLevel2 = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'OptimizeIndexesFragmentationLevel2')
    END

    IF @OptimizeIndexesMinNumberOfPages IS NULL
    BEGIN
        SET @OptimizeIndexesMinNumberOfPages = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'OptimizeIndexesMinNumberOfPages')
    END

    IF @OptimizeIndexesMaxNumberOfPages IS NULL
    BEGIN
        SET @OptimizeIndexesMaxNumberOfPages = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'OptimizeIndexesMaxNumberOfPages')
    END

    IF @OptimizeIndexesSortInTempdb IS NULL
    BEGIN
        SET @OptimizeIndexesSortInTempdb = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'OptimizeIndexesSortInTempdb')
    END

    IF @OptimizeIndexesMaxDOP IS NULL
    BEGIN
        SET @OptimizeIndexesMaxDOP = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'OptimizeIndexesMaxDOP')
    END

    IF @OptimizeIndexesStatisticsModificationLevel IS NULL
    BEGIN
        SET @OptimizeIndexesStatisticsModificationLevel = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'OptimizeIndexesStatisticsModificationLevel')
    END

    IF @OptimizeIndexesStatisticsSample IS NULL
    BEGIN
        SET @OptimizeIndexesStatisticsSample = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'OptimizeIndexesStatisticsSample')
    END

    IF @OptimizeIndexesTimeLimit IS NULL
    BEGIN
        SET @OptimizeIndexesTimeLimit = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'OptimizeIndexesTimeLimit')
    END

    IF @OptimizeIndexesWaitAtLowPriorityMaxDuration IS NULL
    BEGIN
        SET @OptimizeIndexesWaitAtLowPriorityMaxDuration = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'OptimizeIndexesWaitAtLowPriorityMaxDuration')
    END

    IF @OptimizeIndexesWaitAtLowPriorityAbortAfterWait IS NULL
    BEGIN
        SET @OptimizeIndexesWaitAtLowPriorityAbortAfterWait = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'OptimizeIndexesWaitAtLowPriorityAbortAfterWait')
    END

    IF @OptimizeIndexesResumable IS NULL
    BEGIN
        SET @OptimizeIndexesResumable = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'OptimizeIndexesResumable')
    END

    IF @OptimizeIndexesDatabaseOrder IS NULL
    BEGIN
        SET @OptimizeIndexesDatabaseOrder = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'OptimizeIndexesDatabaseOrder')
    END

    EXEC IndexOptimize
        @Databases = @OptimizeIndexesDatabases,
        @FragmentationLow = @OptimizeIndexesFragmentationLow,
        @FragmentationMedium = @OptimizeIndexesFragmentationMedium,
        @FragmentationHigh = @OptimizeIndexesFragmentationHigh,
        @FragmentationLevel1 = @OptimizeIndexesFragmentationLevel1,
        @FragmentationLevel2 = @OptimizeIndexesFragmentationLevel2,
        @MinNumberOfPages = @OptimizeIndexesMinNumberOfPages,
        @MaxNumberOfPages = @OptimizeIndexesMaxNumberOfPages,
        @SortInTempdb = @OptimizeIndexesSortInTempdb,
        @MaxDOP = @OptimizeIndexesMaxDOP,
        @UpdateStatistics = 'ALL',
        @OnlyModifiedStatistics = 'Y',
        @StatisticsModificationLevel = @OptimizeIndexesStatisticsModificationLevel,
        @StatisticsSample = @OptimizeIndexesStatisticsSample,
        @MSShippedObjects = 'Y',
        @TimeLimit = @OptimizeIndexesTimeLimit,
        @WaitAtLowPriorityMaxDuration = @OptimizeIndexesWaitAtLowPriorityMaxDuration,
        @WaitAtLowPriorityAbortAfterWait = @OptimizeIndexesWaitAtLowPriorityAbortAfterWait,
        @Resumable = @OptimizeIndexesResumable,
        @DatabaseOrder = @OptimizeIndexesDatabaseOrder,
        @LogToTable = 'Y'
        
END
GO
