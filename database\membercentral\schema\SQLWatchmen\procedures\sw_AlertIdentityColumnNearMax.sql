----------------------------------------------------------------------------------------------------
/* Query #33: PASSED */
ALTER PROCEDURE [dbo].[sw_AlertIdentityColumnNearMax]
(
	@IdentityColumnAlertThreshold NVARCHAR(128) = NULL,
	@IdentityColumnExcludedDatabases NVARCHAR(128) = NULL,
	@IdentityColumnExcludedTables NVARCHAR(128) = NULL,
	@MailProfileName varchar(128) = NULL,
	@AlertEmail varchar(128) = NULL,
	@Company varchar(128) = NULL,
	@Configuration varchar(128) = NULL
)
AS
BEGIN
	SET NOCOUNT ON

	IF @IdentityColumnAlertThreshold IS NULL
	BEGIN
		SET @IdentityColumnAlertThreshold = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'IdentityColumnAlertThreshold')
	END

	IF @IdentityColumnExcludedDatabases IS NULL
	BEGIN
		SET @IdentityColumnExcludedDatabases = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'IdentityColumnExcludedDatabases')
	END

	IF @IdentityColumnExcludedTables IS NULL
	BEGIN
		SET @IdentityColumnExcludedTables = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'IdentityColumnExcludedTables')
	END

	IF OBJECT_ID('tempdb..#IdentityColumnAlertDatabaseList') IS NOT NULL
	BEGIN
		DROP TABLE #IdentityColumnAlertDatabaseList
	END

	IF OBJECT_ID('tempdb..#IdentityColumnAlert') IS NOT NULL
	BEGIN
		DROP TABLE #IdentityColumnAlert
	END

	CREATE TABLE #IdentityColumnAlertDatabaseList 
	(
		[Row] INT IDENTITY NOT NULL,
		[Database] NVARCHAR(128) NOT NULL
	)

	CREATE TABLE #IdentityColumnAlert
	(
		[Database] NVARCHAR(128) NOT NULL,
		[Schema] NVARCHAR(128) NOT NULL,
		[Table] NVARCHAR(128) NOT NULL,
		[Column] NVARCHAR(128) NOT NULL,
		[IdentityValue] NVARCHAR(128) NOT NULL,
		[DataType] NVARCHAR(8) NOT NULL
	)
	
	INSERT INTO #IdentityColumnAlertDatabaseList ([Database])
	SELECT [name] 
	FROM sys.databases 
	WHERE (',' + @IdentityColumnExcludedDatabases + ',' NOT LIKE '%,' + [name] + ',%')

	DECLARE @Row SMALLINT = 1
	DECLARE @TotalRows SMALLINT = (SELECT COUNT(*) FROM #IdentityColumnAlertDatabaseList)
	DECLARE @SQL NVARCHAR(MAX)
	DECLARE @Database NVARCHAR(128)

	SET @IdentityColumnAlertThreshold = CAST(CAST(@IdentityColumnAlertThreshold AS FLOAT)/100 AS NVARCHAR(128))

	WHILE @Row <= @TotalRows 
	BEGIN
		SET @Database = (SELECT [Database] FROM #IdentityColumnAlertDatabaseList WHERE [Row] = @Row)
		SET @SQL = 
			'USE [' + @Database + '];' + CHAR(13) + CHAR(10) +
			'IF (SELECT TOP 1 [name] FROM sys.identity_columns WHERE [last_value] >' + CHAR(13) + CHAR(10) +
			'(CASE system_type_id WHEN 127 THEN (9223372036854775807 * ' + @IdentityColumnAlertThreshold + ')' + CHAR(13) + CHAR(10) +
			'WHEN 56 THEN (2147483647 * ' + @IdentityColumnAlertThreshold + ')' + CHAR(13) + CHAR(10) +
			'WHEN 52	THEN (32767 * ' + @IdentityColumnAlertThreshold + ')' + CHAR(13) + CHAR(10) +
			'WHEN 48	THEN (255 * ' + @IdentityColumnAlertThreshold + ')' + CHAR(13) + CHAR(10) +
			'END)' + CHAR(13) + CHAR(10) +
			'AND ('',' + @IdentityColumnExcludedTables + ','' NOT LIKE ''%,'' + OBJECT_NAME([object_id]) + '',%'')) IS NOT NULL' + CHAR(13) + CHAR(10) +
			'BEGIN' + CHAR(13) + CHAR(10) +
				'INSERT INTO #IdentityColumnAlert' + CHAR(13) + CHAR(10) +
				'SELECT ''' + @Database + ''',' + CHAR(13) + CHAR(10) +
				'OBJECT_SCHEMA_NAME(object_id),' + CHAR(13) + CHAR(10) +
				'OBJECT_NAME(object_id),' + CHAR(13) + CHAR(10) +
				'[name],' + CHAR(13) + CHAR(10) +
				'CAST([last_value] AS NVARCHAR),' + CHAR(13) + CHAR(10) +
				'CASE system_type_id' + CHAR(13) + CHAR(10) +
					'WHEN 127 THEN ''BIGINT''' + CHAR(13) + CHAR(10) +
					'WHEN 56	THEN ''INT''' + CHAR(13) + CHAR(10) +
					'WHEN 52	THEN ''SMALLINT''' + CHAR(13) + CHAR(10) +
					'WHEN 48	THEN ''TINYINT''' + CHAR(13) + CHAR(10) +
					'END' + CHAR(13) + CHAR(10) +
				'FROM sys.identity_columns WHERE [last_value] >' + CHAR(13) + CHAR(10) +
				'(CASE system_type_id WHEN 127 THEN (9223372036854775807 * ' + @IdentityColumnAlertThreshold + ')' + CHAR(13) + CHAR(10) +
				'WHEN 56 THEN (2147483647 * ' + @IdentityColumnAlertThreshold + ')' + CHAR(13) + CHAR(10) +
				'WHEN 52	THEN (32767 * ' + @IdentityColumnAlertThreshold + ')' + CHAR(13) + CHAR(10) +
				'WHEN 48	THEN (255 * ' + @IdentityColumnAlertThreshold + ')' + CHAR(13) + CHAR(10) +
				'END)' + CHAR(13) + CHAR(10) +
				'AND ('',' + @IdentityColumnExcludedTables + ','' NOT LIKE ''%,'' + OBJECT_NAME([object_id]) + '',%'')' + CHAR(13) + CHAR(10) +
			'END' + CHAR(13) + CHAR(10)

		EXEC (@SQL)

		SET @Row += 1
	END

	SET @IdentityColumnAlertThreshold = CAST(CAST(@IdentityColumnAlertThreshold AS FLOAT)*100 AS NVARCHAR(128))

	IF (SELECT TOP 1 [Database] FROM #IdentityColumnAlert) IS NOT NULL
	BEGIN
		IF @MailProfileName IS NULL
		BEGIN
			SELECT @MailProfileName = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'MailProfileName'
		END

		IF @AlertEmail IS NULL
		BEGIN
			SELECT @AlertEmail = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'AlertEmail'
		END

		IF @Company IS NULL
		BEGIN
			SELECT @Company = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Company'
		END

		IF @Configuration IS NULL
		BEGIN
			SELECT @Configuration = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Configuration'
		END

		DECLARE @Subject varchar(255)
		DECLARE @Body varchar(8000)

		SET @Subject = 'Identity Column Near Max | ' + @Configuration + ' | ' + @Company

		SET @Body = 'At least one table has a column that is over ' + @IdentityColumnAlertThreshold + '% of its maximum value.' + CHAR(13) + CHAR(10)
            + 'Please refer to the details listed below:' + CHAR(13) + CHAR(10)
        SELECT @Body += 'Table: ' + [Database] + '.' + [Schema] + '.' + [Table] + ' | ' + 'Identity Value: ' + [IdentityValue] + ' | ' + 'Data Type: ' + [DataType] + CHAR(13) + CHAR(10) FROM #IdentityColumnAlert

		EXEC msdb.dbo.sp_send_dbmail
			@profile_name = @MailProfileName,
			@recipients = @AlertEmail,
			@subject = @Subject,
			@body = @Body

		INSERT INTO [SQLWatchmen].[dbo].[AlertLog] ([Alert]) VALUES ('Identity Column Near Max')

	END

	IF OBJECT_ID('tempdb..#IdentityColumnAlertDatabaseList') IS NOT NULL
	BEGIN
		DROP TABLE #IdentityColumnAlertDatabaseList
	END

	IF OBJECT_ID('tempdb..#IdentityColumnAlert') IS NOT NULL
	BEGIN
		DROP TABLE #IdentityColumnAlert
	END

END
GO
