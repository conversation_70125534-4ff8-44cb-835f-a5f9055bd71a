USE platformQueue
GO

declare @queueTypeID int, @stringDataTypeID int;
select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'importEvents';
select @stringDataTypeID = dataTypeID from dbo.tblQueueTypesDataColumnDataTypes where dataTypeCode = 'STRING';

insert into dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID, ColumnDesc) 
values (@queueTypeID, 'EventSubTitle', @stringDataTypeID, null);
GO

USE membercentral
GO

ALTER PROC dbo.ev_importEventFromQueue
@itemUID uniqueidentifier

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @recordedByMemberID int, @siteID int, @orgID int, @defaultTimeZoneID int, @ovAction char(1), 
		@MCEventID int, @MCCalendarID int, @EventAllDay bit, @EventCode varchar(15), @EventHidden bit, 
		@ContactInclude bit, @LocationInclude bit, @CancellationInclude bit, @TravelInclude bit, @EventStart datetime, 
		@EventEnd datetime,	@eventContentID int, @EventTitle varchar(200), @EventSubTitle varchar(200), @EventDescription varchar(max), @MCCategoryIDList varchar(max),
		@origCalendarID int, @InternalNotes varchar(max), @RegistrationReplyEmail varchar(200), @MCRegistrationID int,
		@DisplayCredits bit, @expirationContentID int, @ContactTitle varchar(200), @Contact varchar(max), @contentTitle varchar(200), 
		@rawContent varchar(max), @LocationTitle varchar(200), @Location varchar(max), @CancellationTitle varchar(200), 
		@Cancellation varchar(max), @TravelTitle varchar(200), @Travel varchar(max), @InformationTitle varchar(200), 
		@Information varchar(max), @locationContentID int, @cancellationPolicyContentID int, @travelContentID int,
		@informationContentID int, @subEventID int, @existingEvent bit, @ASID int, @crdAuthorityCode varchar(20),
		@crdApproval varchar(50), @crdStatus varchar(20), @crdOfferedID int, @crdStatusID int, @ASTID int, @crdColName sysname,
		@crdValue decimal(6,2), @contactContentID int, @MCParentEventID int, @ParentEventCode varchar(15), @crdupdated bit, 
		@itemStatus int, @statusReady int, @EnableRealTimeRoster bit, @eventSiteResourceID int, @minFieldID int, 
		@fieldReference varchar(128), @dataTypeCode varchar(20), @displayTypeCode varchar(20), @timeID int, 
		@eventAdminSRID int, @crossEventFieldUsageID int, @fieldValue varchar(max), @dataID int, @recurringEvents bit, 
		@RecurringSeriesCode varchar(15), @createdFromEventID int, @recurringSeriesID int, @recurrenceOrder int,
		@recurrenceAFID int, @lockTimeZoneID int, @datePart varchar(20), @dateNum int, @adjustTerm varchar(12), 
		@nextWeekday int, @weekNumber varchar(4), @defaultTimeID int, @lockedTimeID int, @isRecurringEvent bit = 0;
	declare @tblPossibleCredits TABLE (ASID int, authorityID int, authorityCode varchar(20));
	declare @tblPossibleCreditCols TABLE (ASID int, column_name sysname, ASTID int);
	declare @tblCrossEventFields TABLE (fieldID int, fieldReference varchar(128), displayTypeCode varchar(20), dataTypeCode varchar(20));

	-- if itemUID is not readyToProcess, kick out now
	select @statusReady = qs.queueStatusID 
		from platformQueue.dbo.tblQueueStatuses as qs
		inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueType = 'importEvents'
		and qs.queueStatus = 'readyToProcess';
	select @itemStatus = queueStatusID
		from platformQueue.dbo.tblQueueItems
		where itemUID = @itemUID;
	IF @itemStatus is null
		RETURN 0;
	IF @itemStatus <> @statusReady
		RETURN 0;

	-- update status
	EXEC platformQueue.dbo.queue_setStatus @queueType='importEvents', @itemUID=@itemUID, @queueStatus='processingEvent';

	IF OBJECT_ID('tempdb..#tmpEVQueueData') IS NOT NULL 
		DROP TABLE #tmpEVQueueData;

	select qid.columnID, dc.columnname, qid.columnValueString, qid.columnValueDecimal2, qid.columnValueInteger, qid.columnvalueDate, 
		qid.columnValueBit, qid.columnValueText
	into #tmpEVQueueData
	from platformQueue.dbo.tblQueueItemData as qid
	inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
	where qid.itemUID = @itemUID;

	select top 1 @recordedByMemberID=recordedByMemberID, @siteID=siteID
	from platformQueue.dbo.tblQueueItemData
	where itemUID = @itemUID;

	select @orgID=orgID, @defaultTimeZoneID=defaultTimeZoneID from dbo.sites where siteID=@siteID;
	select @ovAction = columnValueString from #tmpEVQueueData where columnname = 'ovAction';
	select @MCEventID = columnValueInteger from #tmpEVQueueData where columnname = 'MCEventID';
	select @MCRegistrationID = columnValueInteger from #tmpEVQueueData where columnname = 'MCRegistrationID';
	select @EventStart = columnValueDate from #tmpEVQueueData where columnname = 'EventStart';
	select @EventEnd = columnValueDate from #tmpEVQueueData where columnname = 'EventEnd';

	IF @MCEventID is not null
		set @existingEvent = 1;
	ELSE
		set @existingEvent = 0;

	SELECT @recurringEvents = recurringEvents
	FROM dbo.siteFeatures 
	WHERE siteID = @siteID;

	IF @recurringEvents = 1 BEGIN
		IF @existingEvent = 1 AND EXISTS (SELECT 1 FROM dbo.ev_events WHERE eventID = @MCEventID AND siteID = @siteID AND recurringSeriesID IS NOT NULL)
			SET @isRecurringEvent = 1;
		ELSE IF @existingEvent = 0 BEGIN
			select @RecurringSeriesCode = isnull((select columnValueString from #tmpEVQueueData where columnname = 'RecurringSeriesCode'),'');

			IF LEN(@RecurringSeriesCode) > 0
				SET @isRecurringEvent = 1;
		END
	END
	
	BEGIN TRAN;
		-- if we are updating event with overwrite setting
		IF @existingEvent = 1 and @ovAction = 'o' BEGIN
			select @MCCalendarID = columnValueInteger from #tmpEVQueueData where columnname = 'MCCalendarID';
			select @MCCategoryIDList = replace(columnValueText,'|',',') from #tmpEVQueueData where columnname = 'MCCategoryIDList';
			select @origCalendarID = calendarID from dbo.ev_calendarEvents where sourceEventID = @MCEventID and calendarID = sourceCalendarID;

			-- move to diff calendar if necessary (this also moves categories)
			IF (@MCCalendarID != @origCalendarID) BEGIN
				EXEC dbo.ev_moveCalendarEvent @eventID=@MCEventID, @fromCalendarID=@origCalendarID, @toCalendarID=@MCCalendarID, 
					@toCategoryIDList=@MCCategoryIDList, @recordedByMemberID=@recordedByMemberID;
			END

			-- set new category
			IF (@MCCalendarID = @origCalendarID) BEGIN
				delete from dbo.ev_eventCategories
				where eventID = @MCEventID;

				INSERT INTO dbo.ev_eventCategories (eventID, categoryID, categoryOrder)
				SELECT @MCEventID, li.listItem, li.autoID
				from dbo.fn_intListToTable(@MCCategoryIDList,',') as li;
			END

			-- update changed fields
			select @EventAllDay = columnValueBit from #tmpEVQueueData where columnname = 'EventAllDay';
			select @EventHidden = columnValueBit from #tmpEVQueueData where columnname = 'EventHidden';
			select @ContactInclude = columnValueBit from #tmpEVQueueData where columnname = 'ContactInclude';
			select @LocationInclude = columnValueBit from #tmpEVQueueData where columnname = 'LocationInclude';
			select @CancellationInclude = columnValueBit from #tmpEVQueueData where columnname = 'CancellationInclude';
			select @TravelInclude = columnValueBit from #tmpEVQueueData where columnname = 'TravelInclude';
			select @InternalNotes = columnValueText from #tmpEVQueueData where columnname = 'InternalNotes';

			--determine if times need to be forced to full day, if allDayEvent
			select @EventAllDay = isnull(@EventAllDay,isAllDayEvent)
			from dbo.ev_events
			where eventID = @MCEventID;

			if (@EventAllDay = 1) BEGIN
				set @EventStart = Convert(DateTime, DATEDIFF(DAY, 0, @EventStart));
				set @EventEnd = dateadd(ms,-3,Convert(DateTime, DATEDIFF(DAY, -1, @EventEnd)));
			END

			update dbo.ev_events
			set isAllDayEvent = @EventAllDay,
				hiddenFromCalendar = isnull(@EventHidden,hiddenFromCalendar),
				emailContactContent = isnull(@ContactInclude,emailContactContent),
				emailLocationContent = isnull(@LocationInclude,emailLocationContent),
				emailCancelContent = isnull(@CancellationInclude,emailCancelContent),
				emailTravelContent = isnull(@TravelInclude,emailTravelContent),
				internalNotes = isnull(@InternalNotes,internalNotes)
			where eventID = @MCEventID;

			-- update event times
			IF @isRecurringEvent = 0 BEGIN
				UPDATE dbo.ev_events set defaultTimeID = null, lockedTimeID = null WHERE eventID = @MCEventID;
				delete from dbo.ev_times where eventID = @MCEventID;
				EXEC dbo.ev_createTime @eventID=@MCEventID, @timeZoneID=@defaultTimeZoneID, @startTime=@EventStart, @endTime=@EventEnd, @timeID=@timeID OUTPUT;
			END

			-- update title/description
			select @EventTitle = columnValueString from #tmpEVQueueData where columnname = 'EventTitle';
			select @EventDescription = columnValueText from #tmpEVQueueData where columnname = 'EventDescription';

			select @eventContentID = eventContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
			select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@eventContentID,1);
			set @EventDescription = isnull(@EventDescription,@rawContent);
			EXEC dbo.cms_updateContent @contentID=@eventContentID, @languageID=1, @isHTML=1, 
				@contentTitle=@EventTitle, @contentDesc='', @rawcontent=@EventDescription;

			-- update other content objects
			select @ContactTitle = columnValueString from #tmpEVQueueData where columnname = 'ContactTitle';
			select @Contact = columnValueText from #tmpEVQueueData where columnname = 'Contact';
			select @LocationTitle = columnValueString from #tmpEVQueueData where columnname = 'LocationTitle';
			select @Location = columnValueText from #tmpEVQueueData where columnname = 'Location';
			select @CancellationTitle = columnValueString from #tmpEVQueueData where columnname = 'CancellationTitle';
			select @Cancellation = columnValueText from #tmpEVQueueData where columnname = 'Cancellation';
			select @TravelTitle = columnValueString from #tmpEVQueueData where columnname = 'TravelTitle';
			select @Travel = columnValueText from #tmpEVQueueData where columnname = 'Travel';
			select @InformationTitle = columnValueString from #tmpEVQueueData where columnname = 'InformationTitle';
			select @Information = columnValueText from #tmpEVQueueData where columnname = 'Information';

			select @contactContentID = contactContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
			select @contentTitle=null, @rawContent=null;
			select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@contactContentID,1);
			set @ContactTitle = isnull(@ContactTitle,@contentTitle);
			set @Contact = isnull(@Contact,@rawContent);
			EXEC dbo.cms_updateContent @contentID=@contactContentID, @languageID=1, @isHTML=1, @contentTitle=@ContactTitle, 
				@contentDesc='', @rawcontent=@Contact;

			select @locationContentID = locationContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
			select @contentTitle=null, @rawContent=null;
			select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@locationContentID,1);
			set @LocationTitle = isnull(@LocationTitle,@contentTitle);
			set @Location = isnull(@Location,@rawContent);
			EXEC dbo.cms_updateContent @contentID=@locationContentID, @languageID=1, @isHTML=1, @contentTitle=@LocationTitle, 
				@contentDesc='', @rawcontent=@Location;

			select @cancellationPolicyContentID = cancellationPolicyContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
			select @contentTitle=null, @rawContent=null;
			select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@cancellationPolicyContentID,1);
			set @CancellationTitle = isnull(@CancellationTitle,@contentTitle);
			set @Cancellation = isnull(@Cancellation,@rawContent);
			EXEC dbo.cms_updateContent @contentID=@cancellationPolicyContentID, @languageID=1, @isHTML=1, @contentTitle=@CancellationTitle, 
				@contentDesc='', @rawcontent=@Cancellation;

			select @travelContentID = travelContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
			select @contentTitle=null, @rawContent=null;
			select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@travelContentID,1);
			set @TravelTitle = isnull(@TravelTitle,@contentTitle);
			set @Travel = isnull(@Travel,@rawContent);
			EXEC dbo.cms_updateContent @contentID=@travelContentID, @languageID=1, @isHTML=1, @contentTitle=@TravelTitle, 
				@contentDesc='', @rawcontent=@Travel;

			select @informationContentID = informationContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
			select @contentTitle=null, @rawContent=null;
			select @contentTitle=contentTitle, @rawContent=rawContent from dbo.fn_getContent(@informationContentID,1);
			set @InformationTitle = isnull(@InformationTitle,@contentTitle);
			set @Information = isnull(@Information,@rawContent);
			EXEC dbo.cms_updateContent @contentID=@informationContentID, @languageID=1, @isHTML=1, @contentTitle=@InformationTitle, 
				@contentDesc='', @rawcontent=@Information;
		END

		-- if we are adding new event
		IF @existingEvent = 0 BEGIN
			select @MCCalendarID = columnValueInteger from #tmpEVQueueData where columnname = 'MCCalendarID';
			select @MCCategoryIDList = columnValueText from #tmpEVQueueData where columnname = 'MCCategoryIDList';
			select @EventAllDay = isnull((select columnValueBit from #tmpEVQueueData where columnname = 'EventAllDay'),0);
			select @EventCode = columnValueString from #tmpEVQueueData where columnname = 'EventCode';
			select @EventHidden = isnull((select columnValueBit from #tmpEVQueueData where columnname = 'EventHidden'),0);
			select @ContactInclude = isnull((select columnValueBit from #tmpEVQueueData where columnname = 'ContactInclude'),0);
			select @LocationInclude = isnull((select columnValueBit from #tmpEVQueueData where columnname = 'LocationInclude'),0);
			select @CancellationInclude = isnull((select columnValueBit from #tmpEVQueueData where columnname = 'CancellationInclude'),0);
			select @TravelInclude = isnull((select columnValueBit from #tmpEVQueueData where columnname = 'TravelInclude'),0);
			select @EventTitle = columnValueString from #tmpEVQueueData where columnname = 'EventTitle';
			select @EventSubTitle = columnValueString from #tmpEVQueueData where columnname = 'EventSubTitle';
			select @EventDescription = isnull((select columnValueText from #tmpEVQueueData where columnname = 'EventDescription'),'');
			select @InternalNotes = isnull((select columnValueText from #tmpEVQueueData where columnname = 'InternalNotes'),'');
			select @ContactTitle = isnull((select columnValueString from #tmpEVQueueData where columnname = 'ContactTitle'),'');
			select @Contact = isnull((select columnValueText from #tmpEVQueueData where columnname = 'Contact'),'');
			select @LocationTitle = isnull((select columnValueString from #tmpEVQueueData where columnname = 'LocationTitle'),'');
			select @Location = isnull((select columnValueText from #tmpEVQueueData where columnname = 'Location'),'');
			select @CancellationTitle = isnull((select columnValueString from #tmpEVQueueData where columnname = 'CancellationTitle'),'');
			select @Cancellation = isnull((select columnValueText from #tmpEVQueueData where columnname = 'Cancellation'),'');
			select @TravelTitle = isnull((select columnValueString from #tmpEVQueueData where columnname = 'TravelTitle'),'');
			select @Travel = isnull((select columnValueText from #tmpEVQueueData where columnname = 'Travel'),'');
			select @InformationTitle = isnull((select columnValueString from #tmpEVQueueData where columnname = 'InformationTitle'),'');
			select @Information = isnull((select columnValueText from #tmpEVQueueData where columnname = 'Information'),'');

			--determine if times need to be forced to full day, if allDayEvent
			if (@EventAllDay = 1) BEGIN
				set @EventStart = Convert(DateTime, DATEDIFF(DAY, 0, @EventStart));
				set @EventEnd = dateadd(ms,-3,Convert(DateTime, DATEDIFF(DAY, -1, @EventEnd)));
			END

			EXEC dbo.ev_createEvent @siteID=@siteID, @calendarid=@MCCalendarID, @eventTypeID=1, @enteredByMemberID=@recordedByMemberID, 
				@eventSubTitle=@EventSubTitle, @lockTimeZoneID=null, @isAllDayEvent=@EventAllDay, @altRegistrationURL=null, @status='A', 
				@reportCode=@EventCode, @hiddenFromCalendar=@EventHidden, @emailContactContent=@ContactInclude, 
				@emailLocationContent=@LocationInclude, @emailCancelContent=@CancellationInclude, @emailTravelContent=@TravelInclude, 
				@eventID=@MCEventID OUTPUT;
			EXEC dbo.ev_createTime @eventID=@MCEventID, @timeZoneID=@defaultTimeZoneID, @startTime=@EventStart, @endTime=@EventEnd, @timeID=@timeID OUTPUT;

			IF len(@InternalNotes) > 0
				update dbo.ev_events
				set internalNotes = @InternalNotes
				where eventID = @MCEventID;

			INSERT INTO dbo.ev_eventCategories (eventID, categoryID, categoryOrder)
			SELECT @MCEventID, li.listItem, li.autoID
			from dbo.fn_intListToTable(@MCCategoryIDList,'|') as li;

			select @eventContentID = eventContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
			EXEC dbo.cms_updateContent @contentID=@eventContentID, @languageID=1, @isHTML=1, @contentTitle=@EventTitle, 
				@contentDesc='', @rawcontent=@EventDescription;

			IF LEN(@ContactTitle) > 0 or LEN(@Contact) > 0 BEGIN
				select @contactContentID = contactContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
				EXEC dbo.cms_updateContent @contentID=@contactContentID, @languageID=1, @isHTML=1, @contentTitle=@ContactTitle, 
					@contentDesc='', @rawcontent=@Contact;
			END

			IF LEN(@LocationTitle) > 0 or LEN(@Location) > 0 BEGIN
				select @locationContentID = locationContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
				EXEC dbo.cms_updateContent @contentID=@locationContentID, @languageID=1, @isHTML=1, @contentTitle=@LocationTitle, 
					@contentDesc='', @rawcontent=@Location;
			END

			IF LEN(@CancellationTitle) > 0 or LEN(@Cancellation) > 0 BEGIN
				select @cancellationPolicyContentID = cancellationPolicyContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
				EXEC dbo.cms_updateContent @contentID=@cancellationPolicyContentID, @languageID=1, @isHTML=1, @contentTitle=@CancellationTitle, 
					@contentDesc='', @rawcontent=@Cancellation;
			END

			IF LEN(@TravelTitle) > 0 or LEN(@Travel) > 0 BEGIN
				select @travelContentID = travelContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
				EXEC dbo.cms_updateContent @contentID=@travelContentID, @languageID=1, @isHTML=1, @contentTitle=@TravelTitle, 
					@contentDesc='', @rawcontent=@Travel;
			END

			IF LEN(@InformationTitle) > 0 or LEN(@Information) > 0 BEGIN
				select @informationContentID = informationContentID FROM dbo.ev_events WHERE eventID=@MCEventID;
				EXEC dbo.cms_updateContent @contentID=@informationContentID, @languageID=1, @isHTML=1, @contentTitle=@InformationTitle, 
					@contentDesc='', @rawcontent=@Information;
			END
		END 

		-- if we are updating registration with overwrite setting
		IF @existingEvent = 1 and @MCRegistrationID is not null and @ovAction = 'o' BEGIN
			select @RegistrationReplyEmail = columnValueString from #tmpEVQueueData where columnname = 'RegistrationReplyEmail';
			select @DisplayCredits = columnValueBit from #tmpEVQueueData where columnname = 'DisplayCredits';
			select @EnableRealTimeRoster = columnValueBit from #tmpEVQueueData where columnname = 'EnableRealTimeRoster';

			update dbo.ev_registration
			set replyToEmail = @RegistrationReplyEmail,
				showCredit = isnull(@DisplayCredits,showCredit),
				enableRealTimeRoster = isnull(@EnableRealTimeRoster,enableRealTimeRoster)
			where registrationID = @MCRegistrationID;
		END

		-- if event does not have registration
		IF @MCRegistrationID is null AND @isRecurringEvent = 0 BEGIN
			select @RegistrationReplyEmail = columnValueString from #tmpEVQueueData where columnname = 'RegistrationReplyEmail';
			select @DisplayCredits = columnValueBit from #tmpEVQueueData where columnname = 'DisplayCredits';
			select @EnableRealTimeRoster = columnValueBit from #tmpEVQueueData where columnname = 'EnableRealTimeRoster';

			-- it could have rsvp or alt reg, so we need to switch it
			IF EXISTS(select registrationID from dbo.ev_registration where eventID = @MCEventID and status = 'A' and siteID = @siteID)
				UPDATE dbo.ev_registration
				SET status = 'D'
				WHERE eventID = @MCEventID 
				AND siteID = @siteID
				and status = 'A';

			UPDATE dbo.ev_events
			SET altRegistrationURL = NULL
			WHERE eventID = @MCEventID
			AND altRegistrationURL is not null;

			EXEC dbo.ev_createRegistration @eventID=@MCEventID, @registrationTypeID=1, @startDate=@EventStart, @endDate=@EventEnd, 
				@registrantCap=null, @replyToEmail=@RegistrationReplyEmail, @notifyEmail='', @isPriceBasedOnActual=1, @bulkCountByRate=0, 
				@registrationID=@MCRegistrationID OUTPUT;

			update dbo.ev_registration
			set showCredit = isnull(@DisplayCredits,1),
				enableRealTimeRoster = isnull(@EnableRealTimeRoster,enableRealTimeRoster)
			where registrationID = @MCRegistrationID;

			select @expirationContentID = expirationContentID FROM dbo.ev_registration WHERE registrationID = @MCRegistrationID;
			EXEC dbo.cms_updateContent @contentID=@expirationContentID, @languageID=1, @isHTML=1, 
				@contentTitle='Expiration Message', @contentDesc='', @rawcontent='Registration for this event has closed.';
		END

		-- handle sub events
		IF @existingEvent = 0 OR (@existingEvent = 1 and @ovAction = 'o') BEGIN
			select @MCParentEventID = columnValueInteger from #tmpEVQueueData where columnname = 'MCParentEventID';
			select @ParentEventCode = nullIf(columnValueString,'') from #tmpEVQueueData where columnname = 'ParentEventCode';

			-- if @MCParentEventID is null but @ParentEventCode is not null, this event should be a child of a recently added event
			IF @MCParentEventID is null and @ParentEventCode is not null BEGIN
				select @MCCalendarID = columnValueInteger from #tmpEVQueueData where columnname = 'MCCalendarID';

				select @MCParentEventID = e.eventID
				from dbo.ev_events as e
				inner join dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID
					and ce.calendarID = @MCCalendarID
					and ce.calendarID = ce.sourceCalendarID
				where e.reportCode = @ParentEventCode
				and e.status = 'A'
				and e.siteID = @siteID;
			END

			-- if @MCParentEventID is not null, this event should be a child of that event	
			IF @MCParentEventID is not null BEGIN
				select @subEventID = subEventID from dbo.ev_subEvents where parentEventID=@MCParentEventID and eventID=@MCEventID;
				IF @subEventID is null 
					insert into dbo.ev_subEvents (parentEventID, eventID)
					values (@MCParentEventID, @MCEventID);
			END
		END

		-- credits
		IF @existingEvent = 0 OR (@existingEvent = 1 and @ovAction = 'o') BEGIN
			insert into @tblPossibleCredits (ASID, authorityID, authorityCode)
			select distinct crdAS.ASID, crdA.authorityID, crdA.authorityCode
			from dbo.crd_sponsors as crdS
			inner join dbo.crd_authoritySponsors as crdAS on crdAS.sponsorID = crdS.sponsorID
			inner join dbo.crd_authorities as crdA on crdA.authorityID = crdAS.authorityID
			where crdS.orgID = @orgID;

			insert into @tblPossibleCreditCols (ASID, column_name, ASTID)
			select crdAS.ASID, crdAS.authorityCode + '_' + crdAT.typeCode, crdAST.ASTID 
			from @tblPossibleCredits as crdAS
			inner join dbo.crd_authorityTypes as crdAT on crdAT.authorityID = crdAS.authorityID
			inner join dbo.crd_authoritySponsorTypes as crdAST on crdAST.ASID = crdAS.ASID and crdAST.typeID = crdAT.typeID;

			select @ASID = min(ASID) from @tblPossibleCredits;
			while @ASID is not null begin
				select @crdAuthorityCode=null, @crdApproval=null, @crdStatus=null, @crdOfferedID=null, @crdStatusID=null, @ASTID=null;

				select @crdAuthorityCode = authorityCode from @tblPossibleCredits where ASID = @ASID;
				select @crdApproval = columnValueString from #tmpEVQueueData where columnname = @crdAuthorityCode + '_approval';
				select @crdStatus = columnValueString from #tmpEVQueueData where columnname = @crdAuthorityCode + '_status';

				IF @crdApproval is not null and @crdStatus is not null BEGIN
					select @crdOfferedID = offeringID from dbo.crd_offerings where ASID = @ASID and eventID = @MCEventID;
					IF @crdOfferedID is null
						EXEC dbo.crd_addOffering @applicationType='Events', @itemID=@MCEventID, @ASID=@ASID, @offeredID=@crdOfferedID OUTPUT;

					select @crdStatusID = statusID from dbo.crd_statuses where [status] = @crdStatus;

					UPDATE dbo.crd_offerings
					SET statusID = @crdStatusID, approvalNum = @crdApproval
					WHERE offeringID = @crdOfferedID;

					select @ASTID = min(ASTID) from @tblPossibleCreditCols where ASID = @ASID;
					while @ASTID is not null begin
						select @crdColName=null, @crdValue=null;

						select @crdColName = column_name from @tblPossibleCreditCols where ASTID = @ASTID;
						select @crdValue = columnValueDecimal2 from #tmpEVQueueData where columnname = @crdColName;

						IF @crdValue is not null
							EXEC dbo.crd_updateOfferingType @offeringID=@crdOfferedID, @ASTID=@ASTID, @creditValue=@crdValue, @updated=@crdupdated OUTPUT;

						select @ASTID = min(ASTID) from @tblPossibleCreditCols where ASID = @ASID and ASTID > @ASTID;
					end
				END

				select @ASID = min(ASID) from @tblPossibleCredits where ASID > @ASID;
			end
		END

		-- cross-event field data
		IF @existingEvent = 0 OR (@existingEvent = 1 and @ovAction = 'o') BEGIN
			select @eventAdminSRID = dbo.fn_getSiteResourceIDForResourceType('EventAdmin',@siteID);
			select @crossEventFieldUsageID = dbo.fn_cf_getUsageID('EventAdmin','Event',null);

			insert into @tblCrossEventFields (fieldID, fieldReference, displayTypeCode, dataTypeCode)
			select f.fieldID, f.fieldReference, ft.displayTypeCode, ft.dataTypeCode
			from dbo.cf_fields as f
			inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID
			inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			where fu.usageID = @crossEventFieldUsageID
			and f.controllingSiteResourceID = @eventAdminSRID
			and len(f.fieldReference) > 0
			and f.isActive = 1
			and ft.displayTypeCode <> 'LABEL'
			and 1 = case when ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX') then case when exists(select 1 from dbo.cf_fieldValues where fieldID = f.fieldID) then 1 else 0 end 
						else 1 end
			order by f.fieldOrder;

			select @eventSiteResourceID = siteResourceID from dbo.ev_events where eventID = @MCEventID;
			IF @eventSiteResourceID IS NOT NULL BEGIN
				DELETE FROM dbo.cf_fieldData
				WHERE itemID = @eventSiteResourceID
				AND itemType = 'CrossEvent';
			
				select @minFieldID = min(fieldID) from @tblCrossEventFields;
				while @minFieldID is not null BEGIN
					select @fieldReference=null, @displayTypeCode=null, @dataTypeCode=null;

					select @fieldReference = fieldReference, @displayTypeCode = displayTypeCode, @dataTypeCode = dataTypeCode 
					from @tblCrossEventFields 
					where fieldID = @minFieldID;

					IF @displayTypeCode in ('SELECT','RADIO','CHECKBOX') BEGIN
						INSERT INTO dbo.cf_fieldData (fieldID, itemType, itemID, valueID, amount)
						select f.fieldID, 'CrossEvent', @eventSiteResourceID, fv.valueID, 0 as amount
						from #tmpEVQueueData as tmp
						inner join @tblCrossEventFields as cevf on cevf.fieldReference = tmp.columnname
						inner join dbo.cf_fields as f on f.fieldID = cevf.fieldID
							and f.fieldID = @minFieldID
						inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
						inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
							and case when ft.dataTypeCode = 'DATE' then cast(cast(tmp.columnValueString as date) as varchar(15)) else tmp.columnValueString end
								 = case when ft.dataTypeCode = 'STRING' then cast(fv.valueString as varchar(max))
										 when ft.dataTypeCode = 'DECIMAL2' then cast(fv.valueDecimal2 as varchar(15))
										 when ft.dataTypeCode = 'INTEGER' then cast(fv.valueInteger as varchar(10))
										 when ft.dataTypeCode = 'BIT' then cast(fv.valueBit as varchar(1))
										 when ft.dataTypeCode = 'DATE' then cast(fv.valueDate as varchar(15))
									else '' end;
					END
					ELSE BEGIN
						select @fieldValue = null, @dataID = null;

						select @fieldValue = columnValueString
						from #tmpEVQueueData as tmp
						inner join @tblCrossEventFields as cevf on cevf.fieldReference = tmp.columnname
						inner join dbo.cf_fields as f on f.fieldID = cevf.fieldID
						where f.fieldID = @minFieldID;

						IF @fieldValue IS NOT NULL
							EXEC dbo.cf_setFieldData @fieldID=@minFieldID, @itemID=@eventSiteResourceID, @itemType='CrossEvent', 
								@valueID=NULL, @fieldValue=@fieldValue, @dataID=@dataID OUTPUT;
					END

					select @minFieldID = min(fieldID) from @tblCrossEventFields where fieldID > @minFieldID;
				END
			END
		END

		-- recurring event
		IF @isRecurringEvent = 1 BEGIN
			SELECT @createdFromEventID = eventID, @lockTimeZoneID = lockTimeZoneID 
			FROM dbo.ev_events
			WHERE siteID = @siteID
			AND [status] = 'A'
			AND reportCode = @RecurringSeriesCode;

			select @recurrenceOrder = isnull((select columnValueInteger from #tmpEVQueueData where columnname = 'MCRecurrenceOrder'),0);

			IF @createdFromEventID IS NOT NULL AND @recurrenceOrder > 0 BEGIN
				SELECT @recurringSeriesID = seriesID, @recurrenceAFID = afID
				FROM dbo.ev_recurringSeries
				WHERE createdFromEventID = @createdFromEventID
				AND siteID = @siteID;

				SELECT @datePart = [datePart], @dateNum = dateNum, @adjustTerm = adjustTerm, @nextWeekday = nextWeekday, @weekNumber = weekNumber
				FROM dbo.af_advanceFormulas
				WHERE AFID = @recurrenceAFID
				AND siteID = @siteID;

				-- update seriesID
				UPDATE dbo.ev_events
				SET recurringSeriesID = @recurringSeriesID
				WHERE eventID = @MCEventID;

				-- update event times
				UPDATE dbo.ev_events 
				SET defaultTimeID = NULL, 
					lockedTimeID = NULL,
					lockTimeZoneID = @lockTimeZoneID
				WHERE eventID = @MCEventID;
				
				DELETE FROM dbo.ev_times 
				WHERE eventID = @MCEventID;
				
				INSERT INTO dbo.ev_times (eventID, timeZoneID, startTime, endTime)
				SELECT @MCEventID, timeZoneID, dbo.fn_af_getAFDate(startTime, @datePart, @dateNum * @recurrenceOrder, @adjustTerm, @nextWeekday, @weekNumber),
					dbo.fn_af_getAFDate(endTime, @datePart, @dateNum * @recurrenceOrder, @adjustTerm, @nextWeekday, @weekNumber)
				FROM dbo.ev_times
				WHERE eventID = @createdFromEventID;

				SELECT @defaultTimeID = timeID
				FROM dbo.ev_times
				WHERE eventID = @MCEventID
				AND timeZoneID = @defaultTimeZoneID;

				IF @lockTimeZoneID IS NOT NULL
					SELECT @lockedTimeID = timeID
					FROM dbo.ev_times
					WHERE eventID = @MCEventID
					AND timeZoneID = @lockTimeZoneID;

				UPDATE dbo.ev_events 
				SET defaultTimeID = @defaultTimeID, 
					lockedTimeID = @lockedTimeID
				WHERE eventID = @MCEventID;
			END
		END
	COMMIT TRAN;

	-- update status
	EXEC platformQueue.dbo.queue_setStatus @queueType='importEvents', @itemUID=@itemUID, @queueStatus='readyToNotify';

	IF OBJECT_ID('tempdb..#tmpEVQueueData') IS NOT NULL 
		DROP TABLE #tmpEVQueueData;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ev_importEvents_import
@siteID int,
@recordedByMemberID int,
@ovAction char(1),
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpCrossEventCustomDetails') IS NOT NULL 
		DROP TABLE #tmpCrossEventCustomDetails;
	CREATE TABLE #tmpCrossEventCustomDetails (rowID int, fieldID int, columnName varchar(255), fieldValue varchar(max));

	declare @nowDate datetime, @queueTypeID int, @statusInserting int, @statusReady int, @itemGroupUID uniqueidentifier, 
			@colList varchar(max), @selColList varchar(max), @dynSQL nvarchar(max), @xmlMessage xml;
	set @nowDate = getdate();
	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='importEvents', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='insertingItems', @queueStatusID=@statusInserting OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	-- all imported events get the same ItemGroupUID
	set @itemGroupUID = NEWID();

	BEGIN TRY
		-- string columns
		set @colList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(columnName) 
		from #tblEvImportCustomCols
		where dataTypeCode = 'STRING'
		and displayTypeCode not in ('SELECT','RADIO','CHECKBOX');

		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #mc_EvImport ALTER COLUMN ' + tbl.listitem + ' varchar(max) null'
				from dbo.fn_varcharListToTable(@colList,',') as tbl;
			exec(@selColList);

			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTable(@colList,',') as tbl;

			set @dynSQL = 'update #mc_EvImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL = 'select tmp.rowID, f.fieldID, tmp.columnName, tmp.valueString
							from (
								select rowID, columnName, valueString
								from #mc_EvImport
								unpivot (valueString for columnName in (' + @colList + ')) u
							) tmp
							inner join #tblEvImportCustomCols as impCols on impCols.columnName = tmp.columnName
							inner join dbo.cf_fields as f on f.fieldID = impCols.fieldID';
			
			INSERT INTO #tmpCrossEventCustomDetails (rowID, fieldID, columnName, fieldValue)
			EXEC(@dynSQL);
		end

		-- integer columns
		set @colList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(columnName) 
		from #tblEvImportCustomCols
		where dataTypeCode = 'INTEGER'
		and displayTypeCode not in ('SELECT','RADIO','CHECKBOX');

		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #mc_EvImport ALTER COLUMN ' + tbl.listitem + ' varchar(10) null'
				from dbo.fn_varcharListToTable(@colList,',') as tbl;
			exec(@selColList);

			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTable(@colList,',') as tbl;

			set @dynSQL = 'update #mc_EvImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL = 'select tmp.rowID, f.fieldID, tmp.columnName, tmp.valueInteger
							from (
								select rowID, columnName, valueInteger
								from #mc_EvImport
								unpivot (valueInteger for columnName in (' + @colList + ')) u
							) tmp
							inner join #tblEvImportCustomCols as impCols on impCols.columnName = tmp.columnName
							inner join dbo.cf_fields as f on f.fieldID = impCols.fieldID
							inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID';
			
			INSERT INTO #tmpCrossEventCustomDetails (rowID, fieldID, columnName, fieldValue)
			EXEC(@dynSQL);
		end

		-- decimal columns
		set @colList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(columnName) 
		from #tblEvImportCustomCols
		where dataTypeCode = 'DECIMAL2'
		and displayTypeCode not in ('SELECT','RADIO','CHECKBOX');

		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #mc_EvImport ALTER COLUMN ' + tbl.listitem + ' varchar(15) null'
				from dbo.fn_varcharListToTable(@colList,',') as tbl;
			exec(@selColList);

			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTable(@colList,',') as tbl;

			set @dynSQL = 'update #mc_EvImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL = 'select tmp.rowID, f.fieldID, tmp.columnName, tmp.valueDecimal2
							from (
								select rowID, columnName, valueDecimal2
								from #mc_EvImport
								unpivot (valueDecimal2 for columnName in (' + @colList + ')) u
							) tmp
							inner join #tblEvImportCustomCols as impCols on impCols.columnName = tmp.columnName
							inner join dbo.cf_fields as f on f.fieldID = impCols.fieldID';
			
			INSERT INTO #tmpCrossEventCustomDetails (rowID, fieldID, columnName, fieldValue)
			EXEC(@dynSQL);
		end

		-- date columns
		set @colList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(columnName) 
		from #tblEvImportCustomCols
		where dataTypeCode = 'DATE'
		and displayTypeCode not in ('SELECT','RADIO','CHECKBOX');

		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #mc_EvImport ALTER COLUMN ' + tbl.listitem + ' varchar(30) null'
				from dbo.fn_varcharListToTable(@colList,',') as tbl;
			exec(@selColList);

			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTable(@colList,',') as tbl;

			set @dynSQL = 'update #mc_EvImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL = 'select tmp.rowID, f.fieldID, tmp.columnName, tmp.valueDate
							from (
								select rowID, columnName, valueDate
								from #mc_EvImport
								unpivot (valueDate for columnName in (' + @colList + ')) u
							) tmp
							inner join #tblEvImportCustomCols as impCols on impCols.columnName = tmp.columnName
							inner join dbo.cf_fields as f on f.fieldID = impCols.fieldID';
			
			INSERT INTO #tmpCrossEventCustomDetails (rowID, fieldID, columnName, fieldValue)
			EXEC(@dynSQL);
		end

		-- select, radio, and checkbox custom field columns
		set @colList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(columnName) 
		from #tblEvImportCustomCols
		where displayTypeCode in ('SELECT','RADIO','CHECKBOX');

		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #mc_EvImport ALTER COLUMN ' + tbl.listitem + ' varchar(max) null'
				from dbo.fn_varcharListToTable(@colList,',') as tbl;
			exec(@selColList);

			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTable(@colList,',') as tbl;

			set @dynSQL = 'update #mc_EvImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL = 'select tmp.rowID, f.fieldID, tmp.columnName, tbl.listitem
							from (
								select rowID, columnName, columnValue
								from #mc_EvImport
								unpivot (columnValue for columnName in (' + @colList + ')) u
							) tmp
							cross apply dbo.fn_varcharListToTable(tmp.columnValue,''|'') as tbl
							inner join #tblEvImportCustomCols as impCols on impCols.columnName = tmp.columnName
							inner join dbo.cf_fields as f on f.fieldID = impCols.fieldID
							inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID';
			
			INSERT INTO #tmpCrossEventCustomDetails (rowID, fieldID, columnName, fieldValue)
			EXEC(@dynSQL);
		end

	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to prepare cross-event fields for import.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));
	END CATCH

	BEGIN TRY
		BEGIN TRAN;
			insert into platformQueue.dbo.tblQueueItems (itemUID, queueStatusID)
			select itemUID, @statusInserting
			from #mc_EvImport;
			
			insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueInteger)
			select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtInt.columnValueInt
			from #mc_EvImport as tmp
			inner join (
				select rowID, columnname, columnValueInt
				from #mc_EvImport
				unpivot (columnValueInt for columnname in (MCCalendarID, MCEventID, MCRegistrationID, MCParentEventID, MCRecurrenceOrder)) u
			) as unPvtInt on unPvtInt.rowID = tmp.rowID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtInt.columnname;

			insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueString)
			select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtString.columnValueString
			from #mc_EvImport as tmp
			inner join (
				select rowID, columnname, columnValueString
				from #mc_EvImport
				unpivot (columnValueString for columnname in (EventTitle, EventSubTitle, EventCode, ParentEventCode, RecurringSeriesCode, RegistrationReplyEmail, ContactTitle, LocationTitle, CancellationTitle, TravelTitle, InformationTitle)) u
			) as unPvtString on unPvtString.rowID = tmp.rowID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtString.columnname;

			insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueDate)
			select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtDate.columnValueDate
			from #mc_EvImport as tmp
			inner join (
				select rowID, columnname, columnValueDate
				from #mc_EvImport
				unpivot (columnValueDate for columnname in (EventStart, EventEnd)) u
			) as unPvtDate on unPvtDate.rowID = tmp.rowID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtDate.columnname;

			insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueBit)
			select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtBit.columnValueBit
			from #mc_EvImport as tmp
			inner join (
				select rowID, columnname, columnValueBit
				from #mc_EvImport
				unpivot (columnValueBit for columnname in (EventHidden, EventAllDay, ContactInclude, LocationInclude, CancellationInclude, TravelInclude, DisplayCredits, EnableRealTimeRoster)) u
			) as unPvtBit on unPvtBit.rowID = tmp.rowID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtBit.columnname;

			insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueText)
			select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtText.columnValueText
			from #mc_EvImport as tmp
			inner join (
				select rowID, columnname, columnValueText
				from #mc_EvImport
				unpivot (columnValueText for columnname in (EventDescription, Contact, Location, Cancellation, Travel, Information, InternalNotes, MCCategoryIDList)) u
			) as unPvtText on unPvtText.rowID = tmp.rowID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtText.columnname;

			insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueString)
			select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, @ovAction
			from #mc_EvImport as tmp
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'ovAction';

			-- add credits
			set @colList = null;
			select @colList = COALESCE(@colList + ',', '') + authorityCode + '_approval,' + authorityCode + '_status' from #tblPossibleCredits;
			if @colList is not null BEGIN
				select @dynSQL = '
					select ''' + cast(@itemGroupUID as varchar(60)) + ''', tmp.itemUID, ' + cast(@recordedByMemberID as varchar(20)) + ', ' + cast(@siteID as varchar(10)) + ', dc.columnID, tmp.rowID, unPvtStr.columnValueString
					from #mc_EvImport as tmp
					inner join (
						select rowID, columnname, columnValueString
						from #mc_EvImport 
						unpivot (columnValueString for columnname in (' + @colList + ')) u
					) as unPvtStr on unPvtStr.rowID = tmp.rowID
					inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = ' + cast(@queueTypeID as varchar(10)) + ' and dc.columnname = unPvtStr.columnname';
				insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueString)
				EXEC(@dynSQL);
			end

			set @colList = null;
			select @colList = COALESCE(@colList + ',', '') + column_name from #tblPossibleCreditCols;
			IF @colList is not null BEGIN
				select @dynSQL = '
					select ''' + cast(@itemGroupUID as varchar(60)) + ''', tmp.itemUID, ' + cast(@recordedByMemberID as varchar(20)) + ', ' + cast(@siteID as varchar(10)) + ', dc.columnID, tmp.rowID, unPvtDec.columnValueDecimal2
					from #mc_EvImport as tmp
					inner join (
						select rowID, columnname, columnValueDecimal2
						from #mc_EvImport 
						unpivot (columnValueDecimal2 for columnname in (' + @colList + ')) u
					) as unPvtDec on unPvtDec.rowID = tmp.rowID
					inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = ' + cast(@queueTypeID as varchar(10)) + ' and dc.columnname = unPvtDec.columnname';
				insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueDecimal2)
				EXEC(@dynSQL);
			END

			insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueString)
			select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, fd.fieldValue
			from #mc_EvImport as tmp
			inner join #tmpCrossEventCustomDetails as fd on fd.rowID = tmp.rowID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = fd.columnname;

			-- resume task
			EXEC dbo.sched_resumeTask @name='Events Import Queue', @engine='BERLinux';

			-- update queue item groups to show ready to process
			update qi WITH (UPDLOCK, HOLDLOCK)
			set qi.queueStatusID = @statusReady,
				qi.dateUpdated = getdate()
			from platformQueue.dbo.tblQueueItems as qi
			inner join #mc_EvImport as tmp on tmp.itemUID = qi.itemUID;

			-- send message to service broker to create all the individual messages
			select @xmlMessage = isnull((
				select 'importEventsLoad' as t, cast(@itemGroupUID as varchar(60)) as u
				FOR XML RAW('mc'), TYPE
			),'<mc/>');
			EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;
		COMMIT TRAN;
	END TRY
	BEGIN CATCH
		IF @@trancount > 0 ROLLBACK TRANSACTION;

		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to queue events for import.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));
	END CATCH

	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblEvErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);

	IF OBJECT_ID('tempdb..#tmpCrossEventCustomDetails') IS NOT NULL 
		DROP TABLE #tmpCrossEventCustomDetails;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ev_importEvents_prepTable
@siteid int,
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @orgID int, @eventAdminSiteResourceID int, @crossEventFieldUsageID int, @recurringEvents bit, 
		@ASID int, @crdAuthorityCode varchar(20), @creditColName varchar(50);
	set @importResult = null;

	select @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);
	select @eventAdminSiteResourceID = dbo.fn_getSiteResourceIDForResourceType('EventAdmin',@siteID);
	select @crossEventFieldUsageID = dbo.fn_cf_getUsageID('EventAdmin','Event',null);

	SELECT @recurringEvents = recurringEvents
	FROM dbo.siteFeatures 
	WHERE siteID = @siteID;

	insert into #tblPossibleCredits (ASID, authorityID, authorityCode)
	select distinct crdAS.ASID, crdA.authorityID, crdA.authorityCode
	from dbo.crd_sponsors as crdS
	inner join dbo.crd_authoritySponsors as crdAS on crdAS.sponsorID = crdS.sponsorID
	inner join dbo.crd_authorities as crdA on crdA.authorityID = crdAS.authorityID
	where crdS.orgID = @orgID;

	insert into #tblPossibleCreditCols (ASID, column_name, ASTID)
	select crdAS.ASID, crdAS.authorityCode + '_' + crdAT.typeCode, crdAST.ASTID
	from #tblPossibleCredits as crdAS
	inner join dbo.crd_authorityTypes as crdAT on crdAT.authorityID = crdAS.authorityID
	inner join dbo.crd_authoritySponsorTypes as crdAST on crdAST.ASID = crdAS.ASID and crdAST.typeID = crdAT.typeID;

	insert into #tblEvImportCustomCols (fieldID, columnName, isRequired, requiredMsg, displayTypeCode, dataTypeCode)
	select f.fieldID, f.fieldReference, f.isRequired, f.requiredMsg, ft.displayTypeCode, ft.dataTypeCode
	from dbo.cf_fields as f
	inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID
	inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
	where fu.usageID = @crossEventFieldUsageID
	and f.controllingSiteResourceID = @eventAdminSiteResourceID
	and len(f.fieldReference) > 0
	and f.isActive = 1
	and ft.displayTypeCode <> 'LABEL'
	and 1 = case when ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX') then case when exists(select 1 from dbo.cf_fieldValues where fieldID = f.fieldID) then 1 else 0 end 
				else 1 end
	order by f.fieldOrder;

	-- *********************************
	-- ensure all required columns exist 
	-- *********************************
	BEGIN TRY
		-- this will get the columns that are required
		IF OBJECT_ID('tempdb..#tblEvOrgCols') IS NOT NULL 
			DROP TABLE #tblEvOrgCols;
		CREATE TABLE #tblEvOrgCols (COLUMN_NAME sysname);

		insert into #tblEvOrgCols
		select 'rowID' union all
		select 'Calendar' union all
		select 'EventTitle' union all
		select 'EventCode' union all
		select 'EventCategory' union all
		select 'EventStart' union all
		select 'EventEnd' union all
		select 'RegistrationReplyEmail';

		-- this will get the columns that are actually in the import
		IF OBJECT_ID('tempdb..#tblEvImportCols') IS NOT NULL 
			DROP TABLE #tblEvImportCols;
		CREATE TABLE #tblEvImportCols (ORDINAL_POSITION int, COLUMN_NAME sysname);

		insert into #tblEvImportCols
		select column_id, [name] 
		from tempdb.sys.columns 
		where object_id = object_id('tempdb..#mc_EvImport');

		INSERT INTO #tblEvErrors (msg)
		select 'The required column ' + org.column_name + ' is missing from your data.'
		from #tblEvOrgCols as org
		left outer join #tblEvImportCols as imp on imp.column_name = org.column_name
		where imp.ORDINAL_POSITION is null;
			IF @@ROWCOUNT > 0 GOTO on_done;

		delete from #tblEvImportCols 
		where column_name in (select COLUMN_NAME from #tblEvOrgCols);

		delete from #tblEvImportCols 
		where column_name in (select columnName from #tblEvImportCustomCols);

		IF OBJECT_ID('tempdb..#tblEvOrgCols') IS NOT NULL 
			DROP TABLE #tblEvOrgCols;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to validate file contains all required columns.');

		GOTO on_done;
	END CATCH

	-- **********
	-- prep table 
	-- **********
	BEGIN TRY
		-- add holding columns
		ALTER TABLE #mc_EvImport ADD MCCalendarID int null, MCEventID int null, MCCategoryIDList varchar(max) null, 
			MCRegistrationID int null, MCParentEventID int null, MCParentRowID int null, MCRecurrenceOrder int,
			itemUID uniqueidentifier NOT NULL DEFAULT(NEWID());

		-- ensure rowID is an int
		ALTER TABLE #mc_EvImport ALTER COLUMN RowID int not null;

		-- add missing columns
		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'EventHidden') BEGIN
			ALTER TABLE #mc_EvImport ADD EventHidden bit NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('EventHidden');
		END ELSE
			delete from #tblEvImportCols where column_name = 'EventHidden';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'EventAllDay') BEGIN
			ALTER TABLE #mc_EvImport ADD EventAllDay bit NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('EventAllDay');
		END ELSE
			delete from #tblEvImportCols where column_name = 'EventAllDay';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'DisplayCredits') BEGIN
			ALTER TABLE #mc_EvImport ADD DisplayCredits bit NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('DisplayCredits');
		END ELSE
			delete from #tblEvImportCols where column_name = 'DisplayCredits';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'ContactInclude') BEGIN
			ALTER TABLE #mc_EvImport ADD ContactInclude bit NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('ContactInclude');
		END ELSE
			delete from #tblEvImportCols where column_name = 'ContactInclude';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'LocationInclude') BEGIN
			ALTER TABLE #mc_EvImport ADD LocationInclude bit NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('LocationInclude');
		END ELSE
			delete from #tblEvImportCols where column_name = 'LocationInclude';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'CancellationInclude') BEGIN
			ALTER TABLE #mc_EvImport ADD CancellationInclude bit NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('CancellationInclude');
		END ELSE
			delete from #tblEvImportCols where column_name = 'CancellationInclude';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'TravelInclude') BEGIN
			ALTER TABLE #mc_EvImport ADD TravelInclude bit NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('TravelInclude');
		END ELSE
			delete from #tblEvImportCols where column_name = 'TravelInclude';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'EventDescription') BEGIN
			ALTER TABLE #mc_EvImport ADD EventDescription varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('EventDescription');
		END ELSE
			delete from #tblEvImportCols where column_name = 'EventDescription';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'EnableRealTimeRoster') BEGIN
			ALTER TABLE #mc_EvImport ADD EnableRealTimeRoster bit NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('EnableRealTimeRoster');
		END ELSE
			delete from #tblEvImportCols where column_name = 'EnableRealTimeRoster';
		
		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'ContactTitle') BEGIN
			ALTER TABLE #mc_EvImport ADD ContactTitle varchar(200) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('ContactTitle');
		END ELSE
			delete from #tblEvImportCols where column_name = 'ContactTitle';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'Contact') BEGIN
			ALTER TABLE #mc_EvImport ADD Contact varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Contact');
		END ELSE
			delete from #tblEvImportCols where column_name = 'Contact';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'LocationTitle') BEGIN
			ALTER TABLE #mc_EvImport ADD LocationTitle varchar(200) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('LocationTitle');
		END ELSE
			delete from #tblEvImportCols where column_name = 'LocationTitle';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'Location') BEGIN
			ALTER TABLE #mc_EvImport ADD Location varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Location');
		END ELSE
			delete from #tblEvImportCols where column_name = 'Location';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'CancellationTitle') BEGIN
			ALTER TABLE #mc_EvImport ADD CancellationTitle varchar(200) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('CancellationTitle');
		END ELSE
			delete from #tblEvImportCols where column_name = 'CancellationTitle';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'Cancellation') BEGIN
			ALTER TABLE #mc_EvImport ADD Cancellation varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Cancellation');
		END ELSE
			delete from #tblEvImportCols where column_name = 'Cancellation';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'TravelTitle') BEGIN
			ALTER TABLE #mc_EvImport ADD TravelTitle varchar(200) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('TravelTitle');
		END ELSE
			delete from #tblEvImportCols where column_name = 'TravelTitle';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'Travel') BEGIN
			ALTER TABLE #mc_EvImport ADD Travel varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Travel');
		END ELSE
			delete from #tblEvImportCols where column_name = 'Travel';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'InformationTitle') BEGIN
			ALTER TABLE #mc_EvImport ADD InformationTitle varchar(200) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('InformationTitle');
		END ELSE
			delete from #tblEvImportCols where column_name = 'InformationTitle';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'Information') BEGIN
			ALTER TABLE #mc_EvImport ADD Information varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('Information');
		END ELSE
			delete from #tblEvImportCols where column_name = 'Information';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'InternalNotes') BEGIN
			ALTER TABLE #mc_EvImport ADD InternalNotes varchar(max) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('InternalNotes');
		END ELSE
			delete from #tblEvImportCols where column_name = 'InternalNotes';
			
		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'EventSubTitle') BEGIN
			ALTER TABLE #mc_EvImport ADD EventSubTitle varchar(200) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('EventSubTitle');
		END ELSE
			delete from #tblEvImportCols where column_name = 'EventSubTitle';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'ParentEventCode') BEGIN
			ALTER TABLE #mc_EvImport ADD ParentEventCode varchar(15) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('ParentEventCode');
		END ELSE
			delete from #tblEvImportCols where column_name = 'ParentEventCode';

		IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = 'RecurringSeriesCode') BEGIN
			ALTER TABLE #mc_EvImport ADD RecurringSeriesCode varchar(15) NULL;
			INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES ('RecurringSeriesCode');
		END ELSE
			delete from #tblEvImportCols where column_name = 'RecurringSeriesCode';

		-- add missing credit columns
		select @ASID = min(ASID) from #tblPossibleCredits;
		while @ASID is not null begin
			select @crdAuthorityCode = authorityCode from #tblPossibleCredits where ASID = @ASID;

			IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = @crdAuthorityCode + '_approval') BEGIN
				EXEC('ALTER TABLE #mc_EvImport ADD ' + @crdAuthorityCode + '_approval varchar(50) NULL;');
				INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES (@crdAuthorityCode + '_approval');
				update #tblPossibleCredits set missingFromFile = 1 where ASID = @ASID and missingFromFile = 0;
			END ELSE BEGIN
				delete from #tblEvImportCols where column_name = @crdAuthorityCode + '_approval';
				update #tblPossibleCredits set anyInFile = 1 where ASID = @ASID and anyInFile = 0;
			END

			IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = @crdAuthorityCode + '_status') BEGIN
				EXEC('ALTER TABLE #mc_EvImport ADD ' + @crdAuthorityCode + '_status varchar(15) NULL;');
				INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES (@crdAuthorityCode + '_status');
				update #tblPossibleCredits set missingFromFile = 1 where ASID = @ASID and missingFromFile = 0;
			END ELSE BEGIN
				delete from #tblEvImportCols where column_name = @crdAuthorityCode + '_status';
				update #tblPossibleCredits set anyInFile = 1 where ASID = @ASID and anyInFile = 0;
			END

			set @creditColName = null;
			select @creditColName = min(column_name) from #tblPossibleCreditCols where ASID = @ASID;
			while @creditColName is not null begin
				IF NOT EXISTS (select ORDINAL_POSITION from #tblEvImportCols where column_name = @creditColName) BEGIN
					EXEC('ALTER TABLE #mc_EvImport ADD ' + @creditColName + ' decimal(6,2) NULL;');
					INSERT INTO #tblColsAdded (COLUMN_NAME) VALUES (@creditColName);
					update #tblPossibleCredits set missingFromFile = 1 where ASID = @ASID and missingFromFile = 0;
				END ELSE BEGIN
					delete from #tblEvImportCols where column_name = @creditColName;
					update #tblPossibleCredits set anyInFile = 1 where ASID = @ASID and anyInFile = 0;
				END
				select @creditColName = min(column_name) from #tblPossibleCreditCols where ASID = @ASID and column_name > @creditColName;
			end

			select @ASID = min(ASID) from #tblPossibleCredits where ASID > @ASID;
		end

		-- all credit columns must be included if one column is included
		INSERT INTO #tblEvErrors (msg)
		SELECT 'Missing credit columns for authority ' + authorityCode + '. If one credit column for an authority is included, all columns for that authority must be included.'
		FROM #tblPossibleCredits
		WHERE anyInFile = 1 and missingFromFile = 1;
			IF @@ROWCOUNT > 0 GOTO on_done;

	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to prepare import table by adding missing columns.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- *************
	-- extra columns 
	-- *************
	BEGIN TRY
		-- extra columns should stop import to prevent accidental misnamed columns
		IF EXISTS (select column_name from #tblEvImportCols) BEGIN
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'The imported file contains the extra column ' + cast(column_name as varchar(300)) + '. Remove this column from your file.' 
			FROM #tblEvImportCols  
			ORDER BY column_name;
		END
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to validate import file for extra columns.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- ***************************************
	-- keep only custom columns in import file
	-- ***************************************
	BEGIN TRY
		IF EXISTS (select columnName from #tblEvImportCustomCols) BEGIN
			delete from #tblEvImportCustomCols
			where columnName not in (select [name] from tempdb.sys.columns where object_id = object_id('tempdb..#mc_EvImport'));
		END
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to prepare import table for custom columns.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- ************************
	-- generate result xml file 
	-- ************************
	on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblEvErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);

	IF OBJECT_ID('tempdb..#tblEvOrgCols') IS NOT NULL 
		DROP TABLE #tblEvOrgCols;
	IF OBJECT_ID('tempdb..#tblEvImportCols') IS NOT NULL 
		DROP TABLE #tblEvImportCols;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ev_importEvents_validate
@siteid int, 
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @ASID int, @crdAuthorityCode varchar(20), @creditColName varchar(50), @minColID int, @mincol varchar(255), 
		@good bit, @reqMsg varchar(800), @isRequired bit, @dataTypeCode varchar(12), @displayTypeCode varchar(12), 
		@recurringEvents bit, @dynSQL nvarchar(max), @queueTypeID int;

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='importEvents', @queueTypeID=@queueTypeID OUTPUT;
	
	set @importResult = null;

	SELECT @recurringEvents = recurringEvents
	FROM dbo.siteFeatures 
	WHERE siteID = @siteID;

	-- ***********
	-- clean table 
	-- ***********
	BEGIN TRY
		-- delete empty rows
		delete from #mc_EvImport where Calendar is null and EventTitle is null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to clean import table.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- ****************
	-- required columns 
	-- ****************
	BEGIN TRY
		-- match on calendar
		update tmp 
		set tmp.MCCalendarID = cals.calendarID 
		from dbo.fn_ev_getCalendarsOnSite(@siteid) as cals
		cross apply dbo.fn_cms_getApplicationInstancePagePath(@siteid,cals.applicationInstanceID) as aip
		inner join #mc_EvImport as tmp on isnull(tmp.calendar,'') = aip.pagename
		where aip.applicationSiteResourceID = cals.siteResourceID
		and aip.applicationSiteResourceType = 'Events';

		-- check for missing calendars
		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN MCCalendarID int not null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ': ' + isnull(calendar,'') + ' does not match an existing calendar.'
			FROM #mc_EvImport
			WHERE MCCalendarID IS NULL
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END CATCH

		-- no blank eventTitles
		update #mc_EvImport set eventTitle = '' where eventTitle is null;

		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing EventTitle.'
		FROM #mc_EvImport
		WHERE eventTitle = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- eventTitles must be at or under 200 chars
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid EventTitle. EventTitles must be 200 characters or less.'
		FROM #mc_EvImport
		WHERE len(EventTitle) > 200 
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;
			
		-- EventSubTitle must be at or under 200 chars
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid EventSubTitle. EventSubTitle must be 200 characters or less.'
		FROM #mc_EvImport
		WHERE len(isnull(EventSubTitle,'')) > 200 
		ORDER BY rowID;

		-- no blank eventCodes
		update #mc_EvImport set eventCode = '' where eventCode is null;

		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing EventCode.'
		FROM #mc_EvImport
		WHERE eventCode = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- eventcode must be at or under 15 chars
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ': ' + eventCode + ' has an invalid EventCode. EventCodes must be 15 characters or less.'
		FROM #mc_EvImport
		WHERE len(eventCode) > 15 
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- eventcode must be unique in file
		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN eventCode varchar(15) not null;
			ALTER TABLE #mc_EvImport ADD PRIMARY KEY (eventCode);
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'EventCode ' + eventCode + ' appears in the file multiple times. EventCodes must be unique.'
			FROM #mc_EvImport
			GROUP BY eventCode
			HAVING count(*) > 1
			ORDER BY eventCode;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END CATCH

		-- match on eventCode
		update tmp 
		set tmp.MCEventID = e.eventID
		from #mc_EvImport as tmp 
		inner join dbo.ev_events as e on e.reportCode = tmp.eventCode
			and e.status = 'A'
			and e.siteID = @siteID
		inner join dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID
			and ce.calendarID = tmp.MCCalendarID
			and ce.calendarID = ce.sourceCalendarID;

		-- lookup registration
		update tmp 
		set tmp.MCRegistrationID = r.registrationID
		from #mc_EvImport as tmp 
		inner join dbo.ev_registration as r on r.eventID = tmp.MCEventID 
			and r.siteID = @siteID
			and r.status = 'A'
			and r.registrationTypeID = 1
		where tmp.MCEventID is not null;


		/* EVENT CATEGORIES */
		-- no blank EventCategory
		update #mc_EvImport set EventCategory = '' where EventCategory is null;

		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing EventCategory.'
		FROM #mc_EvImport
		WHERE EventCategory = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- split category into separate table
		INSERT INTO #tblEVCategories (MCCalendarID, rowID, EventCategory, CategoryOrder)
		SELECT tmp.MCCalendarID, tmp.rowID, li.listItem, MIN(li.autoID)
		FROM #mc_EvImport as tmp
		CROSS APPLY dbo.fn_varcharListToTable(tmp.EventCategory,'|') as li
		GROUP BY tmp.MCCalendarID, tmp.rowID, li.listItem;

		-- match on EventCategory
		update tmp 
		set tmp.MCCategoryID = cat.categoryID  
		from #tblEVCategories as tmp 
		inner join dbo.ev_calendars as c on c.siteID = @siteID and c.calendarID = tmp.MCCalendarID
		inner join dbo.ev_categories as cat on cat.calendarID = c.calendarID and cat.category = tmp.EventCategory;

		-- check for missing EventCategory
		BEGIN TRY
			ALTER TABLE #tblEVCategories ALTER COLUMN MCCategoryID int not null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			SELECT distinct 'EventCategory ' + EventCategory + ' does not match an existing category for that calendar.'
			FROM #tblEVCategories
			WHERE MCCategoryID IS NULL
			ORDER BY 1;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END CATCH


		-- put ordered categoryID list back onto events table
		IF OBJECT_ID('tempdb..#tmpRankedCategories') IS NOT NULL 
			DROP TABLE #tmpRankedCategories;
		IF OBJECT_ID('tempdb..#tmpEVCatConcatenations') IS NOT NULL 
			DROP TABLE #tmpEVCatConcatenations;
		IF OBJECT_ID('tempdb..#tmpEVCatRankedConcatenations') IS NOT NULL 
			DROP TABLE #tmpEVCatRankedConcatenations;
		create table #tmpRankedCategories (rowID int, categoryID varchar(max), categoryOrder int);
		CREATE TABLE #tmpEVCatConcatenations (rowID int, CategoryIDs varchar(max), categoryOrder int);
		CREATE TABLE #tmpEVCatRankedConcatenations (rowID int, CategoryIDs varchar(max), categoryOrder int);

		INSERT INTO #tmpRankedCategories
		SELECT rowID, cast(MCCategoryID as varchar(max)) as categoryID, 
			ROW_NUMBER() OVER (PARTITION BY rowID ORDER BY CategoryOrder) as categoryOrder
		from #tblEVCategories;

		WITH Concatenations AS (
			SELECT rowID, categoryID as CategoryIDs, categoryOrder
			FROM #tmpRankedCategories
			WHERE categoryOrder = 1
				union all
			SELECT c.rowID, (c.CategoryIDs + '|' + l.categoryID) as CategoryIDs, l.categoryOrder
			FROM Concatenations as c
			INNER JOIN #tmpRankedCategories as l ON l.rowID = c.rowID AND l.categoryOrder = c.categoryOrder + 1
		)
		INSERT INTO #tmpEVCatConcatenations (rowID, CategoryIDs, categoryOrder)
		SELECT rowID, CategoryIDs, categoryOrder
		FROM Concatenations;	
		
		INSERT INTO #tmpEVCatRankedConcatenations (rowID, CategoryIDs, categoryOrder)
		SELECT rowID, CategoryIDs, ROW_NUMBER() OVER (PARTITION BY rowID ORDER BY categoryOrder DESC) as categoryOrder
		FROM #tmpEVCatConcatenations;

		UPDATE tmp
		set tmp.MCCategoryIDList = c.CategoryIDs
		from #mc_EvImport as tmp
		inner join #tmpEVCatRankedConcatenations as c on c.rowID = tmp.rowID and c.categoryOrder = 1;

		IF OBJECT_ID('tempdb..#tmpRankedCategories') IS NOT NULL 
			DROP TABLE #tmpRankedCategories;
		IF OBJECT_ID('tempdb..#tmpEVCatConcatenations') IS NOT NULL 
			DROP TABLE #tmpEVCatConcatenations;
		IF OBJECT_ID('tempdb..#tmpEVCatRankedConcatenations') IS NOT NULL 
			DROP TABLE #tmpEVCatRankedConcatenations;

		-- check for missing EventCategory again
		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN MCCategoryIDList varchar(max) not null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing EventCategory.'
			FROM #mc_EvImport
			WHERE MCCategoryIDList IS NULL
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END CATCH

		-- ensure EventStart is datetime (allow nulls for this check)
		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN EventStart datetime null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			VALUES ('The column EventStart contains invalid dates.');

			GOTO on_done;
		END CATCH

		-- check for null EventStart
		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN EventStart datetime not null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required EventStart.'
			FROM #mc_EvImport
			WHERE EventStart IS NULL
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END CATCH

		-- ensure EventEnd is datetime (allow nulls for this check)
		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN EventEnd datetime null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			VALUES ('The column EventEnd contains invalid dates.');

			GOTO on_done;
		END CATCH

		-- check for null EventEnd
		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN EventEnd datetime not null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required EventEnd.'
			FROM #mc_EvImport
			WHERE EventEnd IS NULL
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END CATCH

		-- check dates
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a EventStart after the EventEnd.'
		FROM #mc_EvImport
		WHERE EventStart > EventEnd
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- no blank RegistrationReplyEmail (skip check for recurring events)
		update #mc_EvImport set RegistrationReplyEmail = '' where RegistrationReplyEmail is null;

		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(tmp.rowID as varchar(10)) + ' has a missing RegistrationReplyEmail.'
		FROM #mc_EvImport AS tmp
		LEFT OUTER JOIN dbo.ev_events AS e ON e.siteID = @siteID
			AND e.eventID = tmp.MCEventID
			AND e.recurringSeriesID IS NOT NULL
		WHERE tmp.RegistrationReplyEmail = ''
		AND ISNULL(tmp.RecurringSeriesCode,'') = ''
		AND e.eventID IS NULL
		ORDER BY tmp.rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to validate data in required columns.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- ****************
	-- optional columns 
	-- ****************
	BEGIN TRY
		-- bit columns provided in the original upload need a value for each row
		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'EventHidden') BEGIN
			UPDATE #mc_EvImport set EventHidden = '' where EventHidden is null;
			UPDATE #mc_EvImport set EventHidden = '1' where EventHidden in ('Yes','Y','TRUE');
			UPDATE #mc_EvImport set EventHidden = '0' where EventHidden in ('No','N','FALSE');
			
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the EventHidden column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
			FROM #mc_EvImport
			WHERE EventHidden = ''
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			BEGIN TRY
				ALTER TABLE #mc_EvImport ALTER COLUMN EventHidden bit NULL;
			END TRY
			BEGIN CATCH
				INSERT INTO #tblEvErrors (msg)
				VALUES ('There are invalid values in the EventHidden column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

				GOTO on_done;
			END CATCH
		END

		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'EventAllDay') BEGIN
			UPDATE #mc_EvImport set EventAllDay = '' where EventAllDay is null;
			UPDATE #mc_EvImport set EventAllDay = '1' where EventAllDay in ('Yes','Y','TRUE');
			UPDATE #mc_EvImport set EventAllDay = '0' where EventAllDay in ('No','N','FALSE');
			UPDATE #mc_EvImport set EventAllDay = '1' where EventStart = EventEnd;

			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the EventAllDay column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
			FROM #mc_EvImport
			WHERE EventAllDay = ''
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			BEGIN TRY
				ALTER TABLE #mc_EvImport ALTER COLUMN EventAllDay bit NULL;
			END TRY
			BEGIN CATCH
				INSERT INTO #tblEvErrors (msg)
				VALUES ('There are invalid values in the EventAllDay column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

				GOTO on_done;
			END CATCH
		END

		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'EnableRealTimeRoster') BEGIN
			UPDATE #mc_EvImport set EnableRealTimeRoster = '' where EnableRealTimeRoster is null;
			UPDATE #mc_EvImport set EnableRealTimeRoster = '1' where EnableRealTimeRoster in ('Yes','Y','TRUE');
			UPDATE #mc_EvImport set EnableRealTimeRoster = '0' where EnableRealTimeRoster in ('No','N','FALSE');
			
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the EnableRealTimeRoster column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
			FROM #mc_EvImport
			WHERE EnableRealTimeRoster = ''
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			BEGIN TRY
				ALTER TABLE #mc_EvImport ALTER COLUMN EnableRealTimeRoster bit NULL;
			END TRY
			BEGIN CATCH
				INSERT INTO #tblEvErrors (msg)
				VALUES ('There are invalid values in the EnableRealTimeRoster column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

				GOTO on_done;
			END CATCH
		END

		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'DisplayCredits') BEGIN
			UPDATE #mc_EvImport set DisplayCredits = '' where DisplayCredits is null;
			UPDATE #mc_EvImport set DisplayCredits = '1' where DisplayCredits in ('Yes','Y','TRUE');
			UPDATE #mc_EvImport set DisplayCredits = '0' where DisplayCredits in ('No','N','FALSE');

			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the DisplayCredits column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
			FROM #mc_EvImport
			WHERE DisplayCredits = ''
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			BEGIN TRY
				ALTER TABLE #mc_EvImport ALTER COLUMN DisplayCredits bit NULL;
			END TRY
			BEGIN CATCH
				INSERT INTO #tblEvErrors (msg)
				VALUES ('There are invalid values in the DisplayCredits column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

				GOTO on_done;
			END CATCH
		END

		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'ContactInclude') BEGIN
			UPDATE #mc_EvImport set ContactInclude = '' where ContactInclude is null;
			UPDATE #mc_EvImport set ContactInclude = '1' where ContactInclude in ('Yes','Y','TRUE');
			UPDATE #mc_EvImport set ContactInclude = '0' where ContactInclude in ('No','N','FALSE');

			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the ContactInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
			FROM #mc_EvImport
			WHERE ContactInclude = ''
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			BEGIN TRY
				ALTER TABLE #mc_EvImport ALTER COLUMN ContactInclude bit NULL;
			END TRY
			BEGIN CATCH
				INSERT INTO #tblEvErrors (msg)
				VALUES ('There are invalid values in the ContactInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

				GOTO on_done;
			END CATCH
		END

		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'LocationInclude') BEGIN
			UPDATE #mc_EvImport set LocationInclude = '' where LocationInclude is null;
			UPDATE #mc_EvImport set LocationInclude = '1' where LocationInclude in ('Yes','Y','TRUE');
			UPDATE #mc_EvImport set LocationInclude = '0' where LocationInclude in ('No','N','FALSE');

			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the LocationInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
			FROM #mc_EvImport
			WHERE LocationInclude = ''
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			BEGIN TRY
				ALTER TABLE #mc_EvImport ALTER COLUMN LocationInclude bit NULL;
			END TRY
			BEGIN CATCH
				INSERT INTO #tblEvErrors (msg)
				VALUES ('There are invalid values in the LocationInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

				GOTO on_done;
			END CATCH
		END

		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'CancellationInclude') BEGIN
			UPDATE #mc_EvImport set CancellationInclude = '' where CancellationInclude is null;
			UPDATE #mc_EvImport set CancellationInclude = '1' where CancellationInclude in ('Yes','Y','TRUE');
			UPDATE #mc_EvImport set CancellationInclude = '0' where CancellationInclude in ('No','N','FALSE');

			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the CancellationInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
			FROM #mc_EvImport
			WHERE CancellationInclude = ''
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			BEGIN TRY
				ALTER TABLE #mc_EvImport ALTER COLUMN CancellationInclude bit NULL;
			END TRY
			BEGIN CATCH
				INSERT INTO #tblEvErrors (msg)
				VALUES ('There are invalid values in the CancellationInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

				GOTO on_done;
			END CATCH
		END

		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'TravelInclude') BEGIN
			UPDATE #mc_EvImport set TravelInclude = '' where TravelInclude is null;
			UPDATE #mc_EvImport set TravelInclude = '1' where TravelInclude in ('Yes','Y','TRUE');
			UPDATE #mc_EvImport set TravelInclude = '0' where TravelInclude in ('No','N','FALSE');

			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the TravelInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
			FROM #mc_EvImport
			WHERE TravelInclude = ''
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			BEGIN TRY
				ALTER TABLE #mc_EvImport ALTER COLUMN TravelInclude bit NULL;
			END TRY
			BEGIN CATCH
				INSERT INTO #tblEvErrors (msg)
				VALUES ('There are invalid values in the TravelInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

				GOTO on_done;
			END CATCH
		END

		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'ParentEventCode') BEGIN
			-- parenteventcode must be at or under 15 chars
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid ParentEventCode. ParentEventCode must be 15 characters or less.'
			FROM #mc_EvImport
			WHERE len(isnull(ParentEventCode,'')) > 15 
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			-- ParentEventCode cannot be itself
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid ParentEventCode. An event cannot be a child event of itself.'
			FROM #mc_EvImport
			WHERE len(isnull(ParentEventCode,'')) > 0
			and ParentEventCode = EventCode
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			-- match on ParentEventCode for events created before
			update tmp 
			set tmp.MCParentEventID = e.eventID
			from #mc_EvImport as tmp 
			inner join dbo.ev_events as e on e.reportCode = tmp.ParentEventCode
				and e.status = 'A'
				and e.siteID = @siteID
			inner join dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID
				and ce.calendarID = tmp.MCCalendarID
				and ce.calendarID = ce.sourceCalendarID
			where len(isnull(tmp.ParentEventCode,'')) > 0;

			-- match on ParentEventCode for events in this file
			update tmp 
			set tmp.MCParentRowID = tmp2.rowID
			from #mc_EvImport as tmp 
			inner join #mc_EvImport as tmp2 on tmp2.eventCode = tmp.ParentEventCode and tmp.MCCalendarID = tmp2.MCCalendarID
			where len(isnull(tmp.ParentEventCode,'')) > 0;

			-- ParentEventCode cannot be bad
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid ParentEventCode. No matching EventCode in the file or Control Panel.'
			FROM #mc_EvImport
			WHERE len(isnull(ParentEventCode,'')) > 0
			and MCParentEventID is null
			and MCParentRowID is null
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			-- events with a parent event code must appear in the file after the parent event
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is a sub-event but appears after the master event in the file. Ensure all master events are listed before sub-events.'
			FROM #mc_EvImport
			WHERE MCParentRowID is not null
			and rowID < MCParentRowID
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			-- sub events cannot be parent events
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(tmp.rowID as varchar(10)) + ' is a sub-event but also appears as a parent event. Sub-events cannot contain sub-events.'
			FROM #mc_EvImport as tmp
			inner join #mc_EvImport as tmp2 on tmp2.ParentEventCode = tmp.EventCode
			WHERE (tmp.MCParentRowID is not null OR tmp.MCParentEventID is not null)
			ORDER BY tmp.rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END

		IF @recurringEvents = 1 AND NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'RecurringSeriesCode') BEGIN
			-- RecurringSeriesCode must be at or under 15 chars
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid RecurringSeriesCode. RecurringSeriesCode must be 15 characters or less.'
			FROM #mc_EvImport
			WHERE len(isnull(RecurringSeriesCode,'')) > 15 
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			-- calc recurrence order of events; primary event (RecurringSeriesCode matching event) is already defined
			WITH evRecurrenceOrder AS (
				SELECT rowID, ROW_NUMBER() OVER (PARTITION BY RecurringSeriesCode ORDER BY EventStart) AS MCRecurrenceOrder
				FROM #mc_EvImport
				WHERE len(RecurringSeriesCode) > 0
				AND RecurringSeriesCode <> eventCode
			)
			UPDATE tmp
			SET tmp.MCRecurrenceOrder = e.MCRecurrenceOrder
			FROM #mc_EvImport AS tmp
			INNER JOIN evRecurrenceOrder AS e ON e.rowID = tmp.rowID;
		END

	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to validate data in optional columns.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- **************
	-- credit columns
	-- **************
	BEGIN TRY

		select @ASID = min(ASID) from #tblPossibleCredits;
		while @ASID is not null begin
			select @crdAuthorityCode=null, @creditColName = null;

			select @crdAuthorityCode = authorityCode from #tblPossibleCredits where ASID = @ASID;

			IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = @crdAuthorityCode + '_approval') BEGIN
				select @dynSQL = 'UPDATE #mc_EvImport set [' + @crdAuthorityCode + '_approval] = '''' where [' + @crdAuthorityCode + '_approval] is null;';
				EXEC(@dynSQL);

				select @dynSQL = '
					SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' has an invalid value for ' + @crdAuthorityCode + '_approval. Approvals must be 50 characters or less.''
					FROM #mc_EvImport  
					WHERE len(isnull([' + @crdAuthorityCode + '_approval],'''')) > 50 
					ORDER BY rowID';
				INSERT INTO #tblEvErrors (msg)
				EXEC(@dynSQL);
			END

			IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = @crdAuthorityCode + '_status') BEGIN
				select @dynSQL = '
					SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' has an invalid value for ' + @crdAuthorityCode + '_status. Status must be Approved, Denied, Not Submitted, or Pending.''
					FROM #mc_EvImport  
					WHERE [' + @crdAuthorityCode + '_status] not in ('''',''Approved'',''Denied'',''Not Submitted'',''Pending'') 
					ORDER BY rowID';
				INSERT INTO #tblEvErrors (msg)
				EXEC(@dynSQL);
			END

			select @creditColName = min(column_name) from #tblPossibleCreditCols where ASID = @ASID;
			while @creditColName is not null begin
				IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = @creditColName) BEGIN
					set @dynSQL = '
						BEGIN TRY
							UPDATE #mc_EvImport set [' + @creditColName + '] = null where [' + @creditColName + '] is not null and [' + @creditColName + '] = '''';
							ALTER TABLE #mc_EvImport ALTER COLUMN [' + @creditColName + '] decimal(6,2) NULL;
						END TRY
						BEGIN CATCH
							INSERT INTO #tblEvErrors (msg)
							VALUES (''There are invalid values in the ' + @creditColName + ' column.'')
						END CATCH';
					EXEC(@dynSQL);
				END

				select @creditColName = min(column_name) from #tblPossibleCreditCols where ASID = @ASID and column_name > @creditColName;
			end

			select @ASID = min(ASID) from #tblPossibleCredits where ASID > @ASID;
		end

	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to validate data in credit columns.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- ensure credit columns are included in the queue tables
	BEGIN TRY
		insert into platformQueue.dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID) 
		select @queueTypeID, tmp.column_name, tmp.dataTypeID
		from (
			select authorityCode + '_approval' as column_name, 1 as dataTypeID
			from #tblPossibleCredits
				union all
			select authorityCode + '_status' as column_name, 1 as dataTypeID
			from #tblPossibleCredits
				union all	
			select column_name, 2
			from #tblPossibleCreditCols
		) as tmp
			except 
		select queueTypeID, columnName, dataTypeID
		from platformQueue.dbo.tblQueueTypeDataColumns
		where queueTypeID = @queueTypeID;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to add credit columns to the queue tables.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- ****************************
	-- validate cross-event fields
	-- ****************************
	BEGIN TRY		
		select @minColID = min(fieldID) from #tblEvImportCustomCols;
		while @minColID is not null begin
			select @mincol=null, @isRequired=null, @reqMsg = null, @dataTypeCode=null, @displayTypeCode=null;

			select @mincol=columnName, @isRequired=isRequired, @dataTypeCode=dataTypeCode, @displayTypeCode=displayTypeCode
			from #tblEvImportCustomCols 
			where fieldID = @minColID;

			set @reqMsg = '"' + @mincol + '" data is missing.'

			-- required custom column
			IF @isRequired = 1 BEGIN
				set @dynSQL = '
					SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' Column ' + @reqMsg + ' ''
					FROM #mc_EvImport
					WHERE len(ltrim(rtrim(isnull(' + quotename(@mincol) + ','''')))) = 0
					ORDER BY rowID';

				INSERT INTO #tblEvErrors (msg)
				EXEC(@dynSQL)
					IF @@ROWCOUNT > 0 GOTO on_done;
			END

			IF @displayTypeCode in ('SELECT','RADIO','CHECKBOX') BEGIN
				set @reqMsg = 'Column "' + @mincol + '" option "';
				
				-- bit columns support only select box and radio buttons
				IF @dataTypeCode = 'BIT' BEGIN 
					set @good = 1;
					set @dynSQL = '
						set @good = 1
						BEGIN TRY
							UPDATE #mc_EvImport SET ' + quotename(@mincol) + ' = 1 where ' + quotename(@mincol) + ' = ''TRUE'' OR ' + quotename(@mincol) + ' = ''YES'';
							UPDATE #mc_EvImport SET ' + quotename(@mincol) + ' = 0 where ' + quotename(@mincol) + ' = ''FALSE'' OR ' + quotename(@mincol) + ' = ''NO'';
							ALTER TABLE #mc_EvImport ALTER COLUMN ' + quotename(@mincol) + ' bit null
						END TRY
						BEGIN CATCH
							set @good = 0
						END CATCH';
						exec sp_executesql @dynSQL, N'@good bit output', @good output;
					IF @good = 0 BEGIN
						INSERT INTO #tblEvErrors (msg)
						VALUES ('The column ' + @mincol + ' contains invalid boolean values.');	

						GOTO on_done;
					END
				END

				set @dynSQL = '
					SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ' + @reqMsg + ''' + tbl.listitem  + ''" is invalid.''
					from #mc_EvImport
					cross apply dbo.fn_varcharListToTable(' + quotename(@mincol) + ',''|'') as tbl
					inner join dbo.cf_fields as f on f.fieldID = ' + cast(@minColID as varchar(10)) + ' 
					inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
					left outer join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
						and case when ft.dataTypeCode  = ''DATE'' then cast(cast(tbl.listitem as date) as varchar(15)) else tbl.listitem end
							 = case when ft.dataTypeCode = ''STRING'' then cast(fv.valueString as varchar(max))
									 when ft.dataTypeCode = ''DECIMAL2'' then cast(fv.valueDecimal2 as varchar(15))
									 when ft.dataTypeCode = ''INTEGER'' then cast(fv.valueInteger as varchar(10))
									 when ft.dataTypeCode = ''BIT'' then cast(fv.valueBit as varchar(1))
									 when ft.dataTypeCode = ''DATE'' then cast(fv.valueDate as varchar(15))
								else '''' end
					where fv.valueID is null
					and len(ltrim(rtrim(isnull(' + quotename(@mincol) + ','''')))) > 0
					ORDER BY rowID';

				INSERT INTO #tblEvErrors (msg)
				EXEC(@dynSQL)
					IF @@ROWCOUNT > 0 GOTO on_done;
			END

			IF @dataTypeCode ='INTEGER' and @displayTypeCode = 'TEXTBOX' BEGIN 
				set @good = 1;
				set @dynSQL = '
					set @good = 1
					BEGIN TRY
						UPDATE #mc_EvImport SET ' + quotename(@mincol) + ' = replace(' + quotename(@mincol) + ','','','''');
						UPDATE #mc_EvImport SET ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
						ALTER TABLE #mc_EvImport ALTER COLUMN ' + quotename(@mincol) + ' int null;
					END TRY
					BEGIN CATCH
						set @good = 0
					END CATCH';
					exec sp_executesql @dynSQL, N'@good bit output', @good output;
				IF @good = 0
					INSERT INTO #tblEvErrors (msg)
					VALUES ('The column "' + @mincol + '" contains invalid whole numbers.');
			END

			IF @dataTypeCode ='DECIMAL2' and @displayTypeCode = 'TEXTBOX' BEGIN 
				set @good = 1;
				set @dynSQL = '
					set @good = 1
					BEGIN TRY
						UPDATE #mc_EvImport SET ' + quotename(@mincol) + ' = replace(' + quotename(@mincol) + ','','','''');
						UPDATE #mc_EvImport SET ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
						ALTER TABLE #mc_EvImport ALTER COLUMN ' + quotename(@mincol) + ' decimal(14,2) null;
					END TRY
					BEGIN CATCH
						set @good = 0
					END CATCH';
					exec sp_executesql @dynSQL, N'@good bit output', @good output;
				IF @good = 0
					INSERT INTO #tblEvErrors (msg)
					VALUES ('The column "' + @mincol + '" contains invalid decimal values.');
			END

			IF @dataTypeCode ='DATE' and @displayTypeCode = 'DATE' BEGIN 
				set @good = 1;
				set @dynSQL = '
					set @good = 1
					BEGIN TRY
						UPDATE #mc_EvImport SET ' + quotename(@mincol) + ' = replace(' + quotename(@mincol) + ','','','''');
						UPDATE #mc_EvImport SET ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
						ALTER TABLE #mc_EvImport ALTER COLUMN ' + quotename(@mincol) + ' date null;
					END TRY
					BEGIN CATCH
						set @good = 0
					END CATCH';
					exec sp_executesql @dynSQL, N'@good bit output', @good output;
				IF @good = 0
					INSERT INTO #tblEvErrors (msg)
					VALUES ('The column "' + @mincol + '" contains invalid dates.');
			END

			if exists (select 1 from #tblEvErrors)
				GOTO on_done;

			select @minColID = min(fieldID) from #tblEvImportCustomCols where fieldID > @minColID;
		end
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to validate import file for custom columns.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- ensure custom columns are included in the queue tables
	BEGIN TRY
		insert into platformQueue.dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID) 
		select @queueTypeID, tmp.columnName, tmp.dataTypeID
		from (
			select tmp.columnName, dt.dataTypeID
			from #tblEvImportCustomCols as tmp
			inner join platformQueue.dbo.tblQueueTypesDataColumnDataTypes as dt on dt.dataTypeCode = tmp.dataTypeCode
		) as tmp
			except 
		select queueTypeID, columnName, dataTypeID
		from platformQueue.dbo.tblQueueTypeDataColumns
		where queueTypeID = @queueTypeID;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to add cross-event fields to the queue tables.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- *************************
	-- prepare table for unpivot
	-- *************************
	IF NOT EXISTS (select top 1 * from #tblEvErrors) BEGIN
		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN EventCode varchar(200) NOT NULL;
			ALTER TABLE #mc_EvImport ALTER COLUMN ParentEventCode varchar(200) NULL;
			ALTER TABLE #mc_EvImport ALTER COLUMN RecurringSeriesCode varchar(200) NULL;
			ALTER TABLE #mc_EvImport ALTER COLUMN EventTitle varchar(200) NOT NULL;
			ALTER TABLE #mc_EvImport ALTER COLUMN RegistrationReplyEmail varchar(200) NOT NULL;
			ALTER TABLE #mc_EvImport ALTER COLUMN ContactTitle varchar(200) NULL;
			ALTER TABLE #mc_EvImport ALTER COLUMN LocationTitle varchar(200) NULL;
			ALTER TABLE #mc_EvImport ALTER COLUMN CancellationTitle varchar(200) NULL;
			ALTER TABLE #mc_EvImport ALTER COLUMN TravelTitle varchar(200) NULL;
			ALTER TABLE #mc_EvImport ALTER COLUMN InformationTitle varchar(200) NULL;

			select @ASID = null, @crdAuthorityCode=null;

			select @ASID = min(ASID) from #tblPossibleCredits;
			while @ASID is not null begin
				select @crdAuthorityCode = authorityCode from #tblPossibleCredits where ASID = @ASID;

				select @dynSQL = 'ALTER TABLE #mc_EvImport ALTER COLUMN [' + @crdAuthorityCode + '_approval] varchar(50) NULL;';
				EXEC(@dynSQL);
				select @dynSQL = 'ALTER TABLE #mc_EvImport ALTER COLUMN [' + @crdAuthorityCode + '_status] varchar(50) NULL;';
				EXEC(@dynSQL);

				select @ASID = min(ASID) from #tblPossibleCredits where ASID > @ASID;
			end

			select @minColID = null, @dataTypeCode=null, @mincol=null;

			select @minColID = min(fieldID) from #tblEvImportCustomCols;
			while @minColID is not null begin
				select @dataTypeCode = dataTypeCode, @mincol = columnName
				from #tblEvImportCustomCols 
				where fieldID = @minColID;

				if @dataTypeCode = 'STRING' begin
					select @dynSQL = 'ALTER TABLE #mc_EvImport ALTER COLUMN [' + @mincol + '] varchar(255) NULL;';
					EXEC(@dynSQL);
				end

				select @minColID = min(fieldID) from #tblEvImportCustomCols where fieldID > @minColID;
			end
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			VALUES ('Unable to prepare data for unpivot.');

			INSERT INTO #tblEvErrors (msg)
			VALUES (left(error_message(),300));

			GOTO on_done;
		END CATCH
	END

	-- ************************
	-- generate result xml file 
	-- ************************
	on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblEvErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);
	
	IF OBJECT_ID('tempdb..#tblEventCreditCols') IS NOT NULL 
		DROP TABLE #tblEventCreditCols;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ev_queueCreatingRecurringEvents 
@siteID int,
@createdFromEventID int,
@afID int,
@endDate datetime,
@recordedByMemberID int,
@recurringEventsImportResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#mc_EvImport') IS NOT NULL
		DROP TABLE #mc_EvImport;
	IF OBJECT_ID('tempdb..#tmpRecurringEvents') IS NOT NULL
		DROP TABLE #tmpRecurringEvents;
	IF OBJECT_ID('tempdb..#tmp_CF_ItemIDs') IS NOT NULL
		DROP TABLE #tmp_CF_ItemIDs;
	IF OBJECT_ID('tempdb..#tmp_CF_FieldData') IS NOT NULL
		DROP TABLE #tmp_CF_FieldData;
	IF OBJECT_ID('tempdb..#tmpSWCF') IS NOT NULL
		DROP TABLE #tmpSWCF;
	
	-- bit cols defined as varchar for import validation
	CREATE TABLE #mc_EvImport (rowID int, Calendar varchar(100), EventTitle varchar(200), EventSubTitle varchar(200), InternalNotes varchar(max), EventCode varchar(15) NOT NULL, EventCategory varchar(max), 
		EventStart datetime, EventEnd datetime, EventHidden varchar(10), EventAllDay varchar(10), EventDescription varchar(max), ContactTitle varchar(200), 
		Contact varchar(max), ContactInclude varchar(max), LocationTitle varchar(200), Location varchar(max), LocationInclude varchar(max), 
		CancellationTitle varchar(200), Cancellation varchar(max), CancellationInclude varchar(max), TravelTitle varchar(200), Travel varchar(max), TravelInclude varchar(max), 
		InformationTitle varchar(200), Information varchar(max), RecurringSeriesCode varchar(15), RegistrationReplyEmail varchar(400));
	CREATE TABLE #tmpRecurringEvents (rowID int PRIMARY KEY IDENTITY(1,1), createdFromEventID int, eventCode varchar(15), startDate datetime, endDate datetime);
	CREATE TABLE #tmp_CF_ItemIDs (itemID int, itemType varchar(20));
	CREATE TABLE #tmp_CF_FieldData (fieldID int, fieldValue varchar(max), amount decimal(14,2), itemID int);

	DECLARE @recurringEvents bit, @seriesID int, @eventStart datetime, @eventEnd datetime, @swcfList varchar(max), 
		@fullsql varchar(max), @tmpSuffix varchar(10), @datePart varchar(20), @dateNum int, @adjustTerm varchar(12), 
		@nextWeekday int, @weekNumber varchar(4), @eventStartDate datetime, @eventEndDate datetime, @minRowID int,
		@eventCode varchar(15), @calendarPageName varchar(50), @categoryIDList varchar(200), @categoryList varchar(max);

	SET @tmpSuffix = CAST(@createdFromEventID AS varchar(10));

	SELECT @recurringEvents = recurringEvents
	FROM dbo.siteFeatures 
	WHERE siteID = @siteID;

	SELECT @seriesID = seriesID
	FROM dbo.ev_recurringSeries
	WHERE createdFromEventID = @createdFromEventID
	AND siteID = @siteID;

	IF @recurringEvents = 0 OR @seriesID IS NOT NULL
		GOTO on_done;

	SET @endDate = DATEADD(MS,-3,DATEADD(DAY,1,@endDate));

	SELECT @eventStartDate = et.startTime, @eventEndDate = et.endTime, @calendarPageName = aip.pageName,
		@categoryIDList = cat.categoryIDList
	FROM dbo.ev_events AS e
	INNER JOIN dbo.ev_times AS et ON et.eventid = e.eventID
		AND et.timeID = e.defaultTimeID
	INNER JOIN dbo.ev_calendarEvents AS ce ON ce.sourceEventID = e.eventID 
		AND ce.calendarID = ce.sourceCalendarID
	INNER JOIN dbo.ev_calendars AS c ON c.siteID = @siteID
		AND c.calendarID = ce.calendarID
	INNER JOIN dbo.cms_applicationInstances AS ai ON ai.siteID = @siteID
		AND ai.applicationInstanceID = c.applicationInstanceID
	CROSS APPLY dbo.fn_cms_getApplicationInstancePagePath(@siteID,c.applicationInstanceID) as aip
	LEFT OUTER JOIN dbo.cache_calendarEventsCategoryIDList AS cat ON cat.eventID = e.eventID 
		AND cat.calendarID = ce.calendarID
	WHERE e.eventID = @createdFromEventID
	AND e.siteID = @siteID
	AND aip.applicationSiteResourceID = ai.siteResourceID
	AND aip.applicationSiteResourceType = 'Events';

	IF CAST(@endDate AS DATE) > CAST(DATEADD(YEAR,2,@eventStartDate) AS DATE)
		RAISERROR('Invalid Recurring End Date.', 16, 1);

	SELECT @datePart = [datePart], @dateNum = dateNum, @adjustTerm = adjustTerm, @nextWeekday = nextWeekday, @weekNumber = weekNumber
	FROM dbo.af_advanceFormulas
	WHERE AFID = @afID
	AND siteID = @siteID;

	WITH evDates AS (
		SELECT dbo.fn_af_getAFDate(@eventStartDate,@datePart,@dateNum,@adjustTerm,@nextWeekday,@weekNumber) AS startDate, 
			dbo.fn_af_getAFDate(@eventEndDate,@datePart,@dateNum,@adjustTerm,@nextWeekday,@weekNumber) AS endDate,
			1 AS rowNum
		UNION ALL
		SELECT startDate, endDate, rowNum
		FROM (
			SELECT dbo.fn_af_getAFDate(@eventStartDate,@datePart,@dateNum * (rowNum + 1),@adjustTerm,@nextWeekday,@weekNumber) AS startDate, 
				dbo.fn_af_getAFDate(@eventEndDate,@datePart,@dateNum * (rowNum + 1),@adjustTerm,@nextWeekday,@weekNumber) AS endDate,
				rowNum + 1 AS rowNum
			FROM evDates
		) tmp
		WHERE startDate <= @endDate
	)
	INSERT INTO #tmpRecurringEvents (createdFromEventID, startDate, endDate)
	SELECT @createdFromEventID, startDate, endDate
	FROM evDates
	OPTION (MAXRECURSION 300);

	SELECT @minRowID = MIN(rowID) FROM #tmpRecurringEvents;
	WHILE @minRowID IS NOT NULL BEGIN
		SET @eventCode = NULL;

		EXEC dbo.getUniqueCode @uniqueCode=@eventCode OUTPUT;

		UPDATE #tmpRecurringEvents
		SET eventCode = @eventCode
		WHERE rowID = @minRowID;

		SELECT @minRowID = MIN(rowID) FROM #tmpRecurringEvents WHERE rowID > @minRowID;
	END

	SELECT @categoryList = STRING_AGG(evCat.category,'|')
	FROM dbo.fn_IntListToTable(@categoryIDList,',') as tmpCat
	INNER JOIN dbo.ev_categories as evCat on evCat.categoryID = tmpCat.listItem;

	SET @categoryList = ISNULL(@categoryList,'');

	-- event custom fields	
	IF OBJECT_ID('tempdb..##tmpSWCF'+@tmpSuffix) IS NOT NULL
		EXEC ('DROP TABLE ##tmpSWCF'+@tmpSuffix);
	
	INSERT INTO #tmp_CF_ItemIDs (itemID, itemType)
	SELECT siteResourceID, 'CrossEvent'
	FROM dbo.ev_events
	WHERE eventID = @createdFromEventID
	AND siteID = @siteID;

	EXEC dbo.cf_getFieldData;

	SELECT e.eventID, replace(f.fieldReference,',','') as fieldReference, 
		CASE WHEN ft.displayTypeCode IN ('SELECT','RADIO','CHECKBOX') THEN REPLACE(fd.fieldValue,', ', '|') ELSE fd.fieldValue END AS answer
	INTO #tmpSWCF
	FROM #tmp_CF_FieldData AS fd
	INNER JOIN dbo.cf_fields AS f ON f.fieldID = fd.fieldID
	INNER JOIN dbo.cf_fieldTypes AS ft ON ft.fieldTypeID = f.fieldTypeID
	INNER JOIN dbo.ev_events as e ON e.siteID = @siteID and e.siteResourceID = fd.itemID;

	-- event custom fields pivoted
	set @swcfList = '';
	select @swcfList = COALESCE(@swcfList + ',', '') + quoteName(fieldReference) from #tmpSWCF group by fieldReference;
	IF left(@swcfList,1) = ','
		set @swcfList = right(@swcfList,len(@swcfList)-1);
	IF len(@swcfList) > 0 BEGIN
		-- add swcf cols to import table
		select @fullsql = COALESCE(@fullsql, '') + 'ALTER TABLE #mc_EvImport ADD ' + quoteName(fieldReference)  + ' varchar(max);'
		from #tmpSWCF 
		group by fieldReference;

		EXEC(@fullsql);

		set @fullsql = '
			select * 
			into ##tmpSWCF'+@tmpSuffix+'
			from (
				select eventID, fieldReference, answer
				from #tmpSWCF
			) as cf
			PIVOT (min(answer) for fieldReference in (' + @swcfList + ')) as p ';
		EXEC(@fullsql);
	END
	ELSE
		EXEC('SELECT eventID INTO ##tmpSWCF'+@tmpSuffix+' FROM #tmpSWCF WHERE 0=1');

	SET @fullsql = 'SELECT ROW_NUMBER() OVER (ORDER BY tmp.startDate) AS rowID, '''+@calendarPageName+''', eventcontent.contentTitle, ev.eventSubTitle, ev.internalNotes, tmp.eventCode, '''+@categoryList+''', tmp.startDate, tmp.endDate, 
		ISNULL(ev.hiddenFromCalendar,0), ev.isAllDayEvent, eventcontent.rawContent, contactcontent.contentTitle, contactcontent.rawContent, ISNULL(ev.emailContactContent,0),
		locationcontent.contentTitle, locationcontent.rawContent, ISNULL(ev.emailLocationContent,0), cancelcontent.contentTitle, cancelcontent.rawContent, 
		ISNULL(ev.emailCancelContent,0), travelcontent.contentTitle, travelcontent.rawContent, ISNULL(ev.emailTravelContent,0), 
		informationcontent.contentTitle, informationcontent.rawContent, ev.reportCode, '''' AS RegistrationReplyEmail';
	IF len(@swcfList) > 0
		SET @fullsql = @fullsql + ', swcf.' + replace(@swcfList,',',',swcf.');
	SET @fullsql = @fullsql + '
		FROM #tmpRecurringEvents AS tmp
		INNER JOIN dbo.ev_events AS ev ON ev.siteID = '+CAST(@siteID AS varchar(10))+' AND ev.eventID = tmp.createdFromEventID
		CROSS APPLY dbo.fn_getContent(ev.eventcontentID,1) AS eventcontent
		CROSS APPLY dbo.fn_getContent(ev.locationcontentID,1) AS locationcontent
		CROSS APPLY dbo.fn_getContent(ev.travelcontentID,1) AS travelcontent
		CROSS APPLY dbo.fn_getContent(ev.contactcontentID,1) AS contactcontent
		CROSS APPLY dbo.fn_getContent(ev.cancellationPolicycontentID,1) AS cancelcontent
		CROSS APPLY dbo.fn_getContent(ev.informationContentID,1) AS informationcontent';
	IF len(@swcfList) > 0
		SET @fullsql = @fullsql + '
			LEFT OUTER JOIN ##tmpSWCF'+@tmpSuffix+' AS swcf ON swcf.eventID = ev.eventID';

	
	INSERT INTO #mc_EvImport
	EXEC(@fullsql);

	-- drop global table
	IF OBJECT_ID('tempdb..##tmpSWCF'+@tmpSuffix) IS NOT NULL
		EXEC ('DROP TABLE ##tmpSWCF'+@tmpSuffix);

	BEGIN TRAN;
		INSERT INTO dbo.ev_recurringSeries (siteID, createdFromEventID, afID)
		VALUES (@siteID, @createdFromEventID, @afID);

		SET @seriesID = SCOPE_IDENTITY();

		UPDATE dbo.ev_events
		SET recurringSeriesID = @seriesID
		WHERE eventID = @createdFromEventID;
	COMMIT TRAN;

	-- queue recurring events import
	EXEC dbo.ev_importEvents @siteID=@siteID, @recordedByMemberID=@recordedByMemberID, @ovAction='s', @importResult=@recurringEventsImportResult OUTPUT;

	on_done:

	IF OBJECT_ID('tempdb..#mc_EvImport') IS NOT NULL
		DROP TABLE #mc_EvImport;
	IF OBJECT_ID('tempdb..#tmpRecurringEvents') IS NOT NULL
		DROP TABLE #tmpRecurringEvents;
	IF OBJECT_ID('tempdb..#tmp_CF_ItemIDs') IS NOT NULL
		DROP TABLE #tmp_CF_ItemIDs;
	IF OBJECT_ID('tempdb..#tmp_CF_FieldData') IS NOT NULL
		DROP TABLE #tmp_CF_FieldData;
	IF OBJECT_ID('tempdb..#tmpSWCF') IS NOT NULL
		DROP TABLE #tmpSWCF;
	IF OBJECT_ID('tempdb..##tmpSWCF'+@tmpSuffix) IS NOT NULL
		EXEC ('DROP TABLE ##tmpSWCF'+@tmpSuffix);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROCEDURE dbo.ev_updateUpcomingRecurringEvents 
@siteID int,
@copyFromEventID int,
@recordedByMemberID int,
@recurringEventsImportResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#mc_EvImport') IS NOT NULL
		DROP TABLE #mc_EvImport;
	IF OBJECT_ID('tempdb..#tmpExistingRecurringEvents') IS NOT NULL
		DROP TABLE #tmpExistingRecurringEvents;
	IF OBJECT_ID('tempdb..#tmp_CF_ItemIDs') IS NOT NULL
		DROP TABLE #tmp_CF_ItemIDs;
	IF OBJECT_ID('tempdb..#tmp_CF_FieldData') IS NOT NULL
		DROP TABLE #tmp_CF_FieldData;
	IF OBJECT_ID('tempdb..#tmpSWCF') IS NOT NULL
		DROP TABLE #tmpSWCF;
	
	-- bit cols defined as varchar for import validation
	CREATE TABLE #mc_EvImport (rowID int, Calendar varchar(100), EventTitle varchar(200), EventSubTitle varchar(200), InternalNotes varchar(max), EventCode varchar(15) NOT NULL, EventCategory varchar(max), 
		EventStart datetime, EventEnd datetime, EventHidden varchar(10), EventAllDay varchar(10), EventDescription varchar(max), ContactTitle varchar(200), 
		Contact varchar(max), ContactInclude varchar(max), LocationTitle varchar(200), Location varchar(max), LocationInclude varchar(max), 
		CancellationTitle varchar(200), Cancellation varchar(max), CancellationInclude varchar(max), TravelTitle varchar(200), Travel varchar(max), TravelInclude varchar(max), 
		InformationTitle varchar(200), Information varchar(max), RegistrationReplyEmail varchar(400));
	CREATE TABLE #tmpExistingRecurringEvents (eventID int PRIMARY KEY, siteResourceID int, eventCode varchar(15), startDate datetime, endDate datetime);
	CREATE TABLE #tmp_CF_ItemIDs (itemID int, itemType varchar(20));
	CREATE TABLE #tmp_CF_FieldData (fieldID int, fieldValue varchar(max), amount decimal(14,2), itemID int);

	DECLARE @recurringEvents bit, @recurringSeriesID int, @swcfList varchar(max), @fullsql varchar(max), @tmpSuffix varchar(36), 
		@eventStartDate datetime, @eventEndDate datetime, @calendarPageName varchar(50), @categoryIDList varchar(200), 
		@categoryList varchar(max), @defaultTimeZoneID int, @eventSiteResourceID int;

	SET @tmpSuffix = REPLACE(CAST(NEWID() AS varchar(36)),'-','');

	SELECT @defaultTimeZoneID = defaultTimeZoneID 
	FROM dbo.sites 
	WHERE siteID = @siteID;

	SELECT @recurringEvents = recurringEvents
	FROM dbo.siteFeatures 
	WHERE siteID = @siteID;

	SELECT @recurringSeriesID = e.recurringSeriesID, @eventStartDate = et.startTime, @eventEndDate = et.endTime, 
		@calendarPageName = aip.pageName, @categoryIDList = cat.categoryIDList, @eventSiteResourceID = e.siteResourceID
	FROM dbo.ev_events AS e
	INNER JOIN dbo.ev_times AS et ON et.eventid = e.eventID
		AND et.timeID = e.defaultTimeID
	INNER JOIN dbo.ev_calendarEvents AS ce ON ce.sourceEventID = e.eventID 
		AND ce.calendarID = ce.sourceCalendarID
	INNER JOIN dbo.ev_calendars AS c ON c.siteID = @siteID
		AND c.calendarID = ce.calendarID
	INNER JOIN dbo.cms_applicationInstances AS ai ON ai.siteID = @siteID
		AND ai.applicationInstanceID = c.applicationInstanceID
	CROSS APPLY dbo.fn_cms_getApplicationInstancePagePath(@siteID,c.applicationInstanceID) as aip
	LEFT OUTER JOIN dbo.cache_calendarEventsCategoryIDList AS cat ON cat.eventID = e.eventID 
		AND cat.calendarID = ce.calendarID
	WHERE e.eventID = @copyFromEventID
	AND e.siteID = @siteID
	AND aip.applicationSiteResourceID = ai.siteResourceID
	AND aip.applicationSiteResourceType = 'Events';

	-- site doesn't support recurring events or not an recurring event
	IF @recurringEvents = 0 OR @recurringSeriesID IS NULL
		GOTO on_done;

	SELECT @categoryList = STRING_AGG(evCat.category,'|')
	FROM dbo.fn_IntListToTable(@categoryIDList,',') as tmpCat
	INNER JOIN dbo.ev_categories as evCat on evCat.categoryID = tmpCat.listItem;

	SET @categoryList = ISNULL(@categoryList,'');

	-- upcoming recurring events
	INSERT INTO #tmpExistingRecurringEvents (eventID, siteResourceID, eventCode, startDate, endDate)
	SELECT e.eventID, e.siteResourceID, e.reportCode, et.startTime, et.endTime
	FROM dbo.ev_events AS e
	INNER JOIN dbo.ev_times AS et ON et.eventid = e.eventID
		AND et.timeID = e.defaultTimeID
	WHERE e.recurringSeriesID = @recurringSeriesID
	AND e.siteID = @siteID
	AND e.[status] IN ('A','I')
	AND et.startTime > @eventStartDate;

	IF @@ROWCOUNT = 0
		GOTO on_done;
	
	-- event custom fields	
	IF OBJECT_ID('tempdb..##tmpSWCF'+@tmpSuffix) IS NOT NULL
		EXEC ('DROP TABLE ##tmpSWCF'+@tmpSuffix);
	
	INSERT INTO #tmp_CF_ItemIDs (itemID, itemType)
	SELECT siteResourceID, 'CrossEvent'
	FROM dbo.ev_events
	WHERE eventID = @copyFromEventID
	AND siteID = @siteID;

	EXEC dbo.cf_getFieldData;

	SELECT e.eventID, replace(f.fieldReference,',','') as fieldReference, 
		CASE WHEN ft.displayTypeCode IN ('SELECT','RADIO','CHECKBOX') THEN REPLACE(fd.fieldValue,', ', '|') ELSE fd.fieldValue END AS answer
	INTO #tmpSWCF
	FROM #tmp_CF_FieldData AS fd
	INNER JOIN dbo.cf_fields AS f ON f.fieldID = fd.fieldID
	INNER JOIN dbo.cf_fieldTypes AS ft ON ft.fieldTypeID = f.fieldTypeID
	INNER JOIN dbo.ev_events as e ON e.siteID = @siteID and e.siteResourceID = fd.itemID;

	-- event custom fields pivoted
	set @swcfList = '';
	select @swcfList = COALESCE(@swcfList + ',', '') + quoteName(fieldReference) from #tmpSWCF group by fieldReference;
	IF left(@swcfList,1) = ','
		set @swcfList = right(@swcfList,len(@swcfList)-1);
	IF len(@swcfList) > 0 BEGIN
		-- add swcf cols to import table
		select @fullsql = COALESCE(@fullsql, '') + 'ALTER TABLE #mc_EvImport ADD ' + quoteName(fieldReference)  + ' varchar(max);'
		from #tmpSWCF 
		group by fieldReference;

		EXEC(@fullsql);

		set @fullsql = '
			select * 
			into ##tmpSWCF'+@tmpSuffix+'
			from (
				select eventID, fieldReference, answer
				from #tmpSWCF
			) as cf
			PIVOT (min(answer) for fieldReference in (' + @swcfList + ')) as p ';
		EXEC(@fullsql);
	END
	ELSE
		EXEC('SELECT eventID INTO ##tmpSWCF'+@tmpSuffix+' FROM #tmpSWCF WHERE 0=1');

	-- prep final data for import
	SET @fullsql = 'SELECT DISTINCT ROW_NUMBER() OVER (ORDER BY tmp.startDate) AS rowID, '''+@calendarPageName+''', eventcontent.contentTitle, ev.eventSubTitle, ev.internalNotes, tmp.eventCode, '''+@categoryList+''', 
		tmp.startDate, tmp.endDate, ISNULL(ev.hiddenFromCalendar,0), ev.isAllDayEvent, eventcontent.rawContent, contactcontent.contentTitle, contactcontent.rawContent, 
		ISNULL(ev.emailContactContent,0), locationcontent.contentTitle, locationcontent.rawContent, ISNULL(ev.emailLocationContent,0), cancelcontent.contentTitle, cancelcontent.rawContent, 
		ISNULL(ev.emailCancelContent,0), travelcontent.contentTitle, travelcontent.rawContent, ISNULL(ev.emailTravelContent,0), 
		informationcontent.contentTitle, informationcontent.rawContent, '''' AS RegistrationReplyEmail';
	IF len(@swcfList) > 0
		SET @fullsql = @fullsql + ', swcf.' + replace(@swcfList,',',',swcf.');
	SET @fullsql = @fullsql + '
		FROM #tmpExistingRecurringEvents AS tmp
		INNER JOIN dbo.ev_events AS ev ON ev.siteID = '+CAST(@siteID AS varchar(10))+' AND ev.eventID = '+CAST(@copyFromEventID AS varchar(10))+'
		CROSS APPLY dbo.fn_getContent(ev.eventcontentID,1) AS eventcontent
		CROSS APPLY dbo.fn_getContent(ev.locationcontentID,1) AS locationcontent
		CROSS APPLY dbo.fn_getContent(ev.travelcontentID,1) AS travelcontent
		CROSS APPLY dbo.fn_getContent(ev.contactcontentID,1) AS contactcontent
		CROSS APPLY dbo.fn_getContent(ev.cancellationPolicycontentID,1) AS cancelcontent
		CROSS APPLY dbo.fn_getContent(ev.informationContentID,1) AS informationcontent';
	IF len(@swcfList) > 0
		SET @fullsql = @fullsql + '
			LEFT OUTER JOIN ##tmpSWCF'+@tmpSuffix+' AS swcf ON swcf.eventID = ev.eventID';

	INSERT INTO #mc_EvImport
	EXEC(@fullsql);

	-- drop global table
	IF OBJECT_ID('tempdb..##tmpSWCF'+@tmpSuffix) IS NOT NULL
		EXEC ('DROP TABLE ##tmpSWCF'+@tmpSuffix);

	-- queue recurring events import
	EXEC dbo.ev_importEvents @siteID=@siteID, @recordedByMemberID=@recordedByMemberID, @ovAction='o', @importResult=@recurringEventsImportResult OUTPUT;

	IF @recurringEventsImportResult.value('count(/import/errors/error)','int') = 0 BEGIN
		-- update asset categories
		DELETE csr
		FROM dbo.cms_categorySiteResources AS csr
		INNER JOIN #tmpExistingRecurringEvents AS tmp ON tmp.siteResourceID = csr.siteResourceID;

		INSERT INTO dbo.cms_categorySiteResources (categoryID, siteResourceID)
		SELECT DISTINCT csr.categoryID, tmp.siteResourceID
		FROM #tmpExistingRecurringEvents AS tmp
		INNER JOIN dbo.cms_categorySiteResources AS csr ON csr.siteResourceID = @eventSiteResourceID;
	END

	on_done:

	IF OBJECT_ID('tempdb..#mc_EvImport') IS NOT NULL
		DROP TABLE #mc_EvImport;
	IF OBJECT_ID('tempdb..#tmpExistingRecurringEvents') IS NOT NULL
		DROP TABLE #tmpExistingRecurringEvents;
	IF OBJECT_ID('tempdb..#tmp_CF_ItemIDs') IS NOT NULL
		DROP TABLE #tmp_CF_ItemIDs;
	IF OBJECT_ID('tempdb..#tmp_CF_FieldData') IS NOT NULL
		DROP TABLE #tmp_CF_FieldData;
	IF OBJECT_ID('tempdb..#tmpSWCF') IS NOT NULL
		DROP TABLE #tmpSWCF;
	IF OBJECT_ID('tempdb..##tmpSWCF'+@tmpSuffix) IS NOT NULL
		EXEC ('DROP TABLE ##tmpSWCF'+@tmpSuffix);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
