ALTER PROC dbo.swod_getSeminar
@seminarID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SELECT TOP 1 s.seminarID, 'SWOD' AS SWType, s.seminarName, s.seminarSubTitle, s.seminarDesc, s.isPublished, s.programCode,
		s.offerCertificate, s.allowRegistrants, s.revenueGLAccountID, s.dateCatalogStart, s.dateCatalogEnd, sod.dateOrigPublished,
		sod.offerQA, sod.priceSyndication, sod.blankOnInactivity, sod.allowSyndication, tla.[Description],
		p.orgcode AS publisherOrgCode, p.showUSD, l.layout, sod.seminarLength, sod.layoutID, sod.introMessageText, sod.endofSeminarText,
		(SELECT TOP 1 preReqSeminarID from dbo.tblSeminarsPreReqs WHERE seminarID = @seminarID) AS preReq,
		s.isPriceBasedOnActual, s.lockSettings, s.pushDefaultPricingToOptIns, s.allowOptInRateChange,
		p.handlesOwnPayment, s.freeRateDisplay, sod.dateActivated, s.preventSeminarFees,
		mActive.memberID as submittedByMemberID, mActive.firstname AS submitterFirstName, mActive.firstname + ' ' + mActive.lastname AS submittedByMember, me.email as submittedByEmail, mcs.siteID AS participantSiteID
	FROM dbo.tblSeminars AS s 
	INNER JOIN dbo.tblSeminarsSWOD AS sod ON s.seminarID = sod.seminarID 
	INNER JOIN dbo.tblParticipants AS p ON s.participantID = p.participantID 
	INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
	LEFT JOIN memberCentral.dbo.ams_members as m on m.memberID = sod.submittedByMemberID
	LEFT JOIN memberCentral.dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
	LEFT JOIN memberCentral.dbo.ams_memberEmails as me on me.memberID = mActive.memberID AND me.orgID in (mcs.orgID,1)
	LEFT JOIN memberCentral.dbo.ams_memberEmailTags AS metag ON metag.orgID = m.orgID AND metag.memberID = me.memberID AND metag.emailTypeID = me.emailTypeID
	LEFT JOIN memberCentral.dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID = m.orgID AND metagt.emailTagTypeID = metag.emailTagTypeID AND metagt.emailTagType = 'Primary'
	INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.[State] = p.orgcode
	INNER JOIN dbo.tblSeminarsSWODLayouts AS l ON l.layoutID = sod.layoutID
	WHERE s.seminarID = @seminarID
	AND s.isDeleted = 0;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
