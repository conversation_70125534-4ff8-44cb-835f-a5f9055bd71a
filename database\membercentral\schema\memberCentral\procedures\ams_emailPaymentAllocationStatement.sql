CREATE PROC dbo.ams_emailPaymentAllocationStatement
@siteID int,
@memberID int,
@transactionID int,
@messageToParse varchar(max),
@messageWrapper varchar(max),
@emailTagTypeID int,
@emailFromName varchar(200),
@emailReplyTo varchar(200),
@emailSubject varchar(200),
@contentVersionID int,
@recordedByMemberID int,
@deliveryReportEmail varchar(200),
@overrideEmailList varchar(max),
@sendOnDate datetime,
@markRecipientAsReady bit

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpRecipientsMID') IS NOT NULL
		DROP TABLE #tmpRecipientsMID;
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL
		DROP TABLE #tmpRecipients;
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL
		DROP TABLE #tmpRecipientsCols;
	IF OBJECT_ID('tempdb..#tmpRecipentEmails') IS NOT NULL
		DROP TABLE #tmpRecipentEmails;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;
	CREATE TABLE #tmpRecipientsCols (ORDINAL_POSITION int, COLUMN_NAME sysname, datatype varchar(40));
	CREATE TABLE #tmpRecipientsMID (memberID int, mc_emailBlast_email varchar(255), mc_emailBlast_emailTypeID int);
	CREATE TABLE #tmpRecipentEmails (email varchar(255));
	CREATE TABLE #tmpMergeMDMemberIDs (memberID int PRIMARY KEY);
	CREATE TABLE #tmpMergeMDResults (MCAutoID int IDENTITY(1,1) NOT NULL);

	declare @orgID int, @numRecipients int, @messageTypeID int, @messageStatusIDInserting int, @messageStatusIDQueued int,
		@sendingSiteResourceID int, @supportProviderEmail varchar(100), @supportProviderName varchar(100),
		@defaultOrgIdentityID int, @messageID int, @rawcontent varchar(max), @fieldID int, @fieldName varchar(300),
		@vwSQL varchar(max), @ParamDefinition nvarchar(100), @mcSQL nvarchar(max), @colList varchar(max),
		@colDataType varchar(40), @fieldValueString varchar(200), @contentToParse varchar(max);
	declare @metadataFields TABLE (fieldName varchar(300), fieldID int NULL);

	select @orgID = s.orgID, @sendingSiteResourceID = st.siteResourceID, @defaultOrgIdentityID = s.defaultOrgIdentityID
	from dbo.sites as s
	inner join dbo.admin_siteTools as st on st.siteID = s.siteID and st.siteID = @siteID
	inner join dbo.admin_toolTypes as tt on tt.toolTypeID = st.toolTypeID and tt.toolType = 'TransactionAdmin';

	-- validate transaction exists and is a payment
	IF NOT EXISTS (SELECT 1 FROM dbo.tr_transactions as tts INNER JOIN tr_types as tt ON tts.typeID = tt.typeID WHERE transactionID = @transactionID AND ownedByOrgID = @orgID AND type = 'Payment')
		RAISERROR('Invalid payment transaction ID.',16,1);

	IF @sendOnDate < getDate()
		set @sendOnDate = getDate();

	IF @overrideEmailList IS NOT NULL BEGIN
		INSERT INTO #tmpRecipentEmails (email)
		SELECT distinct listitem
		FROM dbo.fn_varCharListToTableInline(@overrideEmailList,';');

		INSERT INTO #tmpRecipientsMID (memberID, mc_emailBlast_email, mc_emailBlast_emailTypeID)
		select distinct mActive.memberID, tmp.email, metag.emailTypeID
		from dbo.ams_members as m
		inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
		inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID and metag.memberID = mActive.memberID
		inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID and metagt.emailTagTypeID = metag.emailTagTypeID
		cross join #tmpRecipentEmails as tmp
		where m.orgID = @orgID
		and m.activeMemberID = @memberID
		and metagt.emailTagType = 'Primary';
	END
	ELSE
		INSERT INTO #tmpRecipientsMID (memberID, mc_emailBlast_email, mc_emailBlast_emailTypeID)
		select distinct mActive.memberID, me.email, metag.emailTypeID
		from dbo.ams_members as m
		inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
		inner join dbo.ams_memberEmails as me on me.orgID = @orgID
			and me.memberID = mActive.memberID
		inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID and metag.memberID = me.memberID
			and metag.emailTypeID = me.emailTypeID
		inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID
			and metagt.emailTagTypeID = metag.emailTagTypeID
			and metagt.emailTagTypeID = @emailTagTypeID
		where m.orgID = @orgID
		and m.activeMemberID = @memberID
		and mActive.status = 'A'
		and len(me.Email) > 0;

	select @numRecipients = count(*) from #tmpRecipientsMID;

	IF @numRecipients = 0
		RAISERROR('No recipients for message.',16,1);

	-- Get message type for Payment Allocation Statement emails
	select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'EMAILPAYALLOC';
	select @messageStatusIDInserting = statusID from platformMail.dbo.email_statuses where statusCode = 'I';
	select @messageStatusIDQueued = statusID from platformMail.dbo.email_statuses where statusCode = 'Q';

	select TOP 1 @supportProviderName = net.supportProviderName, @supportProviderEmail = net.supportProviderEmail
	from dbo.networks as net
	inner join dbo.networkSites as ns on net.networkID = ns.networkID
	inner join dbo.sites as s on s.siteID = ns.siteID
	where s.siteID = @siteID
	and ns.isLoginNetwork = 1;

	-- add any necessary metadata fields
	SET @contentToParse = @messageToParse + isnull(@emailSubject,'');

	declare @regexMergeCode varchar(40);
	select @regexMergeCode = regexMergeCode from dbo.fn_getServerSettings();

	insert into @metadataFields (fieldName)
	select distinct left([Text],300)
	from dbo.fn_RegexMatches(@contentToParse,@regexMergeCode);

	INSERT INTO #tmpMergeMDMemberIDs (memberID)
	SELECT DISTINCT memberID
	FROM #tmpRecipientsMID;

	EXEC dbo.ams_getMemberDataByMergeCodeContent @orgID=@orgID, @content=@contentToParse,
		@codePrefix='', @membersTableName='#tmpMergeMDMemberIDs', @membersResultTableName='#tmpMergeMDResults',
		@colList=@colList OUTPUT;

	IF OBJECT_ID('tempdb..##tmpPayAllocMemberData') IS NOT NULL
		DROP TABLE ##tmpPayAllocMemberData;

	IF @colList is null
		select memberID
		into ##tmpPayAllocMemberData
		from #tmpRecipientsMID;
	ELSE BEGIN
		set @vwSQL = 'select m.memberID, ' + @colList + '
			into ##tmpPayAllocMemberData
			from #tmpRecipientsMID as m
			inner join #tmpMergeMDResults as vwmd on vwmd.memberID = m.memberID;';
		EXEC(@vwSQL);
	END

	select distinct m.prefix, m.firstName, m.middlename, m.lastName, m.company, m.suffix, m.professionalsuffix,
		m.membernumber, m.firstname + ' ' + m.lastname as fullname,
		m.firstname + isnull(' ' + nullif(m.middlename,''),'') + ' ' + m.lastname + isnull(' ' + nullif(m.suffix,''),'') as extendedname,
		i.organizationName, i.organizationShortName, i.address1 as organizationAddress1, i.address2 as organizationAddress2,
		i.city as organizationCity, s.Code as organizationStateCode, s.Name as organizationState, c.country as organizationCountry,
		c.countryCode as organizationCountryCode, i.postalCode as organizationPostalCode, i.phone as organizationPhone, i.fax as organizationFax,
		i.email as organizationEmail, i.website as organizationWebsite, i.XUserName as organizationXUsername, tmp.mc_emailBlast_email, tmp.mc_emailBlast_emailTypeID, vw.*
	into #tmpRecipients
	FROM #tmpRecipientsMID as tmp
	INNER JOIN dbo.ams_members as m WITH(NOLOCK) on m.memberID = tmp.memberID
	INNER JOIN ##tmpPayAllocMemberData as vw on vw.memberID = m.memberID
	INNER JOIN dbo.orgIdentities as i on i.orgIdentityID = @defaultOrgIdentityID
	INNER JOIN dbo.ams_states as s on s.stateID = i.stateID
	INNER JOIN dbo.ams_countries c on c.countryID = s.countryID;

	IF OBJECT_ID('tempdb..##tmpPayAllocMemberData') IS NOT NULL
		DROP TABLE ##tmpPayAllocMemberData;

	-- get column information for metadata processing
	insert into #tmpRecipientsCols
	select c.column_id, c.name, t.name
	from tempdb.sys.columns as c
	INNER JOIN tempdb.sys.types AS t ON c.user_type_id = t.user_type_id
	where c.object_id = object_id('tempdb..#tmpRecipients');

	alter table #tmpRecipients add recipientID int;

	BEGIN TRAN;
		-- add email_message
		EXEC platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, 
			@sendingSiteResourceID=@sendingSiteResourceID, @isTestMessage=0, @sendOnDate=@sendOnDate, 
			@recordedByMemberID=@recordedByMemberID, @fromName=@emailFromName, @fromEmail=@supportProviderEmail, 
			@replyToEmail=@emailReplyTo, @senderEmail='', @subject=@emailSubject, @contentVersionID=@contentVersionID, 
			@messageWrapper=@messageWrapper, @referenceType=null, @referenceID=null, @consentListIDs=null, @messageID=@messageID OUTPUT;

		-- update deliveryReportEmail
		IF nullIf(@deliveryReportEmail,'') is not null
			update platformMail.dbo.email_messages
			set deliveryReportEmail = @deliveryReportEmail
			where messageID = @messageID;
	COMMIT TRAN;
	
	declare @initialQueuePriority int, @expectedRecipientCount int;
	select @expectedRecipientCount = count(*) from #tmpRecipients;
	select @initialQueuePriority = platformMail.dbo.fn_getInitialRecipientQueuePriority(@messageTypeID,	@expectedRecipientCount);

	-- add recipients as I (not ready to be queued yet)
	insert into platformMail.dbo.email_messageRecipientHistory (messageID, memberID, 
		dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate, emailTypeID, siteID,queuePriority)
	select @messageID, memberID, getdate(), fullname, mc_emailBlast_email, @messageStatusIDInserting, 
		null, null, mc_emailBlast_emailTypeID, @siteID,@initialQueuePriority
	from #tmpRecipients;

	-- we need to support multiple recipientIDs per memberID, so use email address in joins
	update tmp
	set tmp.recipientID = r.recipientID
	from #tmpRecipients as tmp
	inner join platformMail.dbo.email_messageRecipientHistory as r on r.memberID = tmp.memberiD
		and r.messageID = @messageID and r.toEmail = tmp.mc_emailBlast_email;

	-- insert recipient references for Payment Allocation Statement
	insert into platformMail.dbo.email_messageRecipientReferences (recipientID, referenceType, referenceID)
	select recipientID, 'PayAllocStatement', @transactionID 
	from #tmpRecipients;

	-- add metadata fields
	insert into platformMail.dbo.email_metadataFields (fieldName, isMergeField)
	select fieldName, 1
	from @metadataFields
		except
	select fieldName, isMergeField
	from platformMail.dbo.email_metadataFields;

	update tmp
	set tmp.fieldID = MF.fieldID
	from @metadataFields as tmp
	inner join platformMail.dbo.email_metadataFields as MF on MF.fieldName = tmp.fieldName
	where MF.isMergeField = 1;

	-- add recipient metadata
	set @ParamDefinition = N'@messageID int, @fieldID int';		
	select @fieldID = min(fieldID) from @metadataFields;
	while @fieldID is not null BEGIN
		select @fieldName = fieldName from @metadataFields where fieldID = @fieldID;

		-- ensure field is available (could be a bad merge code)
		IF EXISTS (select ORDINAL_POSITION from #tmpRecipientsCols where column_name = @fieldName) BEGIN
			set @fieldValueString = 'isnull(cast([' + @fieldName + '] as varchar(max)),'''')';

			set @mcSQL = 'insert into platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue, recipientID)
				select @messageID, @fieldID, memberID, fieldValue = ' + @fieldValueString + ', recipientID
				from #tmpRecipients;';
			exec sp_executesql @mcSQL, @ParamDefinition, @messageID=@messageID, @fieldID=@fieldID;
		END

		select @fieldID = min(fieldID) from @metadataFields where fieldID > @fieldID;
	END

	-- mark recipients as queued
	if @markRecipientAsReady = 1 
		update mrh 
		set mrh.emailStatusID = @messageStatusIDQueued
		from platformMail.dbo.email_messages as m
		inner join platformMail.dbo.email_messageRecipientHistory as mrh on m.messageID = mrh.messageID
			and m.messageID = @messageID;

	-- return recipients
	select recipientID, memberID, membernumber, mc_emailBlast_email, @messageID as messageID
	from #tmpRecipients;

	IF OBJECT_ID('tempdb..#tmpRecipientsMID') IS NOT NULL 
		DROP TABLE #tmpRecipientsMID;
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	IF OBJECT_ID('tempdb..#tmpRecipentEmails') IS NOT NULL 
		DROP TABLE #tmpRecipentEmails;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
