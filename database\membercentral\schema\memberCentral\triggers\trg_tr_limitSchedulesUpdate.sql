CREATE TRIGGER trg_tr_limitSchedulesUpdate ON dbo.tr_limitSchedules
AFTER UPDATE 
AS 

SET NOCOUNT ON
BEGIN TRY

	IF NOT EXISTS (SELECT * FROM inserted) RETURN;

	-- when changing dates in a limit schedule, flag all transactions in GLs of both ranges.
	IF UPDATE(startdate) OR UPDATE(enddate) BEGIN
		INSERT INTO platformQueue.dbo.queue_transactionLimitFlagging (transactionID, orgID, siteID)
		select t.transactionID, ls.orgID, t.recordedOnSiteID
		from dbo.tr_limitSchedules as ls
		INNER JOIN Inserted as I ON ls.scheduleID = I.scheduleID
		INNER JOIN Deleted as D ON ls.scheduleID = D.scheduleID
		INNER JOIN dbo.tr_limits as l on l.scheduleID = ls.scheduleID
		INNER JOIN dbo.tr_transactions as t on t.ownedByOrgID = ls.orgID AND t.creditGLAccountID in (l.glaccountID,I.overflowGLAccountID)
		WHERE (D.startdate <> I.startdate OR D.enddate <> I.enddate)
		and (t.dateRecorded between D.startdate and D.enddate OR t.dateRecorded between I.startdate and I.enddate);
	END

	-- when changing overflow GL of a schedule, flag all transaction in old GL tied to the schedule
	IF UPDATE(overflowGLAccountID) BEGIN
		INSERT INTO platformQueue.dbo.queue_transactionLimitFlagging (transactionID, orgID, siteID)
		select t.transactionID, ls.orgID, t.recordedOnSiteID
		from dbo.tr_limitSchedules as ls
		INNER JOIN Inserted as I ON ls.scheduleID = I.scheduleID
		INNER JOIN Deleted as D ON ls.scheduleID = D.scheduleID
		INNER JOIN dbo.tr_transactionLimitSchedules as tls on tls.orgID = ls.orgID and tls.scheduleID = ls.scheduleID
		INNER JOIN dbo.tr_transactions as t on t.ownedByOrgID = tls.orgID and t.transactionID = tls.transactionID
		WHERE D.overflowGLAccountID <> I.overflowGLAccountID
		AND t.creditGLAccountID = D.overflowGLAccountID;
	END

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO
