ALTER PROC dbo.swod_getEnrollmentByEnrollmentID
@enrollmentID int,
@includeInactive bit = 0

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SELECT top 1 e.enrollmentID, e.userid, m2.memberID as MCMemberID, e.handlesOwnPayment, u.depomemberdataID, ISNULL(m2.firstname,d.firstName) AS FirstName, 
		ISNULL(m2.lastname,d.lastname) AS LastName, ISNULL(nullif(me.email,''),d.email) AS Email, s.seminarID, s.seminarName, s.seminarSubTitle, p.catalogURL, 
		p.orgcode, e.dateEnrolled, e.dateCompleted, e.bundleOrderID, ec.lastDateToComplete, p.orgcode as signUpOrgCode, 
		s.freeRateDisplay, ISNULL(eo.email,'') AS overrideEmail
	FROM dbo.tblEnrollments AS e 
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID
	INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID 
	INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
	LEFT OUTER JOIN membercentral.dbo.ams_members AS m
		INNER JOIN membercentral.dbo.ams_members as m2 on m2.orgID = m.orgID and m2.memberID = m.activeMemberID
		INNER JOIN memberCentral.dbo.ams_memberEmails AS me ON me.orgID = m2.orgID and me.memberID = m2.memberID
	ON m.memberID = e.MCMemberID
	LEFT OUTER JOIN dbo.tblEnrollmentsAndCredit AS ec on e.enrollmentID = ec.enrollmentID
	LEFT OUTER JOIN memberCentral.dbo.ams_emailAppOverrides AS eo ON eo.itemID = e.enrollmentID AND eo.itemType = 'semwebreg'
	WHERE e.enrollmentID = @enrollmentID
	AND e.isActive = CASE when @includeInactive = 0 THEN 1 else e.isActive END
	ORDER BY e.dateEnrolled DESC;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
