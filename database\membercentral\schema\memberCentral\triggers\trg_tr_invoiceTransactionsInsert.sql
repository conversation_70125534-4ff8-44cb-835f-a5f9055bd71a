CREATE TRIGGER trg_tr_invoiceTransactionsInsert ON dbo.tr_invoiceTransactions
AFTER INSERT 
AS

SET NOCOUNT ON
BEGIN TRY

	IF NOT EXISTS (SELECT * FROM inserted) RETURN

	BEGIN TRY
		SET XACT_ABORT OFF;	

		IF OBJECT_ID('tempdb..#PotentiallyAffectedAcctInvConditionValues') IS NOT NULL 
			DROP TABLE #PotentiallyAffectedAcctInvConditionValues;
		CREATE TABLE #PotentiallyAffectedAcctInvConditionValues (cvid int PRIMARY KEY, orgID int, conditionID int, 
			conditionKeyID int, conditionValueInt int, 
			INDEX IX_PotentiallyAffectedAcctInvConditionValues (orgID, conditionKeyID, conditionValueInt, conditionID));

		declare @IPconditionKeyID int, @ISconditionKeyID int, @affectedConditionCount int;
		select @IPconditionKeyID = conditionKeyID from dbo.ams_virtualGroupConditionKeys where conditionKey = 'acctInvProf';
		select @ISconditionKeyID = conditionKeyID from dbo.ams_virtualGroupConditionKeys where conditionKey = 'acctInvStatus';

		insert into #PotentiallyAffectedAcctInvConditionValues (cvid, orgID, conditionID, conditionKeyID, conditionValueInt)
		SELECT cv.cvid, vgc.orgID, vgc.conditionID, cv.conditionKeyID, cast(cv.conditionValue as int)
		from inserted i 
		inner join dbo.ams_virtualGroupRules vgr on vgr.orgID = i.orgID
			and vgr.isActive = 1
			and vgr.ruleTypeID = 1
			and i.cache_invoiceAmountAfterAdjustment > 0
		inner join dbo.ams_virtualGroupRuleConditionSets vgrcs on vgrcs.ruleVersionID = vgr.activeVersionID
		inner join dbo.ams_virtualGroupRuleConditions vgrc on vgrcs.conditionSetID = vgrc.conditionSetID
		inner join dbo.ams_virtualGroupConditions vgc on vgc.orgID = vgr.orgID
			and vgc.fieldcode = 'acct_inv'
			and vgc.conditionTypeID = 1
			and vgc.conditionID = vgrc.conditionID
		inner join dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = vgc.conditionID 
			and cv.conditionKeyID in (@IPconditionKeyID,@ISconditionKeyID)
		group by cv.cvid, vgc.orgID, vgc.conditionID, cv.conditionKeyID, cast(cv.conditionValue as int);

		SET @affectedConditionCount = @@ROWCOUNT;
		IF @affectedConditionCount > 0 BEGIN

			-- handle any conditions that need updating
			IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
				DROP TABLE #tblMCQRun;
			CREATE TABLE #tblMCQRun (orgID int INDEX IX_tblMCQRun_orgID, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

			INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
			SELECT distinct m.orgID, m.activeMemberID, pacv.conditionID
			from Inserted as i
			inner join dbo.tr_invoices as inv on inv.orgID = i.orgID
				and i.invoiceID = inv.invoiceID
				and i.cache_invoiceAmountAfterAdjustment > 0
			inner join #PotentiallyAffectedAcctInvConditionValues pacv on pacv.orgID = inv.orgID
				and pacv.conditionKeyID = @IPconditionKeyID
				and pacv.conditionValueInt = inv.invoiceProfileID
			inner join #PotentiallyAffectedAcctInvConditionValues pacv2 on pacv2.orgID = inv.orgID
				and pacv2.conditionKeyID = @ISconditionKeyID
				and pacv2.conditionValueInt = inv.statusID
				and pacv2.conditionID = pacv.conditionID
			inner join dbo.ams_members m on m.orgID = inv.orgID
				and m.memberID = inv.assignedToMemberID;

			EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

			IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
				DROP TABLE #tblMCQRun;
		END

		IF OBJECT_ID('tempdb..#PotentiallyAffectedAcctInvConditionValues') IS NOT NULL 
			DROP TABLE #PotentiallyAffectedAcctInvConditionValues;

		SET XACT_ABORT ON;
	END TRY
	BEGIN CATCH
		SET XACT_ABORT ON;
		EXEC dbo.up_MCErrorHandler @raise=0, @email=1;
	END CATCH

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO
