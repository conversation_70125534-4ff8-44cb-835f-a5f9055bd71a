----------------------------------------------------------------------------------------------------
/* Query #39: PASSED */
ALTER PROCEDURE [dbo].[sw_AlertTransactionLogBackupFailed]
(
	@BackupLocation varchar(128) = NULL,
	@MailProfileName varchar(128) = NULL,
	@AlertEmail varchar(128) = NULL,
	@Company varchar(128) = NULL,
	@Configuration varchar(128) = NULL
)
AS
BEGIN
    SET NOCOUNT ON

    IF @BackupLocation IS NULL
    BEGIN
        SET @BackupLocation = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'PrimaryBackupLocation')
    END

    IF @MailProfileName IS NULL
    BEGIN
        SET @MailProfileName = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'MailProfileName')
    END

    IF @AlertEmail IS NULL
    BEGIN
        SET @AlertEmail = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'AlertEmail')
    END

    IF @Company IS NULL
    BEGIN
        SET @Company = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Company')
    END

    IF @Configuration IS NULL
    BEGIN
        SET @Configuration = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Configuration')
    END

    DECLARE @Subject nvarchar(255) = N'Transaction Log Backup Failed | ' + @Configuration + N' | ' + @Company
    DECLARE @Body nvarchar(MAX) = N'Transaction Log Backup to ' + @BackupLocation + N' Failed'

    EXEC msdb.dbo.sp_send_dbmail
        @profile_name = @MailProfileName,
        @recipients = @AlertEmail,
        @subject = @Subject,
        @body = @Body

    INSERT INTO [SQLWatchmen].[dbo].[AlertLog] ([Alert]) VALUES ('Transaction Log Backup Failed')

END
GO
