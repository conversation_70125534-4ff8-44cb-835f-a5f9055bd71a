<cfscript>
variables.applicationReservedURLParams 	= "";
local.customPage.baseURL = "/?#getBaseQueryString(false)#";
local.orgCode	= event.getValue('mc_siteInfo.orgCode');
arguments.event.paramValue('ca','showList');
arguments.event.paramValue('eventStartDate',dateFormat(DateAdd('m',-12,now()),'m/d/yyyy'));
arguments.event.paramValue('eventEndDate',dateFormat(now(),'m/d/yyyy'));
arguments.event.paramValue('membernumber','');

local.periodStartDate = arguments.event.getValue('eventStartDate');
local.periodEndDate = arguments.event.getValue('eventEndDate');
local.membernumber = arguments.event.getValue('membernumber');

// CUSTOM FIELD: ------------------------------------------------------------------------------------------------------
	local.arrCustomFields = [];
	local.tmpField = { name="myCLELiveEventsText", type="CONTENTOBJ", desc="myCLE page Live Events Instructions", value="<p>Editable instructions go here</p>" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="myCLEWebinarsText", type="CONTENTOBJ", desc="myCLE page Webinars Instructions", value="<p>Editable instructions go here</p>" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="myCLEOnlineSeminarsText", type="CONTENTOBJ", desc="myCLE page Online Seminars Live Instructions", value="<p>Editable instructions go here</p>" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'),siteResourceID=this.siteResourceID,arrCustomFields=local.arrCustomFields);	
</cfscript>

<!--- default to show list of events --->
<cfset arguments.event.paramValue('panel','showList')>

<cfif arguments.event.getValue('panel') eq "viewCert">
	
	<cfscript>
	// get encrypted registrantid
	local.encryptedRID = arguments.event.getValue('rid','');
	
	// change xPcmKx to % (case sensitive), decode, fromBase64, and decrypt
	try { local.decryptedRID = val(decrypt(toString(toBinary(URLDecode(replace(local.encryptedRID,"xPcmKx","%","ALL")))),"TRiaL_SMiTH")); } 
	catch (any e) { local.decryptedRID = 0; }
	
	// generate certificate for registrantID
	local.strCertificate = CreateObject("component","model.admin.events.certificate").generateCertificate(registrantID=local.decryptedRID);
	</cfscript>
	
	<!--- redirect to pdf --->
	<cfif len(local.strCertificate.certificateURL)>
		<cflocation url="#local.strCertificate.certificateURL#" addtoken="no">
	<cfelse>
		<cflocation url="/?pg=myCLE&panel=certErr&mode=direct" addtoken="no">
	</cfif>
	
<cfelseif arguments.event.getValue('panel') eq "certErr">
	<cfoutput>
	<div class="tsAppHeading">My CLE</div>
	<br/>
	<div class="tsAppBodyText">
		<b>Sorry...</b>, we were unable to generate a certificate at this time.<br/><br/>
		If you continue to see this message, please contact MN for assistance.
	</div>
	</cfoutput>

<cfelse>

	<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.memberDetails">
			select firstName, middleName, lastName, suffix, professionalsuffix, company
			from dbo.ams_members
			where memberNumber=<cfqueryparam value="#local.membernumber#" cfsqltype="CF_SQL_VARCHAR">
			AND orgID = #arguments.event.getValue('mc_siteinfo.orgID')#
		</cfquery>
	</cfif>

	<!--- CLE history based on event registration with credit SELECTions --->
	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCLE">
		SET NOCOUNT ON;

		DECLARE @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
		DECLARE @orgID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;

		SELECT a.authorityID, a.authorityName as authorityName, eco.ApprovalNum, 
			r.attended,e.eventid,et.startTime as eventStart,evr.startDate,evr.endDate,r.registrantID, r.dateRegistered, 
			cl.contentTitle, rc.creditValueAwarded, isnull(ast.ovTypeName,cat.typeName) as creditType,
			datepart(year,et.startTime) as CLEYear
		FROM dbo.ev_registrants as r
		INNER JOIN dbo.ev_registration as evr on evr.registrationID = r.registrationID AND evr.siteID = @siteID
		INNER JOIN dbo.ev_events as e on e.eventid = evr.eventid AND e.siteID = @siteID
		INNER JOIN dbo.ev_times as et on et.eventID = e.eventID and et.timeZoneID = #arguments.event.getValue('mc_siteinfo.defaultTimeZoneID')#
		INNER JOIN dbo.cms_contentLanguages as cl on cl.contentID = e.eventContentID AND cl.languageID = 1
		INNER JOIN dbo.crd_requests as rc on rc.registrantID = r.registrantID AND rc.creditAwarded = 1 AND r.status='A'
		INNER JOIN dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
		INNER JOIN dbo.crd_offerings as eco on eco.offeringID = ect.offeringID
		INNER JOIN dbo.crd_authoritySponsorTypes as ast on ast.astid = ect.astid
		INNER JOIN dbo.crd_authoritySponsors as ecas on ecas.asid = ast.asid
		INNER JOIN dbo.crd_authorities as a on a.authorityID = ecas.authorityID	
		INNER JOIN dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID 
		INNER JOIN dbo.ams_members as m1 on m1.orgID = @orgID and m1.memberID = r.memberID	
		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
			INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberID = m1.activeMemberID
			<cfif local.membernumber NEQ ''>
				AND m.membernumber = <cfqueryparam value="#local.membernumber#" cfsqltype="CF_SQL_VARCHAR">
			</cfif>
		<cfelse>
			INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberID = m1.activeMemberID
				AND m.memberID = <cfqueryparam value="#session.cfcUser.memberData.memberID#" cfsqltype="CF_SQL_INTEGER">
		</cfif>
		WHERE r.recordedOnSiteID = @siteID
		AND a.authorityName LIKE  '%Wyoming State Bar%'	
		<cfif len(trim(local.periodStartDate))>
			AND et.startTime >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#dateformat(local.periodStartDate,"m/d/yyyy")# 00:00:00.000">	
		</cfif> 
		<cfif len(trim(local.periodEndDate))>
			AND et.startTime <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#dateformat(local.periodEndDate,"m/d/yyyy")# 23:59:59.997">
		</cfif> 
		ORDER BY et.startTime desc, e.eventid, a.authorityName, creditType;
	</cfquery> 

	<!--- CLE Totals for Live Conferences & Events --->
	<cfquery name="local.qryCLETotals" dbtype="query">
		SELECT CLEYear, creditType,authorityName, sum(creditValueAwarded) as totalCLE
		FROM [local].qryCLE
		GROUP BY CLEYear,  authorityName, creditType
		ORDER BY CLEYear, totalCLE
	</cfquery>

	<!--- seminarweb history --->	
	<cfset local.qrySWP = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(local.orgCode).qryAssociation>
	<cfset local.showSW = false>
	<cfset local.depoMemberDataID = val(session.cfcUser.memberData.depomemberdataid)>
	<cfset local.memberIDToUse = session.cfcUser.memberData.memberID>

	<cfif (application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)) AND len(trim(local.membernumber))>
		<cfset local.qryGetDepoMemberData = application.objCustomPageUtils.mem_DepoMemberData(memberNumber=local.membernumber, orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
		<cfset local.depoMemberDataID = val(local.qryGetDepoMemberData.depomemberdataid)>
		<cfset local.memberIDToUse = application.objMember.getMemberIDByMemberNumber(memberNumber=local.membernumber, orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
	</cfif>

	<cfif local.depoMemberDataID>
		<cfstoredproc procedure="sw_getEnrollmentHistory" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.memberIDToUse#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.orgCode#">
			<cfprocresult name="local.qrySWL" resultset="1">
			<cfprocresult name="local.qrySWOD" resultset="2">
			<cfprocresult name="local.qryCertPrograms" resultset="3">
		</cfstoredproc>
		<cfset local.showSW = true>	
		<!--- CLE Totals for Live Webinars --->
		<cfquery name="local.qrySWLCreditAwarded" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			-- swl
				SELECT ca.authorityID, ca.authorityName, datepart(year,e.dateCompleted) as CLEYear, 
					CRDA.credittype.value('(var[@name="value"]/string)[1]','numeric(9,2)') as creditValueAwarded,
					CRDT.credittype.value('(var[@name="displayname"]/string)[1]','varchar(200)') as creditType
				FROM dbo.tblEnrollments as e
				INNER JOIN dbo.tblEnrollmentsSWLive as eswl on eswl.enrollmentID = e.enrollmentID
				INNER JOIN dbo.tblParticipants as p on p.participantID = e.participantID 
				INNER JOIN dbo.swl_SeminarsInMyCatalogMy('#local.orgCode#') as simc on simc.seminarID = e.seminarID
				INNER JOIN dbo.tblUsers as u on u.userID = e.userID
				INNER JOIN dbo.tblEnrollmentsAndCredit AS eac on eac.enrollmentID = e.enrollmentID
				INNER JOIN dbo.tblSeminarsAndCredit AS sac ON sac.seminarCreditID = eac.seminarCreditID 
				INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON csa.CSALinkID = sac.CSALinkID 
				INNER JOIN dbo.tblCreditAuthorities AS ca ON ca.authorityID = csa.authorityID 
				cross apply sac.wddxCreditsAvailable.nodes('/wddxPacket/data/array/struct') as CRDA(credittype)
				cross apply ca.wddxCreditTypes.nodes('/wddxPacket/data/array/struct') as CRDT(credittype)
				WHERE 1=1
				AND u.depomemberdataID = #local.depoMemberDataID#
				AND ca.authorityName LIKE  '%Wyoming State Bar%'	
				AND e.passed = 1
				AND e.isActive = 1
				AND eac.earnedCertificate = 1
				AND CRDA.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)') = CRDT.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)')
				ORDER BY ca.authorityName, ca.authorityID, CLEYear, creditType
		</cfquery>		
		<!--- CLE Totals for Live Conferences & Events --->
		<cfquery name="local.qrySWLCreditTotals" dbtype="query">
			SELECT CLEYear, creditType,authorityName, sum(creditValueAwarded) as totalCLE
			FROM [local].qrySWLCreditAwarded
			GROUP BY CLEYear,  authorityName, creditType
			ORDER BY CLEYear, totalCLE
		</cfquery>
		<!--- CLE Totals for Self-Paced Online Seminars --->
		<cfquery name="local.qrySWODCreditAwarded" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			-- swod
			SELECT ca.authorityID, ca.authorityName, datepart(year,e.dateCompleted) as CLEYear, 
				CRDA.credittype.value('(var[@name="value"]/string)[1]','numeric(9,2)') as creditValueAwarded,
				CRDT.credittype.value('(var[@name="displayname"]/string)[1]','varchar(200)') as creditType
			FROM dbo.tblEnrollments as e
			INNER JOIN dbo.tblEnrollmentsSWOD as eswod on eswod.enrollmentID = e.enrollmentID
			INNER JOIN dbo.tblParticipants as p on p.participantID = e.participantID 
			INNER JOIN dbo.swod_SeminarsInMyCatalogMy('#local.orgCode#') as simc on simc.seminarID = e.seminarID
			INNER JOIN dbo.tblUsers as u on u.userID = e.userID
			INNER JOIN dbo.tblEnrollmentsAndCredit AS eac on eac.enrollmentID = e.enrollmentID
			INNER JOIN dbo.tblSeminarsAndCredit AS sac ON sac.seminarCreditID = eac.seminarCreditID 
			INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON csa.CSALinkID = sac.CSALinkID 
			INNER JOIN dbo.tblCreditAuthorities AS ca ON ca.authorityID = csa.authorityID 
			cross apply sac.wddxCreditsAvailable.nodes('/wddxPacket/data/array/struct') as CRDA(credittype)
			cross apply ca.wddxCreditTypes.nodes('/wddxPacket/data/array/struct') as CRDT(credittype)
			WHERE 1=1 
			AND u.depomemberdataID = #local.depoMemberDataID#
			AND ca.authorityName LIKE  '%Wyoming State Bar%'	
			AND e.passed = 1
			AND e.isActive = 1
			AND eac.earnedCertificate = 1
			AND CRDA.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)') = CRDT.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)')
			ORDER BY ca.authorityName, ca.authorityID, CLEYear, creditType
		</cfquery>
		<cfquery name="local.qrySWODCreditTotals" dbtype="query">
			SELECT CLEYear, creditType,authorityName, sum(creditValueAwarded) as totalCLE
			FROM [local].qrySWODCreditAwarded
			GROUP BY CLEYear,  authorityName, creditType
			ORDER BY CLEYear, totalCLE
		</cfquery>
	</cfif>
	<cfsavecontent variable="local.cleJSnCSS">
		<cfoutput>
		<style type="text/css">
		##clehistory th, ##swlhistory th, ##swodhistory th { text-align:left; border-bottom:1px solid ##666; }
		</style>
		<script language="JavaScript">
			function viewEVCert(rid) {
				var certURL = '/?pg=myCLE&panel=viewCert&mode=stream&rId=' + rid;
				window.open(certURL,'ViewCertificate','width=990,height=500');
			}
		</script>
		<cfif local.showSW>
			<script language="JavaScript">
			function viewCert(eId) {
				var certURL = '/?pg=semWebCatalog&panel=viewCert&mode=direct&eId=' + eId;
				window.open(certURL,'ViewCertificate','width=990,height=500');
			}
			</script>
		</cfif>
		</cfoutput>	
	</cfsavecontent>
	<cfhtmlhead text="#local.cleJSnCSS#">

	<cfsavecontent variable="local.dataHead">
		<cfoutput>
		<script language="javascript">
			$(function(){
				mca_setupDatePickerRangeFields('eventStartDate','eventEndDate');
			});

			function _FB_validateForm(){
				<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
					if ($('##membernumber').val() == '') { 
						alert('Must enter MemberNumber before you can filter report.');
						return false;
					}
				</cfif>
				return true;
			}
		</script>
		<style type="text/css">
			##eventStartDate, ##eventEndDate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; cursor: default;}
			.creditTable td, .creditTable th { border:1px solid ##707070; border-collapse:collapse; padding:4px; }
			.balTable td { border:0px; padding:2px; }
		</style>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#application.objCommon.minText(local.dataHead)#">

	<cfform method="POST" action="#local.customPage.baseURL#" name="frmCLE" onsubmit="return _FB_validateForm();">
		<cfoutput>
			<span style="float:right;">
				<button class="btn" type="button" onClick="window.print();"><i class="icon-print"></i> Print</button>
			</span>
			<h4>My CLE</h4></br>
			<table cellpadding="4" cellspacing="0">	
				<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
					<tr valign="top">
						<td colspan="4" >
							<b>MemberNumber:</b> &nbsp;
							<cfinput class="tsAppBodyText" type="text" name="membernumber" id="membernumber" value="#local.membernumber#" size="30" placeholder="Must enter MemberNumber.">
						</td>
					</tr>
					<tr><td>&nbsp;</td></tr>
				</cfif>
					
				<cfif local.membernumber NEQ ''>
					<tr valign="top">
						<td colspan="4" >
							<cfset local.strFieldSetContent = application.objCustomPageUtils.renderFieldSet(uid='b2587594-20c4-472e-a46c-f928aeb3a1fd', mode="collection")>
							
							<cfset local.fullName = "">
							<cfif StructKeyExists(local.strFieldSetContent.strFields,"m_firstname")>
								<cfset local.fullName = local.fullName & local.memberDetails.firstName>
								<cfif StructKeyExists(local.strFieldSetContent.strFields,"m_middlename")>
									<cfset local.fullName = local.fullName & ' ' & local.memberDetails.middleName>
								</cfif>
								<cfif StructKeyExists(local.strFieldSetContent.strFields,"m_lastname")>
									<cfset local.fullName = local.fullName & ' ' & local.memberDetails.lastName>
								</cfif>
								<cfif StructKeyExists(local.strFieldSetContent.strFields,"m_professionalsuffix")>
									<cfset local.fullName = local.fullName & ' ' & local.memberDetails.professionalSuffix>
								</cfif>
								<cfif StructKeyExists(local.strFieldSetContent.strFields,"m_suffix")>
									<cfset local.fullName = local.fullName & local.memberDetails.suffix>
								</cfif>
							</cfif>
							<cfset local.companyName = "">
							<cfif StructKeyExists(local.strFieldSetContent.strFields,"m_company")>
								<cfset local.companyName = local.memberDetails.company>
							</cfif>
							<table>
								<tr><td colspan="2"><b>CLE Member Details</b></td></tr>
								<cfif  len(trim(local.fullName)) GT 0>
									<tr><td>Name:</td><td>#local.fullName#</td></tr>
								</cfif>
								<cfif  len(trim(local.companyName)) GT 0>
									<tr><td>Company:</td><td>#local.companyName#</td></tr>
								</cfif>
							</table>
						</td>
					</tr> 
					<tr><td>&nbsp;</td></tr>
				</cfif>
				<tr valign="top">
					<td>
						<b>SELECT your dates:</b> &nbsp;
						<cfinput type="text" class="periodDate" name="eventStartDate" id="eventStartDate" value="#local.periodStartDate#" size="14"> 
						&nbsp;to&nbsp;
						<cfinput type="text" class="periodDate"  name="eventEndDate" id="eventEndDate" value="#local.periodEndDate#" size="14">
					</td>
					<td>
						<button class="btn" type="submit">Filter Report</button>
					</td>
				</tr>
			</table>
			<br>
		</cfoutput>
	</cfform>

	<!--- Live Conferences & Events --->
	<cfoutput>
		<div>
			<b>#local.qrySWP.brandConfTab#</b>
			<div class="tsAppBodyText">				
				#local.strPageFields.myCLELiveEventsText#				
			</div>
		</div>
	</cfoutput>	
	<cfif local.qryCLE.recordcount>	
		<cfset local.arr = createObject("java", "java.util.LinkedHashMap").init()/>
		<cfloop query="local.qryCLETotals">
			<cfset local.arr[local.qryCLETotals.cleYear][local.qryCLETotals.authorityName][local.qryCLETotals.creditType] = local.qryCLETotals.totalCLE>
		</cfloop>
		<cfoutput>
		<table cellpadding="2" cellspacing="0" border="0">
			<tr>
				<td class="tsAppBodyText">
					<div class="total">
						<b>Credit Totals</b>
					</div>
				</td>
			</tr>
			<tr>
				<td>
					<cfloop collection="#(local.arr)#" item="local.key">
						<div class="block">
							<div class="total yearDate tsAppBodyText">
								<b>#local.key#</b>
							</div>
							<cfloop collection="#local.arr[local.key]#" item="local.key1">
								<div class="total tsAppBodyText">
									#local.key1#
								</div>
								<cfloop collection="#local.arr[local.key][local.key1]#" item="local.key2">
									<div class="creditType tsAppBodyText">
										#local.arr[local.key][local.key1][local.key2]# #local.key2#
									</div>
								</cfloop>
							</cfloop>
						</div>
					</cfloop>
				</td>
			</tr>
		</table>
		<br/>
		<table width="98%" cellpadding="2" cellspacing="0" border="0" id="clehistory">
		<tr class="tsAppBodyText">
			<th width="90">Date</th>
			<th>Title</th>
			<th colspan="2">Credit Awarded</th>
		</tr>
		</cfoutput>	
		<cfset local.oddeven = 0>
		<cfoutput query="local.qryCLE" group="eventid">
			<cfset local.oddeven = local.oddeven + 1>
			<cfset local.rID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qryCLE.registrantID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
			<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
				<td class="tsAppBodyText">#dateformat(local.qryCLE.eventStart,"mm/dd/yyyy")#</td>
				<td class="tsAppBodyText">#local.qryCLE.contentTitle#</td>
				<td><a href="javascript:viewEVCert('#local.rID#'); "title="Print Certificate"><i class="icon-print"></i></a></td>
				<td class="tsAppBodyText" nowrap>					
					<table>
						<tr>
							<td>
								<cfoutput group="authorityName">
									#local.qryCLE.authorityName#<br/>
									<cfif len(local.qryCLE.ApprovalNum)>
										Approval Number: #local.qryCLE.ApprovalNum#<br/>
									</cfif>
									<cfoutput>
										#local.qryCLE.creditValueAwarded# #local.qryCLE.creditType#<br/>
									</cfoutput>
									<br>
								</cfoutput>
							</td>
						</tr>
					</table>
				</td>
			</tr>	
		</cfoutput>	
		<cfoutput></table></cfoutput>
	<cfelse>
		<cfoutput>
		<div class="tsAppBodyText">
			There are no Live Conferences & Events to display.
		</div>
		</cfoutput>
	</cfif>
	<cfoutput><br/><br/></cfoutput>

	<!--- Live Webinars --->
	<cfoutput>
	<div><b>#local.qrySWP.brandSWLTab#</b>
		<div class="tsAppBodyText">				
			#local.strPageFields.myCLEWebinarsText#				
		</div>
	</div>
	</cfoutput>
	<cfif local.showSW>
		<cfquery name="local.qrySWLOrder"   dbtype="query">
			SELECT * FROM  [local].qrySWL  ORDER BY dateStart DESC
		</cfquery>
		<cfif local.qrySWLOrder.recordcount>
		
			<cfoutput>		
			<table cellpadding="2" cellspacing="0" border="0">
				<cfif local.qrySWLCreditAwarded.recordcount>	
					<cfset local.arr = createObject("java", "java.util.LinkedHashMap").init()/>
					<cfloop query="local.qrySWLCreditTotals">
						<cfset local.arr[local.qrySWLCreditTotals.cleYear][local.qrySWLCreditTotals.authorityName][local.qrySWLCreditTotals.creditType] = local.qrySWLCreditTotals.totalCLE>
					</cfloop>
					<tr>
						<td class="tsAppBodyText">
							<div class="total">
								<b>Credit Totals</b>
							</div>
						</td>
					</tr>
					<tr>
						<td>
							<cfloop collection="#(local.arr)#" item="local.key">
								<div class="block">
									<div class="total yearDate tsAppBodyText">
										<b>#local.key#</b>
									</div>
									<cfloop collection="#local.arr[local.key]#" item="local.key1">
										<div class="total tsAppBodyText">
											#local.key1#
										</div>
										<cfloop collection="#local.arr[local.key][local.key1]#" item="local.key2">
											<div class="creditType tsAppBodyText">
												#local.arr[local.key][local.key1][local.key2]# #local.key2#
											</div>
										</cfloop>
									</cfloop>
								</div>
							</cfloop>
						</td>
					</tr>			
				</cfif>
			</table><br/>
			</cfoutput>
			<cfoutput>			
				<table width="98%" cellpadding="2" cellspacing="0" border="0" id="swlhistory">
					<tr class="tsAppBodyText">
						<th width="90">Date</th>
						<th>Title</th>
						<th>Status</th>
					</tr>
					<cfloop query="local.qrySWLOrder">
						<cfstoredproc procedure="sw_getCreditsforSeminar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qrySWLOrder.seminarID#" null="No">
							<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#event.getValue('mc_siteInfo.siteCode')#">
							<cfprocresult name="local.qryCredit" resultset="1">
						</cfstoredproc>		
						
						<cfquery name="local.qryCreditDistinct" dbtype="query">
							SELECT distinct *
							FROM [local].qryCredit
							ORDER BY authorityCode
						</cfquery>
						
						<cfset local.eID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qrySWLOrder.enrollmentID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
						<tr valign="top">
							<td class="tsAppBodyText">#DateFormat(local.qrySWLOrder.dateStart,'m/d/yyyy')#</td>
							<td class="tsAppBodyText">#encodeForHTML(local.qrySWLOrder.seminarName)#</td>
							<td class="tsAppBodyText" width="120">
								<cfif len(local.qrySWLOrder.dateCompleted) AND local.qrySWLOrder.passed is 1 AND local.qrySWLOrder.offerCertificate>
									<a href="javascript:viewCert('#local.eID#')">View certificate(s)</a>
								<cfelseif len(local.qrySWLOrder.dateCompleted) AND local.qrySWLOrder.passed is 1 AND not local.qrySWLOrder.offerCertificate>
									Completed
								<cfelseif len(local.qrySWLOrder.dateCompleted) AND local.qrySWLOrder.passed is 0>
									Failed
								<cfelseif now() lt local.qrySWLOrder.dateStart>
									Not yet begun
								<cfelse>
									Did not attend
								</cfif>
							</td>
						</tr>
					</cfloop>
				</table><br/><br/>			
			</cfoutput>
		</cfif>
		<cfoutput><br/><br/></cfoutput>
		<!--- SWOD --->
		<!--- Self-Paced Online Seminars--->
		<cfoutput>
			<div><b>#local.qrySWP.brandSWODTab#</b>
				<div class="tsAppBodyText">				
					#local.strPageFields.myCLEOnlineSeminarsText#				
				</div>
			</div>
			<cfif local.qrySWOD.recordcount>
				
				<table cellpadding="2" cellspacing="0" border="0">
					<cfif local.qrySWODCreditAwarded.recordcount>	
						<cfset local.arr = createObject("java", "java.util.LinkedHashMap").init()/>
						<cfloop query="local.qrySWODCreditTotals">
							<cfset local.arr[local.qrySWODCreditTotals.cleYear][local.qrySWODCreditTotals.authorityName][local.qrySWODCreditTotals.creditType] = local.qrySWODCreditTotals.totalCLE>
						</cfloop>
						<tr>
							<td class="tsAppBodyText">
								<div class="total">
									<b>Credit Totals</b>
								</div>
							</td>
						</tr>
						<tr>
							<td>
								<cfloop collection="#(local.arr)#" item="local.key">
									<div class="block">
										<div class="total yearDate tsAppBodyText">
											<b>#local.key#</b>
										</div>
										<cfloop collection="#local.arr[local.key]#" item="local.key1">
											<div class="total tsAppBodyText">
												#local.key1#
											</div>
											<cfloop collection="#local.arr[local.key][local.key1]#" item="local.key2">
												<div class="creditType tsAppBodyText">
													#local.arr[local.key][local.key1][local.key2]# #local.key2#
												</div>
											</cfloop>
										</cfloop>
									</div>
								</cfloop>
							</td>
						</tr>
					</cfif>
				</table><br/>	
				
				<table width="98%" cellpadding="2" cellspacing="0" border="0" id="swodhistory">	
					<tr class="tsAppBodyText">
						<th>Date</th>
						<th>Title</th>
						<th>Status</th>
					</tr>
					<cfloop query="local.qrySWOD">
						<cfset local.eID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qrySWOD.enrollmentID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
						<tr valign="top">
							<td class="tsAppBodyText">#DateFormat(local.qrySWOD.dateEnrolled,'m/d/yyyy')#</td>
							<td class="tsAppBodyText">#encodeForHTML(local.qrySWOD.seminarName)#</td>
							<td class="tsAppBodyText" width="140">
								<cfif len(local.qrySWOD.dateCompleted) is 0>
									<cfif local.qrySWOD.isPublished AND local.qrySWOD.preReqFulfilled>
										<a href="/?pg=swOnDemandPlayer&seminarID=#local.qrySWOD.seminarID#&enrollmentID=#local.qrySWOD.enrollmentID#&orgCode=#arguments.event.getValue('mc_siteinfo.orgCode')#" target="_blank">Begin</a>
									<cfelseif local.qrySWOD.isPublished>
										Awaiting Prereqs
									<cfelse>
										Not available
									</cfif>
								<cfelse>
									<cfif local.qrySWOD.isPublished>
										<a href="/?pg=swOnDemandPlayer&seminarID=#local.qrySWOD.seminarID#&enrollmentID=#local.qrySWOD.enrollmentID#&orgCode=#arguments.event.getValue('mc_siteinfo.orgCode')#" target="_blank">Review</a> |
									</cfif>
									<cfif local.qrySWOD.passed AND local.qrySWOD.offerCertificate>
										<a href="javascript:viewCert('#local.eID#');">Certificate</a>
									<cfelseif local.qrySWOD.passed AND not local.qrySWOD.offerCertificate>
										Completed
									<cfelseif not local.qrySWOD.passed>
										Failed
									</cfif>
								</cfif>
							</td>
						</tr>
					</cfloop>
				</table><br/><br/>
			</cfif>	
		</cfoutput>
	<cfelse>
		<cfoutput>Nothing to report during the selected period.</cfoutput>
	</cfif>
</cfif>