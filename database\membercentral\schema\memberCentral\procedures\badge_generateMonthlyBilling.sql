CREATE PROC dbo.badge_generateMonthlyBilling
@startDate datetime, 
@endDate datetime,
@asOfDate date,
@recordTransactions bit

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @TSTransactionDate smalldatetime = SMALLDATETIMEFROMPARTS(YEAR(@enddate),MONTH(@enddate),DAY(@enddate),23,59);
	
	IF OBJECT_ID('tempdb..#tmpBadgePrintUsageSites') IS NOT NULL 
		DROP TABLE #tmpBadgePrintUsageSites;
	IF OBJECT_ID('tempdb..#tmpBadgePrintRequests') IS NOT NULL 
		DROP TABLE #tmpBadgePrintRequests;
	IF OBJECT_ID('tempdb..#tmpBadgePrintBillingData') IS NOT NULL
		DROP TABLE #tmpBadgePrintBillingData;
	IF OBJECT_ID('tempdb..#tmpBadgePrintBilling') IS NOT NULL 
		DROP TABLE #tmpBadgePrintBilling;

	-- cast noofCallsInOverageFee as decimal for division
	CREATE TABLE #tmpBadgePrintUsageSites (siteID int PRIMARY KEY, siteCode varchar(10));
	CREATE TABLE #tmpBadgePrintRequests (siteCode varchar(10), numRequests int);
	CREATE TABLE #tmpBadgePrintBillingData (orgCode varchar(10), MonthlyFee decimal(18,2), noofCallIncFee int,
		overageFee decimal(14,6), noofCallsInOverageFee decimal(10,2), noFee bit, billingMode varchar(10));
	CREATE TABLE #tmpBadgePrintBilling (siteCode varchar(10), numRequests int, depomemberdataID int, 
		overageFee decimal(14,6), MonthlyFee decimal(18,2), noofCallIncFee int, noofCallsInOverageFee decimal(10,2), 
		NoFee bit, reportDetail varchar(1000), billingState varchar(50), billingZip varchar(25), billingCountry int, 
		billed bit default(1), amountBilled decimal(18,2), billingMode varchar(10));

	-- sites by usage
	INSERT INTO #tmpBadgePrintUsageSites (siteID, siteCode)
	SELECT DISTINCT s.siteID, s.siteCode
	FROM dbo.sites AS s
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = s.siteID
		AND sr.siteResourceID = s.siteResourceID
		AND sr.siteResourceStatusID = 1
	INNER JOIN dbo.siteFeatures AS sf ON sf.siteID = s.siteID
	WHERE sf.badgePrinters = 1;
	
	INSERT INTO #tmpBadgePrintRequests (siteCode, numRequests)
	select s.siteCode, count(api.apilogID)
	from platformStatsMC.dbo.apilog_badges as api
	inner join #tmpBadgePrintUsageSites as s on s.siteID = api.siteID
	where api.dateEntered between @startDate and @endDate
	group by s.siteCode;

	-- billing data
	INSERT INTO #tmpBadgePrintBillingData (orgCode, MonthlyFee, noofCallIncFee, overageFee, noofCallsInOverageFee, NoFee, billingMode)
	SELECT a.orgCode, a.monthlyFee, 0, 0, 0, a.noFee, 'monthly'
	FROM trialsmith.dbo.memberCentralBillingBadgePrinting AS a
	INNER JOIN #tmpBadgePrintUsageSites as s on s.siteCode = a.orgCode
	INNER JOIN (
		SELECT orgCode, MAX(effectiveDate) AS effectiveDate
		FROM trialsmith.dbo.memberCentralBillingBadgePrinting
		WHERE effectiveDate <= @asOfDate
		GROUP BY orgCode
	) tmp ON tmp.orgCode = a.orgCode AND tmp.effectiveDate = a.effectiveDate
		UNION ALL
	SELECT a.orgCode, 0, a.noofCallIncFee, a.overageFee, a.noofCallsInOverageFee, a.noFee, 'request'
	FROM trialsmith.dbo.memberCentralBillingBadgePrinting AS a
	INNER JOIN #tmpBadgePrintUsageSites as s on s.siteCode = a.orgCode
	INNER JOIN (
		SELECT orgCode, MAX(effectiveDate) AS effectiveDate
		FROM trialsmith.dbo.memberCentralBillingBadgePrinting
		WHERE effectiveDate <= @startDate
		GROUP BY orgCode
	) tmp ON tmp.orgCode = a.orgCode AND tmp.effectiveDate = a.effectiveDate;

	-- add any sites that have a fee structure defined but no activity
	INSERT INTO #tmpBadgePrintRequests (siteCode, numRequests)
	SELECT DISTINCT l.orgCode, 0
	FROM #tmpBadgePrintBillingData AS l
	INNER JOIN #tmpBadgePrintUsageSites AS s ON s.siteCode = l.orgCode
	WHERE l.orgCode NOT IN (SELECT siteCode FROM #tmpBadgePrintRequests);

	-- billing info for all orgs
	INSERT INTO #tmpBadgePrintBilling (siteCode, numRequests, depomemberdataID, MonthlyFee, 
		overageFee, noofCallIncFee, noofCallsInOverageFee, NoFee, billingState, 
		billingZip, billingCountry, billingMode)
	SELECT ar.siteCode, ar.numRequests, mcb.depoMemberDataID, l.monthlyFee, 
		l.overageFee, l.noofCallIncFee, l.noofCallsInOverageFee, l.noFee, d.billingState, 
		case when d.billingCountry = 1 then left(d.billingZip,5) else d.billingZip end, 
		d.billingCountry, l.billingMode
	FROM #tmpBadgePrintRequests AS ar
	INNER JOIN trialsmith.dbo.memberCentralBilling AS mcb ON mcb.orgcode = ar.siteCode
	INNER JOIN #tmpBadgePrintBillingData AS l ON l.orgCode = ar.siteCode 
	LEFT OUTER JOIN trialsmith.dbo.depoMemberData AS d ON d.depomemberdataID = mcb.depomemberdataID
	WHERE (l.noFee = 1 AND l.billingMode = 'request') -- single row for noFee orgs
	OR (l.noFee = 0 AND (l.monthlyFee > 0 OR l.overageFee > 0));

	-- add any sites that have badge print requests but no fee structure defined
	INSERT INTO #tmpBadgePrintBilling (siteCode, numRequests, depomemberdataID, overageFee, 
		noofCallIncFee, noofCallsInOverageFee, NoFee, MonthlyFee, billingState, 
		billingZip, billingCountry, billingMode)
	SELECT ar.siteCode, ar.numRequests, mcb.depoMemberDataID, 0, 0, 0, 0, 0, 
		d.billingState, case when d.billingCountry = 1 then left(d.billingZip,5) else d.billingZip end, 
		d.billingCountry, 'request'
	FROM #tmpBadgePrintRequests AS ar
	INNER JOIN trialsmith.dbo.memberCentralBilling AS mcb ON mcb.orgcode = ar.siteCode
	LEFT OUTER JOIN trialsmith.dbo.depoMemberData AS d ON d.depomemberdataID = mcb.depomemberdataID
	LEFT OUTER JOIN #tmpBadgePrintBilling AS lb ON lb.siteCode = ar.siteCode
	WHERE lb.siteCode IS NULL;

	-- monthly transaction detail
	update #tmpBadgePrintBilling
	set reportDetail = format(@asOfDate, 'MMMM yyyy') + 
		' Badge Printing - ' + '$'+cast(MonthlyFee as varchar(10)) + ' monthly fee'
	where MonthlyFee > 0
	and billingMode = 'monthly'
	and NoFee = 0;

	-- request transaction detail
	update #tmpBadgePrintBilling
	set reportDetail = format(DATEADD(MONTH,-1,@asOfDate), 'MMMM yyyy') + 
		' Badge Printing Fee - ' + 
		format(numRequests, 'N0') + ' badge' + case when numRequests <> 1 then 's' else '' end + 
		case when NoFee = 1 then ' - fees waived'
		else '' + 
			case 
				when noofCallIncFee > 0 or overageFee > 0 then ' - ' 
				else '' 
				end +
			case when noofCallIncFee > 0 then cast(noofCallIncFee as varchar(10)) + ' included, ' else '' end +
			case when overageFee > 0 and noofCallsInOverageFee > 0 then '$'+cast(overageFee as varchar(20)) + '/' + cast(cast(noofCallsInOverageFee as int) as varchar(10)) + ' badge' + case when noofCallsInOverageFee <> 1 then 's' else '' end else '' end
		end
	where billingMode = 'request';

	-- no billing DEPOID
	update #tmpBadgePrintBilling
	set billed = 0,
		reportDetail = '**NO BILLING DEPOID** - ' + reportDetail
	where depomemberdataID is null
	and billed = 1;

	-- bad tax records
	update ss
	set ss.billed = 0,
		ss.reportDetail = '**INVALID TAX DATA** - ' + reportDetail
	from #tmpBadgePrintBilling as ss
	left outer join trialsmith.dbo.avalaraTaxTable as att on att.State = ss.billingState and att.ZipCode = ss.billingZip
	where ss.billingCountry = 1
	and ss.billed = 1
	and att.state is null;

	-- no billing rate
	update #tmpBadgePrintBilling
	set billed = 0,
		reportDetail = '**NO BILLING RATE** - ' + reportDetail
	where MonthlyFee = 0 and overageFee = 0 and NoFee = 0
	and billed = 1;

	-- generate amountBilled
	update #tmpBadgePrintBilling
	set amountBilled = MonthlyFee
	where billed = 1
	and billingMode = 'monthly';

	update #tmpBadgePrintBilling
	set amountBilled = 
		case when NoFee = 1 then 0
			when noofCallIncFee > 0 and noofCallIncFee >= numRequests then 0
			when noofCallIncFee > 0 and noofCallsInOverageFee > 0 then ((numRequests - noofCallIncFee) / noofCallsInOverageFee) * overageFee
			when noofCallsInOverageFee > 0 then numRequests / noofCallsInOverageFee * overageFee
			else 0
			end
	where billed = 1
	and billingMode = 'request';

	DECLARE @lastDay smalldatetime;
	EXEC trialsmith.dbo.up_getLastDayOfClosedBillingPeriod @lastDay=@lastDay OUTPUT;
	IF @lastDay >= @TSTransactionDate BEGIN
		SET @recordTransactions = 0;
		UPDATE #tmpBadgePrintBilling SET billed = 0;
	END

	-- record transactions
	IF @recordTransactions = 1
		INSERT INTO trialsmith.dbo.depoTransactions (depoMemberDataID, [Description], AmountBilled, 
			salesTaxAmount, datePurchased, AccountCode, isPayment, stateForTax, zipForTax, statsSessionID)
		SELECT depoMemberDataID, reportDetail, amountBilled, 
			trialsmith.dbo.fn_tax_getTax('TS',billingState,billingZip,'5038',amountBilled),
			@TSTransactionDate, '5038', 0, billingState, billingZip, null
		from #tmpBadgePrintBilling
		where billed = 1;
	
	-- return for the emailed report.
	select siteCode, billed, depomemberdataid, reportDetail, amountBilled, billingState, billingZip
	from #tmpBadgePrintBilling
	order by siteCode;

	IF OBJECT_ID('tempdb..#tmpBadgePrintUsageSites') IS NOT NULL 
		DROP TABLE #tmpBadgePrintUsageSites;
	IF OBJECT_ID('tempdb..#tmpBadgePrintRequests') IS NOT NULL 
		DROP TABLE #tmpBadgePrintRequests;
	IF OBJECT_ID('tempdb..#tmpBadgePrintBillingData') IS NOT NULL
		DROP TABLE #tmpBadgePrintBillingData;
	IF OBJECT_ID('tempdb..#tmpBadgePrintBilling') IS NOT NULL 
		DROP TABLE #tmpBadgePrintBilling;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
