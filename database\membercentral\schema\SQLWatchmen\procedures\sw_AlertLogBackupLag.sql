ALTER PROCEDURE [dbo].[sw_AlertLogBackupLag]
(
	@LogBackupLagAlertThreshold INT = NULL,
	@LogBackupLagExcludedDatabases NVARCHAR(MAX) = NULL,
	@MailProfileName NVARCHAR(MAX) = NULL,
	@AlertEmail NVARCHAR(MAX) = NULL,
	@Company NVARCHAR(MAX) = NULL,
	@Configuration NVARCHAR(MAX) = NULL
)
AS
BEGIN
	SET NOCOUNT ON;

    IF @LogBackupLagAlertThreshold IS NULL
    BEGIN
        SET @LogBackupLagAlertThreshold = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'LogBackupLagAlertThreshold')
    END

    IF @LogBackupLagExcludedDatabases IS NULL
    BEGIN
        SET @LogBackupLagExcludedDatabases = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'LogBackupLagExcludedDatabases')
    END

    IF OBJECT_ID('tempdb..#LogBackupLagAlert') IS NOT NULL
    BEGIN
        DROP TABLE #LogBackupLagAlert
    END

    CREATE TABLE #LogBackupLagAlert
    (
        [Database] NVARCHAR(128) NOT NULL,
        [LastLogBackupDate] DATETIME NOT NULL,
        [BackupAgeHours] INT NOT NULL
    );

    WITH lastLogBackup AS (
        SELECT
            [database_name],
            MAX([backup_finish_date]) AS [LastLogBackupDate]
        FROM [msdb].[dbo].[backupset]
        WHERE [type] = 'L'
        GROUP BY [database_name]
    )
    INSERT INTO #LogBackupLagAlert ([Database], [LastLogBackupDate], [BackupAgeHours])
    SELECT 
        [d].[name] AS [Database],
        COALESCE([llb].[LastLogBackupDate], '1900-01-01 00:00:00.000') AS [LastLogBackupDate],
        DATEDIFF(HOUR, COALESCE([llb].[LastLogBackupDate], '1900-01-01 00:00:00.000'), GETDATE()) AS [BackupAgeHours]
    FROM [master].[sys].[databases] AS [d]
    LEFT JOIN [lastLogBackup] AS [llb] ON 
        [d].[name] = [llb].[database_name]
    WHERE 
        [d].[database_id] <> 2
        AND [d].[recovery_model] <> 3
        AND (',' + @LogBackupLagExcludedDatabases + ',' NOT LIKE '%,' + [d].[name] + ',%');

    IF EXISTS (SELECT * FROM #LogBackupLagAlert WHERE [BackupAgeHours] > @LogBackupLagAlertThreshold)
    BEGIN

        IF @MailProfileName IS NULL
		BEGIN
			SELECT @MailProfileName = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'MailProfileName'
		END

		IF @AlertEmail IS NULL
		BEGIN
			SELECT @AlertEmail = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'AlertEmail'
		END

		IF @Company IS NULL
		BEGIN
			SELECT @Company = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Company'
		END

		IF @Configuration IS NULL
		BEGIN
			SELECT @Configuration = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Configuration'
		END
        
        DECLARE @Subject NVARCHAR(255)
        DECLARE @Message NVARCHAR(MAX)

        SET @Subject = 'Log Backup Lag Alert'
        SET @Message = 'The following databases have not had a log backup in over ' + CAST(@LogBackupLagAlertThreshold AS NVARCHAR) + ' hours:' + CHAR(13) + CHAR(10) + CHAR(13) + CHAR(10)

        SELECT 
            @Message += [Database] + ' | ' + CAST([BackupAgeHours] AS NVARCHAR) + ' hours' + CHAR(13) + CHAR(10)
        FROM #LogBackupLagAlert
        WHERE [BackupAgeHours] > @LogBackupLagAlertThreshold

        INSERT INTO [SQLWatchmen].[dbo].[AlertLog] ([Alert]) VALUES ('LogBackupLag')

        EXEC msdb.dbo.sp_send_dbmail 
            @profile_name = @MailProfileName,
            @recipients = @AlertEmail,
            @subject = @Subject,
            @body = @Message
    END

    IF OBJECT_ID('tempdb..#LogBackupLagAlert') IS NOT NULL
    BEGIN
        DROP TABLE #LogBackupLagAlert
    END

END
GO
