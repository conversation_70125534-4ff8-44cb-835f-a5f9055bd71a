ALTER PROC dbo.tr_prepareInvoiceProfilesImport
@siteID int,
@pathToImport varchar(400),
@importResult XML OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tblImportErrors') IS NOT NULL
		DROP TABLE #tblImportErrors;
	IF OBJECT_ID('tempdb..#tmpOrgInvoiceProfiles') IS NOT NULL
		DROP TABLE #tmpOrgInvoiceProfiles;
	IF OBJECT_ID('tempdb..#tmpOrgInvoiceProfilesMerchantProfiles') IS NOT NULL
		DROP TABLE #tmpOrgInvoiceProfilesMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpOrgUpdateInvoiceProfiles') IS NOT NULL
		DROP TABLE #tmpOrgUpdateInvoiceProfiles;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteInvoiceProfiles') IS NOT NULL
		DROP TABLE #tmpOrgDeleteInvoiceProfiles;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteInvoiceProfilesMerchantProfiles') IS NOT NULL
		DROP TABLE #tmpOrgDeleteInvoiceProfilesMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpSiteSolicitationMessages') IS NOT NULL
		DROP TABLE #tmpSiteSolicitationMessages;

	CREATE TABLE #tblImportErrors (rowID int IDENTITY(1,1), msg varchar(600), errorCode varchar(20));
	CREATE TABLE #tmpOrgInvoiceProfiles (profileID int, profileName varchar(50), enableAutoPay bit, enforcePayOldest bit, 
		notifyEmail varchar(100), allowPartialPayment bit, numDaysDelinquent int, enableProcessingFeeDonation bit, 
		processFeeDonationDefaultSelect bit, solicitationMessageID int, orgIdentityOrgName varchar(100));
	CREATE TABLE #tmpOrgInvoiceProfilesMerchantProfiles (invoiceProfileID int, merchantProfileID int, profileCode varchar(20), profileName varchar(100));
	CREATE TABLE #tmpOrgUpdateInvoiceProfiles (profileName varchar(50));
	CREATE TABLE #tmpOrgDeleteInvoiceProfiles (invoiceProfileID int, profileName varchar(50));
	CREATE TABLE #tmpOrgDeleteInvoiceProfilesMerchantProfiles (invoiceProfileID int, merchantProfileID int, profileCode varchar(20), profileName varchar(100));
	CREATE TABLE #tmpSiteSolicitationMessages (messageID int, title varchar(100), message varchar(800));

	DECLARE @orgID int, @orgCode varchar(10), @siteCode varchar(10), @cmd varchar(400), @svrName varchar(40);
	
	SELECT @siteCode = s.siteCode, @orgCode = o.orgCode, @orgID = o.orgID
	FROM dbo.sites AS s
	INNER JOIN dbo.organizations AS o ON o.orgID = s.orgID
	WHERE s.siteID = @siteID;

	SET @svrName = CAST(SERVERPROPERTY('ServerName') AS varchar(40));

	-- ensure files are present
	IF dbo.fn_FileExists(@pathToImport + 'sync_tr_invoiceProfiles.bcp') = 0 
		OR dbo.fn_FileExists(@pathToImport + 'sync_tr_solicitationMessages.bcp') = 0 
		OR dbo.fn_FileExists(@pathToImport + 'sync_tr_invoiceProfilesMerchantProfiles.bcp') = 0 BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('Required files in the backup file is missing.', 'FILEMISSING');
		GOTO on_done;
	END

	-- delete org sync rows
	DELETE FROM datatransfer.dbo.sync_tr_invoiceProfiles WHERE orgCode = @orgCode;
	DELETE FROM datatransfer.dbo.sync_tr_solicitationMessages WHERE siteCode = @siteCode;
	DELETE FROM datatransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles WHERE orgCode = @orgCode;

	-- import data
	SET @cmd = 'bcp datatransfer.dbo.sync_tr_invoiceProfiles in ' + @pathToImport + 'sync_tr_invoiceProfiles.bcp -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	SET @cmd = 'bcp datatransfer.dbo.sync_tr_solicitationMessages in ' + @pathToImport + 'sync_tr_solicitationMessages.bcp -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	SET @cmd = 'bcp datatransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles in ' + @pathToImport + 'sync_tr_invoiceProfilesMerchantProfiles.bcp -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	-- set orgID/siteID in datatransfer tables. we do this because orgID on one tier may not be the same as another tier
	UPDATE datatransfer.dbo.sync_tr_invoiceProfiles SET orgID = @orgID WHERE orgCode = @orgCode;
	UPDATE datatransfer.dbo.sync_tr_solicitationMessages SET siteID = @siteID WHERE siteCode = @siteCode;
	UPDATE datatransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles SET orgID = @orgID WHERE orgCode = @orgCode;

	INSERT INTO #tmpOrgInvoiceProfiles (profileID, profileName, enableAutoPay, enforcePayOldest, notifyEmail, allowPartialPayment, 
		numDaysDelinquent, enableProcessingFeeDonation, processFeeDonationDefaultSelect, solicitationMessageID, orgIdentityOrgName)
	SELECT ip.profileID, ip.profileName, ip.enableAutoPay, ip.enforcePayOldest, isnull(ip.notifyEmail,''), 
		ip.allowPartialPayment, isnull(ip.numDaysDelinquent,0), ip.enableProcessingFeeDonation, ip.processFeeDonationDefaultSelect, 
		ip.solicitationMessageID, oi.organizationName
	FROM dbo.tr_invoiceProfiles AS ip
	INNER JOIN dbo.orgIdentities AS oi ON oi.orgID = @orgID
		AND oi.orgIdentityID = ip.orgIdentityID
	WHERE ip.orgID = @orgID
	AND ip.[status] = 'A'
	ORDER BY ip.profilename;

	INSERT INTO #tmpOrgInvoiceProfilesMerchantProfiles (invoiceProfileID, merchantProfileID, profileCode, profileName)
	select distinct ipmp.invoiceProfileID, mp.profileID, mp.profileCode, mp.profileName
	from dbo.tr_invoiceProfilesMerchantProfiles as ipmp
	inner join dbo.mp_profiles as mp on mp.profileID = ipmp.merchantProfileID
	inner join dbo.sites as s on s.siteID = mp.siteID
	where s.orgID = @orgID
	and mp.[status] = 'A';

	INSERT INTO #tmpSiteSolicitationMessages (messageID, title, message)
	SELECT messageID, title, message
	FROM dbo.tr_solicitationMessages
	WHERE siteID = @siteID;

	-- update useOrgIdentityID
	UPDATE sip
	SET sip.useOrgIdentityID = oi.orgIdentityID
	FROM dataTransfer.dbo.sync_tr_invoiceProfiles AS sip
	INNER JOIN dbo.orgIdentities AS oi ON oi.orgID = @orgID
		AND oi.organizationName = sip.orgIdentityOrgName
	WHERE sip.orgID = @orgID;

	IF EXISTS (select 1 from dataTransfer.dbo.sync_tr_invoiceProfiles where orgID = @orgID and useOrgIdentityID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) 
		select 'Org Identity "' + orgIdentityOrgName + '" does not match any existing Org Identities.', 'INVALIDORGIDENTITY'
		from dataTransfer.dbo.sync_tr_invoiceProfiles 
		where orgID = @orgID 
		and useOrgIdentityID is null;

		GOTO on_done;
	END

	-- update useMerchantProfileID
	update sipmp
	set sipmp.useMerchantProfileID = mp.profileID
	from dataTransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles as sipmp
	inner join dbo.sites as s on s.orgID = @orgID
	inner join dbo.mp_profiles as mp on mp.siteID = s.siteID and mp.profileCode = sipmp.profileCode
	where sipmp.orgID = @orgID;

	IF EXISTS (select 1 from dataTransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles where orgID = @orgID and useMerchantProfileID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) 
		select 'Profile Code "' + profileCode + '" does not match any existing Merchant Profile Codes', 'INVALIDMP'
		from dataTransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles 
		where orgID = @orgID 
		and useMerchantProfileID is null;

		GOTO on_done;
	END

	-- solicitation messages
	UPDATE spfm
	SET spfm.useMessageID = m.messageID
	FROM dataTransfer.dbo.sync_tr_solicitationMessages AS spfm
	INNER JOIN #tmpSiteSolicitationMessages AS m ON m.message = spfm.message
		AND m.title = spfm.title
	WHERE spfm.siteID = @siteID;

	-- new solicitation messages
	update dataTransfer.dbo.sync_tr_solicitationMessages
	set finalAction = 'A'
	where siteID = @siteID
	and useMessageID is null;

	-- remove invoice profiles
	INSERT INTO #tmpOrgDeleteInvoiceProfiles (invoiceProfileID, profileName)
	select ip.profileID, ip.profileName
	from #tmpOrgInvoiceProfiles as ip
	left outer join dataTransfer.dbo.sync_tr_invoiceProfiles as sip on sip.orgID = @orgID and sip.profileName = ip.profileName
	where sip.profileID is null;

	-- invoice profiles to be deleted is in use
	INSERT INTO #tblImportErrors (msg, errorCode) 
	select distinct 'Invoice Profile "' + profileName + '" is linked to GL Accounts', 'IPINGLUSE'
	from #tmpOrgDeleteInvoiceProfiles as tmp
	inner join dbo.tr_GLAccounts as g on g.orgID = @orgID and g.invoiceProfileID = tmp.invoiceProfileID;

	INSERT INTO #tblImportErrors (msg, errorCode) 
	select distinct 'Invoice Profile "' + profileName + '" is linked to Invoices', 'IPINUSE'
	from #tmpOrgDeleteInvoiceProfiles as tmp
	inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceProfileID = tmp.invoiceProfileID;

	INSERT INTO #tblImportErrors (msg, errorCode) 
	select distinct 'Invoice Profile "' + profileName + '" is linked to Group Assignment Conditions', 'IPINVGCUSE'
	from #tmpOrgDeleteInvoiceProfiles as tmp
	where exists (
		select c.conditionID 
		from dbo.ams_virtualGroupConditions as c 
		inner join dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'acctInvProf'
		where cv.conditionValue = cast(tmp.invoiceProfileID as varchar(20)));

	IF EXISTS (select 1 from #tblImportErrors) 
		GOTO on_done;

	-- update useInvoiceProfileID
	update sipmp
	set sipmp.useInvoiceProfileID = tmp.profileID
	from dataTransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles as sipmp
	inner join dataTransfer.dbo.sync_tr_invoiceProfiles as sip on sip.orgID = @orgID and sip.profileID = sipmp.invoiceProfileID
	inner join #tmpOrgInvoiceProfiles as tmp on tmp.profileName = sip.profileName
	where sipmp.orgID = @orgID;


	-- new invoice profiles
	update sip
	set sip.finalAction = 'A'
	from dataTransfer.dbo.sync_tr_invoiceProfiles as sip
	left outer join #tmpOrgInvoiceProfiles as tmp on tmp.profileName = sip.profileName
	where sip.orgID = @orgID
	and tmp.profileID is null;

	update sipmp
	set sipmp.finalAction = 'A'
	from dataTransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles as sipmp
	inner join dataTransfer.dbo.sync_tr_invoiceProfiles as sip on sip.orgID = @orgID 
		and sip.profileID = sipmp.invoiceProfileID
		and sip.finalAction = 'A'
	where sipmp.orgID = @orgID
	and sipmp.finalAction is null;

	-- update invoice profiles
	INSERT INTO #tmpOrgUpdateInvoiceProfiles (profileName)
	select distinct profileName
	from (
		select sip.profileName, sip.enableAutoPay, sip.enforcePayOldest, sip.notifyEmail, sip.allowPartialPayment, sip.numDaysDelinquent, sip.enableProcessingFeeDonation, 
			sip.processFeeDonationDefaultSelect, spfm.title, spfm.message, sip.orgIdentityOrgName
		from dataTransfer.dbo.sync_tr_invoiceProfiles as sip
		left outer join datatransfer.dbo.sync_tr_solicitationMessages as spfm on spfm.siteID = @siteID
			and spfm.messageID = sip.solicitationMessageID
		where sip.orgID = @orgID
		and sip.finalAction is null
			except
		select tmp.profileName, tmp.enableAutoPay, tmp.enforcePayOldest, tmp.notifyEmail, tmp.allowPartialPayment, tmp.numDaysDelinquent, tmp.enableProcessingFeeDonation, 
			tmp.processFeeDonationDefaultSelect, pfm.title, pfm.message, tmp.orgIdentityOrgName
		from #tmpOrgInvoiceProfiles as tmp
		inner join dataTransfer.dbo.sync_tr_invoiceProfiles as sip on sip.orgID = @orgID and sip.profileName = tmp.profileName
		left outer join #tmpSiteSolicitationMessages as pfm on pfm.messageID = tmp.solicitationMessageID
	) tmp;

	-- update action
	update sip
	set sip.finalAction = 'C'
	from dataTransfer.dbo.sync_tr_invoiceProfiles as sip
	inner join #tmpOrgUpdateInvoiceProfiles as tmp on tmp.profileName = sip.profileName
	where sip.orgID = @orgID
	and sip.finalAction is null;

	-- new invoice merchant profiles
	update sipmp
	set sipmp.finalAction = 'A'
	from dataTransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles as sipmp
	inner join #tmpOrgInvoiceProfiles as tmp on tmp.profileID = sipmp.useInvoiceProfileID
	where sipmp.orgID = @orgID
	and sipmp.finalAction is null
	and not exists (select 1 from #tmpOrgInvoiceProfilesMerchantProfiles where invoiceProfileID = sipmp.useInvoiceProfileID and merchantProfileID = sipmp.useMerchantProfileID)

	-- update action
	update sip
	set sip.finalAction = 'C'
	from dataTransfer.dbo.sync_tr_invoiceProfiles as sip
	inner join dataTransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles as sipmp on sipmp.orgID = @orgID and sipmp.invoiceProfileID = sip.profileID
	where sip.orgID = @orgID
	and sipmp.finalAction = 'A'
	and sip.finalAction is null;

	-- remove invoice profiles merchant profiles
	INSERT INTO #tmpOrgDeleteInvoiceProfilesMerchantProfiles (invoiceProfileID, merchantProfileID, profileCode, profileName)
	select ipmp.invoiceProfileID, ipmp.merchantProfileID, ipmp.profileCode, ipmp.profileName
	from #tmpOrgInvoiceProfilesMerchantProfiles as ipmp
	inner join #tmpOrgInvoiceProfiles as tmp on tmp.profileID = ipmp.invoiceProfileID
	inner join dataTransfer.dbo.sync_tr_invoiceProfiles as sip on sip.orgID = @orgID and sip.profileName = tmp.profileName
	left outer join dataTransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles as sipmp on sipmp.orgID = @orgID 
		and sipmp.useInvoiceProfileID = ipmp.invoiceProfileID
		and sipmp.useMerchantProfileID = ipmp.merchantProfileID
	where sipmp.invoiceProfileID is null;

	-- update action
	update sip
	set sip.finalAction = 'C'
	from dataTransfer.dbo.sync_tr_invoiceProfiles as sip
	inner join #tmpOrgInvoiceProfiles as tmp on tmp.profileName = sip.profileName
	inner join #tmpOrgDeleteInvoiceProfilesMerchantProfiles as tmpD on tmpD.invoiceProfileID = tmp.profileID
	where sip.orgID = @orgID
	and sip.finalAction is null;
	
	on_done:
	-- return the xml results
	select @importResult = (
		select getdate() as "@date", 

			isnull((select distinct profileName as "@profilename"
			from dataTransfer.dbo.sync_tr_invoiceProfiles
			where orgID = @orgID
			and finalAction = 'A'
			FOR XML path('invoiceprofile'), root('newinvoiceprofiles'), type),'<newinvoiceprofiles/>'),

			isnull((select distinct profileName as "@profilename"
			from dataTransfer.dbo.sync_tr_invoiceProfiles
			where orgID = @orgID
			and finalAction = 'C'
			FOR XML path('invoiceprofile'), root('updateinvoiceprofiles'), type),'<updateinvoiceprofiles/>'),

			isnull((select distinct title as "@title", message as "@message"
			from dataTransfer.dbo.sync_tr_solicitationMessages
			where siteID = @siteID
			and finalAction = 'A'
			FOR XML path('solicitationmsg'), root('newsolicitationmsgs'), type),'<newsolicitationmsgs/>'),

			isnull((select distinct invoiceProfileID as "@invoiceprofileid", profileName as "@profilename"
			from #tmpOrgDeleteInvoiceProfiles
			FOR XML path('invoiceprofile'), root('removeinvoiceprofiles'), type),'<removeinvoiceprofiles/>'),

			isnull((select distinct invoiceProfileID as "@invoiceprofileid", merchantProfileID as "@merchantprofileid", 
				profileCode as "@profilecode", profileName as "@profilename"
			from #tmpOrgDeleteInvoiceProfilesMerchantProfiles
			FOR XML path('ipmp'), root('removeinvoiceprofilemerchantprofiles'), type),'<removeinvoiceprofilemerchantprofiles/>'),
			
			isnull((select dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", errorCode as "@errorcode"
			from #tblImportErrors
			order by msg
			FOR XML path('error'), root('errors'), type),'<errors/>')

		for xml path('import'), TYPE);

	IF OBJECT_ID('tempdb..#tblImportErrors') IS NOT NULL
		DROP TABLE #tblImportErrors;
	IF OBJECT_ID('tempdb..#tmpOrgInvoiceProfiles') IS NOT NULL
		DROP TABLE #tmpOrgInvoiceProfiles;
	IF OBJECT_ID('tempdb..#tmpOrgInvoiceProfilesMerchantProfiles') IS NOT NULL
		DROP TABLE #tmpOrgInvoiceProfilesMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpOrgUpdateInvoiceProfiles') IS NOT NULL
		DROP TABLE #tmpOrgUpdateInvoiceProfiles;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteInvoiceProfiles') IS NOT NULL
		DROP TABLE #tmpOrgDeleteInvoiceProfiles;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteInvoiceProfilesMerchantProfiles') IS NOT NULL
		DROP TABLE #tmpOrgDeleteInvoiceProfilesMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpSiteSolicitationMessages') IS NOT NULL
		DROP TABLE #tmpSiteSolicitationMessages;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO