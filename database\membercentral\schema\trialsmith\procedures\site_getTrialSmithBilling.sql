CREATE PROC dbo.site_getTrialSmithBilling
@sitecode varchar(10)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	-- qryOrgHosting
	select scheduleID, effectiveDate, adminRate, adminRateInc, adminRateAddtl, monthlyMin, 
		monthlyFeeAMS, monthlyFeeWeb, noFee, inContract
	from dbo.memberCentralBillingHosting
	where orgCode = @siteCode
	order by effectiveDate;

	-- qryOrgFundraising
	select scheduleID, effectiveDate, monthlyFee, noFee, inContract
	from dbo.memberCentralBillingFundraising
	where orgCode = @siteCode
	order by effectiveDate;

	-- qry<PERSON><PERSON><PERSON><PERSON>
	select scheduleID, effectiveDate, monthlyFee, noFee, inContract
	from dbo.memberCentralBillingLRIS
	where orgCode = @siteCode
	order by effectiveDate;

	-- qryOrgPublications
	select scheduleID, effectiveDate, monthlyFee, noFee, inContract
	from dbo.memberCentralBillingPublications
	where orgCode = @siteCode
	order by effectiveDate;

	-- qryOrgSolicitations
	select scheduleID, effectiveDate, monthlyFee, noFee, inContract
	from dbo.memberCentralBillingSolicitations
	where orgCode = @siteCode
	order by effectiveDate;

	-- qryOrgAPIAccess
	select scheduleID, effectiveDate, monthlyFee, noFee, inContract, noofCallIncFee, overageFee, noofCallsInOverageFee
	from dbo.memberCentralBillingAPIAccess
	where orgCode = @siteCode
	order by effectiveDate;

	-- qryOrgEmailBlast
	select scheduleID, effectiveDate, monthlyFee, monthlyComp, monthlyPerAdminComp, billingRate, includeOtherApps, noFee, inContract
	from dbo.memberCentralBillingEmailBlast
	where orgCode = @siteCode
	order by effectiveDate;

	-- qryOrgDistrictMatching
	select scheduleID, effectiveDate, billingRate, noFee, inContract
	from dbo.memberCentralBillingDistrict
	where orgCode = @siteCode
	order by effectiveDate;

	-- qryOrgAddressUpdate
	select scheduleID, effectiveDate, monthlyFee, billingRate, noFee, inContract
	from dbo.memberCentralBillingAddressUpdate
	where orgCode = @siteCode
	order by effectiveDate;

	-- qryOrgDedicatedServiceMgr
	select scheduleID, effectiveDate, monthlyFee, noFee, inContract
	from dbo.memberCentralBillingDedicatedServiceMgr
	where orgCode = @siteCode
	order by effectiveDate;
		
	-- qryOrgEmailHosting
	select scheduleID, effectiveDate, monthlyFee, noFee, inContract
	from dbo.memberCentralBillingEmailHosting
	where orgCode = @siteCode
	order by effectiveDate;

	-- qryOrgPrivateListServerDomain
	select scheduleID, effectiveDate, monthlyFee, noFee, inContract
	from dbo.memberCentralBillingPrivateListServerDomain
	where orgCode = @siteCode
	order by effectiveDate;

	-- qryOrgPrivateEmailSendingDomain
	select scheduleID, effectiveDate, monthlyFee, noFee, inContract
	from dbo.memberCentralBillingPrivateEmailSendingDomain
	where orgCode = @siteCode
	order by effectiveDate;

	-- qryOrgEntPlatformSecurity
	select scheduleID, effectiveDate, monthlyFee, noFee, inContract
	from dbo.memberCentralBillingEntPlatformSecurity
	where orgCode = @siteCode
	order by effectiveDate;

	-- qryOrgDiscretionary
	select scheduleID, effectiveDate, feeDesc, monthlyFee, numMonths, inContract
	from dbo.memberCentralBillingDiscretionary
	where orgCode = @siteCode
	order by effectiveDate;

	-- qryOrgDiscLists
	select scheduleID, effectiveDate, monthlyFee, monthlyComp, billingRate, billingRatePer, noFee
	from dbo.memberCentralBillingDiscLists 
	where orgCode = @siteCode
	order by effectiveDate;

	-- qryOrgMktLists
	select scheduleID, effectiveDate, monthlyFee, monthlyComp, billingRate, billingRatePer, noFee
	from dbo.memberCentralBillingMktLists 
	where orgCode = @siteCode
	order by effectiveDate;

	-- qryOrgSWMonthly
	select scheduleID, effectiveDate, monthlySupportFee, monthlyPortalHostingFee, inContract
	from dbo.memberCentralBillingSWMonthly
	where orgCode = @siteCode
	order by effectiveDate;

	-- qryOrgSWLive
	select scheduleID, effectiveDate, nonSynInContract, nonSynSetupFeeInc, nonSynSetupFeeAddtl, 
		nonSynSetupFeeAddtlAfter, synSetupFeeInc, synSetupFeeAddtl, synSetupFeeAddtlAfter, nonSynRegFee, 
		nonSynRegFeeMinInc, nonSynRegFeeAddtl, nonSynPctPub, nonSynPctSW, nonSynRegFeeSW, nonSynPctCC, 
		synInContract, synSemEnableSynd, synSemSyndPerSite, synRegFee, synRegFeeMinInc, synRegFeeAddtl, 
		synPctPub, synPctPart, synPctSW, synRegFeeSW, synPctCC
	from dbo.memberCentralBillingSWLive
	where orgCode = @siteCode
	order by effectiveDate;

	-- qryOrgSWOnDemand
	select scheduleID, effectiveDate, semSetupFee, semSetupInc, semSetupIncEa, examSetupFee,semHostOvrFee, 
		semHostVideoFee, nonSynRegFee, nonSynPctPub, nonSynPctSW, nonSynRegFeeSW, nonSynPctCC, 
		synSemEnableSynd, synSemSyndPerSite, synRegFee, synPctPub, synPctPart, synPctSW, synRegfeeSW,
		synPctCC, setupFeeAddtl, conSyncAddtl, conEditAddtl, semSetupInContract, semHostInContract,
		nonSynInContract, synInContract
	from dbo.memberCentralBillingSWOnDemand
	where orgCode = @siteCode
	order by effectiveDate;

	-- qryOrgSWBundles
	select scheduleID, effectiveDate, bunSetupFee, bunSetupInc, bunSetupIncEa, nonSynSWLRegFee,
		nonSynSWLRegFeeMinInc, nonSynSWLRegFeeAddtl, nonSynSWLPctPub, nonSynSWLPctSW, nonSynSWLRegFeeSW,
		nonSynSWLPctCC, synSWLSemEnableSynd, synSWLSemSyndPerSite, synSWLRegFee, synSWLRegFeeMinInc,
		synSWLRegFeeAddtl, synSWLPctPub, synSWLPctPart, synSWLPctSW, synSWLRegFeeSW, synSWLPctCC,
		nonSynSWODRegFee, nonSynSWODPctPub, nonSynSWODPctSW, nonSynSWODRegFeeSW, nonSynSWODPctCC,
		synSWODSemEnableSynd, synSWODSemSyndPerSite, synSWODRegFee, synSWODPctPub, synSWODPctPart,
		synSWODPctSW, synSWODRegfeeSW, synSWODPctCC, bunSetupInContract, nonSynSWLInContract, 
		synSWLInContract, nonSynSWODInContract, synSWODInContract
	from dbo.memberCentralBillingSWBundles
	where orgCode = @siteCode
	order by effectiveDate;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
