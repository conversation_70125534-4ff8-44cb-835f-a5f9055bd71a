CREATE PROCEDURE TwilioMsgStatusTrackingInitiatorQueue_activated
AS

BEGIN

	DECLARE @handle UNIQUEIDENTIFIER;
	DECLARE @message_type SYSNAME;

	BEGIN TRANSACTION;
		WAITFOR (
			RECEIVE TOP(1) @handle = [conversation_handle], @message_type = [message_type_name]
			FROM TwilioMsgStatusTrackingInitiatorQueue
		), TIMEOUT 5000;

		IF @@ROWCOUNT = 1 BEGIN
			-- Expect target response to EndOfStream message.
			IF @message_type = 'https://schemas.microsoft.com/SQL/ServiceBroker/EndDialog' BEGIN
				END CONVERSATION @handle;
			END
		END
	COMMIT TRAN;

END
GO