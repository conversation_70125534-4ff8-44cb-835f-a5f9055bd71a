<cfsavecontent variable="local.registrantCreditsJS">
	<cfoutput>
	<script language="javascript">	
		function doQuickAct() {
			<!--- reset --->
			$('##trAwardOVCredit').hide();
			$('##trAwardCredit').hide();
			$('##trRemoveCredit').hide();
			$("##sel_standard_offeringTypeID").val($("##sel_standard_offeringTypeID option:first").val());
			$('##txt_ov_CreditValue').val('');
			$("##sel_ov_offeringTypeID").val($("##sel_ov_offeringTypeID option:first").val());
			hideACAlert();

			switch ($('##selQuickAct').val()) {
				<cfif NOT arguments.event.valueExists('_rid')>
					case 'markAllAttended': markAllAttended(); break;
					case 'markNoneAttended': markNoneAttended(); break;
					case 'uploadAttendance': uploadAttendance(); break;
				</cfif>
				case 'awardCredit': awardCredit(); break;
				case 'awardOVCredit': awardOVCredit(); break;
				case 'removeCredit': removeCredit(); break;
			}
		};
		<cfif NOT arguments.event.valueExists('_rid')>
			function markAllAttended() {
				var arrEl = document.forms["frmAttendanceCredit"].elements;
				for (var i=0; i < arrEl.length; i++) {
					if (arrEl[i].name.indexOf('attended_') > -1 && arrEl[i].type == 'radio' && arrEl[i].value == '1') {
						arrEl[i].checked = true;	
					}
				}
			};
			function markNoneAttended() {
				var arrEl = document.forms["frmAttendanceCredit"].elements;
				for (var i=0; i < arrEl.length; i++) {
					if (arrEl[i].name.indexOf('attended_') > -1 && arrEl[i].type == 'radio' && arrEl[i].value == '0') {
						arrEl[i].checked = true;	
					}
				}
			};
			function uploadAttendance() {
				$("button##btnSaveChanges").attr('disabled','disabled');
				self.location.href = '#local.importCreditsPromptLink#';
			};
		</cfif>
		function awardCredit() {
			$('##trAwardCredit').show();
		};
		function awardOVCredit() {
			$('##trAwardOVCredit').show();
		};
		function removeCredit() {
			$('##trRemoveCredit').show();
		};
		function validateForm() {
			var qa = $('##selQuickAct').val();
			switch (qa) {
				case 'awardCredit': 
					if (!$('##sel_standard_offeringTypeID') || $('##sel_standard_offeringTypeID').val() == 0) {
						showACAlert('Select which credit should be awarded to all who attended.');
						return false;
					}
					break;
				case 'awardOVCredit': 
					var enteredCreditVal = parseFloat(0+$('##txt_ov_CreditValue').val());
					if (enteredCreditVal.length == 0 || parseFloat(enteredCreditVal) == 0) {
						showACAlert('Enter the number of credits to be awarded.');
						return false;
					}
					if (!$('##sel_ov_offeringTypeID') || $('##sel_ov_offeringTypeID').val() == 0) {
						showACAlert('Select which credit should be awarded.');
						return false;
					}
					break;
				case 'removeCredit':
					if (!$('##sel_remove_offeringTypeID') || $('##sel_remove_offeringTypeID').val() == 0) {
						showACAlert('Select which credit should be removed.');
						return false;
					}
					break;
			}
			hideACAlert();
			return true;
		};
		function hideACAlert() {
			$('##AC_err_div').html('').hide();
		};
		function showACAlert(msg) {
			$('##AC_err_div').html(msg).addClass('alert alert-danger').show();
		};

		$(function(){
			$("##frmAttendanceCredit").submit(function(event){						
				/*
				* 1) stuff all the values of the radio buttons that selected 'Yes' for 'Attended?' into a single hidden form field
				* 2) disable all the 'Attended?' radio buttons so they dont get sent in the form post (too many form fields cause 500 errors from ColdFusion)
				*/				
				var attendedFieldsChecked = $(".attended:checked");
				var arrAttendeeIDs = [];				
				$.each(attendedFieldsChecked, function(index, currentField){										
					var currentFieldName = $(currentField).attr('name');
					var isCurrentFieldChecked = $("input[name="+currentFieldName+"]").prop('checked');
					if (isCurrentFieldChecked){											
						var checkedAttendeeID = currentFieldName.split('_')[1];							
						arrAttendeeIDs.push(checkedAttendeeID);							
					}
				});
				var listOfAttendeeIDs = arrAttendeeIDs.join(",");
				$("##attendedList").val(listOfAttendeeIDs);
				$(".attended").prop('disabled', true);
			});
			top.MCModalUtils.setTitle('Manage Attendance and Credits <cfif arguments.event.valueExists('_rid')>for #encodeForJavaScript(local.qryRegistrants.lastname)#, #encodeForJavaScript(local.qryRegistrants.firstname)# (#encodeForJavaScript(local.qryRegistrants.memberNumber)#)<cfelse>For Filtered Registrants</cfif>');
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.registrantCreditsJS)#">

<cfsavecontent variable="local.stdofferingTypeIDoptions">
	<cfset local.stdofferingTypeID = "">
	<cfloop query="local.qryCreditsGrid">
		<cfset local.offeredCreditTypes = xmlParse(local.qryCreditsGrid.offeredCreditTypes)>
		<cfset local.offeredCreditTypes = local.offeredCreditTypes.xmlRoot>
		<cfloop array="#local.offeredCreditTypes.xmlChildren#" index="local.thisType">
			<cfoutput><option value="#local.thisType.xmlAttributes.offeringTypeID#">#local.thisType.xmlAttributes.creditValue# #local.thisType.xmlAttributes.creditType# - #local.qryCreditsGrid.authorityname#</option></cfoutput>
			<cfset local.stdofferingTypeID = listAppend(local.stdofferingTypeID,local.thisType.xmlAttributes.offeringTypeID)>
		</cfloop>
	</cfloop>
</cfsavecontent>

<cfoutput>
<cfform name="frmAttendanceCredit" id="frmAttendanceCredit" method="POST" class="m-3" action="#this.link.saveAttendanceCredit#">
	<cfinput type="hidden" name="eid" id="eid" value="#arguments.event.getValue('eID')#">
	<cfif arguments.event.valueExists('_rid')>
		<cfinput type="hidden" name="_rid" id="_rid" value="#arguments.event.getValue('_rid')#">
	</cfif>
	
	<div class="row mx-0">
		<div class="col">
			<div class="form-group row">
				<label for="selQuickAct" class="col-sm-3">Quick actions:</label>
				<div class="col-sm-5">
					<select name="selQuickAct" id="selQuickAct" class="form-control form-control-sm" onChange="doQuickAct();">
						<option value=""></option>
						<cfif NOT arguments.event.valueExists('_rid')>
							<option value="markAllAttended">Mark All Attended</option>
							<option value="markNoneAttended">Mark None Attended</option>
							<option value=""></option>
							<option value="uploadAttendance">Upload Attendance and Credits</option>
							<option value=""></option>
						</cfif>
						<option value="awardCredit">Award Standard Credit</option>
						<option value="awardOVCredit">Award Override Credit</option>
						<option value="removeCredit">Remove Credit</option>
					</select>
				</div>
				<div class="col-sm-auto">
					<button id="btnSaveChanges" type="submit" class="btn btn-sm btn-primary" onClick="return validateForm();" <cfif local.qryRegistrants.recordCount EQ 0>disabled</cfif>>Save Changes</button>
				</div>
			</div>
			<div class="form-group row mt-3" id="trAwardCredit" style="display:none;">
				<label for="sel_standard_offeringTypeID" class="col-sm-12">Award:</label>
				<div class="col-sm-12">
					<select name="sel_standard_offeringTypeID" id="sel_standard_offeringTypeID" class="form-control form-control-sm">
						<option value="0"></option>
						<option value="#local.stdofferingTypeID#">All designated credits</option>
						#local.stdofferingTypeIDoptions#
					</select>					
				</div>
				<div class="col-sm-12 mt-1">
					<cfif NOT arguments.event.valueExists('_rid')>
						(will be awarded to all who attended and have not yet been awarded this credit)
					<cfelse>
						(will be awarded if attended and has not yet been awarded this credit)
					</cfif>
				</div>
			</div>
			<div class="form-group row mt-3" id="trRemoveCredit" style="display:none;">
				<label for="sel_remove_offeringTypeID" class="col-sm-12">Remove <cfif NOT arguments.event.valueExists('_rid')>all </cfif>awarded credit for:</label>
				<div class="col-sm-12">
					<select name="sel_remove_offeringTypeID" id="sel_remove_offeringTypeID" class="form-control form-control-sm">
						<option value="0"></option>
						<cfloop query="local.qryCreditsGrid">
							<cfset local.offeredCreditTypes = xmlParse(local.qryCreditsGrid.offeredCreditTypes)>
							<cfset local.offeredCreditTypes = local.offeredCreditTypes.xmlRoot>
							<cfloop array="#local.offeredCreditTypes.xmlChildren#" index="local.thisType">
								<option value="#local.thisType.xmlAttributes.offeringTypeID#">#local.thisType.xmlAttributes.creditType# - #local.qryCreditsGrid.authorityname#</option>
							</cfloop>
						</cfloop>
					</select>
				</div>
			</div>
			<div class="form-group row mt-3" id="trAwardOVCredit" style="display:none;">
				<div class="col-sm-12 pr-0">
					<div class="d-flex flex-row align-items-center">
						<span>Award</span>
						<input type="text" name="txt_ov_CreditValue" id="txt_ov_CreditValue" class="form-control form-control-sm mx-1" value="" style="width:100px;">
						<span class="text-nowrap">credit(s) of</span>
						<select name="sel_ov_offeringTypeID" id="sel_ov_offeringTypeID" class="form-control form-control-sm ml-1">
							<option value="0"></option>
							<cfloop query="local.qryCreditsGrid">
								<cfset local.offeredCreditTypes = xmlParse(local.qryCreditsGrid.offeredCreditTypes)>
								<cfset local.offeredCreditTypes = local.offeredCreditTypes.xmlRoot>
								<cfloop array="#local.offeredCreditTypes.xmlChildren#" index="local.thisType">
									<option value="#local.thisType.xmlAttributes.offeringTypeID#">#local.thisType.xmlAttributes.creditType# - #local.qryCreditsGrid.authorityname#</option>
								</cfloop>
							</cfloop>
						</select>
					</div>
				</div>
				<div class="col-sm-12 mt-1">
					<cfif NOT arguments.event.valueExists('_rid')>
						(will be awarded to all who attended and have not yet been awarded this credit)
					<cfelse>
						(will be awarded if attended and has not yet been awarded this credit)
					</cfif>
				</div>
			</div>
		</div>
	</div>

	<input type="hidden" id="attendedList" name="attendedList" value="">
	<input type="hidden" name="registrantListFiltered" value="#ValueList(local.qryRegistrants.registrantID)#">

	<div id="AC_err_div" class="mb-1" style="display:none;"></div>
	<table class="table table-sm table-striped mt-3">
		<thead>
		<tr>
			<th>Attendee</th>
			<th>Amt Due</th>
			<th class="px-3">Attended?</th>
			<th>Credits Requested/<span style="color:green;">Awarded</span></th>
		</tr>
		</thead>		
		<cfset local.thisOwesMoney = 0>
		<cfloop query="local.qryRegistrants">
			<cfset local.tmpCredits = xmlParse(local.qryRegistrants.creditsXML)>
			<cfset local.tmpCredits = local.tmpCredits.xmlRoot>
			<cfif local.qryRegistrants.amountDue gt 0 and not local.thisOwesMoney and local.qryRegistrants.currentRow is not 1>
				<tr><td colspan="7">&nbsp;</td></tr>
			</cfif>
			<cfset local.thisOwesMoney = local.qryRegistrants.amountDue gt 0>
			<tr>
				<td class="align-top">#local.qryRegistrants.lastname#, #local.qryRegistrants.firstname# (#local.qryRegistrants.memberNumber#)</td>
				<td class="align-top <cfif local.qryRegistrants.amountDue gt 0>font-weight-bold text-danger</cfif>">#dollarformat(local.qryRegistrants.amountDue)#</td>
				<td class="align-top text-nowrap px-3">
					<cfloop list="1,0" index="local.yesno">
						<cfinput type="radio" name="attended_#local.qryRegistrants.registrantID#_" id="attended_#local.qryRegistrants.registrantID#_#local.yesno#" class="attended" required="yes" message="Did #JSStringFormat('#local.qryRegistrants.firstname# #local.qryRegistrants.lastname#')# attend?" value="#local.yesno#" checked="#local.yesno eq local.qryRegistrants.attended#">
						<label for="attended_#local.qryRegistrants.registrantID#_#local.yesno#">#yesNoFormat(local.yesno)#
					</cfloop>
				</td>
				<td class="align-top">
					<cfif arrayLen(local.tmpCredits.xmlChildren)>
						<table class="table table-sm table-borderless">
						<cfloop array="#local.tmpCredits.xmlChildren#" index="local.thiscredit">
							<tr>
								<cfif local.thiscredit.xmlAttributes.creditAwarded>
									<td class="font-weight-bold font-size-sm align-top" style="color:green;" width="20">#local.thiscredit.xmlAttributes.creditValueAwarded#</td>
									<td class="font-weight-bold font-size-sm align-top" style="color:green;">
								<cfelse>
									<td class="align-top" width="20"><cfif local.thiscredit.xmlAttributes.addedViaAward is not 1>Req<cfelse>n/a</cfif></td>
									<td>
								</cfif>
								#local.thiscredit.xmlAttributes.creditType# - #local.thiscredit.xmlAttributes.authority#</td>
							</tr>
						</cfloop>
						</table>
					<cfelse>
						<div class="p-1">None</div>
					</cfif>
				</td>
			</tr>
		</cfloop>
	</table>
</cfform>
</cfoutput>