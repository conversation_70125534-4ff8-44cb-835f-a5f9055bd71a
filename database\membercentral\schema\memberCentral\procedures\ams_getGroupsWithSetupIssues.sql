CREATE PROC dbo.ams_getGroupsWithSetupIssues
@orgID int,
@limitRowsCount int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
				
	DECLARE @totalCount int;

	IF OBJECT_ID('tempdb..#tmpGroups') IS NOT NULL 
		DROP TABLE #tmpGroups;
	CREATE TABLE #tmpGroups (groupID int, rowID int);

	INSERT INTO #tmpGroups (groupID, rowID)
	SELECT g.groupID, ROW_NUMBER() OVER(ORDER BY g.groupPathExpanded)
	FROM dbo.ams_groups AS g
	WHERE g.isSystemGroup = 0
	AND g.allowManualAssignment = 0
	AND g.hideOnGroupLists = 0
	AND g.orgID = @orgID
	AND g.[status] = 'A'
	AND NOT EXISTS (
		SELECT rg.autoID
		FROM dbo.cache_ams_recursiveGroups AS rg
		INNER JOIN dbo.ams_virtualGroupRuleGroups AS vgrg ON vgrg.groupID = rg.startGroupID
		INNER JOIN dbo.ams_virtualGroupRules AS vgr ON vgr.ruleid = vgrg.ruleid
			AND vgr.orgID = @orgID
			AND vgr.isActive = 1
		WHERE rg.orgID = @orgID
		AND rg.groupID = g.groupID
	)
	AND NOT EXISTS (
		SELECT rg.autoID
		FROM dbo.cache_ams_recursiveGroups AS rg
		INNER JOIN dbo.ams_groups AS g2 ON g2.orgID = @orgID 
			AND g2.groupID = rg.startGroupID
			AND g2.allowManualAssignment = 1
		WHERE rg.orgID = @orgID
		AND rg.groupID = g.groupID
	);

	SET @totalCount = @@ROWCOUNT;

	SELECT g.groupID, g.groupPathExpanded AS groupPath, @totalCount AS totalCount
	FROM #tmpGroups AS tmp
	INNER JOIN dbo.ams_groups AS g ON g.orgID = @orgID
		AND g.groupID = tmp.groupID
	WHERE tmp.rowID <= @limitRowsCount
	ORDER BY tmp.rowID;

	IF OBJECT_ID('tempdb..#tmpGroups') IS NOT NULL 
		DROP TABLE #tmpGroups;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO