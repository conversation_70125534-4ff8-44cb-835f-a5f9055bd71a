<cfoutput>
<div class="card card-box">
	<div class="card-header py-1 bg-light">
		<div class="card-header--title font-weight-bold font-size-md">Registrant actions for <cfif local.qrySeminar.isOpen>hosting<cfelse>closing</cfif> your webinar</div>
	</div>
	<div class="card-body pb-3">
		<cfif local.qrySeminar.isOpen AND local.isPublisher >
			<div class="alert alert-info p-2"><i class="fa-solid fa-circle-info"></i>
				Change Room Status from Open to Closed on the Basics tab to access registrant actions for closing your webinar.
			</div>
		</cfif>	
		<cfif NOT local.qrySeminar.isOpen AND (local.hasUploadAttendanceRights OR local.hasManageSWLRegAttendanceRights OR local.hasMassEmailSWLRegistrantsRights OR local.hasMassEmailSWLCertificatesRights)>
			<div class="alert alert-warning p-0 m-0">
				<div class="border-0 p-2 m-1 toolButtonBar">
					<cfif local.hasUploadAttendanceRights>
						<div class="mb-0"><a href="javascript:uploadSWLRegAttendance();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to upload web attendance."><i class="fa-regular fa-upload"></i> Upload Attendance</a></div>
					</cfif>
					<cfif local.hasManageSWLRegAttendanceRights>
						<div class="mb-0"><a href="##" onclick="manageSWLRegAttendance();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to manage attendance."><i class="fa-regular fa-pen-to-square"></i> Manage Attendance</a></div>
					</cfif>
					<cfif local.hasMassEmailSWLRegistrantsRights and (
							listFindNoCase(valuelist(local.qrySeminarForms.loadpoint),"evaluation") or 
							listFindNoCase(valuelist(local.qrySeminarForms.loadpoint),"posttest")
						)>
						<div class="mb-0"><a href="##" onclick="massEmailSWLInstructions();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to email program components."><i class="fa-regular fa-envelope"></i> Email Program Components</a></div>
					</cfif>
					<cfif local.hasMassEmailSWLCertificatesRights>
						<div class="mb-0"><a href="##" onclick="massEmailCertificates('SWL');return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to email certificates to filtered registrants."><i class="fa-regular fa-envelope"></i> Email Certificates</a></div>
					</cfif>
				</div>	
			</div>
		</cfif>
		<div class="p-2 m-1 toolButtonBar" style="border-bottom: none">
			<div class="FilterLinkWrap"><a href="javascript:filterSWLProgramRegistrants();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter registrants."><i class="fa-regular fa-filter"></i> Filter Registrants</a></div>
			<cfif local.qrySeminar.isOpen>			
				<cfif local.hasAddSWLRegistrantRights>
					<div><a href="javascript:addSWReg(0);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Click to add a new registrant."><i class="fa-regular fa-user-plus"></i> Add Registrant</a></div>
				</cfif>
				<cfif local.hasToggleSWLRegistrantsRights>
					<div><a class="toggleSWRegistrants" href="javascript:toggleSWProgramRegistrants();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to allow new registrants."><i class="fa-regular fa-badge-check text-dark"></i> <span class="toggleSWRegistrants" data-allow="1">Allow New Registrants</span></a></div>
				</cfif>
				<div><a href="javascript:exportSWProgramRegistrants();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to download registrants."><i class="fa-regular fa-download"></i> Download Registrants</a></div>
				<cfif local.hasMassEmailSWLRegistrantsRights>
					<div><a href="##" onclick="massEmailSWLInstructions();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to email program components."><i class="fa-regular fa-envelope"></i> Email Program Components</a></div>
				</cfif>
				<cfif local.hasMassEmailSWLMaterialsRights>
					<div><a href="##" onclick="massEmailSWLRegMaterials();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to email materials to filtered registrants."><i class="fa-regular fa-envelope"></i> Email Materials</a></div>
				</cfif>
				<cfif local.hasMassEmailSWLRegistrantsRights>
					<div><a href="##" onclick="massEmailSWRegistrants('SWL');return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to email filtered registrants."><i class="fa-regular fa-envelope"></i> Email Registrants</a></div>
				</cfif>
			<cfelse>
				<cfif local.hasMassEmailSWLMaterialsRights>
					<div class="massEmailSWLRegMaterialsWrap"><a href="##" onclick="massEmailSWLRegMaterials();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to email materials to filtered registrants."><i class="fa-regular fa-envelope"></i> Email Materials</a></div>
				</cfif>
				<cfif local.qrySeminar.offerReplay AND local.hasMassEmailSWLReplayLinkRights>
					<div class="massEmailSWLRegReplayLinksWrap"><a href="##" onclick="massEmailSWLRegReplayLinks();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to email recording links to filtered registrants."><i class="fa-regular fa-envelope"></i> Email Recording</a></div>
				</cfif>
				<cfif local.hasMassEmailSWLRegistrantsRights>
					<div><a href="##" onclick="massEmailSWRegistrants('SWL');return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to email filtered registrants."><i class="fa-regular fa-envelope"></i> Email Registrants</a></div>
				</cfif>
				<div><a href="javascript:exportSWProgramRegistrants();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to download registrants."><i class="fa-regular fa-download"></i> Download Registrants</a></div>
				<cfif local.qrySeminarForms.recordCount>
					<div><a href="javascript:exportSWFormResponses();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to download evaluation/exam responses."><i class="fa-regular fa-download"></i> Download Evaluation/Exam Responses</a></div>
				</cfif>
				<div id="registerSWLRegForSWODTool"<cfif not local.hasRegisterForSWODRights> class="d-none"</cfif>><a href="##" onclick="registerSWLRegForSWODPrompt();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to register filtered registrants for on-demand."><i class="fa-regular fa-user-plus"></i> Register for OnDemand</a></div>
				<cfif local.hasAddSWLRegistrantRights>
					<div><a href="javascript:addSWReg(0);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Click to add a new registrant."><i class="fa-regular fa-user-plus"></i> Add Registrant</a></div>
				</cfif>
				<cfif local.hasToggleSWLRegistrantsRights>
					<div><a class="toggleSWRegistrants" href="javascript:toggleSWProgramRegistrants();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to allow new registrants."><i class="fa-regular fa-badge-check text-dark"></i> <span class="toggleSWRegistrants" data-allow="1">Allow New Registrants</span></a></div>
				</cfif>
			</cfif>				
		</div>
	</div>
</div>


<div id="divFilterForm" class="d-none divSWRegistrantsTool">
	<form name="frmRegistrantFilter" id="frmRegistrantFilter">
		<div class="card card-box mb-1">
			<div class="card-header py-1 bg-light">
				<div class="card-header--title font-weight-bold font-size-md">
					Registrant Filters
				</div>
			</div>
			<div class="card-body pb-2">
				<div class="form-row">
					<div class="col-xl-6 col-lg-12">
						<div class="form-row">
							<div class="col">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<div class="input-group dateFieldHolder">
											<input type="text" name="rDateFrom" id="rDateFrom" value="" class="form-control dateControl">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="rDateFrom"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('rDateFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
											<label for="rDateFrom">Registered From</label>
										</div>
									</div>
								</div>
							</div>
							<div class="col">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<div class="input-group dateFieldHolder">
											<input type="text" name="rDateTo" id="rDateTo" value="" class="form-control dateControl">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="rDateTo"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('rDateTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
											<label for="rDateTo">Registered To</label>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="col-xl-6 col-lg-12">
						<div class="form-row">
							<div class="col-xl-6 col-lg-12">
								<div class="form-label-group mb-2">
									<select name="rAttended" id="rAttended" class="form-control">
										<option value="">Attended or Not Attended</option>
										<option value="1">Attended</option>
										<option value="0">Not Attended</option>
									</select>
									<label for="rAttended">Attended/Not Attended</label>
								</div>
							</div>
							<div class="col-xl-6 col-lg-12">
								<div class="form-label-group mb-2">
									<select name="rHideDeleted" id="rHideDeleted" class="form-control">
										<option value="1">Hide Deleted Registrants</option>
										<option value="0">Show Deleted Registrants</option>
									</select>
									<label for="rHideDeleted">Registrant Status</label>
								</div>
							</div>
						</div>
					</div>
				</div>	
				<div class="form-row">
					<div class="col-xl-3 col-lg-12">
						<div class="form-label-group mb-2">
							<select name="rCredits" id="rCredits" class="form-control">
								<option value="">Awarded Credits or No Credits</option>
								<option value="1">Awarded Credits</option>
								<option value="0">Awarded no credits</option>
							</select>
							<label for="rCredits">Awarded Credits/No Credits</label>
						</div>
					</div>
				</div>
			</div>
			<div class="card-footer p-2 text-right">
				<button type="button" name="btnClearRegFilters" class="btn btn-sm btn-secondary" onclick="clearSWLRegFilters();">Clear Filters</button>
				<button type="button" onclick="dofilterSWLProgramRegistrants()" class="btn btn-sm btn-primary"><i class="fa-light fa-filter"></i> Filter Registrants</button>
			</div>
		</div>
	</form>
</div>

<cfif local.qrySeminarForms.recordCount>
	<div id="divFormResponseDownloadForm" class="d-none divSWRegistrantsTool">
		<div id="divFormResponseDownloadFormArea">
			<div class="card card-box">
				<div class="card-header bg-light">
					<div class="card-header--title font-weight-bold font-size-md">Download Evaluation/Exam Responses</div>
				</div>
				<div class="card-body p-3">
					<div id="err_downloadformresponses" class="alert alert-danger mb-3 d-none"></div>
					Select either CSV or PDF to download evaluation/exam responses for the filtered registrants.
					<br/><br/>
					<cfif local.qrySeminarForms.recordCount eq 1>
						<cfif LCase(local.qrySeminarForms.loadpoint) eq 'evaluation'>
							There is one evaluation attached to this seminar:<br/>
						<cfelse>
							There is one exam attached to this seminar:<br/>
						</cfif>
						<b>#local.qrySeminarForms.loadpoint#</b> - <b>#local.qrySeminarForms.formTitle#</b>
						<br/><br/>
						<input type="hidden" name="formID" id="formID" value="#local.qrySeminarForms.formID#">
					<cfelseif local.qrySeminarForms.recordCount gt 1>
						<div class="form-group row">
							<label for="formID" class="col-auto col-form-label-sm font-size-md">Select Evaluation/Exam</label>
							<div class="col">
								<select name="formID" id="formID" class="form-control form-control-sm">
									<option value="0">Select Evaluation/Exam</option>
									<cfoutput query="local.qrySeminarForms" group="loadpoint">
										<optgroup label="#local.qrySeminarForms.loadpoint#">
										<cfoutput>
											<option value="#local.qrySeminarForms.formID#">#local.qrySeminarForms.formTitle#</option>
										</cfoutput>
										</optgroup>
									</cfoutput>
								</select>
							</div>
						</div>
					</cfif>
					<div class="mb-2">
						<button type="button" class="btn btn-sm btn-secondary" onclick="doExportSWFormResponses('csv');" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to download the individual responses as a CSV.">
							<i class="fa-light fa-file-csv"></i> CSV
						</button>
						<span class="ml-2">This will include individual responses as a CSV.</span>
					</div>
					<div>
						<button type="button" class="btn btn-sm btn-secondary" onclick="doExportSWFormResponses('pdf');" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to download the summary of responses as a PDF.">
							<i class="fa-light fa-file-pdf"></i> PDF
						</button>
						<span class="ml-2">This will be a summary of responses as a PDF.</span>
					</div>
				</div>
			</div>
		</div>
		<div id="divFormResponseDownloadFormAreaLoading" class="d-none"></div>
	</div>
</cfif>

<h5 class="mt-4">Registrants</h5>

<table id="SWLRegistrantsListTable" class="table table-sm table-striped table-bordered" style="width:100%">
	<thead>
		<tr>
			<th>Registrant</th>
			<th>Registered</th>
			<th>Attended</th>
			<th>Credits</th>
			<th>Billed</th>
			<th>Due</th>
			<th>Actions</th>
		</tr>
	</thead>
</table>
</cfoutput>