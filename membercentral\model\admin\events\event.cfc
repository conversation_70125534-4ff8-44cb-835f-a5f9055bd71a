<cfcomponent output="no">
	
	<cffunction name="getSiteResourceIDByCalendarID" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="calendarID" type="numeric" required="true">

		<cfset var qrySiteResource = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qrySiteResource">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;

			SELECT ai.siteResourceID
			FROM dbo.ev_calendars as c
			INNER JOIN dbo.cms_applicationInstances as ai on ai.siteID = @siteID and ai.applicationInstanceID = c.applicationInstanceID
			WHERE c.siteID = @siteID
			AND c.calendarID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.calendarID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn val(qrySiteResource.siteResourceID)>
	</cffunction>

	<cffunction name="getSiteResourceIDByEventID" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="eventID" type="numeric" required="true">

		<cfset var qryResource = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryResource">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;

			SELECT siteResourceID
			FROM dbo.ev_events
			WHERE eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.eventID#">
			AND siteID = @siteID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn val(qryResource.siteResourceID)>
	</cffunction>

	<cffunction name="getSiteResourceIDByRegistrationID" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="registrationID" type="numeric" required="true">
		
		<cfset var qryResource = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryResource">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;

			SELECT e.siteResourceID
			FROM dbo.ev_registration as r
			INNER JOIN dbo.ev_events as e on e.siteID = @siteID and e.eventID = r.eventID
			WHERE r.siteID = @siteID
			AND r.registrationID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.registrationID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn val(qryResource.siteResourceID)>
	</cffunction>

	<cffunction name="getCalendar" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="calendarID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCalendar">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @RTID int, @siteID int, @calendarID int, @orgID int;
			SET @RTID = dbo.fn_getResourceTypeID('Community');
			SET @calendarID = <cfqueryparam value="#arguments.calendarID#" cfsqltype="CF_SQL_INTEGER">;
			SET @siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">;
			SELECT @orgID = orgID FROM dbo.sites where siteID = @siteID;

			select top 1 c.calendarID, ai.siteResourceID, ai.applicationInstanceID, ai.applicationInstanceName as baseCalendarName, 
				communityInstances.applicationInstanceName as communityName, pages.pageName, 
				c.defaultGLAccountID, '' as defaultGLAccountPath, @orgID as calendarOrgID, c.isSWL, c.categoryID, c.showAddCalendarLinks, c.allowWebCalSubscriptions,
				c.defStaffConfirmation, c.defRegConfirmation, c.alwaysShowEventTimezone, c.enableRealTimeRoster, isnull(amf.fieldSetID,0) as fieldSetID,
				calendarName = ai.applicationInstanceName + 
					case 
					WHEN communityInstances.applicationInstanceName is not null THEN ' (' + communityInstances.applicationInstanceName + ')'
					ELSE ''
					END,
				ficu.featureImageConfigID, ficus.featureImageSizeID, fiu.featureImageID, fi.fileExtension as featureImageFileExt,
				c.registrantEditRefundContentID, registrantEditRefundContent.rawContent as registrantEditRefundContent,
				c.defaultView, c.remarketingText, c.remarketingBtnText
			from dbo.ev_calendars as c
			inner join dbo.cms_applicationInstances as ai on ai.siteID = @siteID 
				and c.applicationInstanceID = ai.applicationInstanceID 
			inner join dbo.cms_siteResources as sr on sr.siteID = @siteID
				and ai.siteResourceID = sr.siteResourceID
				and sr.siteResourceStatusID = 1
			inner join dbo.cms_pages as pages on pages.siteID = @siteID 
				and pages.siteResourceID = sr.parentSiteResourceID
			inner join dbo.cms_siteResources as parentResource on parentResource.siteID = @siteID
				and parentResource.siteResourceID = sr.parentSiteResourceID
				and parentResource.siteResourceStatusID = 1
			left outer join dbo.ams_memberFieldUsage as amf on amf.siteResourceID = sr.siteResourceID and amf.area = 'roster'
			left outer join dbo.cms_siteResources as grandparentResource
				inner join dbo.cms_applicationInstances as CommunityInstances on CommunityInstances.siteID = @siteID
					and communityInstances.siteResourceID = grandParentResource.siteResourceID
				on grandparentResource.siteID = @siteID
				and grandparentResource.siteResourceID = parentResource.parentSiteResourceID
				and grandparentResource.siteResourceStatusID = 1
				and grandparentResource.resourceTypeID = @RTID
			left outer join dbo.cms_featuredImageConfigUsages as ficu on ficu.referenceID = c.calendarID and ficu.referenceType = 'calendarEvent'
			left outer join dbo.cms_featuredImageConfigUsagesAndSizes as ficus on ficus.featureImageConfigUsageID = ficu.featureImageConfigUsageID
				and ficus.referenceType = 'viewEventsList'
			left outer join dbo.cms_featuredImageUsages as fiu 
				inner join dbo.cms_featuredImages as fi on fi.featureImageID = fiu.featureImageID
				on fiu.featureImageConfigID = ficu.featureImageConfigID 
				and fiu.referenceID = c.calendarID 
				and fiu.referenceType = 'defaultCalendarEvent'
			cross apply dbo.fn_getContent(c.registrantEditRefundContentID,1) as registrantEditRefundContent
			WHERE c.siteID = @siteID
			AND c.calendarID = @calendarID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=val(local.qryCalendar.defaultGLAccountID), orgID=val(local.qryCalendar.calendarOrgID))>
		<cfset QuerySetCell(local.qryCalendar,'defaultGLAccountPath',local.tmpStrAccount.qryAccount.thePathExpanded)>

		<cfreturn local.qryCalendar>
	</cffunction>

	<cffunction name="updateCalendar" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var qryUpdateCalendar = "">
			
		<cfif arguments.event.getValue('cID',0) gt 0>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryUpdateCalendar">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					IF OBJECT_ID('tempdb..##tmpExistingFeaturedImageUsages') IS NOT NULL
						DROP TABLE ##tmpExistingFeaturedImageUsages;
					IF OBJECT_ID('tempdb..##tmpQueueFeaturedImages') IS NOT NULL 
						DROP TABLE ##tmpQueueFeaturedImages;
					CREATE TABLE ##tmpExistingFeaturedImageUsages (referenceID int, referenceType varchar(30), featureImageConfigID int, featureImageID int, featureImageSizeID int);
					CREATE TABLE ##tmpQueueFeaturedImages (featureImageID int, featureImageSizeID int);
				
					DECLARE @orgID int, @siteID int, @calendarID int, @PrevGLAID int, @NewGLAID int, @calAID int, @calSRID int, @useID int, 
						@enteredByMemberID int, @featureImageConfigUsageID int, @featureImageConfigID int, @existingFeatureImageConfigID int, 
						@featureImageSizeID int, @featureImageConfigUsageAndSizeID int, @fieldsetID int;
					DECLARE @tblFeaturedImages TABLE (featureImageID int);

					set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgid')#">;
					set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
					set @calendarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('cID')#">;
					set @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">;
					set @NewGLAID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('defaultGLAccountID')#">;
					select @PrevGLAID = defaultGLAccountID from dbo.ev_calendars where siteID = @siteID and calendarID = @calendarID;
					set @featureImageConfigID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('featureImageConfigID',0)#">;
					set @featureImageSizeID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('featureImageSizeID',0)#">;
					set @fieldsetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getValue('memFieldSet',0))#">;

					select @calAID = ai.applicationInstanceID, @calSRID = ai.siteResourceID
					FROM dbo.ev_calendars as c
					INNER JOIN dbo.cms_applicationInstances as ai on ai.siteID = @siteID
						AND ai.applicationInstanceID = c.applicationInstanceID
					WHERE c.calendarID = @calendarID
					AND c.siteID = @siteID;

					select top 1 @existingFeatureImageConfigID = ficu.featureImageConfigID
					from dbo.ev_calendars as c
					left outer join dbo.cms_featuredImageConfigUsages as ficu on ficu.referenceID = c.calendarID and ficu.referenceType = 'calendarEvent'
					where c.calendarID = @calendarID
					AND c.siteID = @siteID;

					set @existingFeatureImageConfigID = ISNULL(@existingFeatureImageConfigID, 0);

					BEGIN TRAN;
						<cfif compare(arguments.event.getTrimValue('newapplicationInstanceName'),arguments.event.getValue('oldapplicationInstanceName'))>
							UPDATE dbo.cms_applicationInstances
							SET applicationInstanceName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('newapplicationInstanceName')#">
							WHERE applicationInstanceID = @calAID;

							UPDATE c
							SET c.[verbose] = dbo.ams_getVirtualGroupConditionVerbose(c.conditionID)
							FROM dbo.ams_virtualGroupConditions as c 
							INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
							INNER JOIN dbo.ams_virtualGroupConditionKeys as ck on ck.conditionKeyID = cv.conditionKeyID
							WHERE c.orgID = @orgID
							and c.fieldCode = 'ev_entry'
							AND ck.conditionKey = 'evCalendarID'
							AND cv.conditionValue = cast(@calendarID as varchar(10));
						</cfif>
				
						UPDATE dbo.ev_calendars
						SET defaultGLAccountID = @NewGLAID
							<cfif arguments.event.valueExists("isSWL")>
								, isSWL = <cfqueryparam value="#val(arguments.event.getValue('isSWL'))#" cfsqltype="CF_SQL_BIT">
							<cfelse>
								, isSWL = NULL
							</cfif>
							<cfif arguments.event.valueExists("categoryID") and val(arguments.event.getValue("categoryID"))>
								, categoryID = <cfqueryparam value="#arguments.event.getValue('categoryID')#" cfsqltype="CF_SQL_INTEGER">
							<cfelse>
								, categoryID = NULL
							</cfif>
							, showAddCalendarLinks = <cfqueryparam value="#val(arguments.event.getValue('showAddCalendarLinks',0))#" cfsqltype="CF_SQL_BIT">
							, allowWebCalSubscriptions = <cfqueryparam value="#val(arguments.event.getValue('allowWebCalSubscriptions',0))#" cfsqltype="CF_SQL_BIT">
							, defStaffConfirmation = <cfqueryparam value="#val(arguments.event.getValue('defStaffConfirmation',0))#" cfsqltype="CF_SQL_BIT">
							, defRegConfirmation = <cfqueryparam value="#val(arguments.event.getValue('defRegConfirmation',0))#" cfsqltype="CF_SQL_BIT">
							, alwaysShowEventTimezone = <cfqueryparam value="#val(arguments.event.getValue('dspTZAllEvents',0))#" cfsqltype="CF_SQL_BIT">
							, enableRealTimeRoster = <cfqueryparam value="#val(arguments.event.getValue('enRealRegRoster',0))#" cfsqltype="CF_SQL_BIT">
							, defaultView = <cfqueryparam value="#arguments.event.getValue('defaultView', 'listAll')#" cfsqltype="CF_SQL_VARCHAR">
							, remarketingText = <cfqueryparam value="#arguments.event.getValue('remarketingText')#" cfsqltype="CF_SQL_VARCHAR">
							, remarketingBtnText = <cfqueryparam value="#arguments.event.getValue('remarketingBtnText')#" cfsqltype="CF_SQL_VARCHAR">
						WHERE siteID = @siteID 
						AND calendarID = @calendarID;

						-- if we are changing the default calendar GL, update the non-full reg events to use the new default.
						IF @PrevGLAID <> @NewGLAID
							EXEC dbo.ev_updateEventGLNoReg;

						-- delete any existing real time roster fieldsets
						DELETE FROM	dbo.ams_memberFieldUsage
						WHERE siteResourceID = @calSRID
						AND area = 'roster';
				
						<cfif val(arguments.event.getValue('memFieldSet',0)) gt 0>
							EXEC dbo.ams_createMemberFieldUsage @siteResourceID=@calSRID, @fieldsetID=@fieldsetID, @area='roster', @createSiteResourceID=0, @useID=@useID OUTPUT;
						</cfif>

						IF @featureImageConfigID > 0 BEGIN
							-- save calendar featured image settings
							IF (@existingFeatureImageConfigID > 0 AND @featureImageConfigID <> @existingFeatureImageConfigID) BEGIN
								/* selects all image config usages for events under this calendar & default calendar event */
								INSERT INTO ##tmpExistingFeaturedImageUsages (referenceID, referenceType, featureImageConfigID, featureImageID)
								SELECT DISTINCT events.eventID, fiu.referenceType, ficu.featureImageConfigID, fiu.featureImageID
								FROM dbo.cache_calendarEvents AS cache
								INNER JOIN dbo.ev_calendars AS cal ON cal.calendarID = cache.calendarID 		
									AND cal.calendarID = @calendarID
									AND cal.siteID = @siteID
								INNER JOIN dbo.ev_events as events on events.eventID = cache.eventID and events.siteID = @siteID
								INNER JOIN dbo.cms_featuredImageConfigUsages AS ficu ON ficu.referenceID = cal.calendarID AND ficu.referenceType = 'calendarEvent'
								INNER JOIN dbo.cms_featuredImageUsages AS fiu ON fiu.featureImageConfigID = ficu.featureImageConfigID 
									AND fiu.referenceID = events.eventID
									AND fiu.referenceType = 'eventsList'
								INNER JOIN dbo.cms_featuredImages AS fi ON fi.featureImageID = fiu.featureImageID
									UNION
								SELECT fiu.referenceID, fiu.referenceType, fiu.featureImageConfigID, fiu.featureImageID
								FROM dbo.cms_featuredImages AS fi
								INNER JOIN dbo.cms_featuredImageUsages AS fiu ON fiu.featureImageID = fi.featureImageID
								WHERE fiu.referenceID = @calendarID
								AND fiu.referenceType = 'defaultCalendarEvent';

								-- update existing feature image usages
								UPDATE fiu
								SET fiu.featureImageConfigID = @featureImageConfigID
									OUTPUT inserted.featureImageID INTO @tblFeaturedImages
								FROM dbo.cms_featuredImageUsages as fiu
								INNER JOIN ##tmpExistingFeaturedImageUsages as tmp on tmp.featureImageID = fiu.featureImageID
									AND tmp.referenceID = fiu.referenceID
									AND tmp.referenceType = fiu.referenceType
									AND tmp.featureImageConfigID = fiu.featureImageConfigID;

								UPDATE dbo.cms_featuredImageConfigUsages
								SET featureImageConfigID = @featureImageConfigID
								WHERE referenceID = @calendarID
								AND referenceType = 'calendarEvent'
								AND featureImageConfigID = @existingFeatureImageConfigID;

								/* queue thumbnail generation for all featured images with new config */
								INSERT INTO ##tmpQueueFeaturedImages (featureImageID, featureImageSizeID)
								SELECT tbl.featureImageID, ics.featureImageSizeID
								FROM dbo.cms_featuredImageConfigSizes as ics
								CROSS JOIN @tblFeaturedImages as tbl
								WHERE ics.featureImageConfigID = @featureImageConfigID;

								IF @@ROWCOUNT > 0
									EXEC dbo.cms_queueFeaturedImages @enteredByMemberID=@enteredByMemberID;
							END
							ELSE BEGIN
								EXEC dbo.cms_createFeaturedImageConfigUsage @featureImageConfigID=@featureImageConfigID, @referenceType='calendarEvent', 
									@referenceID=@calendarID, @featureImageConfigUsageID=@featureImageConfigUsageID OUTPUT;
							END

							SELECT @featureImageConfigUsageID = featureImageConfigUsageID
							FROM dbo.cms_featuredImageConfigUsages
							WHERE featureImageConfigID = @featureImageConfigID
							AND referenceID = @calendarID
							AND referenceType = 'calendarEvent';
							
							-- save viewEventsList featureImage size settings
							IF @featureImageSizeID > 0 AND @featureImageConfigUsageID IS NOT NULL BEGIN
								SELECT @featureImageConfigUsageAndSizeID = featureImageConfigUsageAndSizeID
								FROM dbo.cms_featuredImageConfigUsagesAndSizes
								WHERE featureImageConfigUsageID = @featureImageConfigUsageID
								AND referenceType = 'viewEventsList';

								IF @featureImageConfigUsageAndSizeID IS NULL
									EXEC dbo.cms_createFeaturedImageConfigUsageAndSize @featureImageConfigUsageID=@featureImageConfigUsageID, 
										@featureImageSizeID=@featureImageSizeID, @referenceType='viewEventsList', 
										@featureImageConfigUsageAndSizeID=@featureImageConfigUsageAndSizeID OUTPUT;
								ELSE
									UPDATE dbo.cms_featuredImageConfigUsagesAndSizes
									SET featureImageSizeID = @featureImageSizeID
									WHERE featureImageConfigUsageAndSizeID = @featureImageConfigUsageAndSizeID;
							END
							ELSE IF @featureImageConfigUsageID IS NOT NULL BEGIN
								DELETE FROM dbo.cms_featuredImageConfigUsagesAndSizes
								WHERE featureImageConfigUsageID = @featureImageConfigUsageID
								AND referenceType = 'viewEventsList';
							END

						END
						-- remove existing config usages and image usages
						ELSE IF @existingFeatureImageConfigID > 0 BEGIN
							SELECT @featureImageConfigUsageID = featureImageConfigUsageID
							FROM dbo.cms_featuredImageConfigUsages
							WHERE featureImageConfigID = @existingFeatureImageConfigID
							AND referenceID = @calendarID
							AND referenceType = 'calendarEvent';

							DELETE FROM dbo.cms_featuredImageUsages
							WHERE featureImageConfigID = @existingFeatureImageConfigID
							AND referenceID = @calendarID
							AND referenceType = 'defaultCalendarEvent';

							DELETE FROM dbo.cms_featuredImageConfigUsagesAndSizes
							WHERE featureImageConfigUsageID = @featureImageConfigUsageID
							AND referenceType = 'viewEventsList';
							
							DELETE FROM dbo.cms_featuredImageConfigUsages
							WHERE featureImageConfigUsageID = @featureImageConfigUsageID;
						END
					COMMIT TRAN;

					IF OBJECT_ID('tempdb..##tmpExistingFeaturedImageUsages') IS NOT NULL
						DROP TABLE ##tmpExistingFeaturedImageUsages;
					IF OBJECT_ID('tempdb..##tmpQueueFeaturedImages') IS NOT NULL 
						DROP TABLE ##tmpQueueFeaturedImages;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		</cfif>
	</cffunction>

	<cffunction name="moveEventToCalendar" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var qryMoveCalendar = "">

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryMoveCalendar">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @siteID int, @eventID int, @fromCalendarID int, @toCalendarID int, @toCategoryIDList varchar(max), @recordedByMemberID int;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
					SET @eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('eID',0)#">;
					SET @fromCalendarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('cID',0)#">;
					SET @toCalendarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('newCalendarID',0)#">;
					SET @toCategoryIDList = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('newCategoryID','')#">;
					SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">;

					EXEC dbo.ev_moveCalendarEvent @eventID=@eventID, @fromCalendarID=@fromCalendarID, @toCalendarID=@toCalendarID, 
						@toCategoryIDList=@toCategoryIDList, @recordedByMemberID=@recordedByMemberID;

					<cfif arguments.event.getValue('mc_siteInfo.sf_recurringEvents') EQ 1 AND arguments.event.getValue('updateUpcomingRecurringEvents',0) EQ 1>
						IF OBJECT_ID('tempdb..##tmpUpcomingRecurringEvents') IS NOT NULL
							DROP TABLE ##tmpUpcomingRecurringEvents;
						CREATE TABLE ##tmpUpcomingRecurringEvents (eventID int PRIMARY KEY, calendarID int);

						DECLARE @recurringSeriesID int, @eventStartDate datetime, @minEventID int;

						SELECT @recurringSeriesID = e.recurringSeriesID, @eventStartDate = et.startTime
						FROM dbo.ev_events AS e
						INNER JOIN dbo.ev_times AS et ON et.eventid = e.eventID
							AND et.timeID = e.defaultTimeID
						WHERE e.eventID = @eventID
						AND e.siteID = @siteID;

						-- upcoming recurring events
						INSERT INTO ##tmpUpcomingRecurringEvents (eventID, calendarID)
						SELECT e.eventID, ce.calendarID
						FROM dbo.ev_events AS e
						INNER JOIN dbo.ev_calendarEvents AS ce ON ce.sourceEventID = e.eventID 
							AND ce.calendarID = ce.sourceCalendarID
						INNER JOIN dbo.ev_times AS et ON et.eventid = e.eventID
							AND et.timeID = e.defaultTimeID
						WHERE e.recurringSeriesID = @recurringSeriesID
						AND e.siteID = @siteID
						AND e.[status] IN ('A','I')
						AND et.startTime > @eventStartDate;

						SELECT @minEventID = MIN(eventID) FROM ##tmpUpcomingRecurringEvents;
						WHILE @minEventID IS NOT NULL BEGIN
							SET @fromCalendarID = NULL;

							SELECT @fromCalendarID = calendarID
							FROM ##tmpUpcomingRecurringEvents 
							WHERE eventID = @minEventID;
							
							EXEC dbo.ev_moveCalendarEvent @eventID=@minEventID, @fromCalendarID=@fromCalendarID, @toCalendarID=@toCalendarID, 
								@toCategoryIDList=@toCategoryIDList, @recordedByMemberID=@recordedByMemberID;

							SELECT @minEventID = MIN(eventID) FROM ##tmpUpcomingRecurringEvents WHERE eventID > @minEventID;
						END
						
						IF OBJECT_ID('tempdb..##tmpUpcomingRecurringEvents') IS NOT NULL
							DROP TABLE ##tmpUpcomingRecurringEvents;
					</cfif>
				
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="deleteCalendar" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="cID" type="numeric" required="true">
		<cfargument name="cSRID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=arguments.cSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
			<cfif NOT local.tmpRights.Delete>
 				<cfthrow message="invalid request">
 			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDeleteCalendar">
				SET NOCOUNT ON;

				DECLARE @siteResourceID int, @parentSiteResourceID int, @calendarID int, @siteID int;
				SET @calendarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.cID#">;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;

				SELECT @siteResourceID = sr.siteresourceID, @parentSiteResourceID = sr.parentSiteResourceID
				FROM dbo.ev_calendars c
				INNER JOIN dbo.cms_applicationInstances ai on ai.siteID = @siteID 
					AND ai.applicationInstanceID = c.applicationInstanceID 
				INNER JOIN dbo.cms_siteResources sr on sr.siteID = @siteID
					AND ai.siteResourceID = sr.siteResourceID
				WHERE c.calendarID = @calendarID
				AND c.siteID = @siteID;

				EXEC dbo.cms_deleteSiteResourceAndChildren @siteID=@siteID, @siteResourceID=@parentSiteResourceID;

				<!--- mark events associated with this calendar as deleted --->
				UPDATE e
				SET e.status = dbo.fn_setEventStatus(e.eventID)
				FROM dbo.ev_events as e
				INNER JOIN dbo.ev_calendarEvents AS ce ON ce.sourceEventID = e.eventID
					AND ce.calendarID = @calendarID
				WHERE e.siteID = @siteID;
			</cfquery>

			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getCalendarsForSyndication" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="calendarID" type="numeric" required="true">

		<cfset var qryCalendars = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryCalendars">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select calendarID, siteResourceID, applicationInstanceID, calendarName, rightsXML
			FROM (
				SELECT cals.calendarID, cals.siteResourceID, cals.applicationInstanceID, cals.calendarName, cals.siteResourceStatusID,
					dbo.fn_cache_perms_getResourceRightsXML(cals.siteResourceID,<cfqueryparam value="#session.cfcuser.memberdata.memberid#" cfsqltype="CF_SQL_INTEGER">,<cfqueryparam value="#arguments.siteid#" cfsqltype="CF_SQL_INTEGER">) as rightsXML
				FROM dbo.fn_ev_getCalendarsOnSite(<cfqueryparam value="#arguments.siteid#" cfsqltype="CF_SQL_INTEGER">) as cals
				where cals.calendarID <> <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.calendarID#">
			) tmp2
			WHERE rightsXML.exist('(/rights/right[@allowed="1"])[1]') = 1
			ORDER BY calendarName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryCalendars>
	</cffunction>
	
	<cffunction name="getCalendarsForFilters" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
	
		<cfset var qryCalendars = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryCalendars">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @calendarsTbl as table (calendarID int, calendarName varchar(1000), siteResourceStatusID int);
			
			insert into @calendarsTbl (calendarID, calendarName, siteResourceStatusID)
			exec dbo.ev_getCalendars @siteID=<cfqueryparam value="#arguments.siteid#" cfsqltype="CF_SQL_INTEGER">;
			
			select calendarID, calendarName, siteResourceStatusID
			from @calendarsTbl 
			where siteResourceStatusID = 1;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryCalendars>
	</cffunction>
	
	<cffunction name="getCalendarCategories" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any">

		<cfset var qryGetCalendarCategories = "">

		<cfquery name="qryGetCalendarCategories" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select categoryID, category
			from dbo.ev_categories
			where calendarID = <cfqueryparam value="#arguments.event.getValue('cid')#" cfsqltype="cf_sql_integer">
			order by category;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryGetCalendarCategories>
	</cffunction>

	<cffunction name="getEventCategories" access="public" returntype="struct" output="false">
		<cfargument name="calendarID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		
		<cftry>
			<cfstoredproc procedure="ev_getCategories" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.calendarID#">
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="1">
				<cfprocresult name="local.qryCategories">
			</cfstoredproc>
			<cfset local.data.arrCategories = application.objCommon.queryToArrayOfStructures(local.qryCategories)>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
			<cfset local.data.arrCategories = arrayNew(1)>
		</cfcatch>
		</cftry>

		<cfset local.data.success = true>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getEventsForAssetCalendar" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="start" type="date" required="true">
		<cfargument name="end" type="date" required="true">
		<cfargument name="assetsCategoryList" type="string" default="" required="false">

		<cfset var local = structNew()>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryEvents">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			-- get events on site
			IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL
				DROP TABLE ##tmpEventsOnSite;
			CREATE TABLE ##tmpEventsOnSite (calendarID int, eventID int, [status] char(1), isPastEvent bit,
				startTime datetime, endTime datetime, timeZoneID int, timeZoneCode varchar(25), timeZoneAbbr varchar(4),
				displayStartTime datetime, displayEndTime datetime, displayTimeZoneID int, displayTimeZoneCode varchar(25),
				displayTimeZoneAbbr varchar(4), siteResourceID int, isAllDayEvent bit, altRegistrationURL varchar(300),
				eventTitle varchar(200), eventSubTitle varchar(200), locationTitle varchar(200),
				categoryIDList varchar(max));
			EXEC dbo.ev_getEventsOnSite @siteID=#arguments.siteID#, @startDate='#dateformat(arguments.start,"m/d/yyyy")#',
				@endDate='#dateformat(arguments.end,"m/d/yyyy")# 23:59:59.997', @categoryIDList='';

			select tmp.eventID, tmp.calendarID, tmp.displayStartTime as [start], tmp.displayEndTime as [end], tmp.isAllDayEvent as [allDay],
				cp.categoryName + ' / ' + c.categoryName as [title], tmp.eventSubTitle, tmp.categoryIDList, tmp.eventTitle as eventName
			from ##tmpEventsOnSite as tmp
			inner join dbo.cms_categorySiteResources as csr on csr.siteResourceID = tmp.siteResourceID
			inner join dbo.cms_categories as c on c.categoryID = csr.categoryID
				<cfif len(trim(arguments.assetsCategoryList))>
					and c.categoryID in (0#arguments.assetsCategoryList#)
				</cfif>
			left outer join dbo.cms_categories as cP on cP.categoryID = c.parentCategoryID
			where tmp.status = 'A';

			IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL
				DROP TABLE ##tmpEventsOnSite;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.strEventCategories" returntype="struct" columnkey="categoryID">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select distinct evCat.categoryID, evCat.category
				from dbo.fn_IntListToTable(<cfqueryparam cfsqltype="cf_sql_varchar" value="0#ValueList(local.qryEvents.categoryIDList)#">,',') as tmpCat
				INNER JOIN dbo.ev_categories as evCat on evCat.categoryID = tmpCat.listItem;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.arrEvents = arrayNew(1)>

		<cfloop query="local.qryEvents">
			<cfset local.qryCalendar = getCalendar(siteID=arguments.siteID, calendarID=local.qryEvents.calendarID)>

			<cfsavecontent variable="local.eventtime">
				<cfif local.qryEvents.allDay>
					<cfif Month(local.qryEvents.end) is not Month(local.qryEvents.start)
						or Year(local.qryEvents.end) is not Year(local.qryEvents.start)>
						<cfoutput>#DateFormat(local.qryEvents.start, "mmmm d, yyyy")#</cfoutput>
						<cfoutput> - #DateFormat(local.qryEvents.end, "mmmm d, yyyy")#</cfoutput>
					<cfelse>
						<cfoutput>#DateFormat(local.qryEvents.start, "mmmm d")#</cfoutput>
						<cfif DateCompare(local.qryEvents.end,local.qryEvents.start,"d")>
							<cfoutput>-#DateFormat(local.qryEvents.end, "d")#</cfoutput>
						</cfif>
						<cfoutput>#DateFormat(local.qryEvents.start, ", yyyy")#</cfoutput>
					</cfif>
				<cfelse>
					<cfoutput>#DateFormat(local.qryEvents.start, "mmmm d, yyyy")# </cfoutput>
					<cfif DateCompare(local.qryEvents.end,local.qryEvents.start,"d") is 0 and
						DateDiff("n",local.qryEvents.end,local.qryEvents.start) is 0>
						<cfoutput>#TimeFormat(local.qryEvents.start, "h:mm TT")#</cfoutput>
					<cfelseif DateCompare(local.qryEvents.end,local.qryEvents.start,"d")>
						<cfoutput><br/>#TimeFormat(local.qryEvents.start, "h:mm TT")# </cfoutput>
						<cfoutput>- #timeformat(local.qryEvents.end,"h:mm TT")#</cfoutput>
					<cfelse>
						<cfoutput>#TimeFormat(local.qryEvents.start, "h:mm TT")#</cfoutput>
						<cfoutput><br/>- #DateFormat(local.qryEvents.end, "mmmm d, yyyy")#</cfoutput>
						<cfoutput> #TimeFormat(local.qryEvents.end, "h:mm TT")#</cfoutput>
					</cfif>
				</cfif>
			</cfsavecontent>

			<cfsavecontent variable="local.data">
				<cfoutput>
				<div>
					<h4>#local.qryEvents.title#</h4>
					<table>
						<tr valign="top"><td nowrap="true"><b>Event Date:</b></td><td rowspan="5" width="10">&nbsp;</td><td>#local.eventtime#</td></tr>
						<tr valign="top"><td><b>Calendar:</b></td><td>#encodeForHTML(local.qryCalendar.calendarName)#</td></tr>
						<tr valign="top"><td><b>Category:</b></td><td>
							<cfloop list="#local.qryEvents.categoryIDList#" index="local.catID">
								#encodeForHTML(local.strEventCategories[local.catID].category)#<br/>
							</cfloop>
						</td></tr>
						<tr valign="top"><td><b>Event:</b></td><td>#encodeForHTML(local.qryEvents.eventname)# <cfif len(local.qryEvents.eventSubTitle)><div class="text-dim">#encodeForHTML(local.qryEvents.eventSubTitle)#</div></cfif></td></tr>
						<tr valign="top"><td>&nbsp;</td><td><a href="javascript:goToEvent(#local.qryEvents.eventID#);">View event in a new window</a></td></tr>
					</table>
				</div>
				</cfoutput>
			</cfsavecontent>

			<cfset local.tmpStr = structNew() >
			<cfloop list="start,end,allDay,title" index="local.thisCol">
				<!--- this dateformat is here because without it, lucee will SerializeJSON the date and include the timezone offset in CT, which isnt correct. --->
				<cfif listFindNoCase("start,end",local.thisCol)>
					<cfset local.tmpStr[local.thisCol] = dateTimeFormat(local.qryEvents[local.thisCol][local.qryEvents.currentrow],"yyyy-mm-dd'T'HH:nn:ss'Z'") >
				<cfelse>
					<cfset local.tmpStr[local.thisCol] = local.qryEvents[local.thisCol][local.qryEvents.currentrow]>
				</cfif>
			</cfloop>
			<cfset local.tmpStr['description'] = htmleditformat(trim(local.data))>
			
			<cfset arrayAppend(local.arrEvents,local.tmpStr)>
		</cfloop>
		<cfreturn SerializeJSON(local.arrEvents)>
	</cffunction>

	<cffunction name="getEventsForSyndication" access="public" output="false" returntype="string">
		<cfargument name="calID" type="numeric" required="true">
		<cfargument name="catID" type="numeric" required="true">

		<cfset var qryEvents = "">

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryEvents">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#application.objSiteInfo.getSiteInfo(session.mcStruct.siteCode).siteID#">;

				SELECT e.eventID, et.starttime, convert(char(10), et.starttime, 101) + isnull(' - ' + eventContent.contentTitle,'<no title for this event>') as eventTitle
				FROM dbo.ev_events e 
				INNER JOIN dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID 
					AND ce.sourceCalendarID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.calID#">
					AND ce.sourceCalendarID = ce.calendarID
				<cfif arguments.catID gt 0>
					INNER JOIN dbo.ev_eventCategories as ec on ec.eventID = e.eventID
						AND ec.categoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.catID#">
				</cfif>
				INNER JOIN dbo.ev_times as et on et.eventID = e.eventID
					AND et.timeZoneID = <cfqueryparam cfsqltype="cf_sql_integer" value="#application.objSiteInfo.getSiteInfo(session.mcStruct.siteCode).defaultTimeZoneID#">
				INNER JOIN dbo.cms_contentLanguages as eventContent on eventContent.contentID = e.eventContentID and eventContent.languageID = 1
				WHERE e.siteID = @siteID
				AND e.status = 'A'
				AND et.startTime > getDate()
					UNION
				SELECT 0, getDate(), '--- choose an event ---'
				order by starttime;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset qryEvents = QueryNew("eventID,eventTitle","integer,varchar")>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(qryEvents)>
	</cffunction>
	
	<cffunction name="getMasterEvents" access="public" output="false" returntype="Query">
		<cfargument name="Event" type="any" />
		<cfargument name="eventID" type="numeric" required="true">

		<cfset var qryEvents = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryEvents">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			-- get events on site
			IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL
				DROP TABLE ##tmpEventsOnSite;
			CREATE TABLE ##tmpEventsOnSite (calendarID int, eventID int, [status] char(1), isPastEvent bit,
				startTime datetime, endTime datetime, timeZoneID int, timeZoneCode varchar(25), timeZoneAbbr varchar(4),
				displayStartTime datetime, displayEndTime datetime, displayTimeZoneID int, displayTimeZoneCode varchar(25),
				displayTimeZoneAbbr varchar(4), siteResourceID int, isAllDayEvent bit, altRegistrationURL varchar(300),
				eventTitle varchar(200), eventSubTitle varchar(200), locationTitle varchar(200), 
				categoryIDList varchar(max));
			EXEC dbo.ev_getEventsOnSite @siteID=#arguments.event.getValue('mc_siteinfo.siteid')#, @startDate=null, @endDate=null, @categoryIDList='';

			SELECT tmp.eventid, tmp.eventTitle
			FROM ##tmpEventsOnSite as tmp
			inner join dbo.ev_subEvents as subEvent on subEvent.parentEventID = tmp.eventID 	
			where subEvent.eventID = <cfqueryparam value="#arguments.eventID#" cfsqltype="CF_SQL_INTEGER">;

			IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL
				DROP TABLE ##tmpEventsOnSite;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryEvents>
	</cffunction>
	
	<cffunction name="getCategoryCount" access="public" output="false" returntype="numeric">
		<cfargument name="calendarID" type="numeric" required="true">

		<cfset var qryCategories = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryCategories">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT count(categoryID) as categoryCount
			FROM dbo.ev_categories
			WHERE calendarID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.calendarID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryCategories.categoryCount>
	</cffunction>

	<cffunction name="getCategory" access="public" output="false" returntype="query">
		<cfargument name="categoryID" type="numeric" required="true">

		<cfset var qryCategories = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryCategories">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT categoryID, calendarID, category, categoryShort, calColor, visibility
			FROM dbo.ev_categories
			WHERE categoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.categoryID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryCategories>
	</cffunction>

	<cffunction name="getCategoriesForCalendar" access="public" output="false" returntype="any">
		<cfargument name="calID" type="numeric" required="true">
		<cfargument name="includeAll" type="boolean" required="false" default="true">
		<cfargument name="returnJSON" type="boolean" required="false" default="true">
		
		<cfset var qryCategories = "">

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryCategories">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select categoryID, category
				from (
					select 1 as section, cat.categoryID, cat.category
					from dbo.ev_categories as cat
					WHERE cat.calendarID = <cfqueryparam value="#arguments.calID#" cfsqltype="CF_SQL_INTEGER">
					<cfif arguments.includeAll>
							UNION
						SELECT 0 as section, 0, 'All Categories'
					</cfif>
				) as tmp
				order by tmp.section, tmp.category;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset qryCategories = QueryNew("categoryID,category","integer,varchar")>
		</cfcatch>
		</cftry>
		
		<cfif arguments.returnJSON>
			<cfreturn SerializeJSON(qryCategories)>
		<cfelse>
			<cfreturn qryCategories>
		</cfif>
	</cffunction>

	<cffunction name="getCategoriesForSyndication" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="calID" type="numeric" required="true">

		<cfset var qryCategories = "">

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryCategories">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select categoryID, category + ' (' + cast(eventCount as varchar(6)) + ' current event' + case when eventCount = 1 then '' else 's' end + ')' as category, 
					category as categoryName
				from (
					select c.categoryID, c.category, count(*) as eventCount
					from dbo.ev_calendarEvents as ce
					inner join dbo.ev_events as e on e.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
						and e.eventid = ce.sourceEventID
						and e.status = 'A'
					inner join dbo.ev_eventCategories as ec on ec.eventid = e.eventID
					inner join dbo.ev_categories as c on c.categoryID = ec.categoryID
					INNER JOIN dbo.ev_times as et on et.eventID = e.eventID
						and et.timeZoneID = <cfqueryparam cfsqltype="cf_sql_integer" value="#application.objSiteInfo.getSiteInfo(session.mcStruct.siteCode).defaultTimeZoneID#">
						and et.startTime > getDate()
					where ce.calendarID = ce.sourceCalendarID
					and ce.calendarID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.calID#">
					group by c.categoryID, c.category
				) tmp
					UNION
				SELECT 0, '--- choose a category ---', 'All Categories';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset qryCategories = QueryNew("categoryID,category,categoryName","integer,varchar,varchar")>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(qryCategories)>
	</cffunction>

	<cffunction name="getAllSiteCategories" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qryCategories = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryCategories">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT distinct cat.Category, cat.categoryID
			FROM dbo.ev_categories cat
			INNER JOIN dbo.ev_calendars cal ON cal.calendarID = cat.calendarID
				AND cal.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			ORDER BY cat.Category;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryCategories>
	</cffunction>
	
	<cffunction name="getEventAppSettingsXML" access="public" output="false" returntype="xml">
		<cfargument name="siteID" type="numeric" required="true">
		
		<cfset var qryEventAppSettings = "">

		<cfquery name="qryEventAppSettings" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @applicationTypeID int = dbo.fn_getApplicationTypeIDFromName('Events'),
				@siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
				@settingsXML xml;

			select @settingsXML = settingsXML
			from dbo.cms_applicationTypeSettings
			where siteID = @siteID
			and applicationTypeID = @applicationTypeID;

			select isnull(@settingsXML,'<settings />') as settingsXML;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryEventAppSettings.settingsXML>
	</cffunction>
	
	<cffunction name="getTimeZones" access="public" output="false" returntype="query">
		<cfset var qryTimeZones = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryTimeZones">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT timeZoneID, timeZone, timeZoneCode, timeZoneAbbr, zoneOrder
			FROM dbo.timeZones
			ORDER BY zoneOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryTimeZones>
	</cffunction>

	<cffunction name="getRatesByRegistrationID" access="public" output="false" returntype="query">
		<cfargument name="registrationID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.QualifyRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="EventRate", functionName="Qualify")>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ev_getRatesForAdminByRegistrationID">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.registrationID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.QualifyRFID#">
			<cfprocresult name="local.qryRates">
		</cfstoredproc>

		<cfreturn local.qryRates>
	</cffunction>

	<cffunction name="getRateByRateID" access="public" output="false" returntype="query">
		<cfargument name="rateID" type="numeric" required="true">

		<cfset var qryRate = "">

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ev_getRateByRateIDForAdmin">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">
			<cfprocresult name="qryRate">
		</cfstoredproc>

		<cfreturn qryRate>
	</cffunction>

	<cffunction name="getRatesByRateGroupingID" access="public" output="false" returntype="query">
		<cfargument name="registrationID" type="numeric" required="true">
		<cfargument name="rateGroupingID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.allRates = getRatesByRegistrationID(arguments.registrationID)>

		<cfquery name="local.data" dbtype="query">
			SELECT *
			FROM [local].allRates
			WHERE rateGroupingID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rateGroupingID#">
			ORDER BY rateOrder
		</cfquery>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getGroupsbyRegistrationID" access="public" output="false" returntype="query">
		<cfargument name="registrationID" type="numeric" required="yes">

		<cfset var qryRateGroupings = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryRateGroupings">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT rg.rateGroupingID, rg.rateGrouping, rg.registrationID, rg.rateGroupingOrder,
				(select count(rateID) from dbo.ev_rates where rateGroupingID = rg.rateGroupingID) as rateCount
			FROM dbo.ev_rateGrouping rg
			WHERE rg.registrationID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.registrationID#">
			ORDER BY rg.rateGroupingOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryRateGroupings>
	</cffunction>

	<cffunction name="getRSVP" access="public" output="false" returntype="query">
		<cfargument name="rsvpID" type="string" required="true">
		
		<cfset var qryRSVP = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryRSVP">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT rsvpID, registrationID, salutation, firstName, lastName, email, phone, company, dateEntered
			FROM dbo.ev_rsvp
			WHERE rsvpID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rsvpID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryRSVP>
	</cffunction>
	
	<cffunction name="getRateName" access="public" output="false" returntype="string">
		<cfargument name="rateID" type="numeric" required="yes">

		<cfset var qryRate = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryRate">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT case 
				when parentRateID is null then rateName 
				else rateName + ' (bulk discount for ' + convert(varchar, bulkQty) + ')' 
				end as rateName
			FROM dbo.ev_rates
			WHERE rateID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rateID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryRate.rateName>
	</cffunction>

	<cffunction name="getRateDetails" access="public" output="false" returntype="query">
		<cfargument name="rateID" type="numeric" required="yes">

		<cfset var qryRate = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryRate">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT rateMessage, freeRateDisplay
			FROM dbo.ev_rates
			WHERE rateID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rateID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryRate>
	</cffunction>

	<cffunction name="getRateAmount" access="public" output="false" returntype="string">
		<cfargument name="rateID" type="numeric" required="yes">

		<cfset var qryRate = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryRate">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT isnull(rate,'0') as rateAmount
			FROM dbo.ev_rates
			WHERE rateID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rateID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryRate.rateAmount>
	</cffunction>
	
	<cffunction name="getRegistrantRateIDAndFee" access="public" output="false" returntype="struct">
		<cfargument name="registrantID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { rateID='', rateFee=0 }>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRate">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @registrantID int, @rateID int, @rateAmount decimal(18,2);
			set @registrantID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.registrantID#">;

			SELECT @rateID = rateID
			FROM dbo.ev_registrants
			WHERE registrantID = @registrantID;
	
			SET @rateID = isNull(@rateID,0);
	
			select @rateAmount = sum(tsFull.cache_amountAfterAdjustment)
			from dbo.fn_ev_registrantTransactions(@registrantID) as rt
			cross apply dbo.fn_tr_transactionSalesWithDIT(rt.ownedByOrgID,rt.transactionID) as tsFull
			inner join dbo.tr_applications as ta on ta.orgID = rt.ownedByOrgID and ta.transactionID = rt.transactionID and ta.status = 'A'
			where ta.itemType = 'Rate';

			select @rateID as rateID, @rateAmount as rateFee;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryRate.rateID gt 0>
			<cfset local.strReturn.rateID = local.qryRate.rateID>
			<cfset local.strReturn.rateFee = local.qryRate.rateFee>
		</cfif>
			
		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="doDeleteEvent" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="eventID" type="numeric" required="true">

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ev_deleteEvent">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
		</cfstoredproc>
	</cffunction>

	<cffunction name="deleteLinkEvents" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="parentEventID" type="numeric" required="true">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="eventSRID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasEditEventRights(siteID=arguments.mcproxy_siteID, eventSRID=arguments.eventSRID)>
 				<cfthrow message="invalid request">
 			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDeleteLinkEvent">
				set nocount on;

				declare @subEventID int;

				select @subEventID = subeventID 
				from dbo.ev_subEvents 
				where parentEventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.parentEventID#">
				and eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.eventID#">;

				IF @subEventID is not null begin
					delete from dbo.ev_rateMappings
					where subEventID = @subEventID;

					delete from dbo.ev_subEvents 
					where subEventID = @subEventID;
				end
			</cfquery>
			
			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getMasterEventName" access="public" output="false" returntype="struct">
		<cfargument name="eventID" type="string" required="true">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.qryMasterEvent" datasource="#application.dsn.membercentral.dsn#">
			select top 1 parentEventID as pID
			from dbo.ev_subEvents 
			where eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.eventID#">
		</cfquery>
		
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ev_getMetaByEventID">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#local.qryMasterEvent.pID#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#session.mcstruct.languageID#">
			<cfprocresult name="local.qryEvent" resultset="1">
		</cfstoredproc>
		
		<cfset local.data.success = true>
		<cfset local.data.eventname = local.qryEvent.eventContentTitle>
		<cfset local.data.pid = local.qryMasterEvent.pID>
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="getMasterCount" access="public" output="false" returntype="struct">
		<cfargument name="eventID" type="string" required="true">
		
		<cfset	var local = structNew()>
		
		<cfquery name="local.qryEvent" datasource="#application.dsn.membercentral.dsn#">
			select count(*) as count
			from dbo.ev_subEvents
			where eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.eventID#">
		</cfquery>
		
		<cfset local.data.success = true>
		<cfset local.data.pnum = local.qryEvent.count>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getEventFeaturedImagesStruct" access="public" output="false" returntype="struct">
		<cfargument name="orgCode" type="string" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="calendarID" type="numeric" required="true">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="title" type="string" required="true">

		<cfset local.arrConfigs = [ { "ftdExt":"#arguments.siteResourceID#_1", "controllingReferenceID":arguments.calendarID, "controllingReferenceType":"calendarEvent", 
				"referenceID":arguments.eventID, "referenceType":"eventsList", "resourceType":"EventAdmin", 
				"resourceTypeTitle":arguments.title, "onDeleteImageHandler":"", "onSaveImageHandler":"reloadEventFeaturedImageSection", 
				"header":'<h5>Event Featured Image</h5>', "ftdImgClassList":"pl-3" 
			} ]>
		<cfset local.strFeaturedImages = createObject("component","model.admin.common.modules.featuredImages.featuredImages").manageFeaturedImages(orgCode=arguments.orgCode, siteCode=arguments.siteCode, arrConfigs=local.arrConfigs)>

		<cfreturn local.strFeaturedImages>
	</cffunction>

	<cffunction name="getEventFeaturedImagesHTML" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgCode" type="string" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="calendarID" type="numeric" required="true">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="title" type="string" required="true">

		<cfset local.strFeaturedImages = getEventFeaturedImagesStruct(orgCode=arguments.mcproxy_orgCode, siteCode=arguments.mcproxy_siteCode, siteResourceID=arguments.siteResourceID,
			calendarID=arguments.calendarID, eventID=arguments.eventID, title=arguments.title)>
		<cfset local.data["ftdimagehtml"] = local.strFeaturedImages.html>
		<cfset local.data["success"] = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="categoryCheck" access="public" output="false" returntype="boolean">
		<cfargument name="calendarid" type="numeric" required="true">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="category" type="string" required="true">

		<cfset var qryCategoryCheck = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryCategoryCheck">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT top 1 categoryID
			FROM dbo.ev_categories
			WHERE calendarID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.calendarID#">
			AND category = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.category#">
			AND categoryID <> <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.categoryID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryCategoryCheck.recordcount>
	</cffunction>

	<cffunction name="insertCategory" access="public" output="false" returntype="numeric">
		<cfargument name="calendarid" type="numeric" required="true">
		<cfargument name="category" type="string" required="true">
		<cfargument name="categoryShort" type="string" required="true">
		<cfargument name="calcolor" type="string" required="true">
		<cfargument name="visibility" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ev_createCategory">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.calendarid#">
			<cfprocparam type="In" cfsqltype="cf_sql_varchar" value="#arguments.category#">
			<cfprocparam type="In" cfsqltype="cf_sql_varchar" value="#arguments.categoryShort#">
			<cfprocparam type="In" cfsqltype="cf_sql_varchar" value="###arguments.calcolor#">
			<cfprocparam type="In" cfsqltype="cf_sql_varchar" value="#arguments.visibility#">
			<cfprocparam type="Out" cfsqltype="cf_sql_integer" variable="local.categoryID">
		</cfstoredproc>

		<cfreturn local.categoryID>
	</cffunction>

	<cffunction name="updateCategory" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="category" type="string" required="true">
		<cfargument name="categoryShort" type="string" required="true">
		<cfargument name="calcolor" type="string" required="true">
		<cfargument name="visibility" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ev_updateCategory">
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.siteID#">
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.categoryID#">
				<cfprocparam type="In" cfsqltype="cf_sql_varchar" value="#arguments.category#">
				<cfprocparam type="In" cfsqltype="cf_sql_varchar" value="#arguments.categoryShort#">
				<cfprocparam type="In" cfsqltype="cf_sql_varchar" value="###arguments.calcolor#">
				<cfprocparam type="In" cfsqltype="cf_sql_varchar" value="#arguments.visibility#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteCategory" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="reassignCategoryID" type="numeric" required="false" default="0">
		
		<cfset var local = structNew()>
		
		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDeleteCat">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					
					DECLARE @siteID int, @orgID int, @participantID int, @categoryID int, @thisCalendarID int, @newCategoryID int, @SWCalendarID int, 
						@SWCategoryCount int, @recordedByMemberID int;
					DECLARE @tmpSWFeaturedEvents TABLE (featuredID int, eventID int);
					DECLARE @tmpSWEventCategories TABLE (categoryID int);

					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(arguments.mcproxy_siteCode).siteID#">;
					SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(arguments.mcproxy_siteCode).orgID#">;
					SET @categoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.categoryID#">;
					SET @newCategoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.reassignCategoryID#">;
					SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">;

					SELECT @participantID = seminarWeb.dbo.fn_getParticipantIDFromOrgcode(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.mcproxy_siteCode#">);

					SELECT @thisCalendarID = calendarID FROM dbo.ev_categories WHERE categoryID = @categoryID;

					IF NOT EXISTS(SELECT 1 FROM dbo.ev_categories WHERE calendarID = @thisCalendarID AND categoryID <> @categoryID)
						RAISERROR('Unable to delete category',16,1);

					IF @participantID IS NOT NULL BEGIN
						SELECT @SWCalendarID = calendarID, @SWCategoryCount = COUNT(categoryID)
						FROM seminarWeb.dbo.tblParticipantEvents
						WHERE participantID = @participantID
						GROUP BY calendarID;

						IF @SWCategoryCount > 0
							INSERT INTO @tmpSWEventCategories (categoryID)
							select ec.categoryID
							from dbo.ev_categories as ec
							inner join seminarWeb.dbo.tblParticipantEvents as pe on pe.calendarID = @SWCalendarID 
								and pe.calendarID = ec.calendarID
								and pe.categoryID = ec.categoryID
							where pe.participantID = @participantID;
						ELSE
							INSERT INTO @tmpSWEventCategories (categoryID)
							select categoryID
							from dbo.ev_categories
							where calendarID = @SWCalendarID;

						-- featured events
						INSERT INTO @tmpSWFeaturedEvents (featuredID, eventID)
						select fp.featuredID, fp.eventID
						from seminarWeb.dbo.tblFeaturedPrograms as fp
						inner join dbo.ev_eventCategories as evc on evc.eventID = fp.eventID
						inner join @tmpSWEventCategories as tmp on tmp.categoryID = evc.categoryID
						where fp.participantID = @participantID
						and tmp.categoryID = @categoryID;
					END

					BEGIN TRAN;
						<cfif arguments.reassignCategoryID gt 0>
							UPDATE dbo.ev_eventCategories
							SET categoryID = @newCategoryID
							WHERE categoryID = @categoryID;

							-- delete the duplicate entry if this reassigned category already linked to the event
							DELETE ec
							FROM dbo.ev_eventCategories AS ec
							LEFT OUTER JOIN (
								SELECT MIN(eventCategoryID) AS eventCategoryID, eventID, categoryID
								FROM dbo.ev_eventCategories 
								GROUP BY eventID, categoryID
							) AS tmp ON tmp.eventCategoryID = ec.eventCategoryID
							WHERE ec.categoryID = @newCategoryID
							AND tmp.eventCategoryID IS NULL;

							UPDATE dbo.cache_calendarEvents
							SET categoryID = @newCategoryID
							WHERE categoryID = @categoryID;

							DELETE ce
							FROM dbo.cache_calendarEvents AS ce
							LEFT OUTER JOIN (
								SELECT MIN(calendarEventID) AS calendarEventID, calendarID, eventID, reason, sourceCalendarID, categoryID
								FROM dbo.cache_calendarEvents 
								GROUP BY calendarID, eventID, reason, sourceCalendarID, categoryID
							) AS tmp ON tmp.calendarEventID = ce.calendarEventID
							WHERE ce.categoryID = @newCategoryID
							AND tmp.calendarEventID IS NULL;

							UPDATE dbo.ev_calendarEvents
							SET sourceCategoryID = @newCategoryID
							WHERE sourceCategoryID = @categoryID;

							UPDATE dbo.ev_calendarEvents
							SET ovCategoryID = @newCategoryID
							WHERE ovCategoryID = @categoryID;

							EXEC dbo.ev_refreshCalendarEventsCategoryIDList @siteID=@siteID;
						</cfif>

						DELETE FROM dbo.ev_categories WHERE categoryID = @categoryID;

						IF @participantID IS NOT NULL BEGIN
							DELETE FROM seminarWeb.dbo.tblParticipantEvents 
							WHERE participantID = @participantID 
							AND categoryID = @categoryID;

							DELETE FROM @tmpSWEventCategories 
							WHERE categoryID = @categoryID;

							-- delete featured mc events on sw catalog
							IF NOT EXISTS (SELECT 1 FROM @tmpSWEventCategories WHERE categoryID = @newCategoryID) BEGIN
								DELETE fp
								FROM seminarWeb.dbo.tblFeaturedPrograms as fp
								INNER JOIN @tmpSWFeaturedEvents as tmp on tmp.featuredID = fp.featuredID;

								INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
								select'{ "c":"auditLog", "d": {
									"AUDITCODE":"SW",
									"ORGID":' + cast(@orgID as varchar(10)) + ',
									"SITEID":' + cast(@siteID as varchar(10)) + ',
									"ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
									"ACTIONDATE":"' + convert(varchar(20),getdate(),120) + '",
									"MESSAGE":"' + replace(dbo.fn_cleanInvalidXMLChars('EV-' + cast(tmp.eventID as varchar(20)) + ' [' + eventcontent.contentTitle + '] has been removed from the featured programs.'),'"','\"') + '" } }'
								from @tmpSWFeaturedEvents as tmp
								inner join dbo.ev_events as ev on ev.eventID = tmp.eventID
								cross apply dbo.fn_getContent(ev.eventContentID,1) as eventcontent;
							END
						END
					COMMIT TRAN;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
		
	<cffunction name="insertRegistration" access="public" output="false" returntype="query">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="registrationTypeID" type="numeric" required="true">
		<cfargument name="StartDate" type="date" required="true">
		<cfargument name="EndDate" type="date" required="true">
		<cfargument name="replyToEmail" type="string" required="true">
		<cfargument name="notifyEmail" type="string" required="true">

		<cfset var local = structNew()>

		<cfstoredproc procedure="ev_createRegistration" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.registrationTypeID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.startDate#">
			<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.EndDate#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="" null="yes">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.replyToEmail#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.notifyEmail#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.registrationID">
		</cfstoredproc>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryReg">
			SELECT registrationID, expirationContentID
			FROM dbo.ev_registration
			WHERE registrationID = <cfqueryparam value="#val(local.registrationID)#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfreturn local.qryReg>
	</cffunction>
		
	<cffunction name="deleteRegistration" access="public" output="false" returntype="struct">
		<cfargument name="registrationID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDeleteReg">
			UPDATE dbo.ev_registration
			SET status = 'D'
			WHERE registrationID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.registrationID#">
		</cfquery>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="udpateAltURL" access="public" output="false" returntype="void">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="altRegistrationURL" type="string" required="true">

		<cfset var qryUpdateAltURL = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryUpdateAltURL">
			UPDATE dbo.ev_events
			SET altREgistrationURL = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.altRegistrationURL#">
			WHERE eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.eventID#">
		</cfquery>
	</cffunction>

	<cffunction name="deleteAltURL" access="public" output="false" returntype="struct">
		<cfargument name="eventID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.deleteAltURL">
			UPDATE dbo.ev_events
			SET altRegistrationURL = NULL
			WHERE eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.eventID#">
		</cfquery>

		<cfset local.data.success = true>
		<cfreturn local.data>
	</cffunction>
				
	<cffunction name="insertRate" access="public" output="false" returntype="struct">
		<cfargument name="registrationID" type="numeric" required="yes">
		<cfargument name="rateGroupingID" type="numeric" required="yes">
		<cfargument name="GLAccountID" type="numeric" required="yes">
		<cfargument name="rateName" type="string" required="yes">
		<cfargument name="reportCode" type="string" required="yes">
		<cfargument name="rate" type="string" required="yes">
		<cfargument name="freeRateDisplay" type="string" required="yes">
		<cfargument name="rateMessage" type="string" required="yes">
		<cfargument name="rateMessageDisplay" type="boolean" required="yes">
		<cfargument name="isHidden" type="boolean" required="yes">
		<cfargument name="parentRateID" type="numeric" required="yes">
		<cfargument name="qty" type="numeric" required="yes">
		<cfargument name="scheduleID" type="string" required="yes">
		<cfargument name="arrPackages" type="array" required="yes">
		<cfargument name="excludeTicketPackageIDList" type="string" required="yes">
		
		<cfset var local = structNew()>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryInsertRate">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				declare @registrationID int, @rateGroupingID int, @GLAccountID int, @rateName varchar(100), @reportCode varchar(15), @rate decimal(18,2), 
					@freeRateDisplay varchar(5), @rateMessage varchar(400), @rateMessageDisplay bit, @isHidden bit, @parentRateID int, @qty int, 
					@rateID int, @siteResourceID int;
				set @registrationID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.registrationID#">;
				set @rateGroupingID = <cfif arguments.rateGroupingID NEQ 0><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rateGroupingID#"><cfelse>null</cfif>;
				set @GLAccountID = <cfif arguments.GLAccountID gt 0><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.GLAccountID#"><cfelse>null</cfif>;
				set @rateName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#trim(arguments.rateName)#">;
				set @reportCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.reportCode#">;
				set @rate = <cfqueryparam cfsqltype="cf_sql_double" value="#NumberFormat(replace(arguments.rate,',','','ALL'),"0.00")#">;
				<cfif replace(arguments.rate,',','','ALL') eq 0>
					set @freeRateDisplay = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.freeRateDisplay#">;
				<cfelse>
					set @freeRateDisplay = '$0.00';
				</cfif>
				set @rateMessage = <cfqueryparam cfsqltype="cf_sql_varchar" value="#trim(arguments.rateMessage)#">;
				set @rateMessageDisplay = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.rateMessageDisplay#">;
				set @isHidden = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.isHidden#">;
				set @parentRateID = <cfif arguments.parentRateID gt 0><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.parentRateID#"><cfelse>null</cfif>;
				set @qty = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.qty#">;

				BEGIN TRAN;
					EXEC dbo.ev_createRate @registrationID=@registrationID, @rateGroupingID=@rateGroupingID, @GLAccountID=@GLAccountID, 
						@rateName=@rateName, @reportCode=@reportCode, @rate=@rate, @freeRateDisplay=@freeRateDisplay, @rateMessage=@rateMessage, 
						@rateMessageDisplay=@rateMessageDisplay, @isHidden=@isHidden, @parentRateID=@parentRateID, @qty=@qty, 
						@rateID=@rateID OUTPUT, @siteResourceID=@siteResourceID OUTPUT;

					EXEC dbo.ev_reorderRates @rateID=@rateID;

					<cfif listLen(arguments.scheduleID)>
						<cfloop list="#arguments.scheduleID#" index="local.thisScheduleID">
							insert into dbo.ev_ratesAvailable (rateID, scheduleID)
							values (@rateID, <cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisScheduleID#">);
						</cfloop>
					</cfif>

					<cfif arrayLen(arguments.arrPackages)>
						<cfloop array="#arguments.arrPackages#" index="local.thisType">
							insert into dbo.ev_rateTicketPackages (rateID, ticketPackageID, quantity)
							values (
								@rateID,
								<cfqueryparam value="#local.thisType.ticketPackageID#" cfsqltype="cf_sql_integer">,
								<cfqueryparam value="#replace(local.thisType.quantity,',','','ALL')#" cfsqltype="cf_sql_integer">
							);
						</cfloop>
					</cfif>

					<cfif len(arguments.excludeTicketPackageIDList)>
						INSERT INTO dbo.ev_rateExcludeTicketPackages (rateID, ticketPackageID)
						select @rateID, listitem
						from dbo.fn_intListToTableInline(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.excludeTicketPackageIDList#">,',');
					</cfif>
				COMMIT TRAN;	

				SELECT @rateID as rateID, @siteResourceID as siteResourceID

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.data = { rateID=local.qryInsertRate.rateID, siteResourceID=local.qryInsertRate.siteResourceID }>

		<cfreturn local.data>
	</cffunction>
	<cffunction name="updateRate" access="public" output="false" returntype="void">
		<cfargument name="rateID" type="numeric" required="yes">
		<cfargument name="rateGroupingID" type="numeric" required="yes">
		<cfargument name="GLAccountID" type="numeric" required="yes">
		<cfargument name="rateName" type="string" required="yes">
		<cfargument name="reportCode" type="string" required="yes">
		<cfargument name="rate" type="string" required="yes">
		<cfargument name="freeRateDisplay" type="string" required="yes">
		<cfargument name="rateMessage" type="string" required="yes">
		<cfargument name="rateMessageDisplay" type="boolean" required="yes">
		<cfargument name="isHidden" type="boolean" required="yes">
		<cfargument name="qty" type="numeric" required="yes">
		<cfargument name="scheduleID" type="string" required="yes">
		<cfargument name="rateUID" type="string" required="yes">
		<cfargument name="arrPackages" type="array" required="yes">
		<cfargument name="excludeTicketPackageIDList" type="string" required="yes">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateRate">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @rateID int, @oldRateGroupingID int, @newRateGroupingID int;
				SET @rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">;
				SET @newRateGroupingID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateGroupingID#">,0);

				SELECT @oldRateGroupingID = rateGroupingID
				FROM dbo.ev_rates
				WHERE rateID = @rateID;

				<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and len(arguments.rateUID)>
					DECLARE @newRateUID uniqueidentifier;
					SET @newRateUID = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#trim(arguments.rateUID)#">;

					IF EXISTS (select rateID from dbo.ev_rates where uid = @newRateUID and rateID <> @rateID)
						RAISERROR('RateUID already exists',16,1);
				</cfif>

				BEGIN TRAN;
					UPDATE dbo.ev_rates
					SET rateGroupingID = @newRateGroupingID,
						GLAccountID = nullif(<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.GLAccountID#">,0),
						rateName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#trim(arguments.rateName)#">,
						rate = <cfqueryparam cfsqltype="cf_sql_double" value="#NumberFormat(replace(arguments.rate,',','','ALL'),"0.00")#">,
						reportCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.reportCode#">,
						isHidden = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.isHidden#">,
						bulkQty = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.qty#">,
						rateMessage = <cfqueryparam cfsqltype="cf_sql_varchar" value="#left(trim(arguments.rateMessage),400)#">,
						rateMessageDisplay = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.rateMessageDisplay#">
						<cfif replace(arguments.rate,',','','ALL') eq 0>
							, freeRateDisplay = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.freeRateDisplay#">
						</cfif>
						<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and len(arguments.rateUID)>
							, [uid] = @newRateUID
						</cfif>
					WHERE rateID = @rateID;

					delete from dbo.ev_ratesAvailable where rateID = @rateID;

					<cfif listLen(arguments.scheduleID)>
						<cfloop list="#arguments.scheduleID#" index="local.thisScheduleID">
							insert into dbo.ev_ratesAvailable (rateID, scheduleID)
							values (@rateID, <cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisScheduleID#">);
						</cfloop>
					</cfif>

					delete from dbo.ev_rateTicketPackages where rateID = @rateID;

					<cfif arrayLen(arguments.arrPackages)>
						<cfloop array="#arguments.arrPackages#" index="local.thisType">
							insert into dbo.ev_rateTicketPackages (rateID, ticketPackageID, quantity)
							values (
								@rateID,
								<cfqueryparam value="#local.thisType.ticketPackageID#" cfsqltype="cf_sql_integer">,
								<cfqueryparam value="#replace(local.thisType.quantity,',','','ALL')#" cfsqltype="cf_sql_integer">
							);
						</cfloop>
					</cfif>

					DELETE FROM dbo.ev_rateExcludeTicketPackages where rateID = @rateID;

					<cfif len(arguments.excludeTicketPackageIDList)>
						INSERT INTO dbo.ev_rateExcludeTicketPackages (rateID, ticketPackageID)
						select @rateID, listitem
						from dbo.fn_intListToTableInline(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.excludeTicketPackageIDList#">,',');
					</cfif>

					<!--- if rateGrouping changed, reorder this rate to bottom --->
					IF ISNULL(@oldRateGroupingID,0) <> ISNULL(@newRateGroupingID,0) BEGIN
						UPDATE dbo.ev_rates 
						SET rateOrder = 999
						WHERE rateID = @rateID;

						EXEC dbo.ev_reorderRates @rateID=@rateID;
					END
				COMMIT TRAN;	

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="deleteRate" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="rateID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasManageRatesRights(siteID=arguments.mcproxy_siteID, eventID=arguments.eventID, rateID=arguments.rateID)>
 				<cfthrow message="invalid request">
 			</cfif>

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ev_deleteRate">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
		
	<!--- RATE GROUPING --->
	<cffunction name="deleteRateGrouping" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="registrationID" type="numeric" required="true">
		<cfargument name="rateGroupingID" type="numeric" required="true">
		<cfargument name="eventSRID" type="numeric" required="true">

		<cfset var local = structNew()>
		
		<cftry>
			<cfif not hasEditEventRights(siteID=arguments.mcproxy_siteID, eventSRID=arguments.eventSRID)>
 				<cfthrow message="invalid request">
 			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.deleteRateGrouping">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @registrationID int, @rateGroupingID int, @rateIDForReorder int;
					SET @registrationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.registrationID#">;
					SET @rateGroupingID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateGroupingID#">;

					SELECT TOP 1 @rateIDForReorder = rateID 
					FROM dbo.ev_rates 
					WHERE registrationID = @registrationID
					AND rateGroupingID = @rateGroupingID;
					
					BEGIN TRAN
						UPDATE dbo.ev_rates
						SET rateGroupingID = NULL
						WHERE rateGroupingID = @rateGroupingID;

						DELETE FROM dbo.ev_rateGrouping
						WHERE rateGroupingID = @rateGroupingID;

						EXEC dbo.ev_reorderRateGroupings @registrationID=@registrationID;

						IF @rateIDForReorder IS NOT NULL
							EXEC dbo.ev_reorderRates @rateID=@rateIDForReorder;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doRateGroupingMove" access="public" output="false" returntype="struct">
		<cfargument name="rateGroupingID" type="numeric" required="true">
		<cfargument name="registrationID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ev_moveRateGrouping">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.rateGroupingID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.registrationID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getTicket" access="public" output="false" returntype="query">
		<cfargument name="ticketID" type="numeric">

		<cfset var qryTicket = "">
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryTicket">
			SELECT t.ticketID, t.registrationID, t.ticketName, t.ticketDescription, t.assignToMembers, t.inventory, t.autoManageInventory, t.glAccountID,
				(select count(rpid.seatID) 
					from dbo.ev_registrantPackageInstanceSeats as rpid
					inner join dbo.ev_registrantPackageInstances as rpi on rpi.instanceID = rpid.instanceID and rpi.status = 'A'
					inner join dbo.ev_ticketPackages as etp on etp.ticketPackageID = rpi.ticketPackageID
					inner join dbo.ev_tickets as et on et.ticketID = etp.ticketID
					where et.ticketID = t.ticketID
					and rpid.status = 'A') as ticketInventoryCount
			FROM dbo.ev_tickets as t
			WHERE t.ticketID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.ticketID#">
		</cfquery>
		
		<cfreturn qryTicket>
	</cffunction>

	<cffunction name="deleteTicket" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="ticketID" type="numeric" required="true">
		<cfargument name="eventSRID" type="numeric" required="true">

		<cfset var local = structNew()>
		
		<cftry>
			<cfif not hasEditEventRights(siteID=arguments.mcproxy_siteID, eventSRID=arguments.eventSRID)>
 				<cfthrow message="invalid request">
 			</cfif>

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ev_deleteTicket">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.ticketID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfif findNoCase("Cannot delete ticket tied to registrants", cfcatch.detail)>
				<cfset local.data.errmsg = "Cannot delete ticket tied to registrants.">
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doTicketMove" access="public" output="false" returntype="struct">
		<cfargument name="ticketID" type="numeric" required="true">
		<cfargument name="registrationID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">
		
		<cfset var local = structNew()>
		
		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ev_moveTicket">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.ticketID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.registrationID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="insertTicket" access="public" output="false" returntype="numeric">
		<cfargument name="registrationID" type="numeric" required="yes">
		<cfargument name="ticketName" type="string" required="yes">
		<cfargument name="ticketDescription" type="string" required="yes">
		<cfargument name="assignToMembers" type="boolean" required="yes">
		<cfargument name="inventory" type="numeric" required="yes">
		<cfargument name="autoManageInventory" type="boolean" required="yes">
		<cfargument name="GLAccountID" type="numeric" required="yes">

		<cfset var qryInsertTicket = "">
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryInsertTicket">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				declare @registrationID int, @ticketID int;
				set @registrationID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.registrationID#">;

				BEGIN TRAN;
					INSERT INTO dbo.ev_tickets (registrationID, ticketName, ticketDescription, assignToMembers, inventory, autoManageInventory, GLAccountID, sortOrder)
					VALUES (
						@registrationID,
						<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.ticketName#">,
						<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.ticketDescription#">,
						<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.assignToMembers#">,
						<cfif arguments.inventory gt 0><cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.inventory#"><cfelse>null</cfif>,
						<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.autoManageInventory#">,
						<cfif arguments.GLAccountID gt 0><cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.GLAccountID#"><cfelse>null</cfif>,
						999
					);
					SET @ticketID = SCOPE_IDENTITY();

					EXEC dbo.ev_reorderTickets @registrationID=@registrationID;
				COMMIT TRAN;

				select @ticketID as ticketID;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn qryInsertTicket.ticketID>
	</cffunction>

	<cffunction name="updateTicket" access="public" output="false" returntype="void">
		<cfargument name="ticketID" type="numeric" required="yes">
		<cfargument name="ticketName" type="string" required="yes">
		<cfargument name="ticketDescription" type="string" required="yes">
		<cfargument name="assignToMembers" type="boolean" required="yes">
		<cfargument name="inventory" type="numeric" required="yes">
		<cfargument name="autoManageInventory" type="boolean" required="yes">
		<cfargument name="GLAccountID" type="numeric" required="yes">

		<cfset var qryUpdateTicket = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryUpdateTicket">
			UPDATE dbo.ev_tickets
			SET ticketName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.ticketName#">,
				ticketDescription = <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.ticketDescription#">,
				assignToMembers = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.assignToMembers#">,
				inventory = <cfif arguments.inventory gt 0><cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.inventory#"><cfelse>null</cfif>,
				autoManageInventory = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.autoManageInventory#">,
				GLAccountID = <cfif arguments.GLAccountID gt 0><cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.GLAccountID#"><cfelse>null</cfif>
			WHERE ticketID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.ticketID#">
		</cfquery>
	</cffunction>

	<cffunction name="getTicketPackage" access="public" output="false" returntype="query">
		<cfargument name="ticketPackageID" type="numeric">

		<cfset var qryTicketPackage = "">
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryTicketPackage">
			SELECT tp.ticketPackageID, tp.ticketID, tp.ticketPackageName, tp.ticketPackageDescription, tp.ticketPackageConfirmationEmailInfo, tp.ticketCount, tp.adminOnly, tp.inventory, tp.maxPerRegistrant,
				(select count(rpi.instanceID) 
					from dbo.ev_registrantPackageInstances as rpi
					where rpi.ticketPackageID = tp.ticketPackageID
					and rpi.status = 'A') as ticketPackageInventoryCount, t.ticketName
			FROM dbo.ev_ticketPackages as tp
			inner join dbo.ev_tickets as t on t.ticketID = tp.ticketID
			WHERE tp.ticketPackageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.ticketPackageID#">
		</cfquery>
		
		<cfreturn qryTicketPackage>
	</cffunction>

	<cffunction name="deleteTicketPackage" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="ticketPackageID" type="numeric" required="true">
		<cfargument name="eventSRID" type="numeric" required="true">

		<cfset var local = structNew()>
		
		<cftry>
			<cfif not hasEditEventRights(siteID=arguments.mcproxy_siteID, eventSRID=arguments.eventSRID)>
 				<cfthrow message="invalid request">
 			</cfif>

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ev_deleteTicketPackage">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.ticketPackageID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfif findNoCase("Cannot delete ticket package tied to registrants", cfcatch.detail)>
				<cfset local.data.errmsg = "Cannot delete ticket package tied to registrants.">
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doTicketPackageMove" access="public" output="false" returntype="struct">
		<cfargument name="ticketPackageID" type="numeric" required="true">
		<cfargument name="ticketID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">
		
		<cfset var local = structNew()>
		
		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ev_moveTicketPackage">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.ticketPackageID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.ticketID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
			</cfstoredproc>
			
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="insertTicketPackage" access="public" output="false" returntype="numeric">
		<cfargument name="ticketID" type="numeric" required="yes">
		<cfargument name="ticketPackageName" type="string" required="yes">
		<cfargument name="ticketPackageDescription" type="string" required="yes">
		<cfargument name="ticketPackageConfirmationEmailInfo" type="string" required="no">
		<cfargument name="ticketCount" type="numeric" required="yes">
		<cfargument name="adminOnly" type="boolean" required="yes">
		<cfargument name="inventory" type="numeric" required="yes">
		<cfargument name="maxPerRegistrant" type="numeric" required="yes">
		<cfargument name="arrPriceTypes" type="array" required="yes">

		<cfset var qryInsertTicketPackage = "">
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryInsertTicketPackage">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				declare @ticketID int, @ticketPackageID int;
				set @ticketID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.ticketID#">;

				BEGIN TRAN;
					INSERT INTO dbo.ev_ticketPackages (ticketID, ticketPackageName, ticketPackageDescription, ticketPackageConfirmationEmailInfo, ticketCount, adminOnly, inventory, maxPerRegistrant, sortOrder)
					VALUES (
						@ticketID,
						<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.ticketPackageName#">,
						<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.ticketPackageDescription#">,
						<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.ticketPackageConfirmationEmailInfo#">,
						<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ticketCount#">,
						<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.adminOnly#">,
						<cfif arguments.inventory gt 0><cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.inventory#"><cfelse>null</cfif>,
						<cfif arguments.maxPerRegistrant gt 0><cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.maxPerRegistrant#"><cfelse>null</cfif>,
						999
					);
					SET @ticketPackageID = SCOPE_IDENTITY();

					EXEC dbo.ev_reorderTicketPackages @ticketID=@ticketID;

					<cfif arrayLen(arguments.arrPriceTypes)>
						<cfloop array="#arguments.arrPriceTypes#" index="local.thisType">
							insert into dbo.ev_ticketPackageAvailable (ticketPackageID, scheduleID, amount)
							values (
								@ticketPackageID,
								<cfqueryparam value="#local.thisType.scheduleID#" cfsqltype="cf_sql_integer">,
								<cfqueryparam value="#NumberFormat(replace(local.thisType.amount,',','','ALL'),"0.00")#" cfsqltype="CF_SQL_DOUBLE">
								);
						</cfloop>
					</cfif>
				COMMIT TRAN;			

				SELECT @ticketPackageID as ticketPackageID;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn qryInsertTicketPackage.ticketPackageID>
	</cffunction>

	<cffunction name="updateTicketPackage" access="public" output="false" returntype="void">
		<cfargument name="ticketPackageID" type="numeric" required="yes">
		<cfargument name="ticketPackageName" type="string" required="yes">
		<cfargument name="ticketPackageDescription" type="string" required="yes">
		<cfargument name="ticketPackageConfirmationEmailInfo" type="string" required="no">
		<cfargument name="ticketCount" type="numeric" required="yes">
		<cfargument name="adminOnly" type="boolean" required="yes">
		<cfargument name="inventory" type="numeric" required="yes">
		<cfargument name="maxPerRegistrant" type="numeric" required="yes">
		<cfargument name="arrPriceTypes" type="array" required="yes">

		<cfset var qryUpdateTicketPackage = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryUpdateTicketPackage">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				declare @ticketPackageID int;
				set @ticketPackageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.ticketPackageID#">;

				declare @tblSchedule TABLE (scheduleID int, amount decimal(18,2));
				<cfloop array="#arguments.arrPriceTypes#" index="local.thisType">
					insert into @tblSchedule (scheduleID, amount)
					VALUES (
						<cfqueryparam value="#local.thisType.scheduleID#" cfsqltype="cf_sql_integer">, 
						<cfqueryparam value="#NumberFormat(replace(local.thisType.amount,',','','ALL'),"0.00")#" cfsqltype="CF_SQL_DOUBLE">
					);
				</cfloop>

				BEGIN TRAN;
					UPDATE dbo.ev_ticketPackages
					SET ticketPackageName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.ticketPackageName#">,
						ticketPackageDescription = <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.ticketPackageDescription#">,
						ticketPackageConfirmationEmailInfo = <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.ticketPackageConfirmationEmailInfo#">,
						ticketCount = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ticketCount#">,
						adminOnly = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.adminOnly#">,
						inventory = <cfif arguments.inventory gt 0><cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.inventory#"><cfelse>null</cfif>,
						maxPerRegistrant = <cfif arguments.maxPerRegistrant gt 0><cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.maxPerRegistrant#"><cfelse>null</cfif>
					WHERE ticketPackageID = @ticketPackageID;

					<cfif arrayLen(arguments.arrPriceTypes) is 0>
						update dbo.ev_ticketPackageAvailable
						set isActive = 0
						where ticketPackageID = @ticketPackageID;
					<cfelse>
						-- inactivate any schedules that are no longer checked
						-- activate any schedules that are checked and already exist in table
						-- add any schedules that are checked and dont already exist in table
						MERGE dbo.ev_ticketPackageAvailable AS target  
						USING (SELECT @ticketPackageID, scheduleID, amount from @tblSchedule) AS source (ticketPackageID, scheduleID, amount)
						ON (target.ticketPackageID = source.ticketPackageID and target.scheduleID = source.scheduleID)
	    				WHEN MATCHED THEN
	    					UPDATE SET isActive = 1, amount = source.amount
						WHEN NOT MATCHED BY TARGET THEN
							INSERT (ticketPackageID, scheduleID, amount)
							VALUES (@ticketPackageID, source.scheduleID, source.amount)
						WHEN NOT MATCHED BY SOURCE AND target.ticketPackageID = @ticketPackageID THEN
							UPDATE SET isActive = 0;
					</cfif>
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>
	<cffunction name="getTicketPackagesForRate" access="public" output="false" returntype="query">
		<cfargument name="registrationID" type="numeric" required="true">
		<cfargument name="rateID" type="numeric" required="true">

		<cfset var qryTicketPackages = "">
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryTicketPackages">
			SET NOCOUNT ON;

			DECLARE @rateID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">,
				@registrationID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.registrationID#">;

			SELECT t.ticketID, tp.ticketPackageID, t.ticketName, tp.ticketPackageName, t.ticketName + ' \ ' + tp.ticketPackageName as ticketPackageNameExpanded, 
				rtp.quantity, CASE WHEN rexctp.ticketPackageID IS NOT NULL THEN 1 ELSE 0 END as isExcluded
			FROM dbo.ev_ticketPackages as tp
			INNER JOIN dbo.ev_tickets as t on t.ticketID = tp.ticketID
			LEFT OUTER JOIN dbo.ev_rateTicketPackages as rtp on rtp.ticketPackageID = tp.ticketPackageID and rtp.rateID = @rateID
			LEFT OUTER JOIN dbo.ev_rateExcludeTicketPackages as rexctp on rexctp.ticketPackageID = tp.ticketPackageID and rexctp.rateID = @rateID
			WHERE t.registrationID = @registrationID
			ORDER BY t.sortOrder, tp.sortOrder;
		</cfquery>
		
		<cfreturn qryTicketPackages>
	</cffunction>

	<cffunction name="deleteMemberGroup" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="rateid" type="numeric" required="true">
		<cfargument name="groupid" type="numeric" required="true">
		<cfargument name="eventSRID" type="numeric" required="true">
		<cfargument name="include" type="numeric" required="false" default="1">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasEditEventRights(siteID=arguments.mcproxy_siteID, eventSRID=arguments.eventSRID)>
 				<cfthrow message="invalid request">
 			</cfif>

			<cfquery name="local.qrySiteResourceID" datasource="#application.dsn.membercentral.dsn#">
				select siteResourceID
				from dbo.ev_rates
				where rateID = <cfqueryparam value="#arguments.rateid#" cfsqltype="CF_SQL_INTEGER">
				or parentRateID = <cfqueryparam value="#arguments.rateid#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>

			<cfquery name="local.qrySiteResourceRights" datasource="#application.dsn.membercentral.dsn#">
				select srr.resourceID, srr.resourceRightsID, sr.siteID
				from dbo.cms_siteResourceRights as srr
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = srr.resourceID and sr.siteID = srr.siteID
				where srr.resourceID in (<cfqueryparam cfsqltype="cf_sql_integer" value="#valueList(local.qrySiteResourceID.siteResourceID)#" list="yes">)
				AND srr.groupID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.groupID#">
				AND srr.include = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.include#">
			</cfquery>

			<cfloop query="local.qrySiteResourceRights">
				<cfstoredproc procedure="cms_deleteSiteResourceRight" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam cfsqltype="cf_sql_integer" value="#local.qrySiteResourceRights.siteid#">
					<cfprocparam cfsqltype="cf_sql_integer" value="#local.qrySiteResourceRights.resourceID#">
					<cfprocparam cfsqltype="cf_sql_integer" value="#local.qrySiteResourceRights.resourceRightsID#">
				</cfstoredproc>
			</cfloop>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<!--- REGISTRANTS --->
	<cffunction name="removeRegistrant" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="registrantID" type="numeric" required="true">
		<cfargument name="eventSRID" type="numeric" required="true">
		<cfargument name="calendarSRID" type="numeric" required="true">
		<cfargument name="AROption" type="string" required="true">
		<cfargument name="cancellationFee" type="numeric" required="true">
		<cfargument name="GLAccountID" type="numeric" required="true">
		<cfargument name="deallocUsingPaidAmt" type="boolean" required="true">
		<cfargument name="registrantMemberID" type="numeric" required="false" default=0>

		<cfset var local = structNew()>

		<cftry>
			<cfset local.tmpEventRights = application.objSiteResource.buildRightAssignments(siteResourceID=arguments.eventSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
			<cfset local.tmpCalendarRights = application.objSiteResource.buildRightAssignments(siteResourceID=arguments.calendarSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
			<cfif NOT local.tmpEventRights.EditRegistrants AND NOT local.tmpCalendarRights.EditRegistrantsByDefault>
 				<cfthrow message="invalid request">
 			</cfif>

			<cfstoredproc procedure="ev_removeRegistrant" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.registrantID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.statsSessionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.AROption#">
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.cancellationFee#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.GLAccountID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.deallocUsingPaidAmt#">
			</cfstoredproc>
			
			<cfset local.data.showRefund = false>
			
			<cfif val(arguments.registrantMemberID)>
				<cfset local.strMemCredit = CreateObject("component","model.admin.members.members").getMemberCreditAndOutstandingAmount(mcproxy_orgID=arguments.mcproxy_orgID, mcproxy_siteID=arguments.mcproxy_siteID, memberID=arguments.registrantMemberID)>
				<cfif arguments.AROption eq "A" and local.strMemCredit.success and local.strMemCredit.unallocatedamount gt 0>
					<cfset local.data.showRefund = true>
				</cfif>
			</cfif>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeRSVP" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="rsvpID" type="numeric" required="true">
		<cfargument name="eventSRID" type="numeric" required="true">

		<cfset var local = structNew()>
		
		<cftry>	
			<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=arguments.eventSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
			<cfif NOT local.tmpRights.EditRegistrants>
 				<cfthrow message="invalid request">
 			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDeleteRSVP">
				DELETE FROM dbo.ev_rsvp
				WHERE rsvpID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rsvpID#">
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="reloadRegistrationType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="eventSRID" type="numeric" required="true">

		<cfset var local = structNew()>
		
		<cftry>	
			<cfif not hasEditEventRights(siteID=arguments.mcproxy_siteID, eventSRID=arguments.eventSRID)>
 				<cfthrow message="invalid request">
 			</cfif>
			<cfset local.objEvents = createObject("component","model.events.events")>
			<cfset local.strEvent = local.objEvents.getEvent(eventID=arguments.eventID, siteID=arguments.mcproxy_siteID, languageID=1)>
			<cfsavecontent variable=" local.data.regtype">
				<cfoutput>
					<cfif len(local.strEvent.qryEventMeta.altRegistrationURL)>
						<span>Change to <a href="javascript:fnChangeToRSVP();">RSVP</a> or <a href="javascript:fnChangeToFull();">Full Online Registration</a></span>
					<cfelseif local.strEvent.qryEventRegMeta.registrationtypeid is 1 and local.strEvent.qryEventRegMeta.activeRegistrantCount is 0>
						<span>Change to <a href="javascript:fnChangeToRSVP();">RSVP</a> or <a href="javascript:fnChangeToAlt();">Alt URL Registration</a></span>
					<cfelseif local.strEvent.qryEventRegMeta.registrationtypeid is 2 and local.strEvent.qryEventRegMeta.rsvpRegistrantCount is 0>
						<span>Change to <a href="javascript:fnChangeToFull();">Full Online Registration</a> or <a href="javascript:fnChangeToAlt();">Alt URL Registration</a></span>
					<cfelseif local.strEvent.qryEventRegMeta.activeRegistrantCount is 0 AND local.strEvent.qryEventRegMeta.rsvpRegistrantCount is 0>
						<span>Change to <a href="javascript:fnChangeToFull();">Full Online Registration</a> or <a href="javascript:fnChangeToAlt();">Alt URL Registration</a></span>
					</cfif>
					<cfif local.strEvent.qryEventRegMeta.activeRegistrantCount OR local.strEvent.qryEventRegMeta.rsvpRegistrantCount>
						<span class="font-italic">There are active registrants; the registration type cannot be changed.</span>
					</cfif>
				</cfoutput>
			</cfsavecontent>	
			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="insertRSVP" access="public" output="false" returntype="void">
		<cfargument name="registrationID" type="numeric" required="true">
		<cfargument name="salutation" type="string" required="true">
		<cfargument name="firstName" type="string" required="true">
		<cfargument name="lastName" type="string" required="true">
		<cfargument name="company" type="string" required="true">
		<cfargument name="email" type="string" required="true">
		<cfargument name="phone" type="string" required="true">
		<cfscript>
			var local = structNew();
		</cfscript>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.insertRSVP">
			INSERT INTO dbo.ev_rsvp(registrationID,salutation,firstName,lastName,company,email,phone,dateEntered)
			VALUES(
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.registrationID#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.salutation#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.firstName#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.lastName#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.company#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.email#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.phone#">,
				getDate())
		</cfquery>
	</cffunction>

	<cffunction name="udpateRSVP" access="public" output="false" returntype="void">
		<cfargument name="rsvpID" type="numeric" required="true">
		<cfargument name="salutation" type="string" required="true">
		<cfargument name="firstName" type="string" required="true">
		<cfargument name="lastName" type="string" required="true">
		<cfargument name="company" type="string" required="true">
		<cfargument name="email" type="string" required="true">
		<cfargument name="phone" type="string" required="true">
		<cfscript>
			var local = structNew();
		</cfscript>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.udpateRSVP">
			UPDATE dbo.ev_rsvp
			SET
				salutation 	= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.salutation#">,
				firstName 	= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.firstName#">,
				lastName 		= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.lastName#">,
				company			= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.company#">,
				email 			= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.email#">,
				phone 			= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.phone#">
				
			WHERE rsvpID 	= <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rsvpID#">
		</cfquery>
	</cffunction>

	<cffunction name="saveEvaluationFormSubmission" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': true }>
		<cfset local.objFBResponses = CreateObject("component","model.formbuilder.FBResponses")>
		<cfset local.formID = arguments.event.getValue('formID')>
		<cfset local.eventID = val(arguments.event.getValue('eID',0))>
		<cfset local.registrantID = val(arguments.event.getValue('rid',0))>
		<cfset local.qryForm = CreateObject("component","model.formBuilder.FBForms").getForm(formID=local.formID)>

		<cftry>
			<cfset local.responseID = int(val(arguments.event.getValue('FBResponseID',0)))>

			<cfset local.strCheckArgs = {
				"formID": local.formID,
				"enrollmentID": local.registrantID,
				"loadPoint": 'evaluation',
				"formTypeAbbr": local.qryForm.formTypeAbbr
			}>

			<!--- reached the maximum number of submissions --->
			<cfif NOT local.objFBResponses.underMaxResponsesPerUser(strArgs=local.strCheckArgs)>
				<cfset local.returnStruct['success'] = false>
				<cfset local.returnStruct['errmsg'] = "You have reached the maximum number of submissions.">
				<cfreturn local.returnStruct>
			</cfif>

			<cfset local.strSaveArgs = {
				"FBformID": local.formID,
				"FBdepoID": 0,
				"FBorgcode": arguments.event.getValue('mc_siteInfo.orgcode'),
				"formTypeAbbr": local.qryForm.formTypeAbbr,
				"FBholderResponse": 0,
				"formVars": {}
			}>

			<cfloop collection="#arguments.event.getCollection()#" item="local.key">
				<cfif left(local.key, 2) eq "q_">
					<cfset local.strSaveArgs.formVars[local.key] = arguments.event.getValue(local.key)>
				</cfif>
			</cfloop>

			<cfset local.objFBResponses.replaceFormResponse(responseID=local.responseID, strArgs=local.strSaveArgs)>

			<!--- complete evaluation --->
			<cfset CreateObject("component","model.events.events").completeEvaluationForm(eventID=local.eventID, registrantID=local.registrantID, formID=local.formID, responseID=local.responseID)>
		<cfcatch type="any">
			<cfset local.returnStruct['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<!--- SYNDICATION --->
	<cffunction name="insertSyndication" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="calendarID" type="numeric" required="yes">
		<cfargument name="sourceCalendarID" type="numeric" required="yes">
		<cfargument name="sourceCategoryID" type="numeric" required="yes">
		<cfargument name="sourceEventID" type="numeric" required="yes">
		<cfargument name="ovCategoryID" type="numeric" required="yes">

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="dbo.ev_createCalendarEvent">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.calendarID#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.sourceCalendarID#">
			<cfif arguments.sourceCategoryID is not 0>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.sourceCategoryID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" NULL="TRUE">
			</cfif>
			<cfif arguments.sourceEventID is not 0>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.sourceEventID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" NULL="TRUE">
			</cfif>
			<cfif arguments.ovCategoryID is not 0>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.ovCategoryID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" NULL="TRUE">
			</cfif>
		</cfstoredproc>
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="dbo.ev_refreshCalendarEventsCache">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.siteID#">
		</cfstoredproc>
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="dbo.ev_refreshCalendarEventsCategoryIDList">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.siteID#">
		</cfstoredproc>
	</cffunction>

	<cffunction name="updateContent" access="public" output="false" returntype="void">
		<cfargument name="contentID" type="numeric" required="true">
		<cfargument name="languageID" type="numeric" required="true">
		<cfargument name="isHTML" type="boolean" required="true">
		<cfargument name="contentTitle" type="string" required="true">
		<cfargument name="contentDesc" type="string" required="true">
		<cfargument name="rawContent" type="string" required="true">
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="dbo.cms_updateContent">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.contentID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.languageID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isHTML#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.contentTitle#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.contentDesc#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.rawContent#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=application.objSiteInfo.getSiteInfo(session.mcStruct.siteCode).orgID)#">
		</cfstoredproc>
	</cffunction>
	
	<cffunction name="getCustomFields" access="public" output="false" returntype="query">
		<cfargument name="eventID" type="numeric" required="true" />

		<cfset var qryCustomFields = "">

		<cfquery name="qryCustomFields" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @usageID int, @eventID int;
			select @usageID = dbo.fn_cf_getUsageID('Event','Registrant',null);
			set @eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">;

			select f.fieldID, f.fieldReference, f.isRequired, f.requiredMsg, ft.dataTypeCode, ft.displayTypeCode
			from dbo.cf_fields as f 
			inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID 
			inner join dbo.ev_events as e on e.siteResourceID = f.controllingSiteResourceID
			inner join dbo.ev_registration as r on r.eventID = e.eventID and r.siteID = e.siteID
			where e.eventID = @eventID
			and fu.parentUsageID = @usageID
			and f.isActive = 1
			and len(f.fieldReference) > 0
			and ft.displayTypeCode <> 'LABEL'
			and 1 = case 
					when ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX') then case when exists(select 1 from dbo.cf_fieldValues where fieldID = f.fieldID) then 1 else 0 end 
					else 1 end
			order by f.fieldOrder;
		</cfquery>
		
		<cfreturn qryCustomFields>
	</cffunction>

	<cffunction name="deleteAssetCategory" access="public" output="false" returntype="struct" hint="remove category">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="categoryID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>
			
		<cfscript>
			local.eventAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin',siteID=arguments.mcproxy_siteID);
			local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.eventAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID);

			// security 
			// --- might need to send to message page that says they dont have right to view this page
			if (NOT local.tmpRights.manageAssetTypes)	{
				local.data.success = false;
			}
			else {
				local.data = CreateObject('component', 'model.system.platform.category').deleteCategory(categoryID=arguments.categoryID);
			}
		</cfscript>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getSelectedAssetCategories" access="public" output="false" returntype="string">
		<cfargument name="siteResourceID" type="numeric" required="true">
		
		<cfset var local = structNew()>
	
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.selectedCategories">
			select categoryID 
			from dbo.cms_categorySiteResources 
			where siteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteResourceID#">
		</cfquery>
		<cfreturn trim(ValueList(local.selectedCategories.categoryID))>
	</cffunction>

	<cffunction name="checkOverlap" access="public" output="false" returntype="query">
		<cfargument name="eventAdminSiteResourceID" type="numeric" required="true">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="assetIDs" type="string" required="true">
		
		<cfset var local = structNew()>
		
		<!--- overlap code taken from  http://c2.com/cgi/wiki?TestIfDateRangesOverlap which explains its reasoning --->
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryOverlap" result="local.qryOverlapResult">
			set nocount on

			declare @eventID int, @categoryTreeID int
			set @eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#val(arguments.eventID)#">
			select @categoryTreeID = dbo.fn_getCategoryTreeIDForSiteResourceIDandTree(<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.eventAdminSiteResourceID#">,'Event Asset Types')

			-- get current event dates
			declare @starttime datetime, @endTime dateTime
			select @startTime = t.startTime, @endTime = t.endTime
				from dbo.ev_events as e
				inner join dbo.ev_times as t on t.eventid = e.eventid
				inner join dbo.timeZones as z on t.timeZoneID = z.timeZoneID
				inner join dbo.sites as s on s.siteid = e.siteid and s.defaultTimeZoneID = t.timezoneID
				where e.eventid = @eventID

			-- return all overlapping events
			select c.categoryID, c.categoryName, e.eventid, e.isAllDayEvent, z.timeZoneAbbr, t.startTime, t.endTime, eventContent.contentTitle as eventTitle
			from dbo.cms_categories c
			inner join dbo.cms_categories pc on pc.categoryID = c.parentCategoryID
			inner join dbo.cms_categorySiteResources csr on csr.categoryID = c.categoryID
			inner join dbo.ev_events e on e.siteResourceID = csr.siteResourceID and e.status <> 'D'
			inner join dbo.cms_contentLanguages as eventContent on eventContent.contentID = e.eventContentID and eventContent.languageID = 1
			inner join dbo.ev_times t on t.eventid = e.eventid
			inner join dbo.timeZones z on t.timeZoneID = z.timeZoneID
			inner join dbo.sites s on s.siteid = e.siteid and s.defaultTimeZoneID = t.timezoneID
			where c.categoryTreeID = @categoryTreeID
			and pc.isActive = 1 
			and c.isActive = 1 
			and c.parentCategoryID is not NULL
			<cfif arguments.assetIDs NEQ "">
				and c.categoryID in (<cfqueryparam cfsqltype="cf_sql_integer" list="yes" value="#arguments.assetIDs#">)
			<cfelse>
				and c.categoryID = 0
			</cfif>
			and e.eventID != @eventID
			and (@startTime < t.endTime and t.startTime < @endTime)
			order by t.startTime

			set nocount off
		</cfquery>

		<cfreturn local.qryOverlap>
	</cffunction>

	<!--- Roles --->
	<cffunction name="deleteEventRole" access="public" output="false" returntype="struct" hint="remove event role">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="csrid" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfset local.eventAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin',siteID=arguments.mcproxy_siteID)>
			<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.eventAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>

			<cfif NOT local.tmpRights.manageEventRoles>
				<cfthrow message="invalid request">
			</cfif>

			<cfset local.deleteRoleFields = createObject("component","model.admin.common.modules.customFields.customFields").deleteResourceFields(siteID=arguments.mcproxy_siteID,
					resourceType='EventAdmin', areaName='Role', csrid=arguments.csrid, detailID=arguments.categoryID)>

			<cfset local.data = CreateObject('component','model.system.platform.category').deleteCategory(categoryID=arguments.categoryID)>
			<cfif local.data.success>
				<cfquery name="local.qryProcessConditions" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;

					IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL
						DROP TABLE ##tblMCQRun;
					CREATE TABLE ##tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

					DECLARE @siteID int, @categoryID int;
					set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
					set @categoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.categoryID#">;

					INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
					select distinct c.orgID, null, c.conditionID
					from dbo.ams_virtualGroupConditions as c 
					inner join dbo.sites as s on s.orgID = c.orgID
					inner join dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
					inner join dbo.ams_virtualGroupConditionKeys as ck on ck.conditionKeyID = cv.conditionKeyID
					where s.siteID = @siteID
					and c.fieldCode = 'ev_entry'
					and ck.conditionKey = 'evRoleID'
					and cv.conditionValue = cast(@categoryID as varchar(10));
					
					EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

					IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL
						DROP TABLE ##tblMCQRun;
				</cfquery>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getEventRoles" access="public" output="false" returntype="query">
		<cfargument name="siteResourceID" type="numeric" required="true">

		<cfset var qryCats = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryCats">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @categoryTreeID int;
			select @categoryTreeID = dbo.fn_getCategoryTreeIDForSiteResourceIDandTree(#arguments.siteResourceID#,'Event Roles');

			select categoryID, categoryName
			from dbo.cms_categories
			where categoryTreeID = @categoryTreeID
			and isActive = 1 
			and parentCategoryID is NULL
			ORDER BY categoryName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryCats>
	</cffunction>

	<cffunction name="flagRegistrant" access="public" output="false" returntype="string">
		<cfargument name="registrantID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfquery name="local.qryFlag" datasource="#application.dsn.membercentral.dsn#">
				update dbo.ev_registrants
				set isFlagged = 1
				where registrantID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.registrantID#">
				and isFlagged = 0
			</cfquery>
			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.strReturn['success'] = false>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="unflagRegistrant" access="public" output="false" returntype="string">
		<cfargument name="registrantID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cftry>
			<cfquery name="local.qryFlag" datasource="#application.dsn.membercentral.dsn#">
				update dbo.ev_registrants
				set isFlagged = 0
				where registrantID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.registrantID#">
				and isFlagged = 1
			</cfquery>
			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.strReturn['success'] = false>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>
	
	<cffunction name="getEventDocuments" access="public" output="false" returntype="query">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="false" default="0">
		
		<cfset var qryDocuments = "">
		
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryDocuments">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int, @orgID int, @eventContentID int, @contenttitle varchar(200), 
				@eventID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">;
			DECLARE @events TABLE (eventID int PRIMARY KEY);

			SELECT @siteID = s.siteID, @orgID = s.orgID, @eventContentID = e.eventContentID
			FROM dbo.ev_events e
			INNER JOIN dbo.sites s on s.siteID = e.siteID
			WHERE e.eventID = @eventID;

			SELECT @contenttitle = contenttitle
			FROM dbo.cms_contentLanguages
			WHERE siteID = @siteID
			AND contentID = @eventContentID
			AND languageID = 1;

			insert into @events (eventID) values (@eventID);

			insert into @events (eventID)
			select eventID
			from dbo.ev_subEvents
			where parentEventID = @eventID;

			SELECT ed.eventDocumentID, ed.eventID, contenttitle=@contenttitle, dl.docTitle, ed.documentID, dv.author, dv.fileExt,
				isChild = CASE WHEN @eventID <> e.eventID THEN 1 ELSE 0 END, 
				childEventID = CASE WHEN @eventID <> e.eventID THEN @eventID ELSE null END,
				parentEventID=@eventID, edg.eventDocumentGroupingID, 
				edg.eventDocumentGrouping, ISNULL(edg.eventDocumentGroupingOrder,0) as eventDocumentGroupingOrder,
				ed.fileOrder, 
				<cfif arguments.memberID>
					CASE 
					WHEN 
						(select top 1 evr.registrantID
						from dbo.ev_registrants as evr
						inner join dbo.ev_registration as r on r.registrationID = evr.registrationID 
							and r.siteID = @siteID 
							and r.eventID = ed.eventID
						inner join dbo.ams_members as m on m.memberid = evr.memberid and m.orgID=@orgID
						inner join dbo.ams_members as m2 on m2.memberid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
							and m2.activeMemberID = m.activeMemberID and m2.orgID=@orgID
						where evr.status = 'A') > 0 
					THEN 1
					ELSE 0
					END as isRegistrant
				<cfelse>
					0 as isRegistrant
				</cfif>
			FROM @events as e
			INNER JOIN dbo.ev_eventDocuments as ed ON e.eventID = ed.eventID
			INNER JOIN dbo.cms_documents as d ON ed.documentID = d.documentID
				and d.siteID = @siteID
			INNER JOIN dbo.cms_documentLanguages as dl ON d.documentID = dl.documentID and dl.languageID = 1
			INNER JOIN dbo.cms_documentVersions AS dv ON dv.documentLanguageID = dl.documentLanguageID AND dv.isActive = 1
			LEFT OUTER JOIN dbo.ev_eventDocumentGrouping AS edg ON edg.eventDocumentGroupingID = ed.eventDocumentGroupingID
			ORDER BY e.eventID, eventDocumentGroupingOrder, ed.fileOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryDocuments>
	</cffunction>

	<cffunction name="sendPaymentReceipt" access="public" output="false" returntype="struct">
		<cfargument name="receiptUUID" type="string" required="true">
		<cfargument name="sendToEmail" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfset local.strEventReceipts = application.mcCacheManager.sessionGetValue(keyname='strEventReceipts', defaultValue={})>
			<cfif application.mcCacheManager.sessionValueExists('strEventReceipts') AND structKeyExists(local.strEventReceipts,arguments.receiptUUID) and isValid("regex",arguments.sendToEmail,application.regEx.email)>
				<cfset local.strReceipt = duplicate(local.strEventReceipts[arguments.receiptUUID])>
				
				<!--- resend payment receipt --->
				<cfif structKeyExists(local.strReceipt,"recipientID")>
					<cfset CreateObject('component', 'model.admin.emailBlast.emailBlast').resendRecipientEmail(siteID=local.strReceipt.siteID, recipientID=local.strReceipt.recipientID, toEmail=arguments.sendToEmail)>
					<cfset local.data.success = true>

				<!--- send new payment receipt --->
				<cfelse>
					<cfset local.eventsSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='Events',siteID=local.strReceipt.siteID)>

					<!--- get the reply to for the first event in the cart --->
					<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ev_getRegistrationMetaByEventID">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.strReceipt.firstEventID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="1">
						<cfprocresult name="local.qryRegMeta">
					</cfstoredproc>
					<cfset local.firstEventReplyTo = trim(local.qryRegMeta.replyToEmail)>

					<cfset local.replyto = "">
					<cfif len(local.firstEventReplyTo)>
						<cfset local.firstEventReplyTo = replace(local.firstEventReplyTo,",",";","ALL")>
						<cfloop list="#local.firstEventReplyTo#" index="local.item" delimiters=";">
							<cfif isValid("regex",trim(local.item),application.regEx.email)>
								<cfset local.replyto = listAppend(local.replyto,trim(local.item),';')>
							</cfif>
						</cfloop>
					<cfelse>
						<cfset local.replyto = local.strReceipt.email>
					</cfif>

					<cfset local.arrEmailTo = [{ name=local.strReceipt.purchaserName, email=arguments.sendToEmail }]>
					<cfif len(local.strReceipt.purchaserOverrideEmail) AND local.strReceipt.purchaserOverrideEmail NEQ arguments.sendToEmail>
						<cfset local.arrEmailTo.append({ name=local.strReceipt.purchaserName, email=local.strReceipt.purchaserOverrideEmail })>
					</cfif>

					<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name=local.strReceipt.orgname, email=local.strReceipt.networkEmailFrom },
							emailto=local.arrEmailTo,
							emailreplyto=local.replyto,
							emailsubject="Registration Payment Receipt",
							emailtitle="#local.strReceipt.sitename# Payment Receipt",
							emailhtmlcontent=local.strReceipt.receiptData,
							emailAttachments=local.strReceipt.arrInvoicePaths,
							siteID=local.strReceipt.siteID,
							memberID=local.strReceipt.purchaserMemberID,
							messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EVENTREGPAY"),
							sendingSiteResourceID=local.eventsSiteResourceID
					)>
					<cfif NOT local.strResult.success>
						<cfthrow message="#local.strResult.err#">
					<cfelse>
						<cfset local.strEventReceipts[arguments.receiptUUID].messageID = local.strResult.messageID>
						<cfset local.strEventReceipts[arguments.receiptUUID].recipientID = local.strResult.arrRecipientID[1]>
						<cfset local.data.success = true>
					</cfif>
				</cfif>
			<cfelse>
				<cfthrow message="Receipt not found." type="ReceiptNotFound">
			</cfif>
		<cfcatch type="ReceiptNotFound">
			<cfset local.data.success = false>
		</cfcatch>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="getCreditTypesOffered" access="public" returntype="query" output="yes">
		<cfargument name="eventID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryCreditTypes">
			SELECT cat.typeID, a.authorityName, ISNULL(ast.ovTypeName,cat.typeName) AS creditType
			FROM dbo.crd_offerings AS ec 
			INNER JOIN dbo.crd_offeringTypes as ect ON  ect.offeringID = ec.offeringID
			INNER JOIN dbo.crd_authoritySponsorTypes AS ast ON ast.ASTID = ect.ASTID
			INNER JOIN dbo.crd_authorityTypes AS cat ON cat.typeID = ast.typeID
			INNER JOIN dbo.crd_authorities as a on a.authorityID = cat.authorityID
			WHERE ec.eventID = <cfqueryparam value="#arguments.eventID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfreturn local.qryCreditTypes>
	</cffunction>
	
	<cffunction name="copyRatesfromEvent" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="parentEventID" type="numeric" required="true">
		<cfargument name="copyFromEventID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryFromRegID">
			select registrationID
			from dbo.ev_registration
			where eventID = <cfqueryparam value="#arguments.copyFromEventID#" cfsqltype="CF_SQL_INTEGER">
			and siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
			and status = 'A'
		</cfquery>

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryToRegID">
			select registrationID
			from dbo.ev_registration
			where eventID = <cfqueryparam value="#arguments.parentEventID#" cfsqltype="CF_SQL_INTEGER">
			and siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
			and status = 'A'
		</cfquery>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ev_copyRates">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#local.qryFromRegID.registrationID#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#local.qryToRegID.registrationID#">
			<cfprocparam type="In" cfsqltype="cf_sql_bit" value="0">
			<cfprocresult name="local.qryNewRates" resultset="1">
		</cfstoredproc>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="copyRateByEventRateID" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">	
		<cfargument name="parentRateID" type="numeric" required="true">
		<cfargument name="registrationID" type="numeric" required="true">
		<cfargument name="rateName" type="string" required="true">
		<cfargument name="rateGroupingID" type="numeric" required="true" default="0">
		<cfargument name="copyPerms" type="numeric" required="true" default="0">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRates">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					declare @siteID int, @registrationID int, @rateId int, @rateGroupingID int, @copyPerms int, @rateGLAccountID int, @rateName varchar(100),
						@rate decimal(18,2), @newRateID int, @newRatesiteResourceID int, @rateMessage varchar(400), @rateMessageDisplay bit, 
						@freeRateDisplay varchar(5), @ratereportCode varchar(15), @rateQty int, @rateIsHidden bit, @minBulkRateID int, @bulkrate decimal(18,2), 
						@bulksiteResourceID int, @bulkrateqty int, @newBulkRateID int, @newBulkRatesiteResourceID int, @siteResourceID int, 
						@newrateGroupingID int, @srr_rightsID int, @srr_roleid int, @srr_functionID int, @srr_groupid int, @srr_include bit, 
						@srr_inheritedRightsResourceID int, @srr_inheritedRightsFunctionID int, @scheduleID int, @newScheduleID int;
					set @rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.parentRateID#">;
					set @rateName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.rateName#">;
					set @copyPerms = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.copyPerms#">;
					set @registrationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.registrationID#">;
					set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
					
					BEGIN TRAN;
						select @rateGroupingID = null, @rateGLAccountID = null, @ratereportCode = null, 
							@rate = null, @siteResourceID = null, @rateqty = null, @rateIsHidden = null, 
							@rateMessage=null, @rateMessageDisplay=null, @freeRateDisplay=null;

						select @rateGLAccountID=GLAccountID, @rate=rate, @siteResourceID=siteResourceID, @rateqty=bulkQty, 
							@rateIsHidden=isHidden, @rateMessage=rateMessage, @rateMessageDisplay=rateMessageDisplay, @freeRateDisplay=freeRateDisplay
						from dbo.ev_rates 
						where rateID = @rateID;

						<cfif arguments.rateGroupingID GT 0>
							select @rateGroupingID = rg1.rateGroupingID
							from dbo.ev_rateGrouping as rg1 where rg1.registrationID = @registrationID and  rg1.rateGroupingID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateGroupingID#">;;
						</cfif>
					
						EXEC dbo.ev_createRate @registrationID=@registrationID,  
							@rateGroupingID=@rateGroupingID, @GLAccountID=@rateGLAccountID, @rateName=@rateName, 
							@reportCode=@ratereportCode, @rate=@rate, @freeRateDisplay=@freeRateDisplay, @rateMessage=@rateMessage,
							@rateMessageDisplay=@rateMessageDisplay, @isHidden=@rateIsHidden, @parentRateID=null, @qty=@rateqty, 
							@rateID=@newRateID OUTPUT, @siteResourceID=@newRatesiteResourceID OUTPUT;

						select @minBulkRateID = min(r.rateID)
						from dbo.ev_rates as r
						inner join dbo.cms_siteResources as sr on sr.siteID = @siteID and sr.siteResourceID = r.siteResourceID
						where r.parentRateID = @rateID
						and sr.siteResourceStatusID = 1;

						while @minBulkRateID is not null begin
							select @bulkrate=null, @bulkrateqty=null, @rateIsHidden=null;

							select @bulkrate=rate, @bulkrateqty=bulkQty, @rateIsHidden=isHidden 
							from dbo.ev_rates 
							where rateID = @minBulkRateID;

							EXEC dbo.ev_createRate @registrationID=@registrationID,  
								@rateGroupingID=@rateGroupingID, @GLAccountID=@rateGLAccountID, @rateName=@rateName, 
								@reportCode=@ratereportCode, @rate=@bulkrate, @freeRateDisplay=@freeRateDisplay, @rateMessage=@rateMessage, 
								@rateMessageDisplay=@rateMessageDisplay, @isHidden=@rateIsHidden, @parentRateID=@newRateID, @qty=@bulkrateqty, 
								@rateID=@newBulkRateID OUTPUT, @siteResourceID=@newBulkRatesiteResourceID OUTPUT;

							select @minBulkRateID = min(r.rateID)
							from dbo.ev_rates as r
							inner join dbo.cms_siteResources as sr on sr.siteID = @siteID and sr.siteResourceID = r.siteResourceID
							where r.parentRateID = @rateID
							and sr.siteResourceStatusID = 1
							and r.rateID > @minBulkRateID;
						end

						-- copy resource rights for this resource
						IF @copyPerms = 1 BEGIN	
							SET @srr_rightsID = null;
							SELECT @srr_rightsID = min(resourceRightsID) from dbo.cms_siteResourceRights where resourceID = @siteResourceID;
							WHILE @srr_rightsID IS NOT NULL BEGIN
								SELECT @srr_roleid=roleID, @srr_functionID=functionID, @srr_groupid=groupID, 
									@srr_include=[include], @srr_inheritedRightsResourceID=inheritedRightsResourceID, 
									@srr_inheritedRightsFunctionID=inheritedRightsFunctionID
								FROM dbo.cms_siteResourceRights
								WHERE resourceRightsID = @srr_rightsID;

								EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@newRatesiteResourceID, @include=@srr_include, 
									@functionIDList=@srr_functionID, @roleID=@srr_roleid, @groupID=@srr_groupid, @inheritedRightsResourceID=@srr_inheritedRightsResourceID, 
									@inheritedRightsFunctionID=@srr_inheritedRightsFunctionID;

								select @minBulkRateID = min(rateID)
								from dbo.ev_rates
								where parentRateID = @newRateID;

								while @minBulkRateID is not null begin
									select @bulksiteResourceID=siteResourceID
									from dbo.ev_rates 
									where rateID = @minBulkRateID;

									EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@bulksiteResourceID, @include=@srr_include, 
										@functionIDList=@srr_functionID, @roleID=@srr_roleid, @groupID=@srr_groupid, @inheritedRightsResourceID=@srr_inheritedRightsResourceID, 
										@inheritedRightsFunctionID=@srr_inheritedRightsFunctionID;

									select @minBulkRateID = min(rateID)
									from dbo.ev_rates
									where parentRateID = @rateID
									and rateID > @newRateID;
								end
					
								SELECT @srr_rightsID = min(resourceRightsID) from dbo.cms_siteResourceRights where resourceID = @siteResourceID and resourceRightsID > @srr_rightsID;
							END
						END

						insert into dbo.ev_ratesAvailable (rateID, scheduleID)
						select  @newRateID, scheduleID 
						from dbo.ev_ratesAvailable  
						where rateID=@rateID;

						insert into dbo.ev_rateTicketPackages (rateID, ticketPackageID, quantity)
						select  @newRateID, ticketPackageID, quantity
						from dbo.ev_rateTicketPackages  
						where rateID=@rateID;

						insert into dbo.ev_rateExcludeTicketPackages (rateID, ticketPackageID)
						select  @newRateID, ticketPackageID
						from dbo.ev_rateExcludeTicketPackages  
						where rateID=@rateID;

						insert into dbo.ev_rateMappings (subEventID, parentRateID, subRateID)
						select  subEventID, @newRateID, subRateID
						from dbo.ev_rateMappings  
						where parentRateID=@rateID;
					COMMIT TRAN;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="getRateGrouping" access="public" output="false" returntype="string">
		<cfargument name="rateGroupingID" type="numeric">

		<cfset var local = structNew()>
		<cfset local.data.success = true>
		
		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRateGrouping">
				SELECT 
					rg.rateGroupingID,
					rg.rateGrouping,
					rg.registrationID,
					rg.rateGroupingOrder
				FROM dbo.ev_rateGrouping rg
				WHERE rg.rateGroupingID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rateGroupingID#">
			</cfquery>
			<cfset local.data.rateGroupingID = local.qryRateGrouping.rateGroupingID>
			<cfset local.data.rateGrouping = local.qryRateGrouping.rateGrouping>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn serializeJSON(local.data)>
	</cffunction>

	<cffunction name="saveRateGrouping" access="public" output="false" returntype="struct">
		<cfargument name="rateGroupingID" type="numeric" required="true">
		<cfargument name="rateGrouping" type="string" required="true">
		<cfargument name="registrationID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif arguments.rateGroupingID gt 0>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdate">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						DECLARE @rateGroupingID int, @rateGrouping varchar(200), @registrationID int;
						SET @rateGroupingID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rateGroupingID#">;
						SET @rateGrouping = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.rateGrouping#">;
						SET @registrationID = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.registrationID#">;

						IF EXISTS (
							SELECT rateGroupingID
							FROM dbo.ev_rateGrouping
							WHERE registrationID = @registrationID
							AND rateGrouping = @rateGrouping
							AND rateGroupingID <> @rateGroupingID
						)
							RAISERROR('Rate Grouping exists',16,1);

						UPDATE dbo.ev_rateGrouping
						SET rateGrouping = @rateGrouping
						WHERE rateGroupingID = @rateGroupingID;

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
			<cfelse>
				<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ev_createRateGrouping">
					<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.registrationID#">
					<cfprocparam type="In" cfsqltype="cf_sql_varchar" value="#arguments.rateGrouping#">
					<cfprocparam type="Out" cfsqltype="cf_sql_integer" variable="local.rateGroupingID">
				</cfstoredproc>
			</cfif>

			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfif findNoCase("Rate Grouping exists", cfcatch.detail)>
				<cfset local.data.errmsg = "Rate Grouping Name already in use.">
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveMassRates" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="dtzid" type="numeric" required="true">
		<cfargument name="registrationID" type="numeric" required="true">
		<cfargument name="rateIDList" type="string" required="true">
		<cfargument name="isHidden" type="string" required="true">
		<cfargument name="rateGroupingID" type="string" required="true">
		<cfargument name="rate" type="string" required="true">
		<cfargument name="scheduleIDList" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasManageRatesRights(siteID=arguments.mcproxy_siteID, eventID=arguments.eventID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfif NOT arguments.registrationID gt 0>
				<cfthrow message="Registration not found.">
			</cfif>
			<cfif NOT listLen(arguments.rateIDList)>
				<cfthrow message="No rates selected.">
			</cfif>

			<cfif len(arguments.rate)>
				<cfset arguments.rate = abs(rereplace(arguments.rate,'[^\d.]+','','ALL'))>
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateRates">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @registrationID int, @rateGroupingID int, @rateIDForReorder int;
					SET @registrationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.registrationID#">;

					<cfif len(arguments.rateGroupingID)>
						SET @rateGroupingID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateGroupingID#">,0);

						SELECT TOP 1 @rateIDForReorder = rateID 
						FROM dbo.ev_rates 
						WHERE registrationID = @registrationID
						AND ISNULL(rateGroupingID,0) = ISNULL(@rateGroupingID,0);
					</cfif>

					BEGIN TRAN
						UPDATE dbo.ev_rates
						SET registrationID = registrationID
							<cfif listFind("0,1",arguments.isHidden)>
								, isHidden = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.isHidden#">
							</cfif>
							<cfif len(arguments.rateGroupingID)>
								, rateGroupingID = @rateGroupingID
							</cfif>
							<cfif len(arguments.rate)>
								, rate = <cfqueryparam cfsqltype="cf_sql_double" value="#NumberFormat(arguments.rate,"0.00")#">
							</cfif>
						WHERE rateID IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rateIDList#" list="true">)
						AND registrationID = @registrationID;
						
						delete from dbo.ev_ratesAvailable
						where rateID in (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rateIDList#" list="true">);
						
						<cfif len(arguments.scheduleIDList)>
							<cfloop list="#arguments.scheduleIDList#" index="local.thisScheduleID">
								<cfloop list="#arguments.rateIDList#" index="local.thisRateID">
									insert into dbo.ev_ratesAvailable
									values (
										<cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisRateID#">,
										<cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisScheduleID#">
									);
								</cfloop>
							</cfloop>
						</cfif>

						IF @rateIDForReorder IS NOT NULL
							EXEC dbo.ev_reorderRates @rateID=@rateIDForReorder;
					COMMIT TRAN
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="insertEventCustomFields" access="public" output="false" returntype="string">
		<cfargument name="arrCrossEventCustomFields" type="array" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.insertEventCustomFieldsSQL">
			<cfoutput>
				DECLARE @itemID int, @valueID int, @fieldID int, @detail varchar(max), @dataID int;
				set @itemID = @siteResourceID;

				-- add event custom field details
				<cfloop array="#arguments.arrCrossEventCustomFields#" index="local.cf">
					<cfif listFindNoCase("SELECT,RADIO,CHECKBOX",local.cf.displayTypeCode)>
						<cfset local.tempSQL = addEventCustomField_cf_option(itemType='CrossEvent', fieldID=local.cf.fieldID, valueIDList=local.cf.value)>
						#local.tempSQL#
					<cfelseif len(local.cf.value)>
						<cfset local.tempSQL = addEventCustomField_cf_nonOption(itemType='CrossEvent', fieldID=local.cf.fieldID, customText=local.cf.value)>
							#local.tempSQL#
					</cfif>
				</cfloop>
			</cfoutput>
		</cfsavecontent>

		<cfreturn preserveSingleQuotes(local.insertEventCustomFieldsSQL)>
	</cffunction>

	<cffunction name="updateEventCustomFields" access="public" output="false" returntype="string">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="arrCrossEventCustomFields" type="array" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.updateEventCustomFieldsSQL">
			<cfoutput>

				DECLARE @itemID int, @valueID int, @fieldID int, @detail varchar(max), @dataID int;
				set @itemID = #int(val(arguments.siteResourceID))#;

				-- update cross-event field details
				<cfloop array="#arguments.arrCrossEventCustomFields#" index="local.cf">
					<cfif listFindNoCase("SELECT,RADIO,CHECKBOX",local.cf.displayTypeCode)>
						<cfset local.tempSQL = editEventCustomField_cf_option(itemType='CrossEvent', itemID=arguments.siteResourceID, fieldID=local.cf.fieldID, valueIDList=local.cf.value)>
						#local.tempSQL#
					<cfelse>
						<cfset local.tempSQL = editEventCustomField_cf_nonOption(itemType='CrossEvent', itemID=arguments.siteResourceID, fieldID=local.cf.fieldID, customText=local.cf.value)>
							#local.tempSQL#
					</cfif>
				</cfloop>
			</cfoutput>
		</cfsavecontent>

		<cfreturn preserveSingleQuotes(local.updateEventCustomFieldsSQL)>
	</cffunction>

	<cffunction name="addEventCustomField_cf_nonOption" access="private" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="customText" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfif len(arguments.customText)>
			<cfsavecontent variable="local.insertFieldSQL">
				<cfoutput>
				set @fieldID = #arguments.fieldID#;
				set @detail = '#replace(arguments.customText,"'","''","ALL")#';
				set @dataID = null;

				EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@itemID, @itemType='#arguments.itemType#', 
						@valueID=null, @fieldValue=@detail, @dataID=@dataID OUTPUT;
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfset local.insertFieldSQL = "">
		</cfif>

		<cfreturn local.insertFieldSQL>
	</cffunction>

	<cffunction name="editEventCustomField_cf_nonOption" access="private" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="customText" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryGetDataID" datasource="#application.dsn.membercentral.dsn#">
			select dataID 
			from dbo.cf_fieldData 
			where fieldID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">
			and itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">
			and itemType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.itemType#">
		</cfquery>

		<cfif NOT local.qryGetDataID.recordcount>
			<cfset local.updateFieldSQL = addEventCustomField_cf_nonOption(itemType=arguments.itemType, fieldID=arguments.fieldID, customText=arguments.customText)>
		<cfelse>
			<cfsavecontent variable="local.updateFieldSQL">
				<cfoutput>
				set @dataID = #local.qryGetDataID.dataID#;
					
				<cfif len(arguments.customText)>
					set @detail = '#replace(arguments.customText,"'","''","ALL")#'
					set @fieldID = #arguments.fieldID#;
					set @valueID = null;

					EXEC dbo.cf_createFieldValue @fieldID=@fieldID, @fieldValue=@detail, @amount=0, @inventory=null,
						@enteredByMemberID=NULL, @skipAuditLog=1, @valueID=@valueID OUTPUT;

					UPDATE dbo.cf_fieldData
					SET valueID = @valueID
					WHERE dataID = @dataID;
				<cfelse>
					DELETE FROM dbo.cf_fieldData WHERE dataID = @dataID;
				</cfif>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.updateFieldSQL>
	</cffunction>

	<cffunction name="addEventCustomField_cf_option" access="private" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="valueIDList" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.insertFieldSQL">
			<cfoutput>
			<cfloop list="#arguments.valueIDList#" index="local.valueitem">
				<cfif val(local.valueitem) gt 0>
					set @fieldID = #arguments.fieldID#;
					set @valueID = #val(local.valueitem)#;
					set @dataID = null;
					
					EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@itemID, @itemType='#arguments.itemType#', 
							@valueID=@valueID, @fieldValue=NULL, @dataID=@dataID OUTPUT;
				</cfif>
			</cfloop>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.insertFieldSQL>
	</cffunction>
	
	<cffunction name="editEventCustomField_cf_option" access="private" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="valueIDList" type="string" required="true">

		<cfset var local = structNew()>

		<!--- existing options --->
		<cfquery name="local.qryExistingOptions" datasource="#application.dsn.membercentral.dsn#">
			select dataID, valueID
			from dbo.cf_fieldData 
			where fieldID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">
			and itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">
			and itemType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.itemType#">
		</cfquery>

		<!--- get any options we need to remove --->
		<cfquery name="local.qryOptionsToRemove" dbtype="query">
			select dataID, valueID
			from [local].qryExistingOptions
			<cfif listLen(arguments.valueIDList)>
				where valueID NOT IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.valueIDList#" list="true">)
			</cfif>
		</cfquery>
		
		<!--- get any options we need to add --->
		<cfif listLen(arguments.valueIDList)>
			<cfquery name="local.qryOptionsToAdd" datasource="#application.dsn.membercentral.dsn#">
				select valueID
				from dbo.cf_fieldValues
				where fieldID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldID#">
				and valueID IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.valueIDList#" list="true">)
				<cfif local.qryExistingOptions.recordcount>
					and valueID NOT IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#valueList(local.qryExistingOptions.valueID)#" list="true">)
				</cfif>
			</cfquery>
			<cfset local.optionsToAdd = valueList(local.qryOptionsToAdd.valueID)>
		<cfelse>
			<cfset local.optionsToAdd = "">
		</cfif>

		<cfsavecontent variable="local.updateFieldSQL">
			<cfoutput>
			<!--- remove options we dont want --->
			<cfloop query="local.qryOptionsToRemove">
				set @dataID = #val(local.qryOptionsToRemove.dataID)#;
				DELETE FROM dbo.cf_fieldData WHERE dataID = @dataID;
			</cfloop>

			<!--- add new options. pass in the new options only --->
			<cfif len(local.optionsToAdd)>
				<cfset local.tempSQL = addEventCustomField_cf_option(itemType=arguments.itemType, fieldID=arguments.fieldID, valueIDList=local.optionsToAdd)>
				#local.tempSQL#
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.updateFieldSQL>
	</cffunction>
	
	<cffunction name="insertRegistrationSchedule" access="public" output="false" returntype="numeric">
		<cfargument name="registrationID" type="numeric" required="yes">
		<cfargument name="rangeName" type="string" required="yes">
		<cfargument name="startDateTime" type="date" required="yes">
		<cfargument name="endDateTime" type="date" required="yes">
		
		<cfset var local = StructNew()>
		<cfset local.scheduleID = 0>
		
		<cfset local.overlapRegistrationSchedule = checkOverlapRegistrationSchedule(scheduleID=0, registrationID=arguments.registrationID, startDateTime=arguments.startDateTime, endDateTime=arguments.endDateTime)>
														
		<cfif local.overlapRegistrationSchedule.success>
			<cfquery name="local.qryInsertRegSchedule" datasource="#application.dsn.membercentral.dsn#">
				insert into dbo.ev_priceSchedule (registrationID, rangeName, startDate, endDate)
				values (
					<cfqueryparam value="#arguments.registrationID#" cfsqltype="CF_SQL_INTEGER">,
					<cfqueryparam value="#arguments.rangeName#" cfsqltype="CF_SQL_VARCHAR">,
					<cfqueryparam value="#arguments.startDateTime#" cfsqltype="CF_SQL_TIMESTAMP">,
					<cfqueryparam value="#arguments.endDateTime#" cfsqltype="CF_SQL_TIMESTAMP">
				);
				
				SELECT SCOPE_IDENTITY() AS scheduleID;
			</cfquery>
			
			<cfset local.scheduleID = local.qryInsertRegSchedule.scheduleID>
		</cfif>
		
		<cfreturn local.scheduleID>
	</cffunction>
	
	<cffunction name="updateRegistrationSchedule" access="public" output="false" returntype="void">
		<cfargument name="scheduleID" type="numeric" required="yes">
		<cfargument name="registrationID" type="numeric" required="yes">
		<cfargument name="rangeName" type="string" required="yes">
		<cfargument name="startDateTime" type="date" required="yes">
		<cfargument name="endDateTime" type="date" required="yes">
		
		<cfset var local = StructNew()>
		
		<cfset local.overlapRegistrationSchedule = checkOverlapRegistrationSchedule(scheduleID=arguments.scheduleID, registrationID=arguments.registrationID, startDateTime=arguments.startDateTime, endDateTime=arguments.endDateTime)>
		
		<cfif local.overlapRegistrationSchedule.success>
			<cfquery name="local.qryUpdateRegSchedule" datasource="#application.dsn.membercentral.dsn#">
				update dbo.ev_priceSchedule 
				set rangeName = <cfqueryparam value="#arguments.rangeName#" cfsqltype="CF_SQL_VARCHAR">,
					startDate = <cfqueryparam value="#arguments.startDateTime#" cfsqltype="CF_SQL_TIMESTAMP">,
					endDate = <cfqueryparam value="#arguments.endDateTime#" cfsqltype="CF_SQL_TIMESTAMP">
				where scheduleID = <cfqueryparam value="#arguments.scheduleID#" cfsqltype="CF_SQL_INTEGER">;
			</cfquery>
		</cfif>
	</cffunction>
	
	<cffunction name="deleteRegistrationSchedule" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="scheduleID" type="numeric" required="yes">
		<cfargument name="eventSRID" type="numeric" required="yes">

		<cfset var local = structNew()>
		
		<cftry>
			<cfif not hasEditEventRights(siteID=arguments.mcproxy_siteID, eventSRID=arguments.eventSRID)>
 				<cfthrow message="invalid request">
 			</cfif>

			<cfquery name="local.qryDeleteRegSchedule" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					declare @scheduleID int = <cfqueryparam value="#arguments.scheduleID#" cfsqltype="CF_SQL_INTEGER">;

					IF EXISTS (
						select 1 from dbo.ev_ticketPackageAvailable where scheduleID = @scheduleID and isActive = 1
					)
						RAISERROR('Schedule Tied to Package',16,1);

					BEGIN TRAN
						delete from dbo.ev_ticketPackageAvailable 
						where scheduleID = @scheduleID
						and isActive = 0;

						delete from dbo.cf_fieldAvailable_usage1 
						where scheduleID = @scheduleID;
						
						delete from dbo.cf_fieldValuesAvailable_usage1 
						where scheduleID = @scheduleID;
						
						delete from dbo.ev_ratesAvailable
						where scheduleID = @scheduleID;
						
						delete from dbo.ev_priceSchedule
						where scheduleID = @scheduleID;
					COMMIT TRAN
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfif findNoCase("Schedule Tied to Package", cfcatch.detail)>
				<cfset local.data.errmsg = "Cannot delete schedule tied to packages.">
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="doCheckOverlapRegistrationSchedule" access="public" output="false" returntype="struct">
		<cfargument name="sID" type="numeric" required="yes">
		<cfargument name="rID" type="numeric" required="yes">
		<cfargument name="sTime" type="string" required="yes">
		<cfargument name="eTime" type="string" required="yes">
		<cfargument name="tzID" type="numeric" required="yes">
				
		<cfscript>
			var local = structNew();
			local.data = structNew();
			local.data.success = false;
			local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
		</cfscript>
		
		<cftry>
			<cfset local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.tzID)>
			<cfset local.startDateTime = ParseDateTime("#replace(arguments.sTime,' - ',' ')#")>
			<cfset local.endDateTime = ParseDateTime("#replace(arguments.eTime,' - ',' ')#")>
			<cfset local.startDateTime = local.objTZ.convertTimeZone(dateToConvert=local.startDateTime,fromTimeZone=local.regTimeZone,toTimeZone='US/Central')>
			<cfset local.endDateTime = local.objTZ.convertTimeZone(dateToConvert=local.endDateTime,fromTimeZone=local.regTimeZone,toTimeZone='US/Central')>
			<cfset local.checkResult = checkOverlapRegistrationSchedule(scheduleID=arguments.sID, registrationID=arguments.rID, startDateTime=local.startDateTime, endDateTime=local.endDateTime)>
			<cfset local.data.success = local.checkResult.success>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="checkOverlapRegistrationSchedule" access="public" output="false" returntype="struct">
		<cfargument name="scheduleID" type="numeric" required="yes">
		<cfargument name="registrationID" type="numeric" required="yes">
		<cfargument name="startDateTime" type="date" required="yes">
		<cfargument name="endDateTime" type="date" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		<cfset local.data.success = false>
		
		<cftry>
			<cfquery name="local.qryDeleteRegSchedule" datasource="#application.dsn.membercentral.dsn#">
				set nocount on;

				declare @startDateTime datetime, @endDateTime datetime;

				set @startDateTime = <cfqueryparam value="#arguments.startDateTime#" cfsqltype="CF_SQL_TIMESTAMP">;
				set @endDateTime = <cfqueryparam value="#arguments.endDateTime#" cfsqltype="CF_SQL_TIMESTAMP">;
				
				select count(scheduleID) as scheduleCount
				from dbo.ev_priceSchedule
				where registrationID = <cfqueryparam value="#arguments.registrationID#" cfsqltype="CF_SQL_INTEGER">
				<cfif arguments.scheduleID gt 0>
					and scheduleID <> <cfqueryparam value="#arguments.scheduleID#" cfsqltype="CF_SQL_INTEGER">
				</cfif>
				and (
					@startdateTime between startDate and endDate
	 				or @enddateTime between startDate and endDate
	 				or (@startdateTime <= endDate and @enddateTime >= startDate)
				);
			</cfquery>
			
			<cfif local.qryDeleteRegSchedule.scheduleCount gt 0>
				<cfset local.data.success = false>
			<cfelse>
				<cfset local.data.success = true>
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="checkLinkedAvailableRates" access="public" output="false" returntype="struct">
		<cfargument name="sID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		<cfset local.data.success = false>
		<cfset local.data.ratelinkedcount = 0>
		<cfset local.data.fieldlinkedcount = 0>
		
		<cftry>
			<cfquery name="local.qryCheckLinkedRateSchedule" datasource="#application.dsn.membercentral.dsn#">
				select count(autoID) as rateSchedulecount
				from dbo.ev_ratesAvailable
				where scheduleID = <cfqueryparam value="#arguments.sID#" cfsqltype="CF_SQL_INTEGER">;
			</cfquery>
			<cfquery name="local.qryCheckLinkedCustFldSchedule" datasource="#application.dsn.membercentral.dsn#">
				select sum(scheduleCount) as scheduleCount from (
					select count(autoID) as scheduleCount
					from dbo.cf_fieldAvailable_usage1
					where scheduleID = <cfqueryparam value="#arguments.sID#" cfsqltype="CF_SQL_INTEGER">
					
					union
					
					select count(autoID) as scheduleCount
					from dbo.cf_fieldValuesAvailable_usage1
					where scheduleID = <cfqueryparam value="#arguments.sID#" cfsqltype="CF_SQL_INTEGER">
				)tmp;
			</cfquery>
			
			<cfset local.data.ratelinkedcount = local.qryCheckLinkedRateSchedule.rateSchedulecount>
			<cfset local.data.fieldlinkedcount = local.qryCheckLinkedCustFldSchedule.scheduleCount>
			
			<cfif local.qryCheckLinkedRateSchedule.rateSchedulecount gt 0 or local.qryCheckLinkedCustFldSchedule.scheduleCount gt 0>
				<cfset local.data.success = false>
			<cfelse>
				<cfset local.data.success = true>
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="getRegistrationSchedule" access="public" output="false" returntype="query">
		<cfargument name="siteid" type="numeric" required="true">
		<cfargument name="registrationID" type="numeric" required="true">
		
		<cfset var qryRegistrationSchedule = "">
		
		<cfquery name="qryRegistrationSchedule" datasource="#application.dsn.membercentral.dsn#">
			select ep.scheduleID, ep.registrationID, ep.rangeName, ep.startDate, ep.endDate
			from dbo.ev_priceSchedule ep
			inner join dbo.ev_registration as er on er.registrationID = ep.registrationID 
				and er.registrationTypeID = 1
				and er.registrationID = <cfqueryparam value="#arguments.registrationID#" cfsqltype="CF_SQL_INTEGER">
				and er.siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
			inner join dbo.ev_events as e on e.eventID = er.eventID 
				and e.siteID = <cfqueryparam value="#arguments.siteid#" cfsqltype="CF_SQL_INTEGER">
			order by ep.startDate
		</cfquery>
		
		<cfreturn qryRegistrationSchedule>
	</cffunction>
	
	<cffunction name="getEventFieldScheduleMappedPrice" access="public" output="false" returntype="query">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		
		<cfset var qryEventFieldScheduleMappedPrice = "">
		
		<cfquery name="qryEventFieldScheduleMappedPrice" datasource="#application.dsn.membercentral.dsn#">
			SELECT ps.scheduleID, ps.rangeName, cast(cfa.amount as decimal(18,2)) as amount, ps.startDate, ps.endDate
			FROM dbo.ev_priceSchedule ps
			INNER JOIN dbo.ev_registration as r on r.registrationID = ps.registrationID
			INNER JOIN dbo.ev_events as e on e.eventID = r.eventID and e.siteID = r.siteID
			LEFT OUTER JOIN dbo.cf_fieldAvailable_usage1 as cfa on cfa.scheduleID = ps.scheduleID
				AND cfa.fieldID = <cfqueryparam value="#arguments.fieldID#" cfsqltype="CF_SQL_INTEGER">
			LEFT OUTER JOIN dbo.cf_fields as f on f.fieldID = cfa.fieldID
				AND f.isActive = 1
			WHERE e.siteResourceID = <cfqueryparam value="#arguments.siteResourceID#" cfsqltype="CF_SQL_INTEGER">
			ORDER BY ps.startDate;
		</cfquery>

		<cfreturn qryEventFieldScheduleMappedPrice>
	</cffunction>
	
	<cffunction name="getEvFieldOptionScheduleMappedPrice" access="public" output="false" returntype="query">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="valueID" type="numeric" required="true">
		
		<cfset var qryEvFieldOptionScheduleMappedPrice = "">
		
		<cfquery name="qryEvFieldOptionScheduleMappedPrice" datasource="#application.dsn.membercentral.dsn#">
			SELECT ps.scheduleID, ps.rangeName, cast(cfva.amount as decimal(18,2)) as amount, ps.startDate, ps.endDate
			FROM dbo.ev_priceSchedule ps
			INNER JOIN dbo.ev_registration as r on r.registrationID = ps.registrationID
				AND r.[status] = 'A'
			INNER JOIN dbo.ev_events as e on e.eventID = r.eventID  and e.siteID = r.siteID
			LEFT OUTER JOIN dbo.cf_fieldValuesAvailable_usage1 as cfva on cfva.scheduleID = ps.scheduleID
				AND cfva.valueID = <cfqueryparam value="#arguments.valueID#" cfsqltype="CF_SQL_INTEGER">
			LEFT OUTER JOIN dbo.cf_fieldValues as fv on fv.valueID = cfva.valueID
			WHERE e.siteResourceID = <cfqueryparam value="#arguments.siteResourceID#" cfsqltype="CF_SQL_INTEGER">
			ORDER BY ps.startDate;
		</cfquery>

		<cfreturn qryEvFieldOptionScheduleMappedPrice>
	</cffunction>

	<cffunction name="getTickets" access="public" output="false" returntype="query">
		<cfargument name="registrationID" type="numeric" required="true">
		
		<cfset var qryGetTickets = "">
		
		<cfquery name="qryGetTickets" datasource="#application.dsn.membercentral.dsn#">
			select ticketID, ticketName
			from dbo.ev_tickets
			where registrationID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.registrationID#">
			order by sortOrder
		</cfquery>

		<cfreturn qryGetTickets>
	</cffunction>
	
	<cffunction name="getTicketsAndPackages" access="public" output="false" returntype="query">
		<cfargument name="registrationID" type="numeric" required="true">
		
		<cfset var qryTicketsAndPackages = "">
		
		<cfquery name="qryTicketsAndPackages" datasource="#application.dsn.membercentral.dsn#">
			select t.ticketID, t.ticketName, tp.ticketPackageID, tp.ticketPackageName
			from ev_tickets t
			inner join ev_ticketPackages tp on tp.ticketID = t.ticketID
			where t.registrationID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.registrationID#">
			order by t.sortOrder, tp.sortOrder
		</cfquery>

		<cfreturn qryTicketsAndPackages>
	</cffunction>
	
	<cffunction name="getTicketFieldsInfo" access="public" output="false" returntype="struct">
		<cfargument name="fd" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.arrPackages = arrayNew(1)>

		<cfset local.objAdminEventReg = CreateObject("component","eventReg")>
		<cfset local.objCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>
		<cfset arguments.fd = DeserializeJSON(arguments.fd)>

		<cfif not structKeyExists(arguments.fd,"siteID")>
			<cfset arguments.fd['siteID'] = 0>
		</cfif>
		<cfif not structKeyExists(arguments.fd,"registrationID")>
			<cfset arguments.fd['registrationID'] = 0>
		</cfif>
		<cfif not structKeyExists(arguments.fd,"rateID")>
			<cfset arguments.fd['rateID'] = 0>
		</cfif>
		<cfif not structKeyExists(arguments.fd,"registrantID")>
			<cfset arguments.fd['registrantID'] = 0>
		</cfif>
		
		<cfset local.qryTicketDetails = local.objAdminEventReg.getTicketForReg(registrationid=arguments.fd.registrationID, rateID=arguments.fd.rateID)>
		<cfif arguments.fd.registrantID gt 0>
			<cfset local.qryTicketPackagesSelected = local.objAdminEventReg.getTicketPackagesSelected(registrantID=arguments.fd.registrantID)>
		</cfif>

		<cfset local.displayedCurrencyType = application.objSiteInfo.getSiteInfo(session.mcStruct.siteCode).defaultCurrencyType>
		<cfif len(local.displayedCurrencyType)>
			<cfset local.displayedCurrencyType = " #local.displayedCurrencyType#">
		</cfif>
		<cfset local.eventAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin', siteID=arguments.fd.siteID)>

		<!--- put the selected packages into a struct --->
		<cfset local.formRegPackages = structNew()>
		<cfloop collection="#arguments.fd#" item="local.thisField">
			<cfif (left(local.thisField,14) eq "ticketPackage_" and val(arguments.fd[local.thisField]) gt 0) or (left(local.thisField,20) eq "ticketPackageCredit_" and left(arguments.fd[local.thisField],10) eq 'createNew_')>
				<cfif not structKeyExists(local.formRegPackages,GetToken(local.thisField,2,'_'))>
					<cfset structInsert(local.formRegPackages, GetToken(local.thisField,2,'_'), 0, true)>
				</cfif>
				<cfif left(local.thisField,14) eq "ticketPackage_" and val(arguments.fd[local.thisField]) gt 0>
					<cfset local.thisRegSelection = val(arguments.fd[local.thisField])>
				<cfelse>
					<cfset local.thisRegSelection = 1>
				</cfif>
				<cfset local.formRegPackages[GetToken(local.thisField,2,'_')] = local.formRegPackages[GetToken(local.thisField,2,'_')] + local.thisRegSelection>
			</cfif>
		</cfloop>
		
		<cfquery name="local.qryDistinctTicketDetails" dbtype="query">
			select distinct ticketID, ticketPackageID, ticketAssignSeats, ticketsInPackage, ticketName, ticketPackageName, qtyIncludedByRate
			from [local].qryTicketDetails
		</cfquery>
		
		<cfloop query="local.qryDistinctTicketDetails">
			<cfquery name="local.qryTicketPackageAvailablePriceID" dbtype="query">
				select availablePriceID
				from [local].qryTicketDetails
				where ticketPackageID = #local.qryDistinctTicketDetails.ticketPackageID#
			</cfquery>

			<cfset local.thisPackageCustomFieldsXML = local.objCustomFields.getFieldsXML(siteID=arguments.fd.siteID, resourceType='Event', areaName='TicketPackage', csrid=local.eventAdminSiteResourceID, detailID=local.qryDistinctTicketDetails.ticketPackageID, hideAdminOnly=0)>
			<cfset local.thisPackageTicketCustomFieldsXML = local.objCustomFields.getFieldsXML(siteID=arguments.fd.siteID, resourceType='Event', areaName='Ticket', csrid=local.eventAdminSiteResourceID, detailID=local.qryDistinctTicketDetails.ticketID, hideAdminOnly=0)>

			<cfset local.thisPackageCustomFieldXML = xmlParse(local.thisPackageCustomFieldsXML.returnXML).xmlRoot>
			<cfset local.thisPackageTicketCustomFieldXML = xmlParse(local.thisPackageTicketCustomFieldsXML.returnXML).xmlRoot>

			<cfset local.tmpStr = structNew()>
			<cfset local.tmpStr['ticketPackageID'] = local.qryDistinctTicketDetails.ticketPackageID>
			<cfset local.tmpStr['ticketID'] = local.qryDistinctTicketDetails.ticketID>
			<cfset local.tmpStr['ticketName'] = local.qryDistinctTicketDetails.ticketName>
			<cfset local.tmpStr['ticketAssignSeats'] = local.qryDistinctTicketDetails.ticketAssignSeats>
			<cfset local.tmpStr['hasPackageFields'] = arrayLen(local.thisPackageCustomFieldXML.xmlChildren) gt 0>
			<cfset local.tmpStr['hasTicketFields'] = arrayLen(local.thisPackageTicketCustomFieldXML.xmlChildren) gt 0>
			<cfset local.tmpStr['packageName'] = "#local.qryDistinctTicketDetails.ticketName# - #local.qryDistinctTicketDetails.ticketPackageName#">
			<cfset local.tmpStr['ticketsInPackage'] = local.qryDistinctTicketDetails.ticketsInPackage>
			<cfset local.tmpStr['ticketPackageAvailablePriceIDList'] = valueList(local.qryTicketPackageAvailablePriceID.availablePriceID)>
			<cfset local.tmpStr['instances'] = 0>
			<cfset local.tmpStr['showPackage'] = 0>
			<cfset local.tmpStr['includedFromRateCount'] = 0>
			<cfset local.tmpStr['instanceArray'] = arrayNew(1)>
			<cfset local.tmpStr['newInstanceTemplate'] = arrayNew(1)>
			<cfset local.tmpStr['qtyIncludedByRate'] = val(local.qryDistinctTicketDetails.qtyIncludedByRate)>
			<cfset local.tmpStr['showTicketSelections'] = local.tmpStr['ticketAssignSeats'] is 1 OR local.tmpStr['hasTicketFields']>
			<cfset local.tmpStr['displayedCurrencyType'] = local.displayedCurrencyType>

			<cfif arguments.fd.registrantID gt 0>
				<cfquery name="local.qryThisRegPackageInstance" dbtype="query">
					select instanceID, ticketPackageID
					from [local].qryTicketPackagesSelected
					where ticketPackageID = #local.qryDistinctTicketDetails.ticketPackageID#
					and includedFromRate = 0
					order by instanceID
				</cfquery>
				<cfset local.tmpStr.instances = local.qryThisRegPackageInstance.recordCount>
				<cfloop query="local.qryThisRegPackageInstance">
					<cfif structKeyExists(arguments.fd,'ticketPackageCredit_#local.qryThisRegPackageInstance.ticketPackageID#_#local.qryThisRegPackageInstance.currentRow#') and ( left(arguments.fd['ticketPackageCredit_#local.qryThisRegPackageInstance.ticketPackageID#_#local.qryThisRegPackageInstance.currentRow#'],10) eq 'createNew_' or val(arguments.fd['ticketPackageCredit_#local.qryThisRegPackageInstance.ticketPackageID#_#local.qryThisRegPackageInstance.currentRow#']) gt 0 )>
						<cfset local.tmpStr.instances = local.tmpStr.instances - 1>
					</cfif>
				</cfloop>
				
				<cfquery name="local.qryThisRegPackageInstanceRateIncluded" dbtype="query">
					select instanceID, ticketPackageID
					from [local].qryTicketPackagesSelected
					where ticketPackageID = #local.qryDistinctTicketDetails.ticketPackageID#
					and includedFromRate = 1
					order by instanceID
				</cfquery>
				<cfif local.qryThisRegPackageInstanceRateIncluded.recordCount gt local.tmpStr.qtyIncludedByRate>
					<cfset local.tmpStr.instances = local.tmpStr.instances + (local.qryThisRegPackageInstanceRateIncluded.recordCount - local.tmpStr.qtyIncludedByRate)>
				</cfif>
			</cfif>
			
			<cfif structKeyExists(local.formRegPackages,local.qryDistinctTicketDetails.ticketPackageID)>
				<cfset local.tmpStr.instances = local.tmpStr.instances + local.formRegPackages[local.qryDistinctTicketDetails.ticketPackageID] + local.tmpStr.qtyIncludedByRate>
			<cfelseif local.tmpStr.qtyIncludedByRate gt 0>
				<cfset local.tmpStr.instances = local.tmpStr.instances + local.tmpStr.qtyIncludedByRate>
			</cfif>

			<cfif local.tmpStr.instances gt 0 and (local.tmpStr.ticketsInPackage gt 0 or local.tmpStr.ticketAssignSeats is 1 or local.tmpStr.hasPackageFields is 1 or local.tmpStr.hasTicketFields is 1)>
				<cfset local.tmpStr['showPackage'] = 1>
				<cfset local.strArgs = structNew()>
				<cfset local.strArgs['fd'] = arguments.fd>
				<cfset local.strArgs['packageStr'] = local.tmpStr>
				<cfset local.strArgs['registrantID'] = arguments.fd.registrantID>
				<cfset local.strArgs['packageFieldsXML'] = local.thisPackageCustomFieldXML>
				<cfset local.strArgs['ticketFieldsXML'] = local.thisPackageTicketCustomFieldXML>
				<cfset local.strArgs['eventAdminSiteResourceID'] = local.eventAdminSiteResourceID>
				<cfset local.strArgs['displayedCurrencyType'] = local.displayedCurrencyType>
				<cfif arguments.fd.registrantID gt 0>
					<cfquery name="local.strArgs.qryThisPackageInstances" dbtype="query">
						select *
						from [local].qryTicketPackagesSelected
						where ticketPackageID = #local.qryDistinctTicketDetails.ticketPackageID#
						order by instanceID
					</cfquery>
				</cfif>
				<cfset local.tmpStr['instanceArray'] = prepareInstancePackageArr(argumentCollection=local.strArgs)>
			</cfif>

			<!--- prepare newInstanceTemplate --->
			<cfset local.cloneTmpStr = duplicate(local.tmpStr)>
			<cfset local.cloneTmpStr['instances'] = 1>
			<cfset local.cloneTmpStr['showPackage'] = 1>
			<cfset local.cloneTmpStr['qtyIncludedByRate'] = 0>
			<cfset local.cloneTmpStr['instanceArray'] = arrayNew(1)>
			<cfset local.strArgs = structNew()>
			<cfset local.strArgs['fd'] = structNew()>
			<cfset local.strArgs['packageStr'] = local.cloneTmpStr>
			<cfset local.strArgs['registrantID'] = 0>
			<cfset local.strArgs['packageFieldsXML'] = local.thisPackageCustomFieldXML>
			<cfset local.strArgs['ticketFieldsXML'] = local.thisPackageTicketCustomFieldXML>
			<cfset local.strArgs['eventAdminSiteResourceID'] = local.eventAdminSiteResourceID>
			<cfset local.strArgs['displayedCurrencyType'] = local.displayedCurrencyType>
			<cfset local.cloneTmpStr['instanceArray'] = prepareInstancePackageArr(argumentCollection=local.strArgs)>
			<cfset structDelete(local.cloneTmpStr,'newInstanceTemplate')>
			<cfset local.tmpStr['newInstanceTemplate'] = local.cloneTmpStr>

			<cfset arrayAppend(local.arrPackages, local.tmpStr)>
		</cfloop>

		<cfset local.data = structNew()>
		<cfset local.data['success'] = true>
		<cfset local.data['arrPackages'] = local.arrPackages>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="prepareInstancePackageArr" access="private" output="false" returntype="array">
		<cfargument name="fd" type="struct" required="true">
		<cfargument name="packageStr" type="struct" required="true">
		<cfargument name="registrantID" type="numeric" required="true">
		<cfargument name="packageFieldsXML" type="xml" required="true">
		<cfargument name="ticketFieldsXML" type="xml" required="true">
		<cfargument name="eventAdminSiteResourceID" type="numeric" required="true">
		<cfargument name="displayedCurrencyType" type="string" required="true">
		<cfargument name="qryThisPackageInstances" type="query" required="false">

		<cfset var local = structNew()>
		<cfset local.thisPackageDetailsArr = arrayNew(1)>

		<cfif arguments.registrantID gt 0>
			<!--- get all existing transactions for this registration --->
			<cfstoredproc procedure="ev_regTransactionsForConfirmation" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.registrantID#">
				<cfprocresult name="local.qryTotals" resultset="1">
				<cfprocresult name="local.qryRegTransactions" resultset="2">
				<cfprocresult name="local.qryPaymentAllocations" resultset="3">
			</cfstoredproc>
		</cfif>

		<cfset local.thisPackageInstancesIncFromRate = 0>
		<cfloop from="1" to="#arguments.packageStr.instances#" index="local.thisInstanceNum">
			<cfset local.tmpInstanceStruct = structNew()>
			<cfset local.tmpInstanceStruct['instanceID'] = 0>
			<cfset local.tmpInstanceStruct['packageID'] = arguments.packageStr.ticketPackageID>
			<cfset local.tmpInstanceStruct['ticketID'] = arguments.packageStr.ticketID>
			<cfset local.tmpInstanceStruct['instanceNum'] = local.thisInstanceNum>
			<cfset local.tmpInstanceStruct['packageName'] = arguments.packageStr.packageName>
			<cfset local.tmpInstanceStruct['instanceTitle'] = arguments.packageStr.packageName>
			<cfset local.tmpInstanceStruct['includedFromRate'] = 0>
			<cfset local.tmpInstanceStruct['availablePriceID'] = getFirstRegPriceIDFromList(priceIDList=arguments.packageStr.ticketPackageAvailablePriceIDList)>
			<cfif local.tmpInstanceStruct['availablePriceID'] is 0>
				<cfset local.tmpInstanceStruct['availablePriceID'] = ListFirst(arguments.packageStr.ticketPackageAvailablePriceIDList)>
			</cfif>
			<cfset local.tmpInstanceStruct['packageAmount'] = 0>
			<cfset local.tmpInstanceStruct['packageActualFee'] = 0>
			<cfset local.tmpInstanceStruct['tpRemoveInstance'] = 0>
			<cfset local.tmpInstanceStruct['arrPackageFields'] = arrayNew(1)>
			<cfset local.tmpInstanceStruct['arrTicketInstance'] = arrayNew(1)>
			<cfset local.tmpInstanceStruct['qryInstanceSeats'] = QueryNew("seatID,memberID","integer,integer")>

			<cfif arguments.packageStr.instances gt 1>
				<cfset local.tmpInstanceStruct.instanceTitle = local.tmpInstanceStruct.instanceTitle & " (#local.thisInstanceNum# of #arguments.packageStr.instances#)">
			</cfif>

			<cfif arguments.registrantID gt 0>
				<cfif ListLen(valueList(arguments.qryThisPackageInstances.instanceID)) gte local.thisInstanceNum>
					<cfset local.tmpInstanceStruct.instanceID = GetToken(valueList(arguments.qryThisPackageInstances.instanceID),local.thisInstanceNum,",")>
					<cfset local.tmpInstanceStruct.includedFromRate = NumberFormat(GetToken(valueList(arguments.qryThisPackageInstances.includedFromRate),local.thisInstanceNum,","))>
					<cfset local.tmpInstanceStruct.availablePriceID = GetToken(valueList(arguments.qryThisPackageInstances.availablePriceID),local.thisInstanceNum,",")>
					<cfset local.thisPackageInstancesIncFromRate = local.thisPackageInstancesIncFromRate + local.tmpInstanceStruct.includedFromRate>
					<cfquery name="local.tmpInstanceStruct.qryInstanceSeats" datasource="#application.dsn.membercentral.dsn#">
						select seatID, memberID
						from dbo.ev_registrantPackageInstanceSeats
						where instanceID = <cfqueryparam value="#local.tmpInstanceStruct.instanceID#" cfsqltype="CF_SQL_INTEGER">
					</cfquery>

					<cfquery name="local.qryThisTicketPackageOriginalPrice" dbtype="query">
						select ticketPackageAmount
						from arguments.qryThisPackageInstances
						where instanceID = #val(local.tmpInstanceStruct.instanceID)#
					</cfquery>
					<cfset local.tmpInstanceStruct['packageAmount'] = val(local.qryThisTicketPackageOriginalPrice.ticketPackageAmount)>

					<cfquery name="local.qryThisTicketPackageActualPrice" dbtype="query">
						select sum(amount) as amount
						from [local].qryRegTransactions
						where itemType = 'TicketPackInst'
						and itemID = #val(local.tmpInstanceStruct.instanceID)#
					</cfquery>
					<cfset local.tmpInstanceStruct['packageActualFee'] = val(local.qryThisTicketPackageActualPrice.amount)>
					<cfset local.tmpInstanceStruct['dispPackageActualFee'] = dollarformat(local.tmpInstanceStruct['packageActualFee'])>
				</cfif>

				<cfif local.tmpInstanceStruct.includedFromRate eq 1 and local.thisPackageInstancesIncFromRate gt arguments.packageStr.qtyIncludedByRate>
					<cfset local.tmpInstanceStruct.includedFromRate = 0>
					<cfset local.thisPackageInstancesIncFromRate = local.thisPackageInstancesIncFromRate - 1>
					<cfif structKeyExists(arguments.fd,'ticketPackageDebit_#arguments.packageStr.ticketPackageID#_#local.tmpInstanceStruct.instanceID#') and val(arguments.fd['ticketPackageDebit_#arguments.packageStr.ticketPackageID#_#local.tmpInstanceStruct.instanceID#']) gt 0>
						<cfset local.tmpInstanceStruct.availablePriceID = arguments.fd['ticketPackageDebit_#arguments.packageStr.ticketPackageID#_#local.tmpInstanceStruct.instanceID#']>
					</cfif>
				<cfelseif local.tmpInstanceStruct.includedFromRate eq 0 and local.thisPackageInstancesIncFromRate lt arguments.packageStr.qtyIncludedByRate>
					<cfif local.tmpInstanceStruct.instanceID gt 0 and structKeyExists(arguments.fd,'ticketPackageCredit_#arguments.packageStr.ticketPackageID#_#local.thisInstanceNum#') and val(arguments.fd['ticketPackageCredit_#arguments.packageStr.ticketPackageID#_#local.thisInstanceNum#']) gt 0>
						<cfset local.tmpInstanceStruct.includedFromRate = 1>
						<cfset local.thisPackageInstancesIncFromRate = local.thisPackageInstancesIncFromRate + 1>
					<cfelseif local.tmpInstanceStruct.instanceID eq 0>
						<cfset local.tmpInstanceStruct.includedFromRate = 1>
						<cfset local.thisPackageInstancesIncFromRate = local.thisPackageInstancesIncFromRate + 1>
					</cfif>
				</cfif>

				<cfif local.tmpInstanceStruct.includedFromRate eq 0 and ListLen(arguments.packageStr.ticketPackageAvailablePriceIDList) and local.tmpInstanceStruct.availablePriceID eq 0>
					<cfset local.tmpPriceID = getFirstRegPriceIDFromList(priceIDList=arguments.packageStr.ticketPackageAvailablePriceIDList)>
					<cfif local.tmpPriceID is 0>
						<cfset local.tmpPriceID = ListFirst(arguments.packageStr.ticketPackageAvailablePriceIDList)>
					</cfif>
					<cfif structKeyExists(arguments.fd,'ticketPackage_#arguments.packageStr.ticketPackageID#_#local.tmpPriceID#')>
						<cfset local.tmpThisPricePackageSelected = arguments.fd['ticketPackage_#arguments.packageStr.ticketPackageID#_#local.tmpPriceID#']>
					<cfelse>
						<cfset local.tmpThisPricePackageSelected = 0>
					</cfif>
					
					<cfif local.tmpThisPricePackageSelected gt 0>
						<cfset local.tmpInstanceStruct.availablePriceID = local.tmpPriceID>
						<cfset arguments.fd['ticketPackage_#arguments.packageStr.ticketPackageID#_#local.tmpPriceID#'] = local.tmpThisPricePackageSelected - 1>
						<cfif arguments.fd['ticketPackage_#arguments.packageStr.ticketPackageID#_#local.tmpPriceID#'] eq 0>
							<cfset arguments.packageStr.ticketPackageAvailablePriceIDList = ListDeleteAt(arguments.packageStr.ticketPackageAvailablePriceIDList,1)>
						</cfif>
					</cfif>
				</cfif>

				<cfif local.tmpInstanceStruct.instanceID gt 0 and structKeyExists(arguments.fd,'ticketPackageDebit_#arguments.packageStr.ticketPackageID#_#local.tmpInstanceStruct.instanceID#') and arguments.fd['ticketPackageDebit_#arguments.packageStr.ticketPackageID#_#local.tmpInstanceStruct.instanceID#'] eq 'remove'>
					<cfset local.tmpInstanceStruct.tpRemoveInstance = 1>
				</cfif>
			<cfelseif arguments.registrantID eq 0 and local.thisPackageInstancesIncFromRate lt arguments.packageStr.qtyIncludedByRate>
				<cfset local.tmpInstanceStruct.includedFromRate = 1>
				<cfset local.thisPackageInstancesIncFromRate = local.thisPackageInstancesIncFromRate + 1>
			</cfif>

			<cfif arguments.packageStr.hasPackageFields is 1>
				<cfset local.tmpInstanceStruct['arrPackageFields'] = prepareInstancePackageFieldsArr(ticketPackageID=arguments.packageStr.ticketPackageID, 
													packageFieldsXML=arguments.packageFieldsXML, instanceID=local.tmpInstanceStruct.instanceID, 
													registrantID=arguments.registrantID, eventAdminSiteResourceID=arguments.eventAdminSiteResourceID, 
													displayedCurrencyType=arguments.displayedCurrencyType)>
			</cfif>
			<cfif arguments.packageStr.hasTicketFields is 1>
				<cfset local.tmpInstanceStruct['arrTicketInstance'] = prepareInstanceTicketFieldsArr(ticketID=arguments.packageStr.ticketID, 
													ticketPackageID=arguments.packageStr.ticketPackageID, ticketFieldsXML=arguments.ticketFieldsXML, 
													instanceID=local.tmpInstanceStruct.instanceID, registrantID=arguments.registrantID, 
													ticketsInPackage=arguments.packageStr.ticketsInPackage, 
													qryInstanceSeats=local.tmpInstanceStruct['qryInstanceSeats'], 
													eventAdminSiteResourceID=arguments.eventAdminSiteResourceID,
													displayedCurrencyType=arguments.displayedCurrencyType)>
			</cfif>

			<cfset arrayAppend(local.thisPackageDetailsArr, local.tmpInstanceStruct)>
		</cfloop>

		<cfreturn local.thisPackageDetailsArr>
	</cffunction>

	<cffunction name="prepareInstancePackageFieldsArr" access="private" output="false" returntype="array">
		<cfargument name="ticketPackageID" type="numeric" required="true">
		<cfargument name="packageFieldsXML" type="xml" required="true">
		<cfargument name="instanceID" type="numeric" required="true">
		<cfargument name="registrantID" type="numeric" required="true">
		<cfargument name="eventAdminSiteResourceID" type="numeric" required="true">
		<cfargument name="displayedCurrencyType" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.objCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>

		<cfset local.JSONInsanityChars = "^~~~^">

		<cfscript>
			// default grouping fields
			local.arrGroupingFields = [{
				"fieldGroupingID":0,
				"fieldGrouping":'',
				"fieldGroupingDesc":'',
				"arrFields":[]
			}];
			local.strFGFields = {
				"0": xmlSearch(arguments.packageFieldsXML,"//fields/field[@fieldGroupingID='0']")
			};

			// grouping fields
			local.qryFieldGroupings = local.objCustomFields.getFieldUsageFieldGroupings(usageRT='Event', usageAN='TicketPackage', csrid=arguments.eventAdminSiteResourceID, detailID=arguments.ticketPackageID);
			
			for (local.strFieldGrouping in local.qryFieldGroupings) {
				local.strFGFields[local.strFieldGrouping.fieldGroupingID] = xmlSearch(arguments.packageFieldsXML,"//fields/field[@fieldGroupingID='#local.strFieldGrouping.fieldGroupingID#']");

				local.arrGroupingFields.append({
					"fieldGroupingID":local.strFieldGrouping.fieldGroupingID,
					"fieldGrouping":local.strFieldGrouping.fieldGrouping,
					"fieldGroupingDesc":local.strFieldGrouping.fieldGroupingDesc,
					"arrFields":[]
				});
			}
		</cfscript>

		<cfloop array="#local.arrGroupingFields#" index="local.thisGrouping">
			<cfloop array="#local.strFGFields[local.thisGrouping.fieldGroupingID]#" index="local.thisfield">
				<cfset local.tmpPackageFieldStr = structNew()>
				<cfset local.tmpPackageFieldStr['fieldID'] = local.thisfield.xmlattributes.fieldID>
				<cfset local.tmpPackageFieldStr['ticketPackageID'] = arguments.ticketPackageID>
				<cfset local.tmpPackageFieldStr['attributes'] = local.thisfield.xmlattributes>
				<cfset local.tmpPackageFieldStr['attributes']['fieldText'] = local.JSONInsanityChars & local.tmpPackageFieldStr['attributes']['fieldText']>
				<cfset local.tmpPackageFieldStr['dataTypeCode'] = local.thisfield.xmlattributes.dataTypeCode>
				<cfset local.tmpPackageFieldStr['displayTypeCode'] = local.thisfield.xmlattributes.displayTypeCode>
				<cfset local.tmpPackageFieldStr['fieldTypeCode'] = local.thisfield.xmlattributes.fieldTypeCode>
				<cfset local.tmpPackageFieldStr['supportAmt'] = val(local.thisfield.xmlattributes.supportAmt)>
				<cfset local.tmpPackageFieldStr['supportQty'] = val(local.thisfield.xmlattributes.supportQty)>
				<cfset local.tmpPackageFieldStr['isRequired'] = val(local.thisfield.xmlattributes.isRequired)>
				<cfset local.tmpPackageFieldStr['requiredMsg'] = local.thisfield.xmlattributes.requiredMsg>
				<cfset local.tmpPackageFieldStr['children'] = arrayNew(1)>
				<cfset local.tmpPackageFieldStr['allOptionEmptyOrDisabled'] = 0>
				<cfset local.tmpPackageFieldStr['displayedCurrencyType'] = arguments.displayedCurrencyType>
				<cfset local.tmpPackageFieldStr['dispAmount'] = "">
				<cfset local.tmpPackageFieldStr['value'] = "">
				<cfif abs(local.thisfield.xmlattributes.amount) gt 0>
					<cfset local.tmpPackageFieldStr['dispAmount'] = dollarformat(local.thisfield.xmlattributes.amount)>
				</cfif>
				<cfif local.thisfield.xmlattributes.fieldTypeCode eq 'NAMETEXTBOX'>
					<cfset local.tmpPackageFieldStr['isNameTextBoxField'] = 1>
					<cfset local.tmpPackageFieldStr['autoFillRegistrant'] = val(local.thisfield.xmlattributes.autoFillRegistrant)>
				<cfelse>
					<cfset local.tmpPackageFieldStr['isNameTextBoxField'] = 0>
				</cfif>

				<cfif arguments.instanceID gt 0>
					<cfif listFind("SELECT,RADIO,CHECKBOX",local.tmpPackageFieldStr.displayTypeCode)>
						<cfset local.tmpPackageFieldStr['value'] = local.objCustomFields.getFieldOptionsSelected(itemType='ticketPackInstCustom', itemID=arguments.instanceID, fieldID=local.tmpPackageFieldStr.fieldID)>
					<cfelse>
						<cfset local.tmpPackageFieldStr['value'] = local.objCustomFields.getFieldResponseEntered(itemType='ticketPackInstCustom', itemID=arguments.instanceID, fieldID=local.tmpPackageFieldStr.fieldID)>

						<cfif local.thisfield.xmlattributes.displayTypeCode is 'TEXTBOX'>
							<cfif local.thisfield.xmlattributes.supportQty is 1>
								<cfset local.tmpPackageFieldStr['value'] = val(local.tmpPackageFieldStr['value'])>
								<cfset local.tmpPackageFieldStr['qtyIDArr'] = arrayNew(1)>

								<cfif local.thisfield.xmlattributes.supportAmt is 1 and val(local.tmpPackageFieldStr['value']) gt 0>
									<cfset local.qtyIDs = local.objCustomFields.getFieldQTYDetails(itemType='ticketPackInstCustom', itemID=arguments.instanceID, fieldID=local.tmpPackageFieldStr.fieldID, applicationType='Events', trItemType='ticketPackInstCustom')>
									<cfloop query="local.qtyIDs">
										<cfset local.tmpQtyIDStr = structNew()>
										<cfset local.tmpQtyIDStr['row'] = local.qtyIDs.currentrow>
										<cfset local.tmpQtyIDStr['subItemID'] = local.qtyIDs.subItemID>
										<cfset local.tmpQtyIDStr['detail'] = local.qtyIDs.detail>
										<cfset local.tmpQtyIDStr['amount'] = dollarformat(local.qtyIDs.amount)>
										<cfset arrayAppend(local.tmpPackageFieldStr['qtyIDArr'], local.tmpQtyIDStr)>
									</cfloop>
								</cfif>
							<cfelseif local.thisfield.xmlattributes.supportAmt is 1>
								<cfset local.tmpPackageFieldStr['value'] = numberFormat(local.tmpPackageFieldStr['value'],'9.99')>
								<cfset local.tmpPackageFieldStr['actualFee'] = local.objCustomFields.getFieldActualFee(itemType='ticketPackInstCustom', itemID=arguments.instanceID, fieldID=local.tmpPackageFieldStr.fieldID, applicationType='Events', trItemType='ticketPackInstCustom')>
								<cfset local.tmpPackageFieldStr['dispActualFee'] = dollarformat(local.tmpPackageFieldStr['actualFee'])>
							</cfif>
						</cfif>
					</cfif>
				</cfif>

				<cfif local.thisfield.xmlattributes.displayTypeCode eq 'TEXTBOX' and local.thisfield.xmlattributes.supportQty is 1>
					<cfset local.maxQtyAllowed = 99999>
					<cfif local.thisfield.xmlattributes.fieldInventory gt 0>
						<cfif local.thisfield.xmlattributes.fieldInventory lte local.thisfield.xmlattributes.fieldinventoryCount>
							<cfset local.maxQtyAllowed = 0>
							<cfset local.tmpPackageFieldStr['isRequired'] = 0>
						<cfelse>
							<cfset local.maxQtyAllowed = local.thisfield.xmlattributes.fieldInventory-local.thisfield.xmlattributes.fieldinventoryCount>
							<!--- edit case of non monetary qty-field --->
							<cfif local.thisfield.xmlattributes.supportAmt is 0 and val(local.tmpPackageFieldStr['value']) gt 0>
								<cfset local.maxQtyAllowed = local.maxQtyAllowed + val(local.tmpPackageFieldStr['value'])>
							</cfif>
						</cfif>
					</cfif>
					<cfset local.tmpPackageFieldStr['maxQtyAllowed'] = local.maxQtyAllowed>
				</cfif>

				<!--- dont show question at all if select,radio,checkbox and no options defined --->
				<cfif listFind("SELECT,RADIO,CHECKBOX",local.tmpPackageFieldStr.displayTypeCode)>
					<cfif arrayLen(local.thisfield.xmlchildren) is 0>
						<cfset local.tmpPackageFieldStr.allOptionEmptyOrDisabled = 1>
					<cfelse>
						<cfloop array="#local.thisfield.xmlchildren#" index="local.thisoption">
							<cfset local.tmpOptionStr = structNew()>
							<cfset local.tmpOptionStr['attributes'] = local.thisoption.xmlattributes>
							<cfset local.tmpOptionStr['attributes']['fieldValue'] = local.JSONInsanityChars & local.tmpOptionStr['attributes']['fieldValue']>
							<cfset local.tmpOptionStr['unavailable'] = 0>
							<cfset local.tmpOptionStr['dispAmount'] = "">
							<cfset local.tmpOptionStr['actualFee'] = "">
							<cfif abs(local.thisoption.xmlattributes.amount) gt 0>
								<cfset local.tmpOptionStr['dispAmount'] = dollarformat(local.thisoption.xmlattributes.amount)>
							</cfif>
							<!--- skip unavailability check for an already selected option --->
							<cfif len(local.tmpPackageFieldStr['value']) and listFind(local.tmpPackageFieldStr['value'],local.thisoption.xmlattributes.valueID)>
								<cfif arguments.instanceID gt 0>
									<cfset local.tmpOptionStr['actualFee'] = local.objCustomFields.getFieldOptionActualFee(itemType='ticketPackInstCustom', itemID=arguments.instanceID, 
										fieldID=local.tmpPackageFieldStr.fieldID, valueID=local.thisoption.xmlattributes.valueID, applicationType='Events', trItemType='ticketPackInstCustom')>
									<cfset local.tmpOptionStr['dispActualFee'] = dollarformat(local.tmpOptionStr['actualFee'])>
								</cfif>
							<cfelseif local.thisoption.xmlattributes.optionInventory gt 0 and local.thisoption.xmlattributes.optionInventory lte local.thisoption.xmlattributes.optioninventoryCount>
								<cfset local.tmpOptionStr.unavailable = 1>
							<cfelseif local.thisfield.xmlattributes.supportQty is 1 and local.thisfield.xmlattributes.fieldInventory gt 0 and local.thisfield.xmlattributes.fieldInventory lte local.thisfield.xmlattributes.fieldInventoryCount>
									<cfset local.tmpOptionStr.unavailable = 1>
							</cfif>
							<cfset arrayAppend(local.tmpPackageFieldStr.children, local.tmpOptionStr)>
						</cfloop>

						<cfif local.tmpPackageFieldStr.displayTypeCode eq 'CHECKBOX' and len(local.tmpPackageFieldStr['value'])>
							<cfset local.tmpPackageFieldStr['value'] = listToArray(local.tmpPackageFieldStr['value'])>
						</cfif>
					</cfif>
				<!--- append json insanity vars for other display type values --->
				<cfelseif len(local.tmpPackageFieldStr['value'])>
					<cfset local.tmpPackageFieldStr['value'] = local.JSONInsanityChars & local.tmpPackageFieldStr['value']>
				</cfif>
		
				<cfset arrayAppend(local.thisGrouping.arrFields, local.tmpPackageFieldStr)>
			</cfloop>
		</cfloop>
		
		<cfreturn local.arrGroupingFields>
	</cffunction>

	<cffunction name="prepareInstanceTicketFieldsArr" access="private" output="false" returntype="array">
		<cfargument name="ticketID" type="numeric" required="true">
		<cfargument name="ticketPackageID" type="numeric" required="true">
		<cfargument name="ticketFieldsXML" type="xml" required="true">
		<cfargument name="instanceID" type="numeric" required="true">
		<cfargument name="registrantID" type="numeric" required="true">
		<cfargument name="ticketsInPackage" type="numeric" required="true">
		<cfargument name="qryInstanceSeats" type="query" required="true">
		<cfargument name="eventAdminSiteResourceID" type="numeric" required="true">
		<cfargument name="displayedCurrencyType" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.objCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>

		<cfset local.JSONInsanityChars = "^~~~^">
		<cfset local.thisTicketFieldInstanceArr = []>
		
		<cfscript>
			// default grouping fields
			local.arrGroupingFields = [{
				"fieldGroupingID":0,
				"fieldGrouping":'',
				"fieldGroupingDesc":'',
				"arrFields":[]
			}];
			local.strFGFields = {
				"0": xmlSearch(arguments.ticketFieldsXML,"//fields/field[@fieldGroupingID='0']")
			};

			// grouping fields
			local.qryFieldGroupings = local.objCustomFields.getFieldUsageFieldGroupings(usageRT='Event', usageAN='Ticket', csrid=arguments.eventAdminSiteResourceID, detailID=arguments.ticketID);
			
			for (local.strFieldGrouping in local.qryFieldGroupings) {
				local.strFGFields[local.strFieldGrouping.fieldGroupingID] = xmlSearch(arguments.ticketFieldsXML,"//fields/field[@fieldGroupingID='#local.strFieldGrouping.fieldGroupingID#']");

				local.arrGroupingFields.append({
					"fieldGroupingID":local.strFieldGrouping.fieldGroupingID,
					"fieldGrouping":local.strFieldGrouping.fieldGrouping,
					"fieldGroupingDesc":local.strFieldGrouping.fieldGroupingDesc,
					"arrFields":[]
				});
			}
		</cfscript>

		<cfloop from="1" to="#arguments.ticketsInPackage#" index="local.thisSeatNum">
			<cfset local.thisTicketFieldsArr = duplicate(local.arrGroupingFields)>
			<cfloop array="#local.thisTicketFieldsArr#" index="local.thisGrouping">
				<cfloop array="#local.strFGFields[local.thisGrouping.fieldGroupingID]#" index="local.thisfield">
					<cfset local.tmpTicketFieldStr = structNew()>
					<cfset local.tmpTicketFieldStr['ticketID'] = arguments.ticketID>
					<cfset local.tmpTicketFieldStr['ticketPackageID'] = arguments.ticketPackageID>
					<cfset local.tmpTicketFieldStr['instanceID'] = arguments.instanceID>
					<cfset local.tmpTicketFieldStr['fieldID'] = local.thisfield.xmlattributes.fieldID>
					<cfset local.tmpTicketFieldStr['attributes'] = local.thisfield.xmlattributes>
					<cfset local.tmpTicketFieldStr['attributes']['fieldText'] = local.JSONInsanityChars & local.tmpTicketFieldStr['attributes']['fieldText']>
					<cfset local.tmpTicketFieldStr['dataTypeCode'] = local.thisfield.xmlattributes.dataTypeCode>
					<cfset local.tmpTicketFieldStr['displayTypeCode'] = local.thisfield.xmlattributes.displayTypeCode>
					<cfset local.tmpTicketFieldStr['fieldTypeCode'] = local.thisfield.xmlattributes.fieldTypeCode>
					<cfset local.tmpTicketFieldStr['supportAmt'] = val(local.thisfield.xmlattributes.supportAmt)>
					<cfset local.tmpTicketFieldStr['supportQty'] = val(local.thisfield.xmlattributes.supportQty)>
					<cfset local.tmpTicketFieldStr['isRequired'] = val(local.thisfield.xmlattributes.isRequired)>
					<cfset local.tmpTicketFieldStr['requiredMsg'] = local.thisfield.xmlattributes.requiredMsg>
					<cfset local.tmpTicketFieldStr['children'] = arrayNew(1)>
					<cfset local.tmpTicketFieldStr['allOptionEmptyOrDisabled'] = 0>
					<cfset local.tmpTicketFieldStr['displayedCurrencyType'] = arguments.displayedCurrencyType>
					<cfset local.tmpTicketFieldStr['dispAmount'] = "">
					<cfset local.tmpTicketFieldStr['seatNum'] = local.thisSeatNum>
					<cfset local.tmpTicketFieldStr['seatID'] = 0>
					<cfset local.tmpTicketFieldStr['value'] = "">
					<cfif abs(local.thisfield.xmlattributes.amount) gt 0>
						<cfset local.tmpTicketFieldStr['dispAmount'] = dollarformat(local.thisfield.xmlattributes.amount)>
					</cfif>
					<cfif local.thisfield.xmlattributes.fieldTypeCode eq 'NAMETEXTBOX'>
						<cfset local.tmpTicketFieldStr['isNameTextBoxField'] = 1>
						<cfset local.tmpTicketFieldStr['autoFillRegistrant'] = val(local.thisfield.xmlattributes.autoFillRegistrant)>
					<cfelse>
						<cfset local.tmpTicketFieldStr['isNameTextBoxField'] = 0>
					</cfif>

					<cfif arguments.instanceID gt 0>
						<cfset local.thisSeatID = 0>
						<cfset local.thisValue = "">
						<cfif arguments.instanceID gt 0 and arguments.qryInstanceSeats.recordCount gte local.thisSeatNum>
							<cfset local.thisSeatID = GetToken(valueList(arguments.qryInstanceSeats.seatID),local.thisSeatNum,",")>
							<cfif listFind("SELECT,RADIO,CHECKBOX",local.tmpTicketFieldStr.displayTypeCode)>
								<cfset local.thisValue = local.objCustomFields.getFieldOptionsSelected(itemType='ticketPackSeatCustom', itemID=local.thisSeatID, fieldID=local.tmpTicketFieldStr.fieldID)>
							<cfelse>
								<cfset local.thisValue = local.objCustomFields.getFieldResponseEntered(itemType='ticketPackSeatCustom', itemID=local.thisSeatID, fieldID=local.tmpTicketFieldStr.fieldID)>

								<cfif local.tmpTicketFieldStr.displayTypeCode eq 'TEXTBOX'>
									<cfif local.thisfield.xmlattributes.supportQty is 1>
										<cfset local.thisValue = val(local.thisValue)>
										<cfset local.tmpTicketFieldStr['qtyIDArr'] = arrayNew(1)>

										<cfif local.thisfield.xmlattributes.supportAmt is 1 and val(local.thisValue) gt 0>
											<cfset local.qtyIDs = local.objCustomFields.getFieldQTYDetails(itemType='ticketPackSeatCustom', itemID=local.thisSeatID, fieldID=local.tmpTicketFieldStr.fieldID, applicationType='Events', trItemType='ticketPackSeatCustom')>
											<cfloop query="local.qtyIDs">
												<cfset local.tmpQtyIDStr = structNew()>
												<cfset local.tmpQtyIDStr['row'] = local.qtyIDs.currentrow>
												<cfset local.tmpQtyIDStr['subItemID'] = local.qtyIDs.subItemID>
												<cfset local.tmpQtyIDStr['detail'] = local.qtyIDs.detail>
												<cfset local.tmpQtyIDStr['amount'] = dollarformat(local.qtyIDs.amount)>
												<cfset arrayAppend(local.tmpTicketFieldStr['qtyIDArr'], local.tmpQtyIDStr)>
											</cfloop>
										</cfif>
									<cfelseif local.thisfield.xmlattributes.supportAmt is 1>
										<cfset local.thisValue = numberFormat(local.thisValue,'9.99')>
										<cfset local.tmpTicketFieldStr['actualFee'] = local.objCustomFields.getFieldActualFee(itemType='ticketPackSeatCustom', itemID=local.thisSeatID, fieldID=local.tmpTicketFieldStr.fieldID, applicationType='Events', trItemType='ticketPackSeatCustom')>
										<cfset local.tmpTicketFieldStr['dispActualFee'] = dollarformat(local.tmpTicketFieldStr['actualFee'])>
									</cfif>
								</cfif>
							</cfif>
						</cfif>
						
						<cfset local.tmpTicketFieldStr['seatID'] = local.thisSeatID>
						<cfset local.tmpTicketFieldStr['value'] = local.thisValue>
					</cfif>

					<cfif local.thisfield.xmlattributes.displayTypeCode eq 'TEXTBOX' and local.thisfield.xmlattributes.supportQty is 1>
						<cfset local.maxQtyAllowed = 99999>
						<cfif local.thisfield.xmlattributes.fieldInventory gt 0>
							<cfif local.thisfield.xmlattributes.fieldInventory lte local.thisfield.xmlattributes.fieldinventoryCount>
								<cfset local.maxQtyAllowed = 0>
								<cfset local.tmpTicketFieldStr['isRequired'] = 0>
							<cfelse>
								<cfset local.maxQtyAllowed = local.thisfield.xmlattributes.fieldInventory-local.thisfield.xmlattributes.fieldinventoryCount>
								<!--- edit case of non monetary qty-field --->
								<cfif local.thisfield.xmlattributes.supportAmt is 0 and val(local.tmpTicketFieldStr['value']) gt 0>
									<cfset local.maxQtyAllowed = local.maxQtyAllowed + val(local.tmpTicketFieldStr['value'])>
								</cfif>
							</cfif>
						</cfif>
						<cfset local.tmpTicketFieldStr['maxQtyAllowed'] = local.maxQtyAllowed>
					</cfif>

					<!--- dont show question at all if select,radio,checkbox and no options defined --->
					<cfif listFind("SELECT,RADIO,CHECKBOX",local.tmpTicketFieldStr.displayTypeCode)>
						<cfif arrayLen(local.thisfield.xmlchildren) is 0>
							<cfset local.tmpTicketFieldStr.allOptionEmptyOrDisabled = 1>
						<cfelse>
							<cfloop array="#local.thisfield.xmlchildren#" index="local.thisoption">
								<cfset local.tmpOptionStr = structNew()>
								<cfset local.tmpOptionStr['attributes'] = local.thisoption.xmlattributes>
								<cfset local.tmpOptionStr['attributes']['fieldValue'] = local.JSONInsanityChars & local.tmpOptionStr['attributes']['fieldValue']>
								<cfset local.tmpOptionStr['unavailable'] = 0>
								<cfset local.tmpOptionStr['dispAmount'] = "">
								<cfset local.tmpOptionStr['actualFee'] = "">
								<cfif abs(local.thisoption.xmlattributes.amount) gt 0>
									<cfset local.tmpOptionStr['dispAmount'] = dollarformat(local.thisoption.xmlattributes.amount)>
								</cfif>
								<!--- skip unavailability check for an already selected option --->
								<cfif len(local.tmpTicketFieldStr['value']) and listFind(local.tmpTicketFieldStr['value'],local.thisoption.xmlattributes.valueID)>
									<cfif local.tmpTicketFieldStr['seatID'] gt 0>
										<cfset local.tmpOptionStr['actualFee'] = local.objCustomFields.getFieldOptionActualFee(itemType='ticketPackSeatCustom', itemID=local.tmpTicketFieldStr['seatID'], 
											fieldID=local.tmpTicketFieldStr.fieldID, valueID=local.thisoption.xmlattributes.valueID, applicationType='Events', trItemType='ticketPackSeatCustom')>
										<cfset local.tmpOptionStr['dispActualFee'] = dollarformat(local.tmpOptionStr['actualFee'])>
									</cfif>
								<cfelseif local.thisoption.xmlattributes.optionInventory gt 0 and local.thisoption.xmlattributes.optionInventory lte local.thisoption.xmlattributes.optioninventoryCount>
									<cfset local.tmpOptionStr.unavailable = 1>
								<cfelseif local.thisfield.xmlattributes.supportQty is 1 and local.thisfield.xmlattributes.fieldInventory gt 0 and local.thisfield.xmlattributes.fieldInventory lte local.thisfield.xmlattributes.fieldInventoryCount>
									<cfset local.tmpOptionStr.unavailable = 1>
								</cfif>
								<cfset arrayAppend(local.tmpTicketFieldStr.children, local.tmpOptionStr)>
							</cfloop>

							<cfif local.tmpTicketFieldStr.displayTypeCode eq 'CHECKBOX' and len(local.tmpTicketFieldStr['value'])>
								<cfset local.tmpTicketFieldStr['value'] = listToArray(local.tmpTicketFieldStr['value'])>
							</cfif>
						</cfif>
					<!--- append json insanity vars for other display type values --->
					<cfelseif len(local.tmpTicketFieldStr['value'])>
						<cfset local.tmpTicketFieldStr['value'] = local.JSONInsanityChars & local.tmpTicketFieldStr['value']>
					</cfif>

					<cfset arrayAppend(local.thisGrouping.arrFields, local.tmpTicketFieldStr)>
				</cfloop>
			</cfloop>
			<cfset arrayAppend(local.thisTicketFieldInstanceArr, local.thisTicketFieldsArr)>
		</cfloop>

		<cfreturn local.thisTicketFieldInstanceArr>
	</cffunction>

	<cffunction name="getSelectedAssetCategoryDetails" access="public" output="false" returntype="query">
		<cfargument name="siteResourceID" type="numeric" required="true">
		
		<cfset var qrySelectedAssetCategories = "">
	
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qrySelectedAssetCategories">
			select csr.categoryID, c.categoryName, c.parentCategoryID
			from dbo.cms_categorySiteResources as csr 
			inner join dbo.cms_categories as c on c.categoryID = csr.categoryID
			where csr.siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">
			and c.isActive = 1;
		</cfquery>

		<cfreturn qrySelectedAssetCategories>
	</cffunction>

	<cffunction name="getRegistrantDataForEmailing" access="public" output="false" returntype="struct">
		<cfargument name="registrantID" type="numeric" required="true">
		<cfargument name="recipientType" type="string" required="true">
		<cfargument name="mode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.objEvents = createObject("component","model.events.events")>

		<!--- get event info --->
		<cfquery name="local.qryRegistrant" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select top 1 reg.eventid, r.recordedOnSiteID, s.sitecode, s.orgID
			from dbo.ev_registrants as r
			inner join dbo.sites as s on s.siteid = r.recordedOnSiteID
			inner join dbo.ev_registration as reg on reg.registrationID = r.registrationID and reg.siteID = s.siteID
			where r.registrantID = <cfqueryparam value="#arguments.registrantID#" cfsqltype="CF_SQL_INTEGER">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(local.qryRegistrant.siteCode)>
		<cfset local.siteID = local.mc_siteInfo.siteID>
		<cfset local.orgID = local.mc_siteInfo.orgID>

		<cfset local.retStruct = { qryData=getRegistrantDetails(registrantID=arguments.registrantID, orgID=local.orgID, siteID=local.siteID), extendedLinkedMergeCode="", arrResTypeMergeCodes=arrayNew(1), eventData=structNew() }>

		<cfset local.eventAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin', siteID=local.siteID)>
		<cfset local.strEvent = local.objEvents.getEvent(eventID=local.retStruct.qryData.eventID, siteID=local.siteID, languageID=1)>
		<cfset local.ratename = getRateName(rateID=val(local.retStruct.qryData.rateID))>

		<cfset local.showTimeZone = true>
		<cfif local.mc_siteInfo.defaultTimeZoneID eq local.strEvent.qryEventTimes_selected.timezoneID and local.strEvent.qryEventMeta.alwaysShowEventTimezone eq 0>
			<cfset local.showTimeZone = false>
		</cfif>
						
		<!--- event dates/times --->
		<cfsavecontent variable="local.eventtime">
			<cfif local.strEvent.qryEventMeta.isAllDayEvent>
				<cfoutput>#DateFormat(local.strEvent.qryEventTimes_selected.startTime, "ddd, mmmm d, yyyy")#</cfoutput>
				<cfif DateCompare(local.strEvent.qryEventTimes_selected.endTime,local.strEvent.qryEventTimes_selected.startTime,"d")>
					<cfoutput> to </cfoutput>
					<cfoutput>#DateFormat(local.strEvent.qryEventTimes_selected.endTime, "ddd, mmmm d, yyyy")#</cfoutput>
				</cfif>
			<cfelse>
				<cfoutput>#DateFormat(local.strEvent.qryEventTimes_selected.startTime, "ddd, mmmm d, yyyy")# </cfoutput>
				<cfif DateCompare(local.strEvent.qryEventTimes_selected.endTime,local.strEvent.qryEventTimes_selected.startTime,"d") is 0 and
					DateDiff("n",local.strEvent.qryEventTimes_selected.endTime,local.strEvent.qryEventTimes_selected.startTime) is 0>
					<cfoutput>#TimeFormat(local.strEvent.qryEventTimes_selected.startTime, "h:mm TT")# <cfif local.showTimeZone>#local.strEvent.qryEventTimes_selected.timeZoneAbbr#</cfif></cfoutput>
				<cfelseif DateCompare(local.strEvent.qryEventTimes_selected.endTime,local.strEvent.qryEventTimes_selected.startTime,"d") is 0>
					<cfoutput>#TimeFormat(local.strEvent.qryEventTimes_selected.startTime, "h:mm TT")# </cfoutput>
					<cfoutput>- #timeformat(local.strEvent.qryEventTimes_selected.endTime,"h:mm TT")# <cfif local.showTimeZone>#local.strEvent.qryEventTimes_selected.timeZoneAbbr#</cfif></cfoutput>
				<cfelse>
					<cfoutput>#TimeFormat(local.strEvent.qryEventTimes_selected.startTime, "h:mm TT")# to </cfoutput>
					<cfoutput>#DateFormat(local.strEvent.qryEventTimes_selected.endTime, "ddd, mmmm d, yyyy")#</cfoutput>
					<cfoutput> #TimeFormat(local.strEvent.qryEventTimes_selected.endTime, "h:mm TT")# <cfif local.showTimeZone>#local.strEvent.qryEventTimes_selected.timeZoneAbbr#</cfif></cfoutput>
				</cfif>
			</cfif>
		</cfsavecontent>

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = local.mc_siteInfo.mainhostname>
			<cfset local.thisScheme = local.mc_siteInfo.scheme>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			<cfset local.thisScheme = (application.objPlatform.isRequestSecure() ? 'https' : 'http')>
		</cfif>

		<cfset local.eventURL = "#local.thisScheme#://#local.thisHostname#/?#application.objApplications.getAppBaseLink(applicationInstanceID=local.retStruct.qryData.applicationInstanceID, siteID=local.siteID)#&evAction=showDetail&eid=#local.strEvent.qryEventMeta.eventID#">
		<cfset local.retStruct.eventData = { registrantID=local.retStruct.qryData.registrantID, 
											EventURL=local.eventURL, EventTitle=local.strEvent.qryEventMeta.EventContentTitle, EventCode=local.strEvent.qryEventMeta.reportCode,
											EventTime=local.eventtime, ContactTitle=local.strEvent.qryEventMeta.contactContentTitle, 
											LocationTitle=local.strEvent.qryEventMeta.locationContentTitle, 
											CancellationTitle=local.strEvent.qryEventMeta.cancelContentTitle, 
											TravelTitle=local.strEvent.qryEventMeta.travelContentTitle, 
											Categories=replace(ValueList(local.strEvent.qryEventCategories.category,'|'),'|',', ','ALL'), 
											RegistrantRateName=htmlEditFormat(local.ratename), RegistrantRole=local.retStruct.qryData.roleList }>

		<cfset local.qrySelectedAssetCategories = getSelectedAssetCategoryDetails(siteResourceID=local.strEvent.qryEventMeta.siteResourceID)>
		<cfset local.qryAssetCategories = CreateObject('component', 'model.system.platform.category').getCategories(siteResourceID=local.eventAdminSiteResourceID, categoryTreeName='Event Asset Types')>
		<cfloop query="local.qryAssetCategories">
			<cfquery name="local.qryThisAssetType" dbtype="query">
				select categoryName
				from [local].qrySelectedAssetCategories
				where parentCategoryID = #val(local.qryAssetCategories.categoryID)#
			</cfquery>
			<cfset local.retStruct.eventData['evAsset_#local.qryAssetCategories.categoryName#'] = replace(valueList(local.qryThisAssetType.categoryName,'|'),"|",", ","all")>
		</cfloop>
		<cfset local.retStruct.eventData["strEventRoleFieldData"] = getEventRoleFieldData(controllingSiteResourceID=local.eventAdminSiteResourceID, registrantID=local.retStruct.qryData.registrantID)>
		<cfset local.retStruct.eventData["strEventRoleMembers"] = getEventRoleMembers(controllingSiteResourceID=local.eventAdminSiteResourceID, eventID=local.strEvent.qryEventMeta.eventID, orgID=local.orgID, siteID=local.siteID)>
		<cfset local.retStruct.eventData["strEventFieldData"] = getSiteEventCustomFieldDetails(containerSiteResourceID=local.eventAdminSiteResourceID, itemSiteResourceID=local.strEvent.qryEventMeta.siteResourceID)>
		<cfset local.retStruct.eventData["strEventRegFieldData"] = getEventRegFieldData(siteResourceID=local.strEvent.qryEventMeta.siteResourceID, registrantID=local.retStruct.qryData.registrantID)>

		<cfif arguments.recipientType eq 'AttendeeCertificates'>
			<cfswitch expression="#arguments.mode#">
				<cfcase value="preview">
					<cfset local.retStruct.mailAttach = "certificate.pdf">
				</cfcase>
				<cfcase value="testemail">
					<cfset local.strCertificate = createObject("component","certificate").generateCertificate(registrantID=local.retStruct.qryData.registrantID)>
					<cfif structKeyExists(local.strCertificate,"certificatePath")>
						<!--- wait one second for the pdf to appear on the server --->
						<cfloop from="1" to="5" index="local.count">
							<cfif NOT fileExists("#local.strCertificate.certificatePath#")>
								<cfset createObject("java","java.lang.Thread").sleep(1000)>
							<cfelse>
								<cfbreak>
							</cfif>
						</cfloop>
							
						<cfif fileExists(local.strCertificate.certificatePath)>
							<cfset local.retStruct.mailAttach = [ { file="certificate.pdf", folderpath=local.strCertificate.certificateFolderPath } ]>
						</cfif>
					</cfif>
				</cfcase>
			</cfswitch>
		</cfif>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getFilteredRegistrantsForEmailing" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = { itemIDList='', toolType='EventAdmin', catTreeCode='ETEVENTS', extendedLinkedMergeCode='', 
									extraMergeTagList='evEventTime|evEventURL', errorCode='' }>

		<cfif arguments.event.getValue('eID',0) gt 0>
			<cfset local.mode = "regTabEmail">
		<cfelse>
			<cfset local.mode = "regSearchEmail">
		</cfif>

		<cfset local.retStruct.operationMode = local.mode>
		<cfset local.retStruct.qryRegistrants = getRegistrantsFromFilters(event=arguments.event, mode=local.mode)>

		<cfif local.retStruct.qryRegistrants.recordcount is 0>
			<cfset local.retStruct.errorCode = 'norecipient'>
			<cfreturn local.retStruct>
		<cfelse>
			<cfset local.retStruct.itemIDList = valueList(local.retStruct.qryRegistrants.registrantID)>
		</cfif>

		<!--- no email ids defined for all recipients --->
		<cfif val(local.retStruct.qryRegistrants.membersWithEmail) eq 0>
			<cfset local.retStruct.errorCode = 'noemailrecipient'>
			<cfreturn local.retStruct>
		</cfif>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getFilteredAttendeesForEmailing" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = { itemIDList='', toolType='EventAdmin', catTreeCode='ETEVENTS', extendedLinkedMergeCode='', 
									extraMergeTagList='evEventTime|evEventURL', errorCode='' }>

		<cfset local.retStruct.qryRegistrants = getRegistrantsFromFilters(event=arguments.event, mode='emailCertAttendees')>

		<cfif local.retStruct.qryRegistrants.recordcount is 0>
			<cfset local.retStruct.errorCode = 'norecipient'>
			<cfreturn local.retStruct>
		<cfelse>
			<cfset local.retStruct.itemIDList = "#arguments.event.getValue('eID')#|#valueList(local.retStruct.qryRegistrants.registrantID)#">
		</cfif>

		<!--- no email ids defined for all recipients --->
		<cfif val(local.retStruct.qryRegistrants.membersWithEmail) eq 0>
			<cfset local.retStruct.errorCode = 'noemailrecipient'>
			<cfreturn local.retStruct>
		</cfif>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getRegistrantDetails" access="private" output="no" returntype="query">
		<cfargument name="registrantID" type="numeric" required="yes">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">

		<cfset var qryRegistrant = "">

		<cfquery name="qryRegistrant" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">, 
				@siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			
			select r.registrantID, m.memberID, reg.eventID, r.rateID, c.applicationInstanceID, STRING_AGG(cat.categoryName,', ') as roleList
			from dbo.ev_registrants as r
			inner join dbo.ev_registration as reg on reg.siteID = @siteID
				and reg.registrationID = r.registrationID
			inner join dbo.ams_members as m on m.orgID = @orgID 
				and m.memberID = r.memberID
			inner join dbo.ev_calendarEvents as ce on ce.sourceEventID = reg.eventID 
				and ce.calendarID = ce.sourceCalendarID
			inner join dbo.ev_calendars as c on c.siteID = @siteID
				and c.calendarID = ce.calendarID
			left outer join dbo.ev_registrantCategories as rc 
				inner join dbo.cms_categories as cat on cat.categoryID = rc.categoryID
				on rc.registrantID = r.registrantID
			where r.recordedOnSiteID = @siteID 
			and r.registrantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.registrantID#">
			group by r.registrantID, m.memberID, reg.eventID, r.rateID, c.applicationInstanceID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryRegistrant>
	</cffunction>

	<cffunction name="getRegistrantsFromFilters" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="mode" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
		<cfset local.strItemIDSQLVariable = { EventRegCustom='r.registrantID', EventRole="r.registrantID", CrossEvent="e.siteResourceID" }>
		<cfset local.strFields = createObject("component","model.admin.common.modules.customFields.customFields").generateFieldFilterSQL(rc=arguments.event.getCollection(), fieldIDPrefix='rFieldID', 
				fieldExpPrefix='RF', strItemIDSQLVariable=local.strItemIDSQLVariable)>

		<cfif arguments.mode eq "regSearchGrid">
			<cfset local.arrCols = arrayNew(1)>
			<cfset arrayAppend(local.arrCols,"(m.lastname + m.firstname + m.membernumber)")>
			<cfset arrayAppend(local.arrCols,"r.dateRegistered")>
			<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>
		<cfelseif listFindNoCase("regSearchEmailGrid,regTabEmailGrid,previewEmailCertAttendeesGrid,regTabBadgeGrid,regTabSignInSheet",arguments.mode)>
			<cfset arguments.event.paramValue('direct',arguments.event.getValue('orderDir','ASC'))>
			<cfset local.orderby = "(m.lastname + m.firstname + m.membernumber)">
			<cfif arguments.event.getValue('direct') eq "DES"><cfset arguments.event.setValue('direct','DESC')></cfif>
		<cfelseif listFindNoCase("regTabGrid,massRemoveRegistrants",arguments.mode)>
			<cfset local.arrCols = arrayNew(1)>
			<cfset arrayAppend(local.arrCols,"(m.lastname + m.firstname + m.membernumber)")>
			<cfset arrayAppend(local.arrCols,"r.dateRegistered")>
			<cfset arrayAppend(local.arrCols,"r.registrantID")>
			<cfset arrayAppend(local.arrCols,"regFee.totalRegFee")>
			<cfset arrayAppend(local.arrCols,"regFee.totalRegFee-regFee.regFeePaid")>
			<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby',0)+1]>
		</cfif>

		<cfif listFindNoCase("regSearchGrid,regSearchGridExport,regSearchEmailGrid,regSearchEmail",arguments.mode)>
			<cfset local.sourceApp = "regSearch">
		<cfelse>
			<cfset local.sourceApp = "regTab">
		</cfif>

		<cfif arguments.mode eq "regTabSignInSheet">
			<cfset local.resultsFieldsetID = arguments.event.getValue('fsid',0)>
		</cfif>

		<cfset local.rCreditType = arguments.event.getTrimValue('rCreditType','')>

		<cfif arguments.mode eq "regSearchGridExport">
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cfset local.resultsFieldsetID = arguments.event.getValue('fsid',0)>
			<cfset local.tmpSuffix = replace(createUUID(),'-','','ALL')>
		</cfif>

		<cfset local.EventAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteID'))>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRegistrants" result="local.qryRegistrantsResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int, @orgID int, @rDateFrom date, @rDateTo datetime, @rAttended bit, @rCompany varchar(200),	
					@evCalendarID int, @evCategoryID int, @evKeyword varchar(200), @evReportCode varchar(15), 
					@rAssociatedMemberID int, @rAssociatedGroupID int, @evDateFrom date, @evDateTo datetime, @currentMemberID int, 
					@rBillFrom decimal(14,2), @rBillTo decimal(14,2), @rDuesFrom decimal(14,2), @rDuesTo decimal(14,2),
					@rCreditType int, @eventID int, @rRegistrantID int, @outputFieldsXML xml;	
				#PreserveSingleQuotes(local.strFields.filterSetupSQL)#
			
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
				SET @currentMemberID = <cfqueryparam value="#session.cfcuser.memberdata.memberID#" cfsqltype="CF_SQL_INTEGER">;

				<cfif local.sourceApp eq "regSearch">
					<cfif arguments.event.getValue('evDateFrom','') NEQ ''>
						SET @evDateFrom = <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getValue('evDateFrom')#">;
					</cfif>
					<cfif arguments.event.getValue('evDateTo','') NEQ ''>
						SET @evDateTo = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getValue('evDateTo')# 23:59:59.997">;	
					</cfif>
					<cfif arguments.event.getValue('evCalendar','0') gt 0>
						SET @evCalendarID = <cfqueryparam value="#arguments.event.getValue('evCalendar')#" cfsqltype="CF_SQL_INTEGER">;	
					</cfif>
					<cfif arguments.event.getValue('evCategory','0') gt 0>
						SET @evCategoryID = <cfqueryparam value="#arguments.event.getValue('evCategory')#" cfsqltype="CF_SQL_INTEGER">;
					</cfif>
					<cfif arguments.event.getTrimValue('evKeyword','') NEQ ''>
						SET @evKeyword = replace(<cfqueryparam value="#arguments.event.getTrimValue('evKeyword')#" cfsqltype="CF_SQL_VARCHAR">,'_','\_');
					</cfif>
					<cfif arguments.event.getTrimValue('evReportCode','') NEQ ''>
						SET @evReportCode = <cfqueryparam value="#arguments.event.getTrimValue('evReportCode')#" cfsqltype="CF_SQL_VARCHAR">;
					</cfif>
				</cfif>

				<cfif arguments.event.getValue('rDateFrom','') NEQ ''>
					SET @rDateFrom = <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getValue('rDateFrom')#">;	
				</cfif>
				<cfif arguments.event.getValue('rDateTo','') NEQ ''>
					SET @rDateTo = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getValue('rDateTo')# 23:59:59.997">;
				</cfif>
				<cfif arguments.event.getValue('rAttended','') NEQ ''>
					SET @rAttended = <cfqueryparam value="#arguments.event.getValue('rAttended')#" cfsqltype="CF_SQL_BIT">;
				</cfif>
				<cfif arguments.event.getTrimValue('rBillFrom','') NEQ ''>
					SET @rBillFrom = <cfqueryparam value="#abs(rereplace(arguments.event.getTrimValue('rBillFrom',0),'[^\d.]+','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">;
				</cfif>
				<cfif arguments.event.getTrimValue('rBillTo','') NEQ ''>
					SET @rBillTo = <cfqueryparam value="#abs(rereplace(arguments.event.getTrimValue('rBillTo',0),'[^\d.]+','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">;
				</cfif>
				<cfif arguments.event.getTrimValue('rCompany','') NEQ ''>
					SET @rCompany = replace(<cfqueryparam value="#arguments.event.getTrimValue('rCompany')#" cfsqltype="CF_SQL_VARCHAR">,'_','\_');
				</cfif>
				<cfif arguments.event.getTrimValue('rDuesFrom','') NEQ ''>
					SET @rDuesFrom = <cfqueryparam value="#abs(rereplace(arguments.event.getTrimValue('rDuesFrom',0),'[^\d.]+','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">;
				</cfif>
				<cfif arguments.event.getTrimValue('rDuesTo','') NEQ ''>
					SET @rDuesTo = <cfqueryparam value="#abs(rereplace(arguments.event.getTrimValue('rDuesTo',0),'[^\d.]+','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">;
				</cfif>
				<cfif arguments.event.getValue('rAssociatedMemberID',0) gt 0>
					SET @rAssociatedMemberID = <cfqueryparam value="#arguments.event.getValue('rAssociatedMemberID')#" cfsqltype="CF_SQL_INTEGER">;
				</cfif>
				<cfif arguments.event.getValue('rAssociatedGroupID',0) gt 0>
					SET @rAssociatedGroupID = <cfqueryparam value="#arguments.event.getValue('rAssociatedGroupID')#" cfsqltype="CF_SQL_INTEGER">;
				</cfif>

				<cfif local.sourceApp eq "regTab">
					SET @eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('eID')#">;
					<cfif arguments.event.getValue('rCreditType',0) gt 0>
						SET @rCreditType = <cfqueryparam value="#arguments.event.getValue('rCreditType')#" cfsqltype="CF_SQL_INTEGER">;
					</cfif>
					<cfif arguments.event.getValue('_rid',0) gt 0>
						SET @rRegistrantID = <cfqueryparam value="#arguments.event.getValue('_rid')#" cfsqltype="CF_SQL_INTEGER">;
					</cfif>

					-- get event fees
					IF OBJECT_ID('tempdb..##tmpEventsForFee') IS NOT NULL
						DROP TABLE ##tmpEventsForFee; 
					IF OBJECT_ID('tempdb..##tmpEventsForFeeResult') IS NOT NULL
						DROP TABLE ##tmpEventsForFeeResult; 
					IF OBJECT_ID('tempdb..##tblRegistrantFees') IS NOT NULL
						DROP TABLE ##tblRegistrantFees;
					create table ##tmpEventsForFee (eventID int PRIMARY KEY);
					create table ##tmpEventsForFeeResult (eventID int, registrantID int, transactionID int, TransactionIDForRateAdjustment int);
					CREATE TABLE ##tblRegistrantFees (registrantID int PRIMARY KEY, TransactionIDForRateAdjustment int, totalRegFee decimal(18,2), regFeePaid decimal(18,2));

					insert into ##tmpEventsForFee (eventID)
					values (@eventID);

					EXEC dbo.ev_registrantTransactionsByEventBulk;

					INSERT INTO ##tblRegistrantFees (registrantID, TransactionIDForRateAdjustment, totalRegFee, regFeePaid)
					select fees.registrantID, max(fees.TransactionIDForRateAdjustment), sum(ts.cache_amountAfterAdjustment), sum(ts.cache_activePaymentAllocatedAmount)
					from ##tmpEventsForFeeResult as fees
					inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = fees.transactionid
					<cfif arguments.event.getValue('_rid',0) gt 0>
						where fees.registrantID = @rRegistrantID
					</cfif>
					group by fees.registrantID;
				</cfif>

				IF OBJECT_ID('tempdb..##tblRegistrantSearch') IS NOT NULL
					DROP TABLE ##tblRegistrantSearch;
				CREATE TABLE ##tblRegistrantSearch (registrantID int PRIMARY KEY, registrationID int, eventID int, memberID int, calendarID int, rateID int, 
					firstName varchar(100), lastName varchar(100), memberNumber varchar(50), company varchar(200));

				INSERT INTO ##tblRegistrantSearch (registrantID, registrationID, eventID, memberID, calendarID, rateID, firstName, lastName, memberNumber, company)
				SELECT distinct r.registrantID, reg.registrationID, e.eventID, mActive.memberID, cache.calendarID, r.rateID, mActive.FirstName, mActive.LastName, mActive.membernumber, mActive.Company
				FROM dbo.ev_registrants as r
				INNER JOIN dbo.ev_registration as reg ON reg.registrationID = r.registrationID AND reg.siteID = @siteID AND reg.status = 'A'
					<cfif local.sourceApp eq "regTab">
						AND reg.eventID = @eventID
					</cfif>
				INNER JOIN dbo.ev_events as e on reg.eventID = e.eventID and e.siteID = @siteID AND e.status in ('A','I')
					<cfif arguments.event.getTrimValue('evReportCode','') NEQ ''>
						AND e.reportCode = @evReportCode
					</cfif>
				INNER JOIN dbo.cache_calendarEvents as cache on cache.eventID = e.eventID and cache.calendarID = cache.sourceCalendarID
					<cfif arguments.event.getValue('evCalendar','0') gt 0>
						AND cache.calendarID = @evCalendarID
					</cfif>
					<cfif arguments.event.getValue('evCategory','0') gt 0>
						AND cache.categoryID = @evCategoryID
					</cfif>
				INNER JOIN dbo.sites as s on s.siteID = @siteID
				INNER JOIN dbo.ev_times as times on times.eventID = e.eventID and times.timeZoneID = s.defaultTimeZoneID
				LEFT OUTER JOIN dbo.ev_times as lockTimes
					INNER JOIN dbo.timeZones as lockTimeZones on lockTimeZones.timeZoneID = lockTimes.timeZoneID
					on lockTimes.eventID = e.eventID and lockTimes.timeZoneID = e.lockTimeZoneID
				<cfif arguments.event.getTrimValue('evKeyword','') NEQ ''>
					INNER JOIN dbo.cms_contentLanguages as cl on cl.contentID = e.eventContentID and cl.languageID = 1
						AND cl.contentTitle like '%'+@evKeyword+'%' ESCAPE('\')
				</cfif>
				INNER JOIN dbo.ams_members as m ON m.orgID = @orgID and m.memberID = r.memberID
				INNER JOIN dbo.ams_members as mActive ON mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
					<cfif arguments.event.getValue('rAssociatedMemberID',0) gt 0>
						AND mActive.memberID = @rAssociatedMemberID
					</cfif>
					<cfif arguments.event.getTrimValue('rCompany','') NEQ ''>
						AND mActive.company LIKE '%'+@rCompany+'%' ESCAPE('\')
					</cfif>
				<cfif arguments.event.getValue('rAssociatedGroupID',0) gt 0>
					INNER JOIN dbo.cache_members_groups mg ON mg.orgID = @orgID and mg.memberID = mactive.memberID AND mg.groupid = @rAssociatedGroupID
				</cfif>
				<cfif arguments.event.getTrimValue('rBillFrom','') NEQ '' OR arguments.event.getTrimValue('rBillTo','') NEQ '' OR arguments.event.getTrimValue('rDuesFrom','') NEQ '' OR arguments.event.getTrimValue('rDuesTo','') NEQ ''>
					<cfif local.sourceApp eq "regTab">
						LEFT OUTER JOIN ##tblRegistrantFees as regFee on regFee.registrantID = r.registrantID
					<cfelse>
						OUTER APPLY dbo.fn_ev_totalRegFeeAndPaid(@orgID, r.registrantID) as regFee
					</cfif>
				</cfif>
				WHERE 1 = 1
				<cfif arguments.event.getValue('_rid',0) gt 0>
					AND r.registrantID = @rRegistrantID
				</cfif>
				<cfif arguments.event.getValue('rDateFrom','') NEQ '' and arguments.event.getValue('rDateTo','') NEQ ''>
					AND r.dateRegistered BETWEEN @rDateFrom AND @rDateTo
				<cfelseif arguments.event.getValue('rDateFrom','') NEQ ''>
					AND r.dateRegistered >= @rDateFrom
				<cfelseif arguments.event.getValue('rDateTo','') NEQ ''>
					AND r.dateRegistered <= @rDateTo
				</cfif>
				<cfif arguments.event.getValue('evDateFrom','') NEQ '' and arguments.event.getValue('evDateTo','') NEQ ''>
					AND isnull(lockTimes.startTime,times.startTime) between @evDateFrom and @evDateTo
				<cfelseif arguments.event.getValue('evDateFrom','') NEQ ''>
					AND isnull(lockTimes.startTime,times.startTime) >= @evDateFrom
				<cfelseif arguments.event.getValue('evDateTo','') NEQ ''>
					AND isnull(lockTimes.startTime,times.startTime) <= @evDateTo
				</cfif>
				<cfif arguments.event.getValue('rAttended','') NEQ ''>
					AND r.attended = @rAttended
				</cfif>
				<cfif arguments.event.getValue('rStatus',0) eq 0>
					AND r.status = 'A'
				</cfif>
				<cfif ListLen(arguments.event.getValue('rEvRate',''))>
					AND r.rateID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('rEvRate')#" list="true">)
				</cfif>
				<cfif arguments.event.getValue('evEventType','') EQ 'main'>
					AND NOT EXISTS (select 1 from dbo.ev_subevents where eventID = e.eventID)
				<cfelseif arguments.event.getValue('evEventType','') EQ 'sub'>
					AND EXISTS (select 1 from dbo.ev_subevents where eventID = e.eventID)
				</cfif>
				<cfif arguments.event.getTrimValue('rBillFrom','') NEQ ''>
					AND ISNULL(regFee.totalRegFee,0) >= @rBillFrom
				</cfif>
				<cfif arguments.event.getTrimValue('rBillTo','') NEQ ''>
					AND ISNULL(regFee.totalRegFee,0) <= @rBillTo
				</cfif>
				<cfif arguments.event.getTrimValue('rDuesFrom','') NEQ ''>
					AND (ISNULL(regFee.totalRegFee,0)-ISNULL(regFee.regFeePaid,0)) >= @rDuesFrom
				</cfif>
				<cfif arguments.event.getTrimValue('rDuesTo','') NEQ ''>
					AND (ISNULL(regFee.totalRegFee,0)-ISNULL(regFee.regFeePaid,0)) <= @rDuesTo
				</cfif>
				<cfif len(local.rCreditType)>
					<cfif local.rCreditType gt 0>
						AND EXISTS (
							SELECT cr.requestID
							FROM dbo.crd_requests cr
							INNER JOIN dbo.crd_offeringTypes as ecT ON ecT.offeringTypeID = cr.offeringTypeID
							INNER JOIN dbo.crd_authoritySponsorTypes AS ast ON ast.ASTID = ecT.ASTID
							WHERE cr.registrantID = r.registrantID
							AND cr.creditAwarded = 1
							AND ast.typeID = @rCreditType
						)
					<cfelse>
						AND NOT EXISTS (
							SELECT cr.requestID
							FROM dbo.crd_requests cr
							WHERE cr.registrantID = r.registrantID
							AND cr.creditAwarded = 1
						)
					</cfif> 
				</cfif>
				<cfif ListLen(arguments.event.getValue('rEvRole',''))>
					AND (
						<cfif ListFind(arguments.event.getValue('rEvRole'),0)>
							not exists (select 1 from dbo.ev_registrantCategories where registrantID = r.registrantID)
							<cfif ListLen(arguments.event.getValue('rEvRole')) gt 1> OR </cfif>
						</cfif>
						<cfif not ListFind(arguments.event.getValue('rEvRole'),0) or ListLen(arguments.event.getValue('rEvRole')) gt 1>
							exists (select 1 
								from dbo.ev_registrantCategories 
								where registrantID = r.registrantID
								and categoryID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('rEvRole')#" list="true">)
							)
						</cfif>
					)
				</cfif>
				#PreserveSingleQuotes(local.strFields.filterSQL)#;

				<cfif local.sourceApp eq "regTab" and len(arguments.event.getValue('rEvFormCompletion',''))>
					;WITH evRegFormResponses AS (
						SELECT tmp.registrantID, ef.eventFormID, CASE WHEN MAX(r.dateCompleted) IS NOT NULL THEN 1 ELSE 0 END AS isCompleted
						FROM ##tblRegistrantSearch AS tmp
						INNER JOIN dbo.ev_eventsAndForms ef ON ef.eventID = tmp.eventID
						LEFT OUTER JOIN dbo.ev_eventsAndFormResponses AS efr ON efr.registrantID = tmp.registrantID
							AND efr.eventFormID = ef.eventFormID
						LEFT OUTER JOIN formBuilder.dbo.tblResponses AS r on r.responseID = efr.responseID
							AND r.dateCompleted IS NOT NULL
						GROUP BY tmp.registrantID, ef.eventFormID
					)
					DELETE
					FROM ##tblRegistrantSearch
					WHERE registrantID NOT IN (
						SELECT DISTINCT tmp.registrantID
						FROM evRegFormResponses AS tmp
						INNER JOIN dbo.fn_varcharListToTable(<cfqueryparam value="#arguments.event.getTrimValue('rEvFormCompletion')#" cfsqltype="CF_SQL_VARCHAR">,',') AS tmpF ON tmp.eventFormID = PARSENAME(REPLACE(tmpF.listitem,'|','.'),2)
							AND tmp.isCompleted = PARSENAME(REPLACE(tmpF.listitem,'|','.'),1)
					);
				</cfif>

				<cfif listFindNoCase("regSearchGrid,regTabGrid,massRemoveRegistrants",arguments.mode)>
					IF OBJECT_ID('tempdb..##tblRegistrantInvoices') IS NOT NULL
						DROP TABLE ##tblRegistrantInvoices;
					CREATE TABLE ##tblRegistrantInvoices (registrantID int, invoiceID int);

					<cfif arguments.mode eq "regSearchGrid">
						INSERT INTO ##tblRegistrantInvoices (registrantID, invoiceID)
						select tmp.registrantID, it.invoiceID
						from ##tblRegistrantSearch as tmp
						cross apply dbo.fn_ev_registrantTransactions(tmp.registrantID) as rt
						inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = rt.transactionID
						inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
						inner join dbo.tr_invoiceStatuses as invs on invs.statusID = i.statusID
						where invs.status in ('Open','Closed','Delinquent')
						group by tmp.registrantID, it.invoiceID
						having sum(it.cache_invoiceAmountAfterAdjustment - it.cache_activePaymentAllocatedAmount - it.cache_pendingPaymentAllocatedAmount) > 0;
					<cfelseif listFindNoCase("regTabGrid,massRemoveRegistrants",arguments.mode)>
						INSERT INTO ##tblRegistrantInvoices (registrantID, invoiceID)
						select tmp.registrantID, it.invoiceID
						from ##tmpEventsForFeeResult as tmp
						inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = tmp.transactionID
						inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
						inner join dbo.tr_invoiceStatuses as invs on invs.statusID = i.statusID
						where invs.status in ('Open','Closed','Delinquent')
						group by tmp.registrantID, it.invoiceID
						having sum(it.cache_invoiceAmountAfterAdjustment - it.cache_activePaymentAllocatedAmount - it.cache_pendingPaymentAllocatedAmount) > 0;
					</cfif>
				</cfif>

				<cfif listFindNoCase("regSearchEmailGrid,regTabEmailGrid,regSearchEmail,regTabEmail,emailCertAttendees,previewEmailCertAttendeesGrid",arguments.mode)>
					IF OBJECT_ID('tempdb..##tblRegistrantEmails') IS NOT NULL
							DROP TABLE ##tblRegistrantEmails;
					CREATE TABLE ##tblRegistrantEmails (registrantID int, email varchar(255));

					DECLARE @emailTagTypeID int;
					SET @emailTagTypeID = <cfqueryparam value="#arguments.event.getValue('emailTagType',0)#" cfsqltype="CF_SQL_INTEGER">;

					INSERT INTO ##tblRegistrantEmails (registrantID, email)
					SELECT DISTINCT r.registrantID, me.email
					FROM ##tblRegistrantSearch as tmp
					INNER JOIN dbo.ev_registrants as r on r.registrantID = tmp.registrantID
					INNER JOIN dbo.ams_members as m ON m.orgID = @orgID and m.memberID = tmp.memberID
					INNER JOIN dbo.ams_memberEmails as me ON me.orgID = @orgID
						AND me.memberID = m.memberID
					INNER JOIN dbo.ams_memberEmailTags as metag on metag.orgID = @orgID 
						and metag.memberID = me.memberID 
						and metag.emailTypeID = me.emailTypeID
					INNER JOIN dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID
						and metagt.emailTagTypeID = metag.emailTagTypeID 
					WHERE metagt.emailTagTypeID = @emailTagTypeID
					AND me.email <> ''
						UNION
					SELECT DISTINCT tmp.registrantID, eao.email
					FROM ##tblRegistrantSearch AS tmp
					INNER JOIN dbo.ams_emailAppOverrides AS eao ON eao.itemID = tmp.registrantID
						AND eao.itemType = 'eventReg';
				</cfif>

				<cfif arguments.mode eq "regSearchGrid">
					DECLARE @posStart int, @posStartAndCount int, @totalCount int;
					SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
					SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;

					IF OBJECT_ID('tempdb..##tblRegistrants') IS NOT NULL
						DROP TABLE ##tblRegistrants;
					CREATE TABLE ##tblRegistrants (registrationID int, eventID int, calendarID int, eventTitle varchar(800), eventStatus char(1), dateRegistered date,
						registrantID int, firstname varchar(75), lastname varchar(75), membernumber varchar(50), memberID int, 
						company varchar(200), siteResourceID int, attended bit, isFlagged bit, status char(1), rateID int, row int);

					INSERT INTO ##tblRegistrants
					SELECT distinct tmp.registrationID, tmp.eventID, tmp.calendarID, cl.contentTitle as eventTitle, e.status, r.dateRegistered, 
						r.registrantID, m.firstname, m.lastname, m.membernumber, m.memberID, m.company, e.siteResourceID, r.attended, 
						r.isFlagged, r.status, tmp.rateID, ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)# #arguments.event.getValue('orderDir')#) as row
					FROM ##tblRegistrantSearch as tmp
					INNER JOIN dbo.ev_registrants as r on r.registrantID = tmp.registrantID
					INNER JOIN dbo.ev_events as e on e.eventID = tmp.eventID
					INNER JOIN dbo.cms_contentLanguages as cl on cl.contentID = e.eventContentID and cl.languageID = 1
					INNER JOIN dbo.ams_members as m ON m.memberID = tmp.memberID;

					SELECT @totalCount = @@ROWCOUNT;
								
					SELECT tmp.registrationID, tmp.eventID, tmp.calendarID, tmp.eventTitle, tmp.eventStatus, tmp.dateRegistered, tmp.registrantID, 
						tmp.firstname, tmp.lastname, tmp.membernumber, tmp.memberID, tmp.company, tmp.siteResourceID, tmp.attended, 
						tmp.isFlagged, tmp.status, tmp.row, @totalCount as totalCount, regFee.totalRegFee, regFee.totalRegFee-regFee.regFeePaid as amountDue, 
						regFee.TransactionIDForRateAdjustment as rateTID, tmp.calendarId, 
						invoicesDue = case 
										when regFee.totalRegFee-regFee.regFeePaid > 0 
											then (select substring((
											  select ','+ cast(invoiceID as varchar(10)) AS [text()]
												from ##tblRegistrantInvoices
												where registrantID = tmp.registrantID
												For XML PATH ('')
												), 2, 2000)) 
										else '' end,
						dbo.fn_cache_perms_getResourceRightsXML(tmp.siteResourceID,@currentMemberID,@siteID) as rightsXML, 
						CAST( ISNULL((
							SELECT offeringTypeID, creditAwarded 
							FROM (
								SELECT credit.offeringTypeID, credit.creditAwarded
								FROM dbo.crd_requests as credit
								WHERE registrantID = tmp.registrantID
									UNION
								SELECT cr.offeringTypeID, cr.creditAwarded
								FROM dbo.crd_requests as cr
								INNER JOIN dbo.ev_registrants as er on er.registrantID = cr.registrantID and er.memberID = tmp.memberID
								INNER JOIN dbo.ev_registration as reg on reg.registrationID = er.registrationID and reg.siteID = @siteID
								INNER JOIN dbo.ev_subEvents as subEvent on subEvent.eventID = reg.eventID and subEvent.parentEventID = tmp.eventID
							) as credit 
							for XML AUTO, ROOT('credits')
							),'<credits/>') as XML) as creditsXML,
						r.rateName as regrate
					FROM ##tblRegistrants as tmp
					LEFT OUTER JOIN dbo.ev_rates as r on r.rateID = tmp.rateID
					OUTER APPLY dbo.fn_ev_totalRegFeeAndPaid(@orgID, tmp.registrantID) as regFee
					WHERE tmp.row > @posStart
					AND tmp.row <= @posStartAndCount
					ORDER BY row;

					IF OBJECT_ID('tempdb..##tblRegistrants') IS NOT NULL
						DROP TABLE ##tblRegistrants;
				<cfelseif arguments.mode eq "regSearchGridExport">
					IF OBJECT_ID('tempdb..##tmpRegTrans') IS NOT NULL 
						DROP TABLE ##tmpRegTrans;
					IF OBJECT_ID('tempdb..##tmpEventRoles') IS NOT NULL 
						DROP TABLE ##tmpEventRoles;
					IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
						DROP TABLE ##tmpMembers;
					IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
						DROP TABLE ##tmp_membersForFS;
					IF OBJECT_ID('tempdb..##tmp_CF_ItemIDs') is not null
						DROP TABLE ##tmp_CF_ItemIDs;
					IF OBJECT_ID('tempdb..##tmp_CF_FieldData') is not null
						DROP TABLE ##tmp_CF_FieldData;
					IF OBJECT_ID('tempdb..####tmpSWCF#local.tmpSuffix#') IS NOT NULL 
						DROP TABLE ####tmpSWCF#local.tmpSuffix#;
					IF OBJECT_ID('tempdb..####tmpRQ#local.tmpSuffix#') IS NOT NULL 
						DROP TABLE ####tmpRQ#local.tmpSuffix#;
					IF OBJECT_ID('tempdb..##tmpSWCF') IS NOT NULL 
						DROP TABLE ##tmpSWCF;
					IF OBJECT_ID('tempdb..##tmpRQA') IS NOT NULL 
						DROP TABLE ##tmpRQA;
					IF OBJECT_ID('tempdb..####tmpEVExport#local.tmpSuffix#') IS NOT NULL 
						DROP TABLE ####tmpEVExport#local.tmpSuffix#;

					CREATE TABLE ##tmpEventRoles (categoryID int PRIMARY KEY, categoryName varchar(200));
					CREATE TABLE ##tmpMembers (MFSAutoID int IDENTITY(1,1) not null);
					CREATE TABLE ##tmp_membersForFS (memberID int PRIMARY KEY);
					CREATE TABLE ##tmpRegTrans (registrantID int PRIMARY KEY, totalRegFee decimal(14,2), regFeePaid decimal(14,2));
					CREATE TABLE ##tmp_CF_ItemIDs (itemID int, itemType varchar(20));
					CREATE TABLE ##tmp_CF_FieldData (fieldID int, fieldValue varchar(max), amount decimal(14,2), itemID int);
					CREATE TABLE ##tmpRQA (registrantID int, titleOnInvoice varchar(max), answer varchar(max), INDEX IX_tbltmpRQA_registrantID (registrantID));

					DECLARE @fieldSetID int, @eventAdminSRID int, @eventRolesCTID int, @fullsql varchar(max), 
						@swcfList varchar(max), @rqList varchar(max), @eventRoleColumns varchar(max);

					SET @fieldSetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.resultsFieldsetID#">;
					SET @eventAdminSRID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.EventAdminSRID#">;
					SELECT @eventRolesCTID = dbo.fn_getCategoryTreeIDForSiteResourceIDandTree(@eventAdminSRID,'Event Roles');

					-- event roles
					INSERT INTO ##tmpEventRoles (categoryID, categoryName)
					select categoryID, categoryName
					from dbo.cms_categories
					where categoryTreeID = @eventRolesCTID
					and isActive = 1 
					and parentCategoryID is NULL;

					-- system wide event custom fields
					INSERT INTO ##tmp_CF_ItemIDs (itemID, itemType)
					SELECT DISTINCT e.siteResourceID, 'CrossEvent'
					FROM ##tblRegistrantSearch as tmp
					INNER JOIN dbo.ev_events as e on e.eventID = tmp.eventID;

					EXEC dbo.cf_getFieldData;

					SELECT e.eventID, replace(f.fieldReference,',','') as titleOnInvoice, fd.fieldValue AS answer
					INTO ##tmpSWCF
					FROM ##tmp_CF_FieldData AS fd
					INNER JOIN dbo.cf_fields AS f ON f.fieldID = fd.fieldID
					INNER JOIN dbo.ev_events as e ON e.siteResourceID = fd.itemID
					<cfif listLen(arguments.event.getValue('swcfid',''))>
						WHERE f.fieldID IN (<cfqueryparam cfsqltype="cf_sql_integer" list="yes" value="#arguments.event.getValue('swcfid')#">)
					<cfelse>
						WHERE 1=0
					</cfif>;

					-- system wide event custom fields pivoted
					set @swcfList = '';
					select @swcfList = COALESCE(@swcfList + ',', '') + quoteName(titleonInvoice) from ##tmpSWCF group by titleOnInvoice;
					IF left(@swcfList,1) = ','
						set @swcfList = right(@swcfList,len(@swcfList)-1);
					IF len(@swcfList) > 0 BEGIN
						set @fullsql = '
							select * 
							into ####tmpSWCF#local.tmpSuffix#
							from (
								select eventID, titleOnInvoice, answer
								from ##tmpSWCF
							) as cf
							PIVOT (min(answer) for titleonInvoice in (' + @swcfList + ')) as p ';
						EXEC(@fullsql);
					END
					ELSE
						SELECT eventID 
						INTO ####tmpSWCF#local.tmpSuffix# 
						FROM ##tblRegistrantSearch 
						WHERE 0=1;

					-- truncate temp field tables
					TRUNCATE TABLE ##tmp_CF_ItemIDs;
					TRUNCATE TABLE ##tmp_CF_FieldData;

					-- role custom fields
					INSERT INTO ##tmp_CF_ItemIDs (itemID, itemType)
					SELECT DISTINCT registrantID, 'EventRole'
					FROM ##tblRegistrantSearch;

					EXEC dbo.cf_getFieldData;
					
					INSERT INTO ##tmpRQA (registrantID, titleOnInvoice, answer)
					SELECT fd.itemID AS registrantID, replace(tmpR.categoryName + '_' + f.fieldReference,',','') as titleOnInvoice, fd.fieldValue AS answer
					FROM ##tblRegistrantSearch as registrants
					INNER JOIN dbo.ev_registrantCategories as regcat on regcat.registrantID = registrants.registrantID
					INNER JOIN ##tmpEventRoles as tmpR on tmpR.categoryID = regcat.categoryID
					INNER JOIN ##tmp_CF_FieldData as fd on fd.itemID = registrants.registrantID
					INNER JOIN dbo.cf_fields as f on f.fieldID = fd.fieldID
						and f.detailID = regcat.categoryID
					<cfif listLen(arguments.event.getValue('rqID',''))>
						WHERE f.fieldID IN (<cfqueryparam cfsqltype="cf_sql_integer" list="yes" value="#arguments.event.getValue('rqID')#">)
					<cfelse>
						WHERE 1=0
					</cfif>;

					-- role custom fields pivoted
					set @rqList = '';
					select @rqList = COALESCE(@rqList + ',', '') + quoteName(titleonInvoice) from ##tmpRQA group by titleOnInvoice;
					IF left(@rqList,1) = ','
						select @rqList = right(@rqList,len(@rqList)-1);
					IF len(@rqList) > 0 BEGIN
						set @fullsql = '';
						select @fullsql = '
							select * 
							into ####tmpRQ#local.tmpSuffix#
							from (
								select registrantid, titleOnInvoice, answer
								from ##tmpRQA
							) as reg
							PIVOT (min(answer) for titleonInvoice in (' + @rqList + ')) as p ';
						EXEC(@fullsql);
					END
					ELSE
						SELECT registrantID 
						INTO ####tmpRQ#local.tmpSuffix# 
						FROM ##tblRegistrantSearch 
						WHERE 0=1;

					-- get fieldset data
					INSERT INTO ##tmp_membersForFS (memberID)
					select distinct memberID
					from ##tblRegistrantSearch;

					EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@fieldsetID, @existingFields='',
						@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmp_membersForFS', @membersResultTableName='##tmpMembers',
						@linkedMembers=0, @mode='export', @outputFieldsXML=@outputFieldsXML OUTPUT;

					-- add event role columns to report if they exist.
					select @eventRoleColumns = COALESCE(@eventRoleColumns + ',', '') + quoteName(categoryName) from ##tmpEventRoles;

					-- get registrant transactions by event
					IF OBJECT_ID('tempdb..##tmpEventsForFee') IS NOT NULL 
						DROP TABLE ##tmpEventsForFee; 
					IF OBJECT_ID('tempdb..##tmpEventsForFeeResult') IS NOT NULL 
						DROP TABLE ##tmpEventsForFeeResult; 
					CREATE TABLE ##tmpEventsForFee (eventID int PRIMARY KEY);
					CREATE TABLE ##tmpEventsForFeeResult (eventID int, registrantID int, transactionID int, TransactionIDForRateAdjustment int);

					INSERT INTO ##tmpEventsForFee (eventID)
					select distinct eventID
					from ##tblRegistrantSearch;

					EXEC dbo.ev_registrantTransactionsByEventBulk;

					INSERT INTO ##tmpRegTrans (registrantID, totalRegFee, regFeePaid)
					select fees.registrantID, sum(ts.cache_amountAfterAdjustment), sum(ts.cache_activePaymentAllocatedAmount)
					from ##tmpEventsForFeeResult as fees
					inner join ##tblRegistrantSearch as tmp on tmp.registrantID = fees.registrantID
					inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = fees.transactionid
					group by fees.registrantID;

					-- combine with reg data 
					set @fullsql = 'SELECT rf.eventID as [EventID], r.registrantID as [RegistrantID], cl.contentTitle as [EventTitle], 
						e.eventSubTitle as [Event Sub Title], CASE WHEN r.status = ''D'' THEN ''Removed'' ELSE ''Active'' END as [RegistrantStatus],
						r.DateRegistered as [DateRegistered], CASE WHEN r.status <> ''D'' AND r.attended = 1 THEN ''Attended'' ELSE ''Did not attend'' END as [Attended], 
						evregeo.email as [RegistrantEmailOverride], CASE when r.isFlagged = 1 then ''Yes'' else ''No'' end as [IsFlagged], 
						r.internalNotes as [InternalNotes], ';

					set @fullsql=@fullsql + case when len(@swcfList)>0 then 'swcf.' + replace(@swcfList,',',',swcf.') + ', ' else '' end;

					set @fullsql = @fullsql + '
							CASE WHEN r.status <> ''D'' 
								THEN '''' + CAST(isnull((SELECT SUM(creditValueAwarded)
											FROM (
												SELECT cr.requestID, cr.creditValueAwarded
												FROM dbo.crd_requests as cr
												WHERE cr.registrantID = rf.registrantID
												AND cr.creditAwarded = 1
													UNION
												SELECT cr.requestID, cr.creditValueAwarded
												FROM dbo.crd_requests as cr
												INNER JOIN dbo.ev_registrants as er on er.registrantID = cr.registrantID 
													and cr.creditAwarded = 1
													and er.memberID = rf.memberID
												INNER JOIN dbo.ev_registration as reg on reg.registrationID = er.registrationID and reg.siteID = ' + cast(@siteID as varchar(10)) + '
												INNER JOIN dbo.ev_subEvents as subEvent on subEvent.eventID = reg.eventID and subEvent.parentEventID = rf.eventID
											) as credit),0) as varchar(10))
								 + '' credits earned'' ELSE ''None'' END as [Total Credit],';

					if @eventRoleColumns <> '' set @fullsql=@fullsql + ' eventroles.*, ';
					set @fullsql = @fullsql + case when len(@rqList)>0 then 'rtq.' + replace(@rqList,',',',rtq.') + ', ' else '' end;
					set @fullsql = @fullsql + 'regFee.totalRegFee as [Amount Billed], regFee.totalRegFee-regFee.regFeePaid as [Amount Due], ';

					set @fullsql=@fullsql + ' m.* 
						INTO ####tmpEVExport#local.tmpSuffix#
						FROM dbo.ev_registrants as r
						INNER JOIN ##tblRegistrantSearch as rf on rf.registrantID = r.registrantID 
						INNER JOIN dbo.ev_events as e on e.eventID = rf.eventID
						INNER JOIN dbo.cms_contentLanguages as cl on cl.contentID = e.eventContentID and cl.languageID = 1
						INNER JOIN ##tmpMembers as m ON m.memberID = rf.memberID
						LEFT OUTER JOIN ##tmpRegTrans as regFee on regFee.registrantID = r.registrantID
						LEFT OUTER JOIN ####tmpSWCF#local.tmpSuffix# as swcf on swcf.eventID = rf.eventID 
						LEFT OUTER JOIN ####tmpRQ#local.tmpSuffix# as rtq on rtq.registrantID = r.registrantID 
						LEFT OUTER JOIN dbo.ams_emailAppOverrides as evregeo on evregeo.itemType = ''eventreg'' and evregeo.itemID = r.registrantID	';

					if @eventRoleColumns <> ''
						set @fullsql = @fullsql + 
						'					
						OUTER apply (
							select '+@eventRoleColumns+'
							from (
								select tmpR.categoryName
								from dbo.ev_registrantCategories er
								inner join ##tmpEventRoles as tmpR on tmpR.categoryID = er.categoryID
								where er.registrantID = r.registrantID
							) tmp
							PIVOT (min(categoryName) for categoryName in ('+@eventRoleColumns+')) as pvt
						) eventroles
						';

					-- put into temp table for export
					EXEC(@fullsql);

					ALTER TABLE ####tmpEVExport#local.tmpSuffix# DROP COLUMN EventID;
					ALTER TABLE ####tmpEVExport#local.tmpSuffix# DROP COLUMN RegistrantID;
					ALTER TABLE ####tmpEVExport#local.tmpSuffix# DROP COLUMN memberID;

					DECLARE @selectsql varchar(max) = '
						SELECT *, ROW_NUMBER() OVER(order by [RegistrantStatus], [DateRegistered]) as mcCSVorder 
						*FROM* ####tmpEVExport#local.tmpSuffix#';
					EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#local.strFolder.folderPathUNC#\RegistrantSearch.csv', @returnColumns=0;

					SELECT '#local.strFolder.folderPath#/RegistrantSearch.csv' as csvFileName, 'RegistrantSearch.csv' as displayName;

					IF OBJECT_ID('tempdb..##tmpRegTrans') IS NOT NULL 
						DROP TABLE ##tmpRegTrans;
					IF OBJECT_ID('tempdb..##tmpEventRoles') IS NOT NULL 
						DROP TABLE ##tmpEventRoles;
					IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
						DROP TABLE ##tmpMembers;
					IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
						DROP TABLE ##tmp_membersForFS;
					IF OBJECT_ID('tempdb..##tmp_CF_ItemIDs') is not null
						DROP TABLE ##tmp_CF_ItemIDs;
					IF OBJECT_ID('tempdb..##tmp_CF_FieldData') is not null
						DROP TABLE ##tmp_CF_FieldData;
					IF OBJECT_ID('tempdb..####tmpSWCF#local.tmpSuffix#') IS NOT NULL 
						DROP TABLE ####tmpSWCF#local.tmpSuffix#;
					IF OBJECT_ID('tempdb..####tmpRQ#local.tmpSuffix#') IS NOT NULL 
						DROP TABLE ####tmpRQ#local.tmpSuffix#;
					IF OBJECT_ID('tempdb..##tmpSWCF') IS NOT NULL 
						DROP TABLE ##tmpSWCF;
					IF OBJECT_ID('tempdb..##tmpRQA') IS NOT NULL 
						DROP TABLE ##tmpRQA;
					IF OBJECT_ID('tempdb..####tmpEVExport#local.tmpSuffix#') IS NOT NULL 
						DROP TABLE ####tmpEVExport#local.tmpSuffix#;
					IF OBJECT_ID('tempdb..##tmpEventsForFee') IS NOT NULL 
						DROP TABLE ##tmpEventsForFee; 
					IF OBJECT_ID('tempdb..##tmpEventsForFeeResult') IS NOT NULL 
						DROP TABLE ##tmpEventsForFeeResult; 
				<cfelseif listFindNoCase("regSearchEmailGrid,regTabEmailGrid,regTabBadgeGrid",arguments.mode)>
					DECLARE @posStart int, @posStartAndCount int, @totalCount int;
					SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
					SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;

					IF OBJECT_ID('tempdb..##tblRegistrants') IS NOT NULL
						DROP TABLE ##tblRegistrants;
					CREATE TABLE ##tblRegistrants (registrantID int, firstname varchar(75), lastname varchar(75), membernumber varchar(50),
						company varchar(200), eventTitle varchar(800), email varchar(255), row int);

					<cfif listFindNoCase("regSearchEmailGrid,regTabEmailGrid",arguments.mode)>
						INSERT INTO ##tblRegistrants (registrantID, firstname, lastname, membernumber, company, eventTitle, email, row)
						SELECT distinct r.registrantID, m.firstname, m.lastname, m.membernumber, m.company, cl.contentTitle as eventTitle, 
							tmpRegEmail.email, ROW_NUMBER() OVER (ORDER BY #local.orderby# #arguments.event.getValue('direct')#) as row
						FROM ##tblRegistrantSearch as tmp
						INNER JOIN dbo.ev_registrants as r on r.registrantID = tmp.registrantID
						INNER JOIN dbo.ev_events as e on e.eventID = tmp.eventID
						INNER JOIN dbo.cms_contentLanguages as cl on cl.contentID = e.eventContentID and cl.languageID = 1
						INNER JOIN dbo.ams_members as m ON m.memberID = tmp.memberID
						INNER JOIN ##tblRegistrantEmails as tmpRegEmail on tmpRegEmail.registrantID = tmp.registrantID;
					<cfelse>
						INSERT INTO ##tblRegistrants (registrantID, firstname, lastname, membernumber, company, eventTitle, row)
						SELECT distinct r.registrantID, m.firstname, m.lastname, m.membernumber, m.company, cl.contentTitle as eventTitle,
							ROW_NUMBER() OVER (ORDER BY #local.orderby# #arguments.event.getValue('direct')#) as row
						FROM ##tblRegistrantSearch as tmp
						INNER JOIN dbo.ev_registrants as r on r.registrantID = tmp.registrantID
						INNER JOIN dbo.ev_events as e on e.eventID = tmp.eventID
						INNER JOIN dbo.cms_contentLanguages as cl on cl.contentID = e.eventContentID and cl.languageID = 1
						INNER JOIN dbo.ams_members as m ON m.memberID = tmp.memberID;
					</cfif>

					SELECT @totalCount = COUNT(*) FROM ##tblRegistrants;

					SELECT registrantID, lastname, firstname, membernumber, company, eventTitle, email, @totalCount as totalCount
					FROM ##tblRegistrants
					WHERE row > @posStart
					AND row <= @posStartAndCount
					ORDER by row;

					IF OBJECT_ID('tempdb..##tblRegistrants') IS NOT NULL
						DROP TABLE ##tblRegistrants;
				<cfelseif listFindNoCase("regSearchEmail,regTabEmail",arguments.mode)>
					DECLARE @membersWithEmail int;

					SELECT @membersWithEmail = COUNT(*) FROM ##tblRegistrantEmails;

					SELECT distinct tmp.registrantID, tmp.eventID, c.applicationInstanceID, m.membernumber, @membersWithEmail as membersWithEmail
					FROM ##tblRegistrantSearch as tmp
					INNER JOIN dbo.ams_members as m ON m.memberID = tmp.memberID
					inner join dbo.ev_calendarEvents as ce on ce.sourceEventID = tmp.eventID and ce.calendarID = ce.sourceCalendarID
					inner join dbo.ev_calendars as c on c.siteID = @siteID and c.calendarID = ce.calendarID
					ORDER BY tmp.registrantID;
				<cfelseif arguments.mode eq "queueBadgePrinting">
					DECLARE @readyToProcessStatusID int, @badgeTemplateID int, @badgeDeviceID int;
					SET @badgeTemplateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fBadgeTemplateID',0)#">;
					SET @badgeDeviceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('badgeDeviceID',0)#">;
					EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='badgePrinting', @queueStatus='readyToProcess', @queueStatusID=@readyToProcessStatusID OUTPUT;

					INSERT INTO platformQueue.dbo.queue_badgePrinting (siteID, registrantID, templateID, deviceID, statusID, dateAdded, dateUpdated)
					SELECT DISTINCT @siteID, registrantID, @badgeTemplateID, @badgeDeviceID, @readyToProcessStatusID, GETDATE(), GETDATE()
					FROM ##tblRegistrantSearch;

					IF @@ROWCOUNT > 0
						EXEC dbo.sched_resumeTask @name='Process Badge Printing Queue', @engine='MCLuceeLinux';

					SELECT 1 AS success;
				<cfelseif arguments.mode eq "massRemoveRegistrants">
					DECLARE @massRemoveItemGroupUID uniqueidentifier = NEWID(), @massRemoveStatusReady int, @massRemovexmlMessage xml,
						@massRemoveAROption varchar(1), @massRemoveCancellationFee decimal(19,2), @massRemoveGLAccountID int, @massRemoveDeallocUsingPaidAmt bit;
					SET @massRemoveAROption = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('AROption','C')#">;
					SET @massRemoveCancellationFee = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.event.getValue('cancellationFee',0)#">;
					SET @massRemoveGLAccountID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('GLAccountID',0)#">;
					SET @massRemoveDeallocUsingPaidAmt = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('deallocUsingPaidAmt',0)#">;
					EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='evRegistrantDelete', @queueStatus='readyToProcess', @queueStatusID=@massRemoveStatusReady OUTPUT;

					INSERT INTO platformQueue.dbo.queue_evRegistrantDelete (itemGroupUID, siteID, orgID, registrantID, AROption, cancellationFee, 
						GLAccountID, deallocUsingPaidAmt, statusID, dateAdded, dateUpdated, recordedByMemberID)
					SELECT @massRemoveItemGroupUID, @siteID, @orgID, tmp.registrantID, @massRemoveAROption, @massRemoveCancellationFee, 
						@massRemoveGLAccountID, @massRemoveDeallocUsingPaidAmt, @massRemoveStatusReady, GETDATE(), GETDATE(), @currentMemberID
					FROM ##tblRegistrantSearch as tmp
					LEFT OUTER JOIN platformQueue.dbo.queue_evRegistrantDelete AS qid ON qid.registrantID = tmp.registrantID
					WHERE qid.itemID IS NULL;

					-- Send message to service broker to create all the individual messages
					SELECT @massRemovexmlMessage = ISNULL((
						SELECT 'evRegistrantDeleteLoad' AS t, CAST(@massRemoveItemGroupUID AS varchar(60)) AS u
						FOR XML RAW('mc'), TYPE
					),'<mc/>');
					EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@massRemovexmlMessage;

					SELECT 1 AS success;
				<cfelseif arguments.mode eq "regTabGrid">
					DECLARE @posStart int, @posStartAndCount int;
					SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
					SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;
					
					DECLARE @totalCount int, @eventEvalsCount int;
					
					IF OBJECT_ID('tempdb..##tblRegistrants') IS NOT NULL
						DROP TABLE ##tblRegistrants;
					IF OBJECT_ID('tempdb..##tblMembersWithCerts') IS NOT NULL
						DROP TABLE ##tblMembersWithCerts;
					IF OBJECT_ID('tempdb..##tblRegistrantEvals') IS NOT NULL
						DROP TABLE ##tblRegistrantEvals;
					CREATE TABLE ##tblRegistrants (dateRegistered datetime, registrantID int, firstname varchar(75), lastname varchar(75), 
						membernumber varchar(50), memberID int, company varchar(200), attended bit, isFlagged bit, status char(1), 
						totalRegFee decimal(14,2), amountDue decimal(14,2), rateTID int, eventStatus char(1), rateID int, row int);
					CREATE TABLE ##tblMembersWithCerts (memberID int PRIMARY KEY, certificateID int);
					CREATE TABLE ##tblRegistrantEvals (registrantID int PRIMARY KEY, pendingEvalCount int);

					SELECT @eventEvalsCount = COUNT(eventFormID) FROM dbo.ev_eventsAndForms WHERE eventID = @eventID;

					INSERT INTO ##tblRegistrants
					SELECT distinct r.dateRegistered, r.registrantID, m.firstname, m.lastname, m.membernumber, m.memberID, m.company, 
						r.attended, r.isFlagged, r.status, regFee.totalRegFee, regFee.totalRegFee-regFee.regFeePaid as amountDue,
						regFee.TransactionIDForRateAdjustment as rateTID, e.status, tmp.rateID, 
						ROW_NUMBER() OVER (ORDER BY r.status, #preserveSingleQuotes(local.orderby)# #arguments.event.getValue('orderDir','asc')#, r.registrantID desc) as row
					FROM ##tblRegistrantSearch as tmp
					INNER JOIN dbo.ev_registrants as r on r.registrantID = tmp.registrantID
					INNER JOIN dbo.ev_events as e on e.eventID = tmp.eventID
					INNER JOIN dbo.ams_members as m ON m.memberID = tmp.memberID
					LEFT OUTER JOIN ##tblRegistrantFees as regFee on regFee.registrantID = r.registrantID;

					SELECT @totalCount = @@ROWCOUNT;

					-- get the event and sub events so we can check for certificates for any of them
					declare @tblSubEvents TABLE (masterEventID int, childEventID int);
					insert into @tblSubEvents
					select eventID as masterEventID, eventID as childEventID
					FROM dbo.ev_events
					where eventID = @eventID
						union
					select e.eventID as masterEventID, subEvent.eventID as childEventID
					FROM dbo.ev_events as e
					inner join dbo.ev_subEvents as subEvent on subEvent.parentEventID = e.eventID and e.eventID = @eventID;

					-- members who have earned a certificate for any of them
					INSERT INTO ##tblMembersWithCerts
					SELECT tmp.memberID, max(c.certificateID) as certificateID 
					FROM ##tblRegistrants as tmp
					INNER JOIN dbo.ams_members as m on m.activeMemberID = tmp.memberID
					INNER JOIN dbo.ev_registrants as r on r.memberID = m.memberID and r.attended = 1 and r.status = 'A'
					INNER JOIN dbo.ev_registration as rn ON rn.registrationID = r.registrationID and rn.siteID = @siteID AND rn.status = 'A' 
					INNER JOIN @tblSubEvents as se on se.childEventID = rn.eventID
					inner join dbo.crd_requests as rc on rc.registrantID = r.registrantID and rc.creditAwarded = 1 
					inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID 
					inner join dbo.crd_authoritySponsorTypes as ecast on ecast.astid = ect.astid 
					inner join dbo.crd_certificates as c on c.certificateID = ecast.LiveApprovedCertificateID 
					where tmp.attended = 1
					and tmp.status = 'A'
					group by tmp.memberID;

					IF @eventEvalsCount > 0
						INSERT INTO ##tblRegistrantEvals (registrantID, pendingEvalCount)
						SELECT tmp.registrantID, @eventEvalsCount - COUNT(DISTINCT efr.eventFormID)
						FROM ##tblRegistrants as tmp
						LEFT OUTER JOIN dbo.ev_eventsAndFormResponses AS efr
							INNER JOIN dbo.ev_eventsAndForms AS ef ON ef.eventFormID = efr.eventFormID
								AND ef.eventID = @eventID
							INNER JOIN formbuilder.dbo.tblForms as f ON f.formID = ef.formID
								AND f.isDeleted = 0
							INNER JOIN formbuilder.dbo.tblResponses AS r ON r.responseID = efr.responseID
								AND r.isActive = 1
								AND r.dateCompleted IS NOT NULL
							ON efr.registrantID = tmp.registrantID
						GROUP BY tmp.registrantID;

					SELECT tmp.dateRegistered, tmp.registrantID, tmp.firstname, tmp.lastname, tmp.membernumber, tmp.memberID, 
						tmp.company, tmp.attended, tmp.isFlagged, tmp.status, @totalCount as totalCount, tmp.totalRegFee, 
						tmp.amountDue, tmp.rateTID, eventStatus,
						invoicesDue = case 
										when tmp.amountDue > 0 
											then (select substring((
											  select ','+ cast(invoiceID as varchar(10)) AS [text()]
												from ##tblRegistrantInvoices
												where registrantID = tmp.registrantID
												For XML PATH ('')
												), 2, 2000)) 
										else '' end,
						cast(isnull((
							select offeringTypeID, creditAwarded 
							from (
								select credit.offeringTypeID, credit.creditAwarded
								from dbo.crd_requests as credit
								where registrantID = tmp.registrantID
									union
								select cr.offeringTypeID, cr.creditAwarded
								from dbo.crd_requests as cr
								inner join dbo.ev_registrants as er on er.registrantID = cr.registrantID
									and er.memberID = tmp.memberID
								inner join dbo.ev_registration as reg on reg.registrationID = er.registrationID and reg.siteID = @siteID
								inner join dbo.ev_subEvents as subEvent on subEvent.eventID = reg.eventID
									and subEvent.parentEventID = @eventID
							) as credit
							for XML AUTO, ROOT('credits')
							),'<credits/>') as xml) as creditsXML, 
						case when certs.certificateID is null then 0 else 1 end as showCert,
						case when tmp.attended = 1 and isnull(evals.pendingEvalCount,0) > 0 then 1 else 0 end as showEval,
						(select STRING_AGG(c.categoryName,'|')
						from dbo.ev_registrantCategories as rc 
						inner join dbo.cms_categories as c on c.categoryID = rc.categoryID
						where rc.registrantID = tmp.registrantID
						and c.isActive = 1) as regRoles,
						r.rateName as regrate
					FROM ##tblRegistrants as tmp
					LEFT OUTER JOIN dbo.ev_rates as r on r.rateID = tmp.rateID
					LEFT OUTER JOIN ##tblMembersWithCerts as certs on certs.memberID = tmp.memberID
					LEFT OUTER JOIN ##tblRegistrantEvals as evals on evals.registrantID = tmp.registrantID
					WHERE tmp.row > @posStart
					AND tmp.row <= @posStartAndCount
					ORDER BY tmp.row;

					IF OBJECT_ID('tempdb..##tblRegistrants') IS NOT NULL
						DROP TABLE ##tblRegistrants;
					IF OBJECT_ID('tempdb..##tblMembersWithCerts') IS NOT NULL
						DROP TABLE ##tblMembersWithCerts;
					IF OBJECT_ID('tempdb..##tblRegistrantEvals') IS NOT NULL
						DROP TABLE ##tblRegistrantEvals;
				<cfelseif arguments.mode eq "regTabSignInSheet">
					
					DECLARE @fieldSetID int, @colList varchar(max), @joinList varchar(max), @vwSQL varchar(max), @signinExtendedNameCol varchar(100),
						@orderByCols varchar(100);

					IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
						DROP TABLE ##tmpMembers;
					IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
						DROP TABLE ##tmp_membersForFS;
					IF OBJECT_ID('tempdb..##tmpMDMemberIDs') IS NOT NULL
						DROP TABLE ##tmpMDMemberIDs;
					IF OBJECT_ID('tempdb..##tmpMDResults') IS NOT NULL
						DROP TABLE ##tmpMDResults;
					CREATE TABLE ##tmpMembers (MFSAutoID int IDENTITY(1,1) not null);
					CREATE TABLE ##tmp_membersForFS (memberID int PRIMARY KEY);
					CREATE TABLE ##tmpMDMemberIDs (memberID int PRIMARY KEY);
					CREATE TABLE ##tmpMDResults (MCAutoID int IDENTITY(1,1) NOT NULL);
					
					SET @fieldSetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.resultsFieldsetID#">;
					
					IF @fieldSetID > 0 BEGIN
						-- get fieldset data
						INSERT INTO ##tmp_membersForFS (memberID)
						select distinct memberID
						from ##tblRegistrantSearch;
						
						EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@fieldsetID, @existingFields='',
							@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmp_membersForFS', @membersResultTableName='##tmpMembers',
							@linkedMembers=0, @mode='export', @outputFieldsXML=@outputFieldsXML OUTPUT;
					END

					IF @fieldSetID > 0 AND EXISTS (select [name] from tempdb.sys.columns where object_id = object_id('tempdb..##tmpMembers') and [name] = 'Extended Name') BEGIN
						SET @signinExtendedNameCol = 'mtmp.[Extended Name]';
						SET @joinList = ISNULL(@joinList,'') + ' LEFT OUTER JOIN ##tmpMembers as mtmp ON mtmp.memberID = tmp.memberID'
						SET @orderByCols = 'mtmp.[Extended Name], m.membernumber';
					END
					ELSE BEGIN
						SET @signinExtendedNameCol = 'm.lastname + '', '' + m.firstname';
						SET @orderByCols = 'm.lastname, m.firstname, m.membernumber';
					END

					INSERT INTO ##tmpMDMemberIDs (memberID)
					SELECT DISTINCT memberID
					FROM ##tblRegistrantSearch;

					EXEC dbo.ams_prepMemberAllViewData @orgID=@orgID, @membersTableName='##tmpMDMemberIDs',
						@membersResultTableName='##tmpMDResults', @colList=@colList OUTPUT;
					
					IF @colList IS NOT NULL BEGIN
						SET @colList = ',' + @colList;
						SET @joinList = ISNULL(@joinList,'') + ' inner join ##tmpMDResults as vwmd on vwmd.memberID = tmp.memberID';
					END
					
					<!--- NOTE: any new column added below should also be added in corresponding RSVP select in getRSVPRegistrants or bypass with a condition in signinsheet view --->
					SET @vwSQL = 'SELECT reg.registrantID, r.rateName, m.memberID, m.firstname, m.lastname, m.membernumber, m.company, '+ @signinExtendedNameCol +' AS signin_extendedName' + ISNULL(@colList,'') + '
						FROM ##tblRegistrantSearch AS tmp
						INNER JOIN dbo.ev_registrants AS reg on reg.registrantID = tmp.registrantID
						LEFT OUTER JOIN dbo.ev_rates AS r
							INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = r.siteResourceID
							INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID
								AND srs.siteResourceStatusDesc = ''Active''
							ON r.rateID = reg.rateID
						INNER JOIN dbo.ams_members AS m ON m.memberID = tmp.memberID
						' +	ISNULL(@joinList,'') +
						' ORDER BY '+ @orderByCols +';';

					EXEC(@vwSQL);

					IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
						DROP TABLE ##tmpMembers;
					IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
						DROP TABLE ##tmp_membersForFS;
					IF OBJECT_ID('tempdb..##tmpMDMemberIDs') IS NOT NULL
						DROP TABLE ##tmpMDMemberIDs;
					IF OBJECT_ID('tempdb..##tmpMDResults') IS NOT NULL
						DROP TABLE ##tmpMDResults;
				<cfelseif arguments.mode eq "regTabCreditGrid">
					SELECT r.registrantID, m.firstname, m.lastname, m.membernumber, r.attended, 
						regFee.totalRegFee-regFee.regFeePaid as amountDue,
						cast(isnull((
							select credit.offeringTypeID, isnull(ast.ovTypeName,cat.typeName) as creditType, 
								isnull(ca.authorityName,'') as authority, credit.creditAwarded, 
								credit.creditValueAwarded, credit.addedViaAward
							from dbo.crd_requests as credit
							inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = credit.offeringTypeID
							inner join dbo.crd_authoritySponsorTypes as ast on ast.ASTID = ect.ASTID
							inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
							inner join dbo.crd_authorities as ca on ca.authorityId = cat.authorityID
							where credit.registrantID = r.registrantID
							for XML AUTO, ROOT('credits')
						),'<credits/>') as xml) as creditsXML
					FROM ##tblRegistrantSearch as tmp
					INNER JOIN dbo.ev_registrants as r on r.registrantID = tmp.registrantID
					INNER JOIN dbo.ams_members as m ON m.memberID = tmp.memberID
					LEFT OUTER JOIN ##tblRegistrantFees as regFee on regFee.registrantID = r.registrantID
					ORDER BY case when regFee.totalRegFee-regFee.regFeePaid > 0 then 1 else 0 end desc, m.lastname, m.firstname, m.membernumber;
				<cfelseif arguments.mode eq "regTabDownload">
					select r.registrantID
					FROM ##tblRegistrantSearch as tmp
					INNER JOIN dbo.ev_registrants as r on r.registrantID = tmp.registrantID
					WHERE r.status = 'A';
				<cfelseif listFindNoCase("emailCerts,emailCertAttendees,previewEmailCertAttendeesGrid",arguments.mode)>
					IF OBJECT_ID('tempdb..##tblMainEvRegistrants') is not null
						DROP TABLE ##tblMainEvRegistrants;
					IF OBJECT_ID('tempdb..##tblMembersWithCerts') is not null
						DROP TABLE ##tblMembersWithCerts;
					CREATE TABLE ##tblMainEvRegistrants (registrantID int, memberID int, eventID int);
					CREATE TABLE ##tblMembersWithCerts (memberID int);

					INSERT INTO ##tblMainEvRegistrants (registrantID, memberID, eventID)
					SELECT tmp.registrantID, tmp.memberID, tmp.eventID
					FROM ##tblRegistrantSearch as tmp
					INNER JOIN dbo.ev_registrants as r on r.registrantID = tmp.registrantID
					WHERE r.status = 'A';

					-- get the event and sub events so we can check for certificates for any of them
					declare @tblSubEvents TABLE (masterEventID int, childEventID int);
					insert into @tblSubEvents
					select eventID as masterEventID, eventID as childEventID
					FROM dbo.ev_events
					where eventID = @eventID
						union
					select e.eventID as masterEventID, subEvent.eventID as childEventID
					FROM dbo.ev_events as e
					inner join dbo.ev_subEvents as subEvent on subEvent.parentEventID = e.eventID and e.eventID = @eventID;

					-- members who have earned a certificate for any of them
					INSERT INTO ##tblMembersWithCerts
					SELECT DISTINCT tmp.memberID
					FROM ##tblMainEvRegistrants as tmp
					INNER JOIN dbo.ams_members as m on m.activeMemberID = tmp.memberID
					INNER JOIN dbo.ev_registrants as r on r.memberID = m.memberID and r.attended = 1 and r.status = 'A'
					INNER JOIN dbo.ev_registration as rn ON rn.registrationID = r.registrationID AND rn.siteID = @siteID AND rn.status = 'A' 
					INNER JOIN @tblSubEvents as se on se.childEventID = rn.eventID
					INNER JOIN dbo.crd_requests as rc on rc.registrantID = r.registrantID and rc.creditAwarded = 1 
					INNER JOIN dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID 
					INNER JOIN dbo.crd_authoritySponsorTypes as ecast on ecast.astid = ect.astid 
					INNER JOIN dbo.crd_certificates as c on c.certificateID = ecast.LiveApprovedCertificateID;

					<cfif arguments.mode eq 'emailCerts'>
						SELECT DISTINCT tmp.registrantID 
						FROM ##tblMainEvRegistrants as tmp
						INNER JOIN ##tblMembersWithCerts as cert on cert.memberID = tmp.memberID;
					<cfelseif arguments.mode eq 'emailCertAttendees'>
						DECLARE @membersWithEmail int;
						
						SELECT @membersWithEmail = COUNT(*) 
						FROM ##tblMainEvRegistrants as tmp
						INNER JOIN ##tblMembersWithCerts as cert on cert.memberID = tmp.memberID
						INNER JOIN ##tblRegistrantEmails as tmpRegEmail on tmpRegEmail.registrantID = tmp.registrantID;

						SELECT distinct tmp.registrantID, tmp.eventID, c.applicationInstanceID, m.membernumber, @membersWithEmail as membersWithEmail
						FROM ##tblMainEvRegistrants as tmp
						INNER JOIN ##tblMembersWithCerts as cert on cert.memberID = tmp.memberID
						INNER JOIN dbo.ams_members as m ON m.memberID = tmp.memberID
						inner join dbo.ev_calendarEvents as ce on ce.sourceEventID = tmp.eventID and ce.calendarID = ce.sourceCalendarID
						inner join dbo.ev_calendars as c on c.siteID = @siteID and c.calendarID = ce.calendarID
						ORDER BY tmp.registrantID;
					<cfelseif arguments.mode eq 'previewEmailCertAttendeesGrid'>
						DECLARE @posStart int, @posStartAndCount int, @totalCount int;
						SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
						SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;
						
						IF OBJECT_ID('tempdb..##tblRegistrants') IS NOT NULL
							DROP TABLE ##tblRegistrants;
						CREATE TABLE ##tblRegistrants (registrantID int, firstname varchar(75), lastname varchar(75), membernumber varchar(50),
							company varchar(200), email varchar(400), row int);

						INSERT INTO ##tblRegistrants
						SELECT distinct tmp.registrantID, m.firstname, m.lastname, m.membernumber, m.company, tmpRegEmail.email, 
							ROW_NUMBER() OVER (ORDER BY #local.orderby# #arguments.event.getValue('direct')#) as row
						FROM ##tblMainEvRegistrants as tmp
						INNER JOIN ##tblMembersWithCerts as cert on cert.memberID = tmp.memberID
						INNER JOIN dbo.ams_members as m ON m.memberID = tmp.memberID
						INNER JOIN ##tblRegistrantEmails as tmpRegEmail on tmpRegEmail.registrantID = tmp.registrantID;

						SELECT @totalCount = @@ROWCOUNT;

						SELECT registrantID, lastname, firstname, membernumber, company, email, @totalCount as totalCount
						FROM ##tblRegistrants
						WHERE row > @posStart
						AND row <= @posStartAndCount
						ORDER by row;

						IF OBJECT_ID('tempdb..##tblRegistrants') IS NOT NULL
							DROP TABLE ##tblRegistrants;
					</cfif>

					IF OBJECT_ID('tempdb..##tblMainEvRegistrants') is not null
						DROP TABLE ##tblMainEvRegistrants;
					IF OBJECT_ID('tempdb..##tblMembersWithCerts') is not null
						DROP TABLE ##tblMembersWithCerts;
				<cfelseif arguments.mode eq 'formResponses'>
					DECLARE @formID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getTrimValue('formID')#">;
						<cfif arguments.event.getTrimValue('exportType') eq "csv">
							EXEC dbo.ev_exportRegistrantFormResponses @formID=@formID, @filename="#arguments.event.getValue('strFolder').folderPathUNC#\Responses.csv";
							SELECT 1 AS "success";
						<cfelseif arguments.event.getTrimValue('exportType') eq "pdf">
							DECLARE @xmlResult xml;
							EXEC dbo.ev_getRegistrantsFormResponseSummaryXML @formID=@formID, @xmlResult=@xmlResult OUTPUT;
							SELECT @xmlResult AS xmlResult;
						</cfif>
				</cfif>

				<cfif local.sourceApp eq "regTab">
					IF OBJECT_ID('tempdb..##tblRegistrantFees') IS NOT NULL
						DROP TABLE ##tblRegistrantFees;
				</cfif>
				<cfif listFindNoCase("regSearchGrid,regTabGrid,massRemoveRegistrants",arguments.mode)>
					IF OBJECT_ID('tempdb..##tblRegistrantInvoices') IS NOT NULL
						DROP TABLE ##tblRegistrantInvoices;
				</cfif>
				<cfif listFindNoCase("regSearchEmailGrid,regTabEmailGrid,regSearchEmail,regTabEmail",arguments.mode)>
					IF OBJECT_ID('tempdb..##tblRegistrantEmails') IS NOT NULL
						DROP TABLE ##tblRegistrantEmails;
				</cfif>

				IF OBJECT_ID('tempdb..##tblRegistrantSearch') IS NOT NULL
					DROP TABLE ##tblRegistrantSearch;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.qryRegistrants>
	</cffunction>
	
	<cffunction name="getRSVPRegistrants" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRegistrants">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			-- extra columns added here because of usage in signinsheets
			SELECT rsvp.rsvpID, rsvp.firstname, rsvp.lastname, rsvp.company, '' as memberNumber, '' as rateName, 0 as memberID, 0 as registrantID, 
				isnull(nullIf(rsvp.salutation,'') + ' ','') + rsvp.firstname + ' ' + rsvp.lastname as signin_extendedName
			FROM dbo.ev_rsvp as rsvp
			INNER JOIN dbo.ev_registration as reg ON reg.registrationID = rsvp.registrationID and reg.status = 'A'
			AND reg.eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('eID')#">
			AND reg.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			ORDER BY lastname;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn local.qryRegistrants>
	</cffunction>

	<cffunction name="getSiteEventCustomFields" access="public" output="false" returntype="query">
		<cfargument name="siteResourceID" type="numeric" required="yes">
		
		<cfset var qryEventFields = ''>

		<cfquery name="qryEventFields" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @usageID int, @eventAdminSiteResourceID int;
			select @usageID = dbo.fn_cf_getUsageID('EventAdmin','Event',null);
			set @eventAdminSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">;

			select f.fieldReference
			from dbo.cf_fields as f
			inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID
			where fu.usageID = @usageID
			and f.controllingSiteResourceID = @eventAdminSiteResourceID
			and len(f.fieldReference) > 0
			and f.isActive = 1
			order by f.fieldOrder;
		</cfquery>

		<cfreturn qryEventFields>
	</cffunction>

	<cffunction name="getSiteEventCustomFieldDetails" access="public" output="false" returntype="struct">
		<cfargument name="containerSiteResourceID" type="numeric" required="yes">
		<cfargument name="itemSiteResourceID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.strEventFieldData = structNew()>

		<cfquery name="local.qryEventFieldData" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpSWCF') is not null
				DROP TABLE ##tmpSWCF;
			CREATE TABLE ##tmpSWCF (fieldID int, titleOnInvoice varchar(128), answer varchar(max));

			DECLARE @crossEventFieldUsageID int, @controllingSiteResourceID int, @itemSiteResourceID int;
			SET @controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.containerSiteResourceID#">;
			SET @itemSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemSiteResourceID#">;

			SELECT @crossEventFieldUsageID = dbo.fn_cf_getUsageID('EventAdmin','Event',NULL);

			INSERT INTO ##tmpSWCF (fieldID, titleOnInvoice, answer)
			SELECT fieldID, fieldReference, STRING_AGG(customValue,', ')
			FROM dbo.fn_cf_getResponses (@itemSiteResourceID, 'CrossEvent', NULL)
			GROUP BY fieldID, fieldReference;

			SELECT f.fieldReference as titleOnInvoice, '' as answer
			FROM dbo.cf_fields as f
			LEFT OUTER JOIN ##tmpSWCF as tmp on tmp.fieldID = f.fieldID
			WHERE f.controllingSiteResourceID = @controllingSiteResourceID
			AND f.usageID = @crossEventFieldUsageID
			AND f.isActive = 1
			AND len(f.fieldReference) > 0
			AND tmp.fieldID is null
				UNION ALL
			SELECT titleOnInvoice, ANSWER
			FROM ##tmpSWCF;

			IF OBJECT_ID('tempdb..##tmpSWCF') is not null
				DROP TABLE ##tmpSWCF;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfloop query="local.qryEventFieldData">
			<cfif not structKeyExists(local.strEventFieldData,local.qryEventFieldData.titleOnInvoice)>
				<cfset local.strEventFieldData[local.qryEventFieldData.titleOnInvoice] = local.qryEventFieldData.answer>
			</cfif>
		</cfloop>

		<cfreturn local.strEventFieldData>
	</cffunction>

	<cffunction name="getEventRoleFields" access="public" output="false" returntype="query">
		<cfargument name="siteResourceID" type="numeric" required="yes">
		
		<cfset var qryEventRoleFields = ''>

		<cfquery name="qryEventRoleFields" datasource="#application.dsn.membercentral.dsn#">
			declare @roleFieldUsageID int;

			select @roleFieldUsageID = dbo.fn_cf_getUsageID('EventAdmin','Role',NULL);

			select c.categoryName + '_' + f.fieldReference as titleOnInvoice
			from dbo.cf_fields as f
			inner join dbo.cms_categories as c on c.categoryID = f.detailID
			where f.controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">
			and f.usageID = @roleFieldUsageID
			and f.isActive = 1
			and c.isActive = 1
			and len(f.fieldReference) > 0
			order by c.categoryName, f.fieldReference;
		</cfquery>
		
		<cfreturn qryEventRoleFields>
	</cffunction>

	<cffunction name="getEventRoleFieldData" access="public" output="false" returntype="struct">
		<cfargument name="controllingSiteResourceID" type="numeric" required="yes">
		<cfargument name="registrantID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.strEventRoleFieldData = structNew()>

		<cfquery name="local.qryEventRoleFieldData" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpRoleFieldData') is not null
				DROP TABLE ##tmpRoleFieldData;
			CREATE TABLE ##tmpRoleFieldData (fieldID int, titleOnInvoice varchar(128), answer varchar(max));

			DECLARE @roleFieldUsageID int, @controllingSiteResourceID int, @registrantID int;
			SET @controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.controllingSiteResourceID#">;
			SET @registrantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.registrantID#">;

			SELECT @roleFieldUsageID = dbo.fn_cf_getUsageID('EventAdmin','Role',NULL);

			INSERT INTO ##tmpRoleFieldData (fieldID, titleOnInvoice, answer)
			SELECT fieldID, fieldReference, STRING_AGG(customValue,', ')
			FROM dbo.fn_cf_getResponses (@registrantID, 'EventRole', NULL)
			GROUP BY fieldID, fieldReference;

			SELECT c.categoryName + '_' + f.fieldReference as titleOnInvoice, '' as answer
			FROM dbo.cf_fields as f
			INNER JOIN dbo.cms_categories as c on c.categoryID = f.detailID
			LEFT OUTER JOIN ##tmpRoleFieldData as tmp on tmp.fieldID = f.fieldID
			WHERE f.controllingSiteResourceID = @controllingSiteResourceID
			AND f.usageID = @roleFieldUsageID
			AND f.isActive = 1
			AND c.isActive = 1
			AND len(f.fieldReference) > 0
			AND tmp.fieldID is null
				UNION ALL
			SELECT c.categoryName + '_' + fd.titleOnInvoice as titleOnInvoice, fd.answer
			FROM ##tmpRoleFieldData as fd
			INNER JOIN dbo.cf_fields as f on f.fieldID = fd.fieldID
			INNER JOIN dbo.cms_categories as c on c.categoryID = f.detailID;

			IF OBJECT_ID('tempdb..##tmpRoleFieldData') is not null
				DROP TABLE ##tmpRoleFieldData;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfloop query="local.qryEventRoleFieldData">
			<cfif not structKeyExists(local.strEventRoleFieldData,local.qryEventRoleFieldData.titleOnInvoice)>
				<cfset local.strEventRoleFieldData[local.qryEventRoleFieldData.titleOnInvoice] = local.qryEventRoleFieldData.answer>
			</cfif>
		</cfloop>

		<cfreturn local.strEventRoleFieldData>
	</cffunction>

	<cffunction name="getEventRoleMembers" access="public" output="false" returntype="struct">
		<cfargument name="controllingSiteResourceID" type="numeric" required="true">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strEventRoleMembers = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryEventRoleMembers">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @categoryTreeID int, @eventID int, @orgID int, @siteID int, @registrationID int;
			set @eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">;
			set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			select @categoryTreeID = dbo.fn_getCategoryTreeIDForSiteResourceIDandTree(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.controllingSiteResourceID#">,'Event Roles');

			select @registrationID = registrationID
			from dbo.ev_registration 
			where siteID = @siteID 
			and eventID = @eventID 
			and [status] = 'A';

			select categoryName as titleOnInvoice, STRING_AGG(memberName,', ') as memberName
			from 
			(select distinct c.categoryName , mActive.firstName + isnull(' ' + nullif(mActive.middleName,''),'') + ' ' + mActive.lastName  as memberName
			from dbo.ev_registrants as r
			inner join dbo.ev_registrantCategories as rc on r.registrantID = rc.registrantID
			inner join dbo.cms_categories as c on c.categoryTreeID = @categoryTreeID
				and rc.categoryID = c.categoryID
				and c.isActive = 1 
				and c.parentCategoryID is null
			inner join dbo.ams_members as m on m.orgID = @orgID
				and m.memberID = r.memberID 
			inner join dbo.ams_members as mActive on mActive.orgID = @orgID
				and mActive.memberID = m.activeMemberID
			where r.recordedOnSiteID = @siteID
			and r.registrationID = @registrationID
			and r.[status] = 'A') as tmp
			group by categoryName
				union all
			select c.categoryName as titleOnInvoice, '' as memberName
			from dbo.cms_categories as c
			left outer join dbo.ev_registrantCategories as rc 
				inner join dbo.ev_registrants as r on r.recordedOnSiteID = @siteID 
					and r.registrantID = rc.registrantID 
					and r.[status] = 'A'
				inner join dbo.ev_registration as reg on reg.siteID = @siteID
					and reg.registrationID = r.registrationID 
					and reg.eventID = @eventID 
					and reg.siteID = r.recordedOnSiteID
				on rc.categoryID = c.categoryID
			where c.categoryTreeID = @categoryTreeID
			and c.isActive = 1 
			and c.parentCategoryID is null
			and rc.registrantCategoryID is null;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfloop query="local.qryEventRoleMembers">
			<cfif not structKeyExists(local.strEventRoleMembers,local.qryEventRoleMembers.titleOnInvoice)>
				<cfset local.strEventRoleMembers[local.qryEventRoleMembers.titleOnInvoice] = local.qryEventRoleMembers.memberName>
			</cfif>
		</cfloop>

		<cfset local.queryAllEventRoles = getEventRoles(siteResourceID=arguments.controllingSiteResourceID)>

		<cfloop query="local.queryAllEventRoles">
			<cfif not structKeyExists(local.strEventRoleMembers,local.queryAllEventRoles.categoryName)>
				<cfset local.strEventRoleMembers[local.queryAllEventRoles.categoryName] = "">
			</cfif>
		</cfloop>

		<cfreturn local.strEventRoleMembers>
	</cffunction>

	<cffunction name="getEventRegFields" access="public" output="false" returntype="query">
		<cfargument name="eventID" type="numeric" required="yes">
		
		<cfset var qryEventRegFields = ''>

		<cfquery name="qryEventRegFields" datasource="#application.dsn.membercentral.dsn#">
			select f.fieldReference
			from dbo.ev_events as e 
			inner join dbo.cf_fields as f on f.controllingSiteResourceID = e.siteResourceID
			where e.eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.eventID#">
			and f.isActive = 1
			and len(f.fieldReference) > 0;
		</cfquery>
		
		<cfreturn qryEventRegFields>
	</cffunction>

	<cffunction name="getEventRegFieldData" access="public" output="false" returntype="struct">
		<cfargument name="siteResourceID" type="numeric" required="yes">
		<cfargument name="registrantID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.strEventRegFieldData = structNew()>

		<cfquery name="local.qryEventRegFieldData" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpRegFieldData') is not null
				DROP TABLE ##tmpRegFieldData;
			CREATE TABLE ##tmpRegFieldData (fieldID int, fieldReference varchar(128), answer varchar(max));

			DECLARE @siteResourceID int, @registrantID int;
			SET @siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">;
			SET @registrantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.registrantID#">;
			
			INSERT INTO ##tmpRegFieldData (fieldID, fieldReference, answer)
			SELECT fieldID, fieldReference, STRING_AGG(customValue,', ')
			FROM dbo.fn_cf_getResponses (@registrantID, 'EventRegCustom', NULL)
			GROUP BY fieldID, fieldReference;

			SELECT f.fieldReference, '' as answer
			FROM dbo.cf_fields as f
			INNER JOIN dbo.ev_events as e on e.siteResourceID = f.controllingSiteResourceID
				and f.controllingSiteResourceID = @siteResourceID
			LEFT OUTER JOIN ##tmpRegFieldData as tmp on tmp.fieldID = f.fieldID
			WHERE f.isActive = 1
			AND len(f.fieldReference) > 0
			AND tmp.fieldID is null
				UNION ALL
			SELECT fieldReference, ANSWER
			FROM ##tmpRegFieldData;

			IF OBJECT_ID('tempdb..##tmpRegFieldData') is not null
				DROP TABLE ##tmpRegFieldData;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfloop query="local.qryEventRegFieldData">
			<cfif not structKeyExists(local.strEventRegFieldData,local.qryEventRegFieldData.fieldReference)>
				<cfset local.strEventRegFieldData[local.qryEventRegFieldData.fieldReference] = local.qryEventRegFieldData.answer>
			</cfif>
		</cfloop>

		<cfreturn local.strEventRegFieldData>
	</cffunction>

	<cffunction name="getFirstRegPriceIDFromList" access="private" output="false" returntype="numeric">
		<cfargument name="priceIDList" type="string" required="yes">
		
		<cfset var qryFirstRegPriceID = ''>

		<cfquery name="qryFirstRegPriceID" datasource="#application.dsn.membercentral.dsn#">
			select top 1 tpa.priceID
			from dbo.ev_ticketPackageAvailable as tpa 
			inner join dbo.ev_priceSchedule as ps on ps.scheduleID = tpa.scheduleID
			where tpa.priceID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="0#arguments.priceIDList#">)
			and tpa.isActive = 1
			order by ps.startDate;
		</cfquery>
		
		<cfreturn val(qryFirstRegPriceID.priceID)>
	</cffunction>

	<cffunction name="checkEventCode" access="public" output=false returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="reportCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cfquery name="local.qryCheck" datasource="#application.dsn.membercentral.dsn#">
			SELECT eventID
			FROM dbo.ev_events
			WHERE siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			AND eventID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">
			AND reportCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.reportCode#">
			AND [status] in ('A','I')
		</cfquery>

		<cfset local.data.success = local.qryCheck.recordCount eq 0>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="saveEventsFilter" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			getEventsFilter();
			local.EventsFilter = application.mcCacheManager.sessionGetValue(keyname='EventsFilter',defaultValue={});
			local.EventsFilter_listFilter_hash = hash(serializeJSON(local.EventsFilter.listFilter), "SHA", "UTF-8");
			local.fromDt 		= dateFormat(now(), "m/d/yyyy");
			local.toDt 			= dateFormat(dateAdd("d",365,now()), "m/d/yyyy");
			local.EventsFilter.listFilter.fDateFrom = arguments.event.getValue('fDateFrom',local.fromDt);
			local.EventsFilter.listFilter.fDateTo = arguments.event.getValue('fDateTo',local.toDt);
			local.EventsFilter.listFilter.fCalendar = arguments.event.getValue('fCalendar',0);
			local.EventsFilter.listFilter.fCategory = arguments.event.getValue('fCategory',0);
			local.EventsFilter.listFilter.fKeyword = arguments.event.getValue('fKeyword','');
			local.EventsFilter.listFilter.fReportCode = arguments.event.getValue('fReportCode','');
			local.EventsFilter.listFilter.fRegType = arguments.event.getValue('fRegType',0);
			local.EventsFilter.listFilter.fHideDeleted = arguments.event.getValue('fHideDeleted',1);
			local.EventsFilter.listFilter.fEventType = arguments.event.getValue('fEventType','all');
			local.EventsFilter.listFilter.isSearch = arguments.event.getValue('isSearch','0');
			
			if (local.EventsFilter_listFilter_hash NEQ hash(serializeJSON(local.EventsFilter.listFilter), "SHA", "UTF-8")) 
				application.mcCacheManager.sessionSetValue(keyname='EventsFilter', value=local.EventsFilter);
			local.data.success = true;
		</cfscript>
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="getEventsFilter" access="public" output="false" returntype="struct">
		<cfscript>
			var local = structNew();
			local.fromDt 		= dateFormat(now(), "m/d/yyyy");
			local.toDt 			= dateFormat(dateAdd("d",365,now()), "m/d/yyyy");
			local.tmpStr = { fDateFrom=local.fromDt, fDateTo=local.toDt, fCalendar=0, fCategory=0, fKeyword='', fReportCode='', fRegType=0, 
				fHideDeleted=1,fEventType='all',isSearch=''};

			local.EventsFilter = application.mcCacheManager.sessionGetValue(keyname='EventsFilter',defaultValue={});
			local.EventsFilter_hash = hash(serializeJSON(local.EventsFilter), "SHA", "UTF-8");
			if (NOT structKeyExists(local.EventsFilter,"listFilter"))
				local.EventsFilter.listFilter = duplicate(local.tmpStr);
			
			for (local.thiskey in local.tmpStr) {
				if (not structKeyExists(local.EventsFilter.listFilter, local.thiskey))
					structInsert(local.EventsFilter.listFilter, local.thiskey, local.tmpStr[local.thiskey], true);
			}
			if (local.EventsFilter_hash NEQ hash(serializeJSON(local.EventsFilter), "SHA", "UTF-8")) 
				application.mcCacheManager.sessionSetValue(keyname='EventsFilter', value=local.EventsFilter);
		</cfscript>

		<cfreturn local.EventsFilter>
	</cffunction>

	<cffunction name="savecalendarEventsFilter" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.calendarId = arguments.event.getValue('cID','');
			local.listFilterKey = 'listFilter_' & local.calendarId;
			getcalendarEventsFilter(local.calendarId);
			local.calendarEventsFilter = application.mcCacheManager.sessionGetValue(keyname='calendarEventsFilter',defaultValue={});
			local.calendarEventsFilter_hash = hash(serializeJSON(local.calendarEventsFilter), "SHA", "UTF-8");
			local.fromDt 		= dateFormat(now(), "m/d/yyyy");
			local.toDt 			= dateFormat(dateAdd("d",365,now()), "m/d/yyyy");
			local.tmpStr = {};
			local.calendarEventsFilter[local.listFilterKey].fDateFrom = arguments.event.getValue('fDateFrom',local.fromDt);
			local.calendarEventsFilter[local.listFilterKey].fDateTo = arguments.event.getValue('fDateTo',local.toDt);
			local.calendarEventsFilter[local.listFilterKey].fCategory = arguments.event.getValue('fCategory',0);
			local.calendarEventsFilter[local.listFilterKey].fKeyword = arguments.event.getValue('fKeyword','');
			local.calendarEventsFilter[local.listFilterKey].fReportCode = arguments.event.getValue('fReportCode','');
			local.calendarEventsFilter[local.listFilterKey].fRegType = arguments.event.getValue('fRegType',0);
			local.calendarEventsFilter[local.listFilterKey].fHideDeleted = arguments.event.getValue('fHideDeleted',1);
			local.calendarEventsFilter[local.listFilterKey].fEventType = arguments.event.getValue('fEventType','all');
			local.calendarEventsFilter[local.listFilterKey].isSearch = arguments.event.getValue('isSearch','0');
						
			if (local.calendarEventsFilter_hash NEQ hash(serializeJSON(local.calendarEventsFilter), "SHA", "UTF-8")) 
				application.mcCacheManager.sessionSetValue(keyname='calendarEventsFilter', value=local.calendarEventsFilter);
			local.data.success = true;
		</cfscript>
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="getcalendarEventsFilter" access="public" output="false" returntype="struct">
		<cfargument name="calendarId" type="string" required="true">
		<cfscript>
			var local = structNew();
			local.fromDt 		= dateFormat(now(), "m/d/yyyy");
			local.toDt 			= dateFormat(dateAdd("d",365,now()), "m/d/yyyy");
			local.tmpStr = { fDateFrom=local.fromDt, fDateTo=local.toDt, fCategory=0, fKeyword='', fReportCode='', fRegType=0,	fHideDeleted=1,fEventType='all',isSearch=''};

			local.calendarEventsFilter = application.mcCacheManager.sessionGetValue(keyname='calendarEventsFilter',defaultValue={});
			local.calendarEventsFilter_hash = hash(serializeJSON(local.calendarEventsFilter), "SHA", "UTF-8");
			local.listFilterKey = 'listFilter_' & arguments.calendarId;
			if (NOT structKeyExists(local.calendarEventsFilter, local.listFilterKey))
				local.calendarEventsFilter[local.listFilterKey] = duplicate(local.tmpStr);
			
			for (local.thiskey in local.tmpStr) {
				if (not structKeyExists(local.calendarEventsFilter[local.listFilterKey], local.thiskey))
					structInsert(local.calendarEventsFilter[local.listFilterKey], local.thiskey, local.tmpStr[local.thiskey], true);
			}
			if (local.calendarEventsFilter_hash NEQ hash(serializeJSON(local.calendarEventsFilter), "SHA", "UTF-8")) 
				application.mcCacheManager.sessionSetValue(keyname='calendarEventsFilter', value=local.calendarEventsFilter);
		</cfscript>
		<cfreturn local.calendarEventsFilter[local.listFilterKey]>
	</cffunction>

	<cffunction name="massUpdateRegistrants" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfargument name="qryUpdateColumns" type="query" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.errMsg = "">
		
  		<cftry>
  			<cfset local.strSQLPrep = createObject("component","model.admin.common.modules.massUpdate.massUpdate").prepBCPToTableSQL(event=arguments.event, qryUpdateColumns=arguments.qryUpdateColumns, updateTableName='##ev_registrantsUpdate')>
			<cfif not local.strSQLPrep.success>
				<cfthrow message="There was an error processing final update.">
			</cfif>

			<cfquery name="local.qryUpdate" datasource="#application.dsn.membercentral.dsn#" result="local.qryUpdateResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					IF OBJECT_ID('tempdb..##ev_registrantsUpdate') IS NOT NULL
						DROP TABLE ##ev_registrantsUpdate;

					declare @siteID int, @eventID int, @runByMemberID int, @updateResult xml;
					set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
					set @eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('eventID',0)#">;
					set @runByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">;

					-- bcp to table
					BEGIN TRY
						#PreserveSingleQuotes(local.strSQLPrep.sql)#
					END TRY
					BEGIN CATCH
						select @updateResult = '<update><errors><error msg="Unable to upload the file for processing." /><error msg="' + dbo.fn_RegExReplace(error_message(), '[^A-Za-z0-9_\- ]', '') + '" /></errors></update>';
						GOTO on_done;
					END CATCH
					
					-- update
					BEGIN TRY
						set @updateResult = null;
						EXEC dbo.ev_massUpdateRegistrants @siteID=@siteID, @eventID=@eventID, @runByMemberID=@runByMemberID, @updateResult=@updateResult OUTPUT;
					END TRY
					BEGIN CATCH
						select @updateResult = '<update><errors><error msg="Unable to process the update file." /><error msg="' + dbo.fn_RegExReplace(error_message(), '[^A-Za-z0-9_\- ]', '') + '" /></errors></update>';
						GOTO on_done;
					END CATCH
				
					on_done:
					declare @errCount int;
					select @errCount = @updateResult.value('count(/update/errors/error)','int');
					SELECT @updateResult as updateResult, @errCount as errCount; 

					IF OBJECT_ID('tempdb..##ev_registrantsUpdate') IS NOT NULL
						DROP TABLE ##ev_registrantsUpdate;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.returnStruct.updateResultXML = xmlparse(local.qryUpdate.updateResult)> 
			<cfset local.returnStruct.errCount = local.qryUpdate.errCount>
			<cfif local.returnStruct.errCount gt 0>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.strUpdateDetails = getMassUpdateRegistrantDetails(event=arguments.event, qryUpdateColumns=arguments.qryUpdateColumns)>
			</cfif>
			<cfcatch type="Any">
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errMsg = "There was a problem importing the files. Try the upload again or contact us for assistance.">
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			 </cfcatch> 
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getMassUpdateRegistrantDetails" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfargument name="qryUpdateColumns" type="query" required="yes">

		<cfset var local = structNew()>
		
		<cfset local.strFormFields = structNew()>
		<cfset local.arrUpdateColumnDetails = arrayNew(1)>

		<cfset structInsert(local.strFormFields, 'resourceType', 'Event')>
		<cfset structInsert(local.strFormFields, 'eventID', arguments.event.getValue('eventID',0))>
		<cfset structInsert(local.strFormFields, 'bcpfilename', arguments.event.getValue('bcpfilename',''))>
		<cfset structInsert(local.strFormFields, 'uploadedFileFieldList', arguments.event.getValue('uploadedFileFieldList',''))>

		<cfloop query="arguments.qryUpdateColumns">
			<cfset local.tmpStr = { columnID=arguments.qryUpdateColumns.columnID, mappedColValue=arguments.event.getValue('mcupdcol_map_#arguments.qryUpdateColumns.columnID#',''),
									mappedColOverrideValue='' }>
			<cfif local.tmpStr.mappedColValue EQ '_override_value_'>
				<cfset local.tmpStr.mappedColOverrideValue = arguments.event.getValue('mcupdcol_override_#arguments.qryUpdateColumns.columnID#','')>
			</cfif>
			<cfset arrayAppend(local.arrUpdateColumnDetails, local.tmpStr)>
		</cfloop>

		<cfset local.strUpdateDetails = { strFormFields=local.strFormFields, arrUpdateColumnDetails=local.arrUpdateColumnDetails }>

		<cfreturn local.strUpdateDetails>
	</cffunction>

	<cffunction name="showMassUpdateRegistrantResults" access="package" output="false" returntype="string">
		<cfargument name="strUpdateResult" type="struct" required="yes">
		<cfargument name="doAgainURL" type="string" required="yes">
		<cfargument name="eventID" type="numeric" required="yes">

		<cfscript>
			var local = structNew();
			local.data = '';
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<cfif NOT arguments.strUpdateResult.success>
				<div id="divRegistrantUpdateErrorScreen" style="background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00;">
					<h4>Mass Update Results</h4>
					<div><b>The update was stopped and requires your attention.</b></div>
					<br/>

					<cfif structKeyExists(arguments.strUpdateResult,"updateResultXML")>
						<cfset local.arrErrors = XMLSearch(arguments.strUpdateResult.updateResultXML,"/update/errors/error")>
						<div>
							<cfif arrayLen(local.arrErrors) gt 200>
								<b>Only the first 200 errors are shown.</b><br/><br/>
							</cfif>
							<cfset local.thisErrNum = 0>
							<cfloop array="#local.arrErrors#" index="local.thisErr">
								<cfset local.thisErrNum = local.thisErrNum + 1>
								#local.thisErr.xmlAttributes.msg#<br/>
								<cfif local.thisErrNum is 200>
									<cfbreak>
								</cfif>
							</cfloop>
						</div>
					<cfelse>
						<div>#arguments.strUpdateResult.errMsg#</div>
					</cfif>
					<button class="btn btn-sm btn-secondary mt-2" name="btnDoOver" type="button" onclick="self.location.href='#arguments.doAgainURL#';">Try upload again</button>
					<cfif structKeyExists(arguments.strUpdateResult,"previousMappingScreen")>
						&nbsp;<a href="##" onClick="$('##divRegistrantUpdateErrorScreen').hide();$('##divRegistrantUpdateMappingScreen').show(300);return false;">Return to Column Mapping</a>
					</cfif>
				</div>
				<cfif structKeyExists(arguments.strUpdateResult,"previousMappingScreen")>
					<div id="divRegistrantUpdateMappingScreen" style="display:none;">
						#arguments.strUpdateResult.previousMappingScreen#
					</div>
				</cfif>
			<cfelse>
				<h4>Mass Update Registrant Results</h4>
				<p><b>Update Has Been Completed</b></p>
				<div class="mb-2">
					Mass Update of Registrants has been completed.<br/>
				</div>
				<button class="btn btn-sm btn-secondary" name="btnDoOver" type="button" onclick="self.location.href='#arguments.doAgainURL#';">Upload another file</button>
			</cfif>
			</cfoutput>
		</cfsavecontent>
			
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getEventRegistrationIDByEventID" access="public" output="false" returntype="numeric">
		<cfargument name="eventID" type="numeric" required="true">

		<cfset var qryEventRegistrationInfo = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryEventRegistrationInfo">
			SELECT r.registrationID, r.registrationtypeID		
			FROM dbo.ev_registration as r
			INNER JOIN dbo.ev_events as e on e.eventid = r.eventid and r.siteID = e.siteID
			WHERE r.eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.eventID#">
		</cfquery>

		<cfreturn val(qryEventRegistrationInfo.registrationID)>
	</cffunction>

	<cffunction name="getEventSetupIssues" access="public" output="false" returntype="array">
		<cfargument name="strEvent" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.arrEventSetupIssues = arrayNew(1)>

		<cfif arguments.strEvent.qryEventMeta.status eq 'I'>
			<cfset arrayAppend(local.arrEventSetupIssues,"Event is inactive. Change the status to active on the Details tab.")>
		</cfif>

		<cfif not len(arguments.strEvent.qryEventMeta.categoryIDList)>
			<cfset arrayAppend(local.arrEventSetupIssues,"There are no visible categories assigned to this event. Assign one or more non-admin categories.")>
		</cfif>
		
		<cfif arguments.strEvent.qryEventRegMeta.recordCount eq 0 and len(arguments.strEvent.qryEventMeta.altRegistrationURL) eq 0>
			<cfset arrayAppend(local.arrEventSetupIssues,"Registration has not been enabled for this event on the Registration tab.")>
		<cfelseif listFindNoCase("Reg,RSVP",arguments.strEvent.qryEventRegMeta.registrationtype) and len(arguments.strEvent.qryEventRegMeta.replyToEmail) eq 0>
			<cfset arrayAppend(local.arrEventSetupIssues,"On the Registration tab, enter the e-mail address to which confirmation e-mail replies will be sent.")>
		</cfif>

		<cfif arguments.strEvent.qryEventRegMeta.registrationtype eq 'Reg'>
			<cfif arguments.strEvent.qryEventRegMeta.regCapReached>
				<cfset arrayAppend(local.arrEventSetupIssues,"The registrant cap has been reached. Change the cap on the Registration tab.")>
			</cfif>

			<cfif NOT len(arguments.strEvent.qryEventRegMeta.startDate) OR now() lt arguments.strEvent.qryEventRegMeta.startdate
					OR NOT len(arguments.strEvent.qryEventRegMeta.endDate) OR now() gt arguments.strEvent.qryEventRegMeta.enddate>
				<cfset arrayAppend(local.arrEventSetupIssues,"We are currently outside the defined registration date range as set on the Registration tab.")>
			</cfif>

			<cfquery name="local.qryRegistrationSchedule" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT ep.scheduleID, CASE WHEN GETDATE() BETWEEN ep.startDate AND ep.endDate THEN 1 ELSE 0 END as isCurrentSchedule
				FROM dbo.ev_priceSchedule as ep
				INNER JOIN dbo.ev_registration as er ON er.registrationID = ep.registrationID AND er.registrationTypeID = 1
					AND er.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.strEvent.qryEventMeta.siteID#">
				INNER JOIN dbo.ev_events as e ON e.eventID = er.eventID AND e.status <> 'D'
				WHERE ep.registrationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.strEvent.qryEventRegMeta.registrationID#">
				AND e.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.strEvent.qryEventMeta.siteID#">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfif local.qryRegistrationSchedule.recordCount eq 0>
				<cfset arrayAppend(local.arrEventSetupIssues,"There are no registration schedule date ranges set on the Registration tab.")>
			<cfelse>
				<cfset local.qryCurrentSchedule = QueryFilter(local.qryRegistrationSchedule, function(thisRow) { return arguments.thisRow.isCurrentSchedule eq 1; })>
				<cfif local.qryCurrentSchedule.recordCount eq 0>
					<cfset arrayAppend(local.arrEventSetupIssues,"We are currently outside of all registration schedule date ranges as set on the Registration tab.")>
				</cfif>
			</cfif>

			<cfif NOT arguments.strEvent.merchantProfileCheck>
				<cfset arrayAppend(local.arrEventSetupIssues,"Payment methods have not been associated to this event on the Rates Tab.")>
			</cfif>

			<cfset local.qryRates = getRatesByRegistrationID(registrationID=arguments.strEvent.qryEventRegMeta.registrationID)>
			<cfquery name="local.qryFrontEndRates" dbtype="query">
				select rateID, groupID, include
				from [local].qryRates
				where isHidden = 0
			</cfquery>
			<cfif local.qryFrontEndRates.recordCount eq 0>
				<cfset arrayAppend(local.arrEventSetupIssues,"No rates are defined on the Rates tab or are available for front-end registration.")>
			<cfelse>
				<cfset local.qryAvailableRates = QueryFilter(local.qryFrontEndRates, function(thisRow) { return arguments.thisRow.groupID gt 0 and arguments.thisRow.include eq 1; })>
				<cfif arguments.strEvent.qryEventRegMeta.isPriceBasedOnActual eq 1 and local.qryAvailableRates.recordCount eq 0>
					<cfset arrayAppend(local.arrEventSetupIssues,"Rate selection is limited to qualifying group memberships but no rates have assigned groups.")>
				<cfelse>
					<cfquery name="local.qryHasRateSchedule" datasource="#application.dsn.memberCentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						SELECT TOP 1 scheduleID 
						FROM dbo.ev_ratesAvailable
						WHERE rateID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#valueList(local.qryFrontEndRates.rateID)#">);

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					<cfif local.qryHasRateSchedule.recordCount eq 0>
						<cfset arrayAppend(local.arrEventSetupIssues,"No rates are associated to any of the defined registration schedules.")>
					</cfif>
				</cfif>
			</cfif>

			<cfquery name="local.qryEventGL" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT TOP 1 glAccountID 
				FROM dbo.tr_GLAccounts
				WHERE orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(session.mcStruct.siteCode).orgID#">
				AND glAccountID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.strEvent.qryEventMeta.glAccountID#">
				AND [status] = 'A';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfif local.qryEventGL.recordCount eq 0>
				<cfset arrayAppend(local.arrEventSetupIssues,"Revenue GL Account defined on the Rates tab cannot accept revenue transactions.")>
			</cfif>
		<cfelseif arguments.strEvent.qryEventRegMeta.registrationtype eq 'RSVP'>
			<cfif NOT len(arguments.strEvent.qryEventRegMeta.startDate) OR now() lt arguments.strEvent.qryEventRegMeta.startdate
					OR NOT len(arguments.strEvent.qryEventRegMeta.endDate) OR now() gt arguments.strEvent.qryEventRegMeta.enddate>
				<cfset arrayAppend(local.arrEventSetupIssues,"Registration is not open as defined by the allowed registration date range on the Registration tab.")>
			</cfif>
		</cfif>
		
		<cfreturn local.arrEventSetupIssues>
	</cffunction>

	<cffunction name="saveEventSettings" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="defaultAction" type="string" required="true">
		<cfargument name="ResultsFieldSet" type="numeric" required="true">
		<cfargument name="evshowPhotosRegStep1" type="boolean" required="true">
		<cfargument name="evForceLoginForReg" type="boolean" required="true">
		<cfargument name="newAccFieldSet" type="numeric" required="true">
		<cfargument name="adminEvRegSearchFormFieldSetID" type="numeric" required="true">
		<cfargument name="adminEvRegResultsFieldSetID" type="numeric" required="true">
		<cfargument name="adminEvRegNewAcctFieldSetID" type="numeric" required="true">
		<cfargument name="sponsorFeatureImageConfigID" type="numeric" required="true">
		<cfargument name="evSponsorFeatureImageSizeID" type="numeric" required="true">
		<cfargument name="swSponsorFeatureImageSizeID" type="numeric" required="true">
		<cfargument name="sidebarPosition" type="string" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfxml variable="local.settingsXML">
				<cfoutput>
					<settings>
						<setting name="defaultAction" value="#arguments.defaultAction#" />
						<setting name="showPhotosRegStep1" value="<cfif arguments.evshowPhotosRegStep1>1<cfelse>0</cfif>" />
						<setting name="forceLoginForReg" value="<cfif arguments.evForceLoginForReg>1<cfelse>0</cfif>" />
						<setting name="sidebarPosition" value="#arguments.sidebarPosition#" />
					</settings>
				</cfoutput>
			</cfxml>
			<!--- remove the <xml> tag, specifically the encoding. --->
			<cfset local.settingsXML = replaceNoCase(toString(local.settingsXML),'<?xml version="1.0" encoding="UTF-8"?>','')>

			<cfset local.arrEvRegFieldSets = [
				{ "area":"regIdResults", "fieldSetID":arguments.ResultsFieldSet },
				{ "area":"regIdNewacct", "fieldSetID":arguments.newAccFieldSet },
				{ "area":"adminEvRegSearchForm", "fieldSetID":arguments.adminEvRegSearchFormFieldSetID },
				{ "area":"adminEvRegResults", "fieldSetID":arguments.adminEvRegResultsFieldSetID },
				{ "area":"adminEvRegNewAcct", "fieldSetID":arguments.adminEvRegNewAcctFieldSetID },
			]>

			<cfset local.EventAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin',siteID=arguments.mcproxy_siteID)>

			<cfquery name="local.qrySaveEventSettings" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					declare @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">, 
						@applicationTypeID int, @eventAdminSRID int, @settingsXML xml, @applicationTypeSettingID int,
						@currentFSUseID int, @currentFSID int, @newFSID int, @FSArea varchar(20), @useID int;

					SELECT @applicationTypeID = dbo.fn_getApplicationTypeIDFromName('Events');
					SET @eventAdminSRID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.EventAdminSRID#">;
					SET @settingsXML = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.settingsXML#">;

					select @applicationTypeSettingID = applicationTypeSettingID
					from dbo.cms_applicationTypeSettings
					where siteID = @siteID
					and applicationTypeID = @applicationTypeID;

					BEGIN TRAN;
						<cfloop array="#local.arrEvRegFieldSets#" index="local.strFS">
							<cfif local.strFS.fieldSetID GT 0>
								SELECT @currentFSUseID = NULL, @currentFSID = NULL, @newFSID = NULL, @FSArea = NULL;
								SET @newFSID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.strFS.fieldSetID#">;
								SET @FSArea = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strFS.area#">;

								SELECT TOP 1 @currentFSUseID = useID, @currentFSID = fieldsetID
								FROM dbo.ams_memberFieldUsage
								WHERE siteResourceID = @eventAdminSRID
								AND area = @FSArea;

								IF ISNULL(@currentFSID,0) <> @newFSID BEGIN
									IF @currentFSUseID IS NOT NULL
										DELETE FROM dbo.ams_memberFieldUsage
										WHERE useID = @currentFSUseID;
								
									EXEC dbo.ams_createMemberFieldUsage @siteResourceID=@eventAdminSRID, @fieldsetID=@newFSID, @area=@FSArea,
										@createSiteResourceID=0, @useID=@useID OUTPUT;
								END
							</cfif>
						</cfloop>
					
						-- insert
						IF @applicationTypeSettingID IS NULL BEGIN
							INSERT INTO dbo.cms_applicationTypeSettings (siteID, applicationTypeID, settingsXML)
							VALUES (@siteID, @applicationTypeID, @settingsXML);

							SELECT @applicationTypeSettingID = SCOPE_IDENTITY();
						END 
						-- update
						ELSE 
							UPDATE dbo.cms_applicationTypeSettings
							SET settingsXML = @settingsXML
							WHERE applicationTypeSettingID = @applicationTypeSettingID;
					COMMIT TRAN;

					SELECT @applicationTypeSettingID as applicationTypeSettingID;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.arrFeaturedImageSizes = [ { "referenceType":"viewEventDetails", "sizeID":arguments.evSponsorFeatureImageSizeID },
													{ "referenceType":"viewSemwebDetails", "sizeID":arguments.swSponsorFeatureImageSizeID } ]>
			<cfset createObject("component","model.admin.common.modules.featuredImages.featuredImages").saveFeaturedImageConfigSettings(
					featureImageConfigID=arguments.sponsorFeatureImageConfigID, referenceID=arguments.mcproxy_siteID, referenceType="evSiteSponsor", 
					arrFeaturedImageSizes=local.arrFeaturedImageSizes)>
			
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getRegistrantOverrideEmail" access="public" output="false" returntype="string">
		<cfargument name="registrantID" type="numeric" required="true">

		<cfset var qryRegistrantOverrideEmail = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryRegistrantOverrideEmail">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT email
			FROM dbo.ams_emailAppOverrides
			WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.registrantID#">
			AND itemType = 'eventreg';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryRegistrantOverrideEmail.email>
	</cffunction>

	<cffunction name="getEventScheduleMappedPrice" access="public" output="false" returntype="query">
		<cfargument name="registrationID" type="numeric" required="true">
		<cfargument name="ticketPackageID" type="numeric" required="true">

		<cfset var qryEventScheduleMappedPrice = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryEventScheduleMappedPrice">
			SELECT ps.scheduleID, ps.rangeName, cast(tpa.amount as decimal(18,2)) as amount, ps.startDate, ps.endDate
			FROM dbo.ev_priceSchedule ps
			LEFT OUTER JOIN dbo.ev_ticketPackageAvailable as tpa on tpa.scheduleID = ps.scheduleID
				AND tpa.ticketPackageID = <cfqueryparam value="#arguments.ticketPackageID#" cfsqltype="CF_SQL_INTEGER">
				and tpa.isActive = 1
			WHERE ps.registrationID = <cfqueryparam value="#arguments.registrationID#" cfsqltype="CF_SQL_INTEGER">
			ORDER BY ps.startDate
		</cfquery>

		<cfreturn qryEventScheduleMappedPrice>
	</cffunction>

	<cffunction name="getTicketPackageExcludedRates" access="public" output="false" returntype="query">
		<cfargument name="ticketPackageID" type="numeric" required="true">

		<cfset var qryTicketPackageExcludedRates = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryTicketPackageExcludedRates">
			SELECT isnull(rg.rateGrouping + ' / ','') + r.rateName as rateNameExpanded
			FROM dbo.ev_rateExcludeTicketPackages as etp
			INNER JOIN dbo.ev_rates as r on r.rateID = etp.rateID
			LEFT OUTER JOIN dbo.ev_rateGrouping as rg on rg.rateGroupingID = r.rateGroupingID
			WHERE etp.ticketPackageID = <cfqueryparam value="#arguments.ticketPackageID#" cfsqltype="CF_SQL_INTEGER">
			ORDER BY r.rateOrder, rg.rateGroupingOrder
		</cfquery>

		<cfreturn qryTicketPackageExcludedRates>
	</cffunction>

	<cffunction name="queueUpdateSearchText" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="eventID" type="numeric" required="true">

		<cfstoredproc procedure="ev_queueEventSearchText" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">
		</cfstoredproc>
	</cffunction>

	<cffunction name="getSWEventCalendar" access="public" output="false" returntype="query">
		<cfargument name="participantID" type="numeric" required="true">

		<cfset var qrySWEventCalendar = "">

		<cfquery datasource="#application.dsn.tlasites_seminarWeb.dsn#" name="local.qrySWEventCalendar">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT calendarID, COUNT(categoryID) AS categoryCount
			FROM dbo.tblParticipantEvents
			WHERE participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
			GROUP BY calendarID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qrySWEventCalendar>
	</cffunction>

	<cffunction name="getEventsOnSiteFromFilters" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="mode" type="string" required="true">

		<cfset var local = structNew()>

		<!--- check for sw participation --->
		<cfset local.showSWFeaturedCol = false>
		<cfset local.strAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=arguments.event.getValue('mc_siteinfo.siteCode'))>
		<cfif local.strAssociation.keyExists("qryAssociation") AND local.strAssociation.qryAssociation.isConf>
			<cfset local.qrySWEventCalendar = getSWEventCalendar(participantID=local.strAssociation.qryAssociation.participantID)>
			<cfset local.showSWFeaturedCol = val(local.qrySWEventCalendar.calendarID) GT 0>
		</cfif>

		<!--- handle ordering --->
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"startTime")>
		<cfset arrayAppend(local.arrCols,"eventTitle")>
		<cfset local.orderby = "#local.arrcols[arguments.event.getValue('orderby')+1]# #arguments.event.getValue('orderDir')#">

		<cfset local.startDateValue = arguments.event.getValue('fDateFrom', DateAdd("yyyy", -5, now()))>
		<cfset local.endDateValue = arguments.event.getValue('fDateTo', DateAdd("yyyy", 2, now()))>

		<cfif local.startDateValue LT CreateDateTime(1753, 1, 1, 0, 0, 0) OR local.startDateValue GT CreateDateTime(9999, 12, 31, 23, 59, 59)>
			<cfset local.startDateValue = CreateDateTime(1753, 1, 1, 0, 0, 0)>
		</cfif>

		<cfif local.endDateValue LT CreateDateTime(1753, 1, 1, 0, 0, 0) OR local.endDateValue GT CreateDateTime(9999, 12, 31, 23, 59, 59)>
			<cfset local.endDateValue = CreateDateTime(9999, 12, 31, 23, 59, 59)>
		</cfif>

		<!--- get events --->
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryEvents" result="local.qryEventsResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @RTID int, @posStart int, @posStartPlusCount int, @totalCount int, @siteID int, @orgID int;
				set @RTID = dbo.fn_getResourceTypeID('Community');
				set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
				set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgid')#">;
				set @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')# ">;
				set @posStartPlusCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')# ">;

				-- get events on site
				IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
					DROP TABLE ##tmpEventsOnSite;
				CREATE TABLE ##tmpEventsOnSite (calendarID int, eventID int, [status] char(1), isPastEvent bit,
					startTime datetime, endTime datetime, timeZoneID int, timeZoneCode varchar(25), timeZoneAbbr varchar(4),
					displayStartTime datetime, displayEndTime datetime, displayTimeZoneID int, displayTimeZoneCode varchar(25),
					displayTimeZoneAbbr varchar(4), siteResourceID int, isAllDayEvent bit, 
					altRegistrationURL varchar(300), eventTitle varchar(200), eventSubTitle varchar(200), locationTitle varchar(200), 
					categoryIDList varchar(max));
				EXEC dbo.ev_getEventsOnSite @siteID=#arguments.event.getValue('mc_siteinfo.siteid')#, 
					@startDate=<cfif val(arguments.event.getValue('peID',0))>null<cfelse>'#dateFormat(local.startDateValue,"m/d/yyyy")#'</cfif>, 
					@endDate=<cfif val(arguments.event.getValue('peID',0))>null<cfelse>'#dateFormat(local.endDateValue,"mm/dd/yyyy")# 23:59:59.997'</cfif>, 
					@categoryIDList='<cfif arguments.event.getValue('fCategory',0) gt 0>#arguments.event.getValue('fCategory')#</cfif>',
					@incAdminOnlyCatEv=1;

				-- filter events
				IF OBJECT_ID('tempdb..##tmpEvents') IS NOT NULL 
					DROP TABLE ##tmpEvents; 
				CREATE TABLE ##tmpEvents (applicationInstanceId int,calendarID int, eventid int PRIMARY KEY, [status] char(1), eventTitle varchar(200), eventSubTitle varchar(200),
					regType varchar(10), calendarName varchar(400), startTime datetime, endTime datetime, categoryIDList varchar(max), siteResourceID int, 
					hasRegistration bit, forceSelect bit, registrationTypeID int, registrationID int, calSiteResourceID int, recurringSeriesID int,
					INDEX IX_tmpEvents_startTime_eventTitle (startTime, eventTitle, eventid)); 

				INSERT INTO ##tmpEvents (applicationInstanceId, calendarID, eventid, status, eventTitle, eventSubTitle, regType, calendarName, startTime, endTime, 
					categoryIDList, siteResourceID, hasRegistration, forceSelect, registrationTypeID, registrationID, calSiteResourceID, recurringSeriesID)
				SELECT ai.applicationInstanceId, tmp.calendarID, e.eventid, tmp.status, tmp.eventTitle, tmp.eventSubTitle,
					CASE 
					WHEN e.altRegistrationURL IS NULL AND ert.registrationType  = '' THEN ''
					WHEN e.altRegistrationURL IS NOT NULL THEN 'Alt'
					ELSE ert.registrationType 
					END as regType,
					ai.applicationInstanceName + 
						case 
						WHEN communityInstances.applicationInstanceName is not null THEN ' (' + communityInstances.applicationInstanceName + ')'
						ELSE ''
						END as calendarName,
					tmp.startTime, tmp.endTime, tmp.categoryIDList, tmp.siteResourceID, 
					hasRegistration = case when er.registrationID is null then 0 else 1 end,
					<cfif val(arguments.event.getValue('peID',0))>
						subEvent.forceSelect, 
					<cfelse>
						null,
					</cfif>
					er.registrationTypeID, er.registrationID, ai.siteResourceID as calSiteResourceID, e.recurringSeriesID
				FROM dbo.ev_events as e
				INNER JOIN ##tmpEventsOnSite as tmp on tmp.eventID = e.eventID
				LEFT OUTER JOIN dbo.ev_registration as er 
					INNER JOIN dbo.ev_registrationTypes as ert on er.registrationTypeID = ert.registrationTypeID
						<cfif arguments.event.getValue('fRegType',0) gt 0>
							and ert.registrationTypeID = <cfqueryparam value="#arguments.event.getValue('fRegType')#" cfsqltype="CF_SQL_INTEGER">
						</cfif>
					on er.eventID = e.eventID and er.siteID = @siteID and er.status = 'A'
				INNER JOIN dbo.ev_calendars as c on c.siteID = @siteID and c.calendarid = tmp.calendarID
				inner join dbo.cms_applicationInstances as ai on c.applicationInstanceID = ai.applicationInstanceID and ai.siteID = @siteID
				inner join dbo.cms_siteResources as sr on sr.siteID = @siteID and ai.siteResourceID = sr.siteResourceID
				inner join dbo.cms_siteResources as parentResource on parentResource.siteID = @siteID and parentResource.siteResourceID = sr.parentSiteResourceID
				left outer join dbo.cms_siteResources as grandparentResource
					inner join dbo.cms_applicationInstances as CommunityInstances on communityInstances.siteResourceID = grandParentResource.siteResourceID
					on grandparentResource.siteResourceID = parentResource.parentSiteResourceID
					and grandparentResource.resourceTypeID = @RTID
				<cfif val(arguments.event.getValue('peID',0))>	
					inner join dbo.ev_subEvents as subEvent on subEvent.eventID = e.eventID
						and subEvent.parentEventID = <cfqueryparam value="#arguments.event.getValue('peID',0)#" cfsqltype="CF_SQL_INTEGER">
				</cfif>
				WHERE e.siteID = @siteID
				<cfif arguments.event.getValue('fCalendar',0) gt 0>
					and tmp.calendarID = <cfqueryparam value="#arguments.event.getValue('fCalendar')#" cfsqltype="CF_SQL_INTEGER">
				</cfif>	
				<cfif len(arguments.event.getTrimValue('fKeyword',''))>
					and tmp.eventTitle + isnull(' '+tmp.eventSubTitle,'') like <cfqueryparam value="%#replace(arguments.event.getValue('fKeyword'),'_','\_','ALL')#%" cfsqltype="cf_sql_varchar"> ESCAPE('\')
				</cfif>
				<cfif arguments.event.getValue('fHideDeleted',0) is 1>
					and tmp.status <> 'D'
				</cfif>
				<cfif arguments.event.getValue('fRegType',0) gt 0>
					and er.registrationID is not null
				</cfif>	
				<cfif arguments.event.getValue('fEventType','') eq 'main' and not val(arguments.event.getValue('peID',0))>
					and NOT EXISTS (select se.eventID from dbo.ev_subevents se where se.eventID = e.eventID)
				</cfif>
				<cfif arguments.event.getValue('fEventType','') eq 'sub' and not val(arguments.event.getValue('peID',0))>
					and EXISTS (select se.eventID from dbo.ev_subevents se where se.eventID = e.eventID)
				</cfif>
				<cfif arguments.event.getValue('fEventType','') eq 'rev'>
					and e.recurringSeriesID is not null
				</cfif>
				<cfif len(arguments.event.getTrimValue('fReportCode',''))>
					and e.ReportCode = <cfqueryparam value="#arguments.event.getTrimValue('fReportCode')#" cfsqltype="cf_sql_varchar">
				</cfif>;

				SELECT @totalCount = count(*) from ##tmpEvents;

				-- add in admin-only event categories to the list. put at end since order doesnt matter.
				WITH adminCats AS (
					SELECT tmp.eventID, tmp.calendarID, STRING_AGG(evcat.categoryID,',') as adminCatIDs
					FROM ##tmpEvents as tmp
					INNER JOIN dbo.cache_calendarEvents AS cache on cache.calendarID = tmp.calendarID and cache.eventID = tmp.eventID
					INNER JOIN dbo.ev_categories AS evcat ON evcat.categoryID = cache.categoryID AND evcat.visibility = 'A'
					GROUP BY tmp.eventID, tmp.calendarID
				)
				UPDATE tmp
				SET tmp.categoryIDList = case 
					when nullif(tmp.categoryIDList,'') is null then adminCats.adminCatIDs
					else tmp.categoryIDList + ',' + adminCats.adminCatIDs
					end
				FROM ##tmpEvents as tmp
				INNER JOIN adminCats on adminCats.eventID = tmp.eventID and adminCats.calendarID = tmp.calendarID;

				-- get event fees
				IF OBJECT_ID('tempdb..##tmpEventsForFee') IS NOT NULL 
					DROP TABLE ##tmpEventsForFee; 
				IF OBJECT_ID('tempdb..##tmpEventsForFeeResult') IS NOT NULL 
					DROP TABLE ##tmpEventsForFeeResult; 
				IF OBJECT_ID('tempdb..##tmpEventsFee') IS NOT NULL 
					DROP TABLE ##tmpEventsFee;
				create table ##tmpEventsForFee (eventID int PRIMARY KEY);
				create table ##tmpEventsForFeeResult (eventID int, registrantID int, transactionID int, TransactionIDForRateAdjustment int);
				CREATE TABLE ##tmpEventsFee (eventID int PRIMARY KEY, regFee decimal(18,2));

				IF OBJECT_ID('tempdb..##eventIDs') IS NOT NULL
					DROP TABLE ##eventIDs;
				CREATE TABLE ##eventIDs (eventID int PRIMARY KEY, row int INDEX IX_eventIDs_row);

				-- Get events where the fee may be displayed and only get tranactions for those.
				insert into ##eventIDs(eventID, row)
				select eventID, row
				FROM (
					select eventID, ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#, eventTitle, eventid) as row
					from ##tmpEvents
				) AS tmp
				WHERE row > @posStart AND row <= @posStartPlusCount
				ORDER by row;

				insert into ##tmpEventsForFee (eventID)
				select eventID from ##eventIDs;

				EXEC dbo.ev_registrantTransactionsByEventBulk;

				INSERT INTO ##tmpEventsFee (eventID, regFee)
				select fees.eventID, sum(ts.cache_amountAfterAdjustment) as regFee
				from ##tmpEventsForFeeResult as fees
				inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = fees.transactionid
				group by fees.eventID;

				-- SW Events
				<cfif local.showSWFeaturedCol>
					IF OBJECT_ID('tempdb..##tmpSWEventCategories') IS NOT NULL 
						DROP TABLE ##tmpSWEventCategories;
					IF OBJECT_ID('tempdb..##tmpSWEvents') IS NOT NULL 
						DROP TABLE ##tmpSWEvents;
					CREATE TABLE ##tmpSWEventCategories (categoryID int PRIMARY KEY);
					CREATE TABLE ##tmpSWEvents (eventID int PRIMARY KEY);

					DECLARE @SWCalendarID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySWEventCalendar.calendarID#">;

					<cfif val(local.qrySWEventCalendar.categoryCount) EQ 0>
						INSERT INTO ##tmpSWEventCategories (categoryID)
						SELECT categoryID
						FROM dbo.ev_categories
						WHERE calendarID = @SWCalendarID;
					<cfelse>
						INSERT INTO ##tmpSWEventCategories (categoryID)
						SELECT ec.categoryID
						FROM dbo.ev_categories AS ec
						INNER JOIN seminarWeb.dbo.tblParticipantEvents AS pe ON pe.calendarID = @SWCalendarID 
							AND pe.calendarID = ec.calendarID
							AND pe.categoryID = ec.categoryID
						WHERE pe.participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.strAssociation.qryAssociation.participantID#">;
					</cfif>

					INSERT INTO ##tmpSWEvents (eventID)
					select distinct tmp.eventID
					from ##tmpEvents as tmp
					inner join ##eventIDs as ev on ev.eventID = tmp.eventID
					inner join dbo.ev_eventCategories as ec on ec.eventID = ev.eventID
					inner join ##tmpSWEventCategories as sevc on sevc.categoryID = ec.categoryID;
				</cfif>

				-- just the ones we are displaying
				IF OBJECT_ID('tempdb..##tmpEventsJUSTPAGE') IS NOT NULL 
					DROP TABLE ##tmpEventsJUSTPAGE;

				<!--- get only the events we are going to show on the page --->
				select *
				INTO ##tmpEventsJUSTPAGE
				FROM (
					select *, ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#, eventTitle, eventid) as row
					from ##tmpEvents
				) as innerTmp
				WHERE row > @posStart
				AND row <= @posStartPlusCount;

				<!--- get rights for each calendar in the page --->
				declare @tblCalSRID TABLE (calSiteResourceID int, calRightsXML xml);
				insert into @tblCalSRID (calSiteResourceID)
				select distinct calSiteResourceID
				from ##tmpEventsJUSTPAGE;

				UPDATE @tblCalSRID
				SET calRightsXML = dbo.fn_cache_perms_getResourceRightsXML(calSiteResourceID,<cfqueryparam value="#session.cfcuser.memberdata.memberid#" cfsqltype="CF_SQL_INTEGER">,@siteID);

				-- final result
				SELECT tmp.applicationInstanceId, tmp.eventID, tmp.status, tmp.eventTitle, tmp.eventSubTitle, tmp.regType, tmp.calendarID, tmp.calendarName, tmp.startTime, tmp.endTime, 
					tmp.categoryIDList, tmp.hasRegistration, <cfif val(arguments.event.getValue('peID',0))>tmp.forceSelect, </cfif>
					tmp.row, @totalCount as totalCount, <cfif local.showSWFeaturedCol>1<cfelse>0</cfif> as showSWFeaturedCol,
					totalFees = case
						when tmp.hasRegistration = 1 and tmp.registrationTypeID = 1 then (select regFee from ##tmpEventsFee where eventID = tmp.eventID)
						else 0
						end,
					numRegistrants = case 
						when tmp.registrationID is null then 0
						WHEN tmp.registrationTypeID = 1 then (select count(*) from dbo.ev_registrants where registrationid = tmp.registrationID and status = 'A') 
						ELSE (select count(*) from dbo.ev_rsvp where registrationid = tmp.registrationID) 
						end,
					numSubEvents = (select count(*) from ev_subEvents tmpev where tmpev.parenteventID = tmp.eventID),
					isSubEvent = (select count(*) from ev_subEvents tmpev where tmpev.eventID = tmp.eventID),
					tmp.siteResourceID as eventSRID, tmp.recurringSeriesID,
					dbo.fn_cache_perms_getResourceRightsXML(tmp.siteResourceID,<cfqueryparam value="#session.cfcuser.memberdata.memberid#" cfsqltype="CF_SQL_INTEGER">,@siteID) as rightsXML,
					tmpCalSRID.calRightsXML
					<cfif local.showSWFeaturedCol>
						, case when fp.featuredID is not null then 1 else 0 end as isSWFeatured
						, case when tmpSW.eventID is not null then 1 else 0 end as isSWEvent
					<cfelse>
						, 0 as isSWFeatured
						, 0 as isSWEvent
					</cfif>
				FROM ##tmpEventsJUSTPAGE AS tmp
				INNER JOIN @tblCalSRID as tmpCalSRID on tmpCalSRID.calSiteResourceID = tmp.calSiteResourceID
				<cfif local.showSWFeaturedCol>
					LEFT OUTER JOIN seminarWeb.dbo.tblFeaturedPrograms AS fp ON fp.eventID = tmp.eventID
					LEFT OUTER JOIN ##tmpSWEvents AS tmpSW ON tmpSW.eventID = tmp.eventID
				</cfif>
				ORDER by tmp.row;

				IF OBJECT_ID('tempdb..##tmpEventsJUSTPAGE') IS NOT NULL 
					DROP TABLE ##tmpEventsJUSTPAGE;
				IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL
					DROP TABLE ##tmpEventsOnSite;
				IF OBJECT_ID('tempdb..##tmpEvents') IS NOT NULL
					DROP TABLE ##tmpEvents;
				IF OBJECT_ID('tempdb..##tmpEventsForFee') IS NOT NULL
					DROP TABLE ##tmpEventsForFee;
				IF OBJECT_ID('tempdb..##tmpEventsForFeeResult') IS NOT NULL
					DROP TABLE ##tmpEventsForFeeResult;
				IF OBJECT_ID('tempdb..##tmpEventsFee') IS NOT NULL
					DROP TABLE ##tmpEventsFee;
				IF OBJECT_ID('tempdb..##eventIDs') IS NOT NULL
					DROP TABLE ##eventIDs;
				<cfif local.showSWFeaturedCol>
					IF OBJECT_ID('tempdb..##tmpSWEventCategories') IS NOT NULL 
						DROP TABLE ##tmpSWEventCategories;
					IF OBJECT_ID('tempdb..##tmpSWEvents') IS NOT NULL 
						DROP TABLE ##tmpSWEvents;
				</cfif>

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.qryEvents>
	</cffunction>

	<cffunction name="getEventDocument" access="public" output="false" returntype="query">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="eventDocumentID" type="numeric" required="true">
		
		<cfset var qryEventDocument = "">
		
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryEventDocument">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT ed.eventDocumentID, ed.documentID, ed.eventID, ed.eventDocumentGroupingID, d.dateCreated, 
				dl.documentLanguageID, dl.docTitle, dl.docDesc, dv.documentVersionID, dv.fileName, dv.fileExt, 
				dv.author, dv.dateModified, l.languageCode
			FROM dbo.ev_eventDocuments as ed
			INNER JOIN dbo.cms_documents as d ON ed.documentID = d.documentID
			INNER JOIN dbo.cms_documentLanguages as dl ON d.documentID = dl.documentID
			INNER JOIN dbo.cms_documentVersions as dv ON dl.documentLanguageID = dv.documentLanguageID and dv.isActive = 1
			INNER JOIN dbo.cms_languages as l ON l.languageID = dl.languageID
			INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = d.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
			WHERE ed.eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">
			AND ed.eventDocumentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventDocumentID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryEventDocument>
	</cffunction>

	<cffunction name="getEventDocumentGroupings" access="public" output="false" returntype="query">
		<cfargument name="eventID" type="numeric" required="true">
		
		<cfset var qryEventDocumentGroupings = "">
		
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryEventDocumentGroupings">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT eventDocumentGroupingID, eventDocumentGrouping, eventDocumentGroupingOrder
			FROM dbo.ev_eventDocumentGrouping
			WHERE eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">
			ORDER BY eventDocumentGroupingOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryEventDocumentGroupings>
	</cffunction>

	<cffunction name="deleteEventDocumentGrouping" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="eventDocumentGroupingID" type="numeric" required="true">

		<cfset var local = structNew()>
		
		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDeleteEvDocGrouping">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @eventID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">,
						@eventDocumentGroupingID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventDocumentGroupingID#">;
				
					BEGIN TRAN;
						UPDATE dbo.ev_eventDocuments
						SET eventDocumentGroupingID = NULL,
							fileOrder = 999
						WHERE eventDocumentGroupingID = @eventDocumentGroupingID
						AND eventID = @eventID;

						DELETE FROM dbo.ev_eventDocumentGrouping
						WHERE eventDocumentGroupingID = @eventDocumentGroupingID
						AND eventID = @eventID;

						EXEC dbo.ev_reorderEventDocumentGroupings @eventID=@eventID;
						EXEC dbo.ev_reorderEventDocuments @eventDocumentGroupingID=NULL, @eventID=@eventID;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doEventDocGroupingMove" access="public" output="false" returntype="struct">
		<cfargument name="eventDocumentGroupingID" type="numeric" required="true">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ev_moveEventDocumentGrouping">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.eventDocumentGroupingID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteEventDocument" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="eventSRID" type="numeric" required="true">
		<cfargument name="eventDocumentID" type="numeric" required="true">
		<cfargument name="documentID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasEditEventRights(siteID=arguments.mcproxy_siteID, eventSRID=arguments.eventSRID)>
 				<cfthrow message="invalid request">
 			</cfif>

			<cfset CreateObject("component","model.system.platform.document").deleteDocument(siteID=arguments.mcproxy_siteID, documentID=arguments.documentID)>

			<cfquery name="local.qryDeleteEvDoc" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @eventID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">,
						@eventDocumentID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventDocumentID#">,
						@eventDocumentGroupingID int;

					SELECT @eventDocumentGroupingID = eventDocumentGroupingID
					FROM dbo.ev_eventDocuments
					WHERE eventDocumentID = @eventDocumentID;

					BEGIN TRAN;
						DELETE FROM dbo.ev_eventDocuments
						WHERE eventDocumentID = @eventDocumentID
						AND eventID = @eventID;

						EXEC dbo.ev_reorderEventDocuments @eventDocumentGroupingID=@eventDocumentGroupingID, @eventID=@eventID;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getEventDocumentGroupingName" access="public" output="false" returntype="struct">
		<cfargument name="eventDocumentGroupingID" type="numeric" required="true">
		
		<cfset var qryEventDocumentGrouping = "">
		
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryEventDocumentGrouping">
			SELECT eventDocumentGroupingID, eventDocumentGrouping
			FROM dbo.ev_eventDocumentGrouping
			WHERE eventDocumentGroupingID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventDocumentGroupingID#">
		</cfquery>

		<cfreturn { 'success':true, 'eventdocumentgrouping':qryEventDocumentGrouping.eventDocumentGrouping }>
	</cffunction>

	<cffunction name="saveEventDocumentGrouping" access="public" output="false" returntype="struct">
		<cfargument name="eventDocumentGroupingID" type="numeric" required="true">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="eventDocumentGrouping" type="string" required="true">
		
		<cfset var local = structNew()>
		
		<cftry>
			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryUpdateEventDocumentGrouping">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @eventDocumentGroupingID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventDocumentGroupingID#">,
						@eventID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">,
						@eventDocumentGrouping varchar(200) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.eventDocumentGrouping#">;

					IF EXISTS (
						SELECT 1
						FROM dbo.ev_eventDocumentGrouping
						WHERE eventID = @eventID
						AND eventDocumentGrouping = @eventDocumentGrouping
						AND eventDocumentGroupingID <> @eventDocumentGroupingID
					)
						RAISERROR('Duplicate Grouping Name',16,1);

					UPDATE dbo.ev_eventDocumentGrouping
					SET eventDocumentGrouping = @eventDocumentGrouping
					WHERE eventDocumentGroupingID = @eventDocumentGroupingID;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfif findNoCase("Duplicate Grouping Name", cfcatch.detail)>
				<cfset local.data.errmsg = "Grouping Name already in use.">
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="hasEventDocumentsForAnEvent" access="public" output="false" returntype="boolean">
		<cfargument name="eventID" type="numeric" required="true">
		
		<cfset var qryEventDocuments = "">
		
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryEventDocuments">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT COUNT(ed.eventDocumentID) AS evDocCount
			FROM dbo.ev_eventDocuments AS ed
			INNER JOIN dbo.cms_documents AS d ON ed.documentID = d.documentID
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = d.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID 
				AND srs.siteResourceStatusDesc = 'Active'
			WHERE ed.eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn val(qryEventDocuments.evDocCount) GT 0>
	</cffunction>

	<cffunction name="generateMaterialsEmail" access="public" output="no" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="registrantID" type="numeric" required="true">
		<cfargument name="emailTo" type="string" required="false" default="">
		<cfargument name="customText" type="string" required="false" default="">
		<cfargument name="emailSubject" type="string" required="false" default="">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.objTSTZ = CreateObject("component","model.system.platform.tsTimeZone")>
		<cfset local.objEvents = CreateObject("component","model.events.events")>
		
		<cfquery name="local.qryRegistrantDetails" datasource="#application.dsn.membercentral.dsn#">
			select TOP 1 m.activeMemberID as memberID, reg.eventID, r.rateID, r.registrationID, s.siteCode, 
				r.isFlagged, r.internalNotes, ISNULL(eo.email,'') as overrideEmail
			from dbo.ev_registrants as r
			inner join dbo.ev_registration as reg on reg.registrationID = r.registrationID
				and reg.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			inner join dbo.ams_members as m on m.memberID = r.memberID
			inner join dbo.sites as s on s.siteID = r.recordedOnSiteID
			left outer join dbo.ams_emailAppOverrides as eo on eo.itemID = r.registrantID and eo.itemType = 'eventreg'
			where r.registrantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.registrantID#">
		</cfquery>
		
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(local.qryRegistrantDetails.sitecode)>
		<cfset local.qryRegistrant = CreateObject("component","eventReg").getRegistrantInfo(mid=local.qryRegistrantDetails.memberID)>
		<cfset local.qryMainEmail = application.objMember.getMainEmail(local.qryRegistrantDetails.memberID)>
		<cfset local.memberKey = application.objMergeCodes.generateMemberKey(orgcode=local.qryRegistrant.orgcode, membernumber=local.qryRegistrant.membernumber)>
		<cfset local.eventAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin', siteID=local.mc_siteInfo.siteid)>
		<cfset local.strEvent = local.objEvents.getEvent(eventID=local.qryRegistrantDetails.eventID, siteID=local.mc_siteInfo.siteid, languageID=1)>
		<cfset local.qryDocuments = getEventDocuments(eventID=local.qryRegistrantDetails.eventID, memberID=local.qryRegistrantDetails.memberID)>
		<cfset local.qryMainEventDocuments = QueryFilter(local.qryDocuments, 
												function(thisRow) { 
													return arguments.thisRow.isChild EQ 0 AND arguments.thisRow.isRegistrant EQ 1;
												})>
		<cfset local.qrySubEventDocuments = QueryFilter(local.qryDocuments, 
												function(thisRow) { 
													return arguments.thisRow.isChild EQ 1 AND arguments.thisRow.isRegistrant EQ 1;
												})>
		
		<cfset local.showTimeZone = true>
		<cfif local.mc_siteInfo.defaultTimeZoneID eq local.strEvent.qryEventTimes_selected.timezoneID and local.strEvent.qryEventMeta.alwaysShowEventTimezone eq 0>
			<cfset local.showTimeZone = false>
		</cfif>
		
		<!--- event dates/times --->
		<cfsavecontent variable="local.eventtime">
			<cfif local.strEvent.qryEventMeta.isAllDayEvent>
				<cfoutput>#DateFormat(local.strEvent.qryEventTimes_selected.startTime, "ddd, mmmm d, yyyy")#</cfoutput>
				<cfif DateCompare(local.strEvent.qryEventTimes_selected.endTime,local.strEvent.qryEventTimes_selected.startTime,"d")>
					<cfoutput> to </cfoutput>
					<cfoutput>#DateFormat(local.strEvent.qryEventTimes_selected.endTime, "ddd, mmmm d, yyyy")#</cfoutput>
				</cfif>
			<cfelse>
				<cfoutput>#DateFormat(local.strEvent.qryEventTimes_selected.startTime, "ddd, mmmm d, yyyy")# </cfoutput>
				<cfif DateCompare(local.strEvent.qryEventTimes_selected.endTime,local.strEvent.qryEventTimes_selected.startTime,"d") is 0 and
					DateDiff("n",local.strEvent.qryEventTimes_selected.endTime,local.strEvent.qryEventTimes_selected.startTime) is 0>
					<cfoutput>#TimeFormat(local.strEvent.qryEventTimes_selected.startTime, "h:mm TT")# <cfif local.showTimeZone>#local.strEvent.qryEventTimes_selected.timeZoneAbbr#</cfif></cfoutput>
				<cfelseif DateCompare(local.strEvent.qryEventTimes_selected.endTime,local.strEvent.qryEventTimes_selected.startTime,"d") is 0>
					<cfoutput>#TimeFormat(local.strEvent.qryEventTimes_selected.startTime, "h:mm TT")# </cfoutput>
					<cfoutput>- #timeformat(local.strEvent.qryEventTimes_selected.endTime,"h:mm TT")# <cfif local.showTimeZone>#local.strEvent.qryEventTimes_selected.timeZoneAbbr#</cfif></cfoutput>
				<cfelse>
					<cfoutput>#TimeFormat(local.strEvent.qryEventTimes_selected.startTime, "h:mm TT")# to </cfoutput>
					<cfoutput>#DateFormat(local.strEvent.qryEventTimes_selected.endTime, "ddd, mmmm d, yyyy")#</cfoutput>
					<cfoutput> #TimeFormat(local.strEvent.qryEventTimes_selected.endTime, "h:mm TT")# <cfif local.showTimeZone>#local.strEvent.qryEventTimes_selected.timeZoneAbbr#</cfif></cfoutput>
				</cfif>
			</cfif>
		</cfsavecontent>

		<cfquery name="local.qryGetSRID" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#createTimeSpan(0,0,1,0)#">
			SET NOCOUNT ON;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			select ai.siteResourceID, ai.applicationInstanceID
			from dbo.ev_events as e
			inner join dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID and ce.calendarID = ce.sourceCalendarID
			inner join dbo.ev_calendars as c on c.siteID = @siteID and c.calendarID = ce.calendarID
			inner join dbo.cms_applicationInstances as ai on ai.siteID = @siteID and ai.applicationInstanceID = c.applicationInstanceID
			where e.siteID = @siteID
			AND e.eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryRegistrantDetails.eventID#">;
		</cfquery>
		<cfset local.baseProgramLink = CreateObject("component","model.appLoader").getAppBaseLink(applicationInstanceID=local.qryGetSRID.applicationInstanceID)>
		<cfset local.eventDocURL = "#local.mc_siteInfo.scheme#://#local.mc_siteInfo.mainhostname#/?#local.baseProgramLink#&evAction=eventDocDownload&eid=#local.qryRegistrantDetails.eventID#&documentID=[[MCEVDOCUMENTID]]&mk=#local.memberKey#">	

		<cfsavecontent variable="local.returnStruct.emailcontent">
			<cfinclude template="dsp_emailMaterials.cfm">
		</cfsavecontent>

		<cfset local.returnStruct.emailcontent = application.objResourceRenderer.qualifyAllLinks(content=local.returnStruct.emailcontent, siteid=arguments.siteID)>		
		<cfset local.returnStruct.emailTitle = "#local.strEvent.qryEventMeta.EventContentTitle# Materials">

		<!--- determine replyto --->
		<cfset local.replyto = trim(local.strEvent.qryEventRegMeta.replyToEmail)>
		<cfif NOT len(local.replyto)>
			<cfset local.replyto = local.mc_siteInfo.supportProviderEmail>
		</cfif>
		
		<cfset local.returnStruct.memberID = local.qryRegistrant.memberID>
		<cfset local.returnStruct.registrantName = "#local.qryRegistrant.firstName# #local.qryRegistrant.lastname#">

		<!--- mail collection --->
		<cfscript>
		local.returnStruct.replyto = local.replyto;
		local.returnStruct.subject = len(trim(arguments.emailSubject)) GT 0 ? trim(arguments.emailSubject) : '#local.strEvent.qryEventMeta.EventContentTitle# Materials';
		local.emailTo = arguments.emailTo;
		
		if (len(local.emailTo)) {
			local.stToEmail = replace(replace(local.emailTo,',',';','ALL'),' ','','ALL');
			local.toEmailArr = listToArray(local.stToEmail,';');
			for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
				if (len(local.toEmailArr[local.i]) and not isValid("regex",local.toEmailArr[local.i],application.regEx.email)) {
					arrayDeleteAt(local.toEmailArr,local.i);
				}
			}
			local.emailTo = arrayToList(local.toEmailArr,'; ');
		}

		local.returnStruct.arrEmailTo = [];
		local.toEmailArr = listToArray(local.emailTo,';');
		for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
			local.returnStruct.arrEmailTo.append({ name:local.returnStruct.registrantName, email:local.toEmailArr[local.i] });
		}

		// add primary email address
		if (len(local.qryMainEmail.email) AND NOT listFindNoCase(local.emailTo,local.qryMainEmail.email,';'))
			local.returnStruct.arrEmailTo.append({ name:local.returnStruct.registrantName, email:local.qryMainEmail.email });
		// add override email address
		if (len(local.qryRegistrantDetails.overrideEmail) AND NOT listFindNoCase(local.emailTo,local.qryRegistrantDetails.overrideEmail,';'))
			local.returnStruct.arrEmailTo.append({ name:local.returnStruct.registrantName, email:local.qryRegistrantDetails.overrideEmail });
		</cfscript>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="addRedirect" access="public" output="false" returntype="void" hint="add Quick Link for event entry">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="redirectName" type="string" required="true">
		<cfargument name="redirectURL" type="string" required="true">
		<cfargument name="oldRedirectID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.objAlias = CreateObject("component","model.admin.alias.alias")>
		<cfset local.redirectID = local.objAlias.insertAlias(siteID=arguments.siteID, redirectName=arguments.redirectName, redirectURL=arguments.redirectURL)>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.update">
			UPDATE dbo.ev_events 
			SET redirectID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.redirectID#">
			WHERE eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.eventID#">
		</cfquery>

		<cfif arguments.oldRedirectID>
			<cfset local.objAlias.deleteAlias(redirectIDList=arguments.oldRedirectID, siteID=arguments.siteID)>
		</cfif>
	</cffunction>

	<cffunction name="getEventForms" access="public" output="false" returntype="query" hint="get forms linked with the event">
		<cfargument name="eventID" type="numeric" required="true">
		
		<cfset var qryForms = "">
		
		<cfstoredproc procedure="ev_getEventForms" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">
			<cfprocresult name="qryForms" resultset="1">
		</cfstoredproc>

		<cfreturn qryForms>
	</cffunction>

	<cffunction name="hasManageRatesRights" access="private" returntype="boolean" output="no">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="eventID" type="numeric" required="yes">
		<cfargument name="rateID" type="numeric" required="false" default="0">

		<cfset var local = structNew()>
		<cfset local.hasRights = false>

		<cftry>
			<cfquery name="local.qryPermissions" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @hasPermissions bit = 0, @eventSRID int, @rate decimal(18,2), @rightsXML xml;

					SELECT @eventSRID = siteResourceID
					FROM dbo.ev_events
					WHERE eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">;

					SELECT @rightsXML = dbo.fn_cache_perms_getResourceRightsXML(@eventSRID,
						<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">,
						<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">);

					<cfif arguments.rateID gt 0>
						SELECT @rate = rate
						FROM dbo.ev_rates
						WHERE rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">;

						IF (@rate > 0 AND @rightsXML.exist('/rights/right[@functionName="ManagePaidRates"][@allowed="1"]') = 1) OR
							(@rate = 0 AND @rightsXML.exist('/rights/right[@functionName="ManageFreeRates"][@allowed="1"]') = 1) BEGIN
							SET @hasPermissions = 1;
						END
					<cfelse>
						IF @rightsXML.exist('/rights/right[@functionName="ManagePaidRates"][@allowed="1"]') = 1 OR
							@rightsXML.exist('/rights/right[@functionName="ManageFreeRates"][@allowed="1"]') = 1 BEGIN
							SET @hasPermissions = 1;
						END
					</cfif>

					SELECT @hasPermissions AS hasPermissions;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.hasRights = val(local.qryPermissions.hasPermissions) ? true : false>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.hasRights = false>
		</cfcatch>
		</cftry>

		<cfreturn local.hasRights>
	</cffunction>

	<cffunction name="hasEditEventRights" access="public" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="eventSRID" type="numeric" required="yes">

		<cfset var tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=arguments.eventSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>

		<cfreturn structKeyExists(tmpRights,"EditEvent") and tmpRights.EditEvent is 1>
	</cffunction>

	<cffunction name="removeRegPackageInstance" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="instanceID" type="numeric" required="true">
		<cfargument name="tpID" type="numeric" required="true">
		<cfargument name="registrantID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.mcproxy_siteCode)>
		<cfset local.instanceID = arguments.instanceID>
		<cfset local.ticketPackageID = arguments.tpID>
		<cfset local.defaultCurrencyType = "">
		<cfif local.mc_siteInfo.showCurrencyType is 1>
			<cfset local.defaultCurrencyType = " #local.mc_siteInfo.defaultCurrencyType#">
		</cfif>

		<cfquery name="local.qryRegPackageInstances" datasource="#application.dsn.membercentral.dsn#">
			select rpi.instanceID, t.ticketName + ' \ ' + tp.ticketPackageName as ticketPackageNameExpanded, tp.ticketCount,
				rpi.availablePriceID, ROW_NUMBER() OVER (ORDER BY rpi.instanceID) as row
			from dbo.ev_registrantPackageInstances rpi
			inner join dbo.ev_ticketPackages as tp on tp.ticketPackageID = rpi.ticketPackageID
			inner join dbo.ev_tickets as t on t.ticketID = tp.ticketID
			where rpi.ticketPackageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.ticketPackageID#">
			and rpi.registrantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.registrantID#">
			and rpi.status = 'A';
		</cfquery>
		<cfquery name="local.qryThisRegPackageInstance" dbtype="query">
			select *
			from [local].qryRegPackageInstances
			where instanceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.instanceID#">
		</cfquery>
		<cfstoredproc procedure="ev_regTransactionsForConfirmation" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.registrantID#">
			<cfprocresult name="local.qryTotals" resultset="1">
			<cfprocresult name="local.qryRegTransactions" resultset="2">
			<cfprocresult name="local.qryPaymentAllocations" resultset="3">
		</cfstoredproc>
		<cfquery name="local.qryThisRegPackageInstanceDetails" datasource="#application.dsn.membercentral.dsn#">
			select dataID
			from dbo.cf_fieldData
			where itemID = <cfqueryparam value="#local.instanceID#" cfsqltype="CF_SQL_INTEGER">
			and itemType = 'ticketPackInstCustom'
		</cfquery>
		<cfquery name="local.qryThisRegTicketSeatDetails" datasource="#application.dsn.membercentral.dsn#">
			select fd.dataID 
			from dbo.cf_fieldData as fd
			inner join dbo.ev_registrantPackageInstanceSeats as rpis on rpis.seatID = fd.itemID and fd.itemType = 'ticketPackSeatCustom'
			where rpis.instanceID = <cfqueryparam value="#local.instanceID#" cfsqltype="CF_SQL_INTEGER">
			and rpis.status = 'A'
		</cfquery>
		<cfquery name="local.qryThisPackageInstancePrice" dbtype="query">
			select sum(amount) as amount
			from [local].qryRegTransactions
			where itemID = <cfqueryparam value="#local.instanceID#" cfsqltype="CF_SQL_INTEGER">
			and itemType = 'TicketPackInst'
		</cfquery>
		<cfquery name="local.qryThisPackageInstanceCustomPrice" dbtype="query">
			select sum(amount) as amount
			from [local].qryRegTransactions
			where itemID in (0#valueList(local.qryThisRegPackageInstanceDetails.dataID)#)
			and itemType = 'ticketPackInstCustom'
		</cfquery>
		<cfquery name="local.qryThisPackageTicketSeatCustomPrice" dbtype="query">
			select sum(amount) as amount
			from [local].qryRegTransactions
			where itemID in (0#valueList(local.qryThisRegTicketSeatDetails.dataID)#)
			and itemType = 'ticketPackSeatCustom'
		</cfquery>

		<cfreturn { "success": true, "ticketpackagenameexpanded": local.qryThisRegPackageInstance.ticketPackageNameExpanded, "rownumber" : val(local.qryThisRegPackageInstance.row),
					"totalcount": val(local.qryRegPackageInstances.recordCount), "packageinstanceamount": dollarformat(val(local.qryThisPackageInstancePrice.amount)), "defaultcurrencytype": local.defaultCurrencyType,
					"packageinstancecustomamount": dollarformat(val(local.qryThisPackageInstanceCustomPrice.amount)), "ticketcount":val(local.qryThisRegPackageInstance.ticketCount), 
					"ticketseatcustomamount": dollarformat(val(local.qryThisPackageTicketSeatCustomPrice.amount)),"totalamount":dollarformat(val(local.qryThisPackageInstancePrice.amount) + val(local.qryThisPackageInstanceCustomPrice.amount) + val(local.qryThisPackageTicketSeatCustomPrice.amount))}>
	</cffunction>

	<cffunction name="getEventDefaultBadgeDetails" access="public" output="false" returntype="query">
		<cfargument name="eID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qryBadgeTemplateData = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryBadgeTemplateData">
			
		SELECT 
			ISNULL(defaultBadgeTemplateID,0) as defaultBadgeTemplateID, isnull(badgeDeviceID,0) as badgeDeviceID 
		FROM dbo.ev_events 
		WHERE eventID = <cfqueryparam value="#arguments.eID#" cfsqltype="CF_SQL_INTEGER">
		AND siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfreturn qryBadgeTemplateData>
	</cffunction>

	<cffunction name="saveDefaultBadgeTemplate" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="eID" type="numeric" required="true">
		<cfargument name="badgeTemplateID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfquery name="local.qrySaveDefaultBadgeTemplate" datasource="#application.dsn.membercentral.dsn#">
				UPDATE dbo.ev_events
				SET defaultBadgeTemplateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.BadgeTemplateID#">
				WHERE eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eID#">
				AND siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			</cfquery>

			<cfset local.data.success = true>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.data = "<b>An error occurred. Try again or contact support for assistance.</b>">
			<cfreturn local.data>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveBadgeDevice" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="eID" type="numeric" required="true">
		<cfargument name="badgeDeviceID" type="numeric" required="true">

		<cfset var qrySaveBadgeDevice = "">

		<cftry>
			<cfquery name="qrySaveBadgeDevice" datasource="#application.dsn.membercentral.dsn#">
				UPDATE dbo.ev_events
				SET badgeDeviceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.badgeDeviceID#">
				WHERE eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eID#">
				AND siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			</cfquery>

			<cfreturn { "success":true }>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfreturn { "success":false }>
		</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="printRegistrantBadge" access="public" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="registrantID" type="numeric" required="true">
		<cfargument name="deviceID" type="numeric" required="true">
		<cfargument name="templateContent" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.objDevice = CreateObject("component","model.admin.badges.device")>
		<cfset local.qryBadgeGateways = local.objDevice.getBadgeGateways()>
		<cfset local.qryDevice = local.objDevice.getDeviceDetails(siteID=arguments.siteID, deviceID=arguments.deviceID)>
		<cfset local.strResourcePrintData = getRegistrantDataForEmailing(registrantID=arguments.registrantID, recipientType="Registrants", mode="previewBadge")>
		<cfset local.parseContent = parseContentWithMergeCodes(siteID=arguments.siteID, siteCode=arguments.siteCode, content=arguments.templateContent, strResourcePrintData=local.strResourcePrintData)>

		<cfif len(local.parseContent.content)>
			<cfset local.printBadge = CreateObject("component","model.system.platform.printers.zebra").PrintBadge(siteID=arguments.siteID, deviceID=arguments.deviceID, 
										serialNumber=local.qryDevice.serialNumber, ZPL=local.parseContent.content)>
		<cfelse>
			<cfset local.printBadge = false>
		</cfif>

		<cfif local.printBadge>
			<cfquery name="local.qryAddLog" datasource="#application.dsn.platformstatsMC.dsn#">
				INSERT INTO dbo.apilog_badges (siteID, deviceID, registrantID, dateEntered)
				VALUES (
					<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
					<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.deviceID#">,
					<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.registrantID#">,
					GETDATE()
				)
			</cfquery>
		</cfif>
		
		<cfreturn local.printBadge>
	</cffunction>

	<cffunction name="getRegistrantBadgePreview" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="registrantID" type="numeric" required="true">
		<cfargument name="resourceType" type="string" required="true">
		<cfargument name="recipientType" type="string" required="true">
		<cfargument name="badgeWidth" type="numeric" required="true">
		<cfargument name="badgeHeight" type="numeric" required="true">
		<cfargument name="templateContent" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>
		
		<cfset local.strResourcePrintData = getRegistrantDataForEmailing(registrantID=arguments.registrantID, recipientType=arguments.recipientType, mode="previewBadge")>
		<cfset local.parseContent = parseContentWithMergeCodes(siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode, content=arguments.templateContent, strResourcePrintData=local.strResourcePrintData)>
		<cfset local.printPreview = CreateObject("component","model.admin.badges.device").printPreview(width=arguments.badgeWidth, height=arguments.badgeHeight, zpl=local.parseContent.content)>

		<cfsavecontent variable="local.data">
			<cfif StructKeyExists(local.printPreview.printer, "imagePreview") AND isImage(local.printPreview.printer.imagePreview)>
				<cfoutput>#local.printPreview.printer.imagePreview#</cfoutput>
			<cfelse>
				<cfoutput>
					<div>
						<h5>Error</h5>
						#local.printPreview.printer.ErrorMessage#
					</div>
				</cfoutput>
			</cfif>
		</cfsavecontent>

		<cfset local.retStruct.templateDisp = local.data>
		<cfset local.retStruct.registrantID = arguments.registrantID>
		<cfset local.retStruct.success = true>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="parseContentWithMergeCodes" access="private" output="no" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="content" type="string" required="true">
		<cfargument name="strResourcePrintData" type="struct" required="true">

		<cfset var local = StructNew()>

		<cfset arguments.content = urlDecode(arguments.content)>

		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = local.mc_siteInfo.mainhostname>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
		</cfif>

		<cfset local.memberInfo = application.objMember.getMemberInfo(memberID=val(arguments.strResourcePrintData.qryData.memberID))>
		<cfset local.qryMemberFields = application.objMergeCodes.getMergeViewFields(orgID=local.mc_siteInfo.orgID, memberID=local.memberInfo.memberID, content=arguments.content)>	
		
		<cfset local.tempMemberData = { memberID=local.memberInfo.memberID, firstName=local.memberInfo.FirstName, middleName=local.memberInfo.MiddleName,
							 			lastName=local.memberInfo.LastName, company=local.memberInfo.Company, suffix=local.memberInfo.Suffix, prefix=local.memberInfo.Prefix, 
							 			professionalSuffix=local.memberInfo.professionalSuffix, membernumber=local.memberInfo.membernumber, orgcode=local.memberInfo.orgcode,
							 			siteID=arguments.siteID, hostname=local.thisHostname, useRemoteLogin=local.mc_siteInfo.useRemoteLogin }>

		<cfloop array="#getMetaData(local.qryMemberFields)#" index="local.thisColumn">
			<cfif NOT StructKeyExists(local.tempMemberData,local.thisColumn.Name)>
				<cfset local.thisTempVal = local.qryMemberFields[local.thisColumn.Name][1]>
				<cfset structInsert(local.tempMemberData,local.thisColumn.Name,local.thisTempVal,true)>
			</cfif>
		</cfloop>

		<!--- resource specific merge codes --->
		<cfif structKeyExists(arguments.strResourcePrintData,"arrResTypeMergeCodes") and arrayLen(arguments.strResourcePrintData.arrResTypeMergeCodes)>
			<cfloop array="#arguments.strResourcePrintData.arrResTypeMergeCodes#" index="local.thisMergeCode">
				<cfif findNoCase("[[#local.thisMergeCode.mergeCode#]]", '#arguments.content#')>
					<cfset local.colValue = arguments.strResourcePrintData.qryData[local.thisMergeCode.columnName]>
					<cfif len(local.colValue)>
						<cfif local.thisMergeCode.isDateField>
							<cfset arguments.content = replaceNoCase(arguments.content,"[[#local.thisMergeCode.mergeCode#]]",dateFormat(local.colValue,'m/d/yyyy'),"all")>
						<cfelseif local.thisMergeCode.isAmountField>
							<cfset arguments.content = replaceNoCase(arguments.content,"[[#local.thisMergeCode.mergeCode#]]",dollarFormat(local.colValue),"all")>
						<cfelse>
							<cfset arguments.content = replaceNoCase(arguments.content,"[[#local.thisMergeCode.mergeCode#]]",local.colValue,"all")>
						</cfif>
					<cfelse>
						<cfset arguments.content = replaceNoCase(arguments.content,"[[#local.thisMergeCode.mergeCode#]]","","all")>
					</cfif>
				</cfif>
			</cfloop>
		</cfif>

		<cfset local.strContentArgs = { content=arguments.content, memberdata=local.tempMemberData, orgcode=local.mc_siteInfo.orgcode, sitecode=arguments.siteCode }>

		<cfset local.strContentArgs.eventData = arguments.strResourcePrintData.eventData>

		<cfset local.strMergedContent = application.objMergeCodes.processMergeCodes(argumentcollection=local.strContentArgs)>

		<cfset local.strReturn = { content=local.strMergedContent.content }>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="isRecurringEventsInImportEventsQueue" access="public" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="seriesID" type="numeric" required="true">

		<cfset var qryCheckQueue = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryCheckQueue">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int, @seriesID int, @createdFromEventCode varchar(15), @inImportEventsQueue bit = 0;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			SET @seriesID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seriesID#">;

			SELECT @createdFromEventCode = e.reportCode
			FROM dbo.ev_recurringSeries AS rs
			INNER JOIN dbo.ev_events AS e ON e.siteID = @siteID
				AND e.eventID = rs.createdFromEventID
				AND e.recurringSeriesID = rs.seriesID
			WHERE rs.seriesID = @seriesID
			AND rs.siteID = @siteID;

			IF EXISTS (select 1
					from platformQueue.dbo.tblQueueItems as qi
					inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.queueStatusID
					inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
						and qt.queueType = 'importEvents'
					inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
					inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
						and dc.columnName = 'RecurringSeriesCode'
						and qid.columnValueString = @createdFromEventCode)
				SET @inImportEventsQueue = 1;

			SELECT @inImportEventsQueue AS inImportEventsQueue;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryCheckQueue.inImportEventsQueue EQ 1>
	</cffunction>

	<cffunction name="getRecurringEvents" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="seriesID" type="numeric" required="true">
		<cfargument name="startEventID" type="numeric" required="false" default="0">
		<cfargument name="count" type="numeric" required="false" default="0">

		<cfset var qryEvents = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryEvents">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpEvents') IS NOT NULL 
				DROP TABLE ##tmpEvents;
			CREATE TABLE ##tmpEvents (eventID int PRIMARY KEY, eventTitle varchar(200), startTime datetime, endTime datetime, timeZone varchar(15), isAllDayEvent bit, [row] int);

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
				@seriesID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seriesID#">,
				@defaultTimeZoneID int, @startEventRow int = 0;

			SELECT @defaultTimeZoneID = defaultTimeZoneID 
			FROM dbo.sites 
			WHERE siteID = @siteID;

			INSERT INTO ##tmpEvents (eventID, eventTitle, startTime, endTime, timeZone, isAllDayEvent, [row])
			SELECT e.eventID, cl.contentTitle, ISNULL(lockTimes.startTime,et.startTime), ISNULL(lockTimes.endTime,et.endTime),
				ISNULL(ltz.timeZone,etz.timeZone), e.isAllDayEvent, 
				ROW_NUMBER() OVER (ORDER BY ISNULL(lockTimes.startTime,et.startTime), ISNULL(lockTimes.endTime,et.endTime))
			FROM dbo.ev_events as e
			INNER JOIN dbo.ev_times as et on et.eventID = e.eventID and et.timeZoneID = @defaultTimeZoneID
			INNER JOIN dbo.timeZones as etz on etz.timeZoneID = et.timeZoneID
			INNER JOIN dbo.cms_contentLanguages as cl on cl.siteid = @siteID AND cl.contentID = e.eventContentID and cl.languageID = 1
			LEFT OUTER JOIN dbo.ev_times as lockTimes on lockTimes.eventID = e.eventID and lockTimes.timeZoneID = e.lockTimeZoneID
			LEFT OUTER JOIN dbo.timeZones as ltz on ltz.timeZoneID = lockTimes.timeZoneID
			WHERE e.siteID = @siteID
			AND e.recurringSeriesID = @seriesID
			AND e.status IN ('A','I');

			<cfif arguments.startEventID>
				SELECT @startEventRow = [row] 
				FROM ##tmpEvents 
				WHERE eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.startEventID#">;
			</cfif>

			SELECT <cfif arguments.count> TOP (#arguments.count#)</cfif> eventID, eventTitle, startTime, endTime, timeZone, isAllDayEvent, [row]
			FROM ##tmpEvents
			<cfif arguments.startEventID>
				WHERE [row] > @startEventRow
			</cfif>
			ORDER BY [row];
			
			IF OBJECT_ID('tempdb..##tmpEvents') IS NOT NULL 
				DROP TABLE ##tmpEvents;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryEvents>
	</cffunction>

	<cffunction name="getAdvanceFormulas" access="public" returnType="query">
		<cfargument name="siteID" type="numeric" required="true">	

		<cfset var qryAllAFs = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryAllAFs">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			SELECT AFID, afName
			FROM dbo.af_advanceFormulas
			WHERE siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			ORDER BY afName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryAllAFs>
	</cffunction>

	<cffunction name="getEventTimeDetails" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="eventID" type="numeric" required="true">
		
		<cfset var qryEvent = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryEvent">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
				@eventID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">,
				@defaultTimeZoneID int, @registrationID int;

			SELECT @defaultTimeZoneID = defaultTimeZoneID 
			FROM dbo.sites 
			WHERE siteID = @siteID;

			SELECT @registrationID = registrationID
			FROM dbo.ev_registration 
			WHERE siteID = @siteID 
			AND eventID = @eventID 
			AND [status] = 'A';

			SELECT e.eventID, ISNULL(lockTimes.startTime,et.startTime) AS startTime, ISNULL(lockTimes.endTime,et.endTime) AS endTime,
				ISNULL(ltz.timeZoneID,etz.timeZoneID) AS timeZoneID, e.isAllDayEvent, e.lockTimeZoneID, e.recurringSeriesID, 
				es.afID AS recurringSeriesAFID, ISNULL(@registrationID,0) AS registrationID, e.siteResourceID
			FROM dbo.ev_events as e
			INNER JOIN dbo.ev_times as et on et.eventID = e.eventID and et.timeZoneID = @defaultTimeZoneID
			INNER JOIN dbo.timeZones as etz on etz.timeZoneID = et.timeZoneID
			LEFT OUTER JOIN dbo.ev_times as lockTimes on lockTimes.eventID = e.eventID and lockTimes.timeZoneID = e.lockTimeZoneID
			LEFT OUTER JOIN dbo.timeZones as ltz on ltz.timeZoneID = lockTimes.timeZoneID
			LEFT OUTER JOIN dbo.ev_recurringSeries AS es ON es.siteID = @siteID AND es.seriesID = e.recurringSeriesID
			WHERE e.siteID = @siteID
			AND e.eventID = @eventID
			AND e.status IN ('A','I');
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryEvent>
	</cffunction>

	<cffunction name="saveSponsorInfoForUpcomingRecurringEvents" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="eventSRID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif NOT hasEditEventRights(siteID=arguments.mcproxy_siteID, eventSRID=arguments.eventSRID)>
 				<cfthrow message="invalid request">
 			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySaveSponsorInfo">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					IF OBJECT_ID('tempdb..##tmpUpcomingRecurringEvents') IS NOT NULL 
						DROP TABLE ##tmpUpcomingRecurringEvents;
					CREATE TABLE ##tmpUpcomingRecurringEvents (eventID int PRIMARY KEY);

					DECLARE @siteID int, @eventID int, @recurringSeriesID int, @eventStartDate datetime;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
					SET @eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">;

					SELECT @recurringSeriesID = e.recurringSeriesID, @eventStartDate = et.startTime
					FROM dbo.ev_events AS e
					INNER JOIN dbo.ev_times AS et ON et.eventid = e.eventID
						AND et.timeID = e.defaultTimeID
					WHERE e.eventID = @eventID
					AND e.siteID = @siteID
					AND e.status IN ('A','I');
					
					INSERT INTO ##tmpUpcomingRecurringEvents (eventID)
					SELECT e.eventID
					FROM dbo.ev_events AS e
					INNER JOIN dbo.ev_times AS et ON et.eventid = e.eventID
						AND et.timeID = e.defaultTimeID
					WHERE e.recurringSeriesID = @recurringSeriesID
					AND e.siteID = @siteID
					AND e.[status] IN ('A','I')
					AND et.startTime > @eventStartDate;

					IF EXISTS (SELECT 1 FROM ##tmpUpcomingRecurringEvents) BEGIN
						BEGIN TRAN;
							DELETE su
							FROM dbo.sponsorsUsage AS su
							INNER JOIN ##tmpUpcomingRecurringEvents AS tmp ON tmp.eventID = su.referenceID
							WHERE su.referenceType = 'Events';

							INSERT INTO dbo.sponsorsUsage (sponsorID, referenceType, referenceID, sponsorOrder)
							SELECT DISTINCT su.sponsorID, su.referenceType, tmp.eventID, su.sponsorOrder
							FROM ##tmpUpcomingRecurringEvents AS tmp
							INNER JOIN dbo.sponsorsUsage AS su ON su.referenceType = 'Events'
								AND su.referenceID = @eventID;
						COMMIT TRAN;
					END

					IF OBJECT_ID('tempdb..##tmpUpcomingRecurringEvents') IS NOT NULL 
						DROP TABLE ##tmpUpcomingRecurringEvents;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getEndDateOfRecurringEventSeries" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="seriesID" type="numeric" required="true">

		<cfset var qryRecurringEvents = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryRecurringEvents">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT MAX(et.startTime) AS endsOn
			FROM dbo.ev_events as e
			INNER JOIN dbo.ev_times as et on et.eventID = e.eventID and et.timeID = e.defaultTimeID
			WHERE e.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			AND e.recurringSeriesID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seriesID#">
			AND e.status IN ('A','I');
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryRecurringEvents.endsOn>
	</cffunction>

	<cffunction name="validateTicketAndPackageName" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="eID" type="numeric" required="true">
		<cfargument name="ticketID" type="numeric" required="true">
		<cfargument name="registrationID" type="numeric" required="true">
		<cfargument name="strType" type="string" required="true">
		<cfargument name="ticketName" type="string" required="true">
		<cfargument name="ticketPackageID" type="numeric" required="false" default="0">
		
		<cfset var local = structNew()>
		<cfset local.recordCount = 0>
		
		<cfif arguments.strType eq 'ticket'>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryNameExist">
				SET XACT_ABORT, NOCOUNT ON;
				
				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">, 
					@eventID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eID#">,
					@registrationID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.registrationID#">,
					@ticketName varchar(max) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.ticketName#">;

				SELECT TOP 1 evr.registrationID
				FROM  dbo.ev_registration evr
				INNER JOIN dbo.ev_tickets evt ON evt.registrationID = evr.registrationID 
					AND evt.ticketName = @ticketName
					<cfif arguments.ticketID gt 0>
						AND evt.ticketID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ticketID#">
					</cfif>
				WHERE evr.registrationID = @registrationID
				AND evr.eventID = @eventID 
				AND evr.siteID = @siteID;
			</cfquery>	

			<cfset local.recordCount = local.qryNameExist.recordcount>

		<cfelseif arguments.strType eq 'ticketPackage'>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryNameExist">
				SET XACT_ABORT, NOCOUNT ON;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">, 
					@eventID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eID#">,
					@registrationID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.registrationID#">,
					@ticketName varchar(max) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.ticketName#">;

				SELECT TOP 1 evr.registrationID
				FROM  dbo.ev_registration evr
				INNER JOIN dbo.ev_tickets evt ON evt.registrationID = evr.registrationID
					AND evt.ticketID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ticketID#">
				INNER JOIN dbo.ev_ticketPackages evtp ON evtp.ticketID = evt.ticketID
					AND evtp.ticketPackageName = @ticketName
					<cfif arguments.ticketPackageID gt 0>
						AND evtp.ticketPackageID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ticketPackageID#">
					</cfif>
				WHERE evr.registrationID = @registrationID
				AND evr.eventID = @eventID
				AND evr.siteID = @siteID;
			</cfquery>	

			<cfset local.recordCount = local.qryNameExist.recordcount>
		</cfif>
		
		<cfset local.returnStruct.success = true>
		<cfif local.recordCount gt 0>
			<cfset local.returnStruct.success = false>
		</cfif>
		
		<cfreturn local.returnStruct>
	</cffunction>


	<cffunction name="processEventFileUpload" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="eventDocumentID" type="numeric" required="true">
		<cfargument name="oldDocumentID" type="numeric" required="true">
		<cfargument name="filename" type="string" required="false" default="">

		<cfset var local = structNew()>
		<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
		<cfset local.data = {}>

		<cfsetting requesttimeout="200">

		<cftry>

			<!--- Validate the request --->
			<cfquery name="local.qryValidateEventDoc" datasource="#application.dsn.membercentral.dsn#">
				SELECT ed.eventDocumentID, ed.documentID, ed.eventID, e.siteID
				FROM dbo.ev_eventDocuments ed
				INNER JOIN dbo.ev_events e ON e.eventID = ed.eventID
				WHERE ed.eventDocumentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventDocumentID#">
				AND e.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				AND ed.documentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.oldDocumentID#">
			</cfquery>

			<cfif local.qryValidateEventDoc.recordCount eq 1>
				<cfset local.eventDocument = getEventDocument(eventID=local.qryValidateEventDoc.eventID, eventDocumentID=arguments.eventDocumentID)>

				<cfif structKeyExists(local.eventDocument, "documentLanguageID") AND len(local.eventDocument.documentLanguageID)>
					<!--- Upload the new file --->
					<cfset local.fileUploaded = true>
					<cftry>
						<cfset local.newFile = local.objDocument.uploadFile("form.filename")>
						<cfset local.fileUploaded = local.newFile.uploadComplete>
					<cfcatch type="any">
						<cfset local.fileUploaded = false>
					</cfcatch>
					</cftry>

					<cfif local.fileUploaded>
						<cfset local.objDocument.forceFileExtentionIfBlank(local.newFile)>

						<cfset local.documentVersionID = local.objDocument.insertVersion(
                            orgcode=arguments.mcproxy_orgCode,
                            sitecode=arguments.mcproxy_siteCode,
                            fileData=local.newFile,
                            documentLanguageID=local.eventDocument.documentLanguageID,
                            contributorMemberID=session.cfcuser.memberdata.memberID,
                            recordedByMemberID=session.cfcuser.memberdata.memberID,
                            author=local.eventDocument.author,
                            oldFileExt=local.newFile.clientFileExt
                        )>

						<cfset local.data.success = true>
						<cfset local.data.documentID = arguments.oldDocumentID>
						<cfset local.data.documentVersionID = local.documentVersionID>
						<cfset local.data.message = "File uploaded successfully">
					<cfelse>
						<cfset local.data.success = false>
						<cfset local.data.message = "File upload failed">
					</cfif>
				<cfelse>
					<cfset local.data.success = false>
					<cfset local.data.message = "Unable to find document information">
				</cfif>
			<cfelse>
				<cfset local.data.success = false>
				<cfset local.data.message = "Invalid event document or insufficient permissions">
			</cfif>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
			<cfset local.data.message = "An error occurred during file upload">
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

</cfcomponent>