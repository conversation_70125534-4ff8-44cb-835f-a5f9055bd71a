----------------------------------------------------------------------------------------------------
/* Query #47: PASSED */
ALTER PROCEDURE [dbo].[sw_LogBlitzWho]
(
    @BlitzWhoDatabase NVARCHAR(128) = NULL,
    @BlitzWhoSchema NVARCHAR(128) = NULL,
    @BlitzWhoTable NVARCHAR(128) = NULL,
    @BlitzWhoRetention TINYINT = NULL,
    @BlitzWhoExpertMode BIT = NULL 
)
AS
BEGIN
    SET NOCOUNT ON;
    
    IF @BlitzWhoDatabase IS NULL
    BEGIN
        SET @BlitzWhoDatabase = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BlitzWhoDatabase')
    END

    IF @BlitzWhoSchema IS NULL
    BEGIN
        SET @BlitzWhoSchema = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BlitzWhoSchema')
    END

    IF @BlitzWhoTable IS NULL
    BEGIN
        SET @BlitzWhoTable = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BlitzWhoTable')
    END

    IF @BlitzWhoRetention IS NULL
    BEGIN
        SET @BlitzWhoRetention = (SELECT CAST([Value] AS TINYINT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BlitzWhoRetention')
    END

    IF @BlitzWhoExpertMode IS NULL
    BEGIN
        SET @BlitzWhoExpertMode = (SELECT CAST([Value] AS BIT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BlitzWhoExpertMode')
    END

    EXEC sp_BlitzWho 
        @OutputDatabaseName = @BlitzWhoDatabase,
        @OutputSchemaName = @BlitzWhoSchema,
        @OutputTableName = @BlitzWhoTable,
        @OutputTableRetentionDays = @BlitzWhoRetention,
        @ExpertMode = @BlitzWhoExpertMode

END
GO
