CREATE PROC dbo.sub_aajImportBackOut
@siteID int,
@importID int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpSubsToDelete') IS NOT NULL 
		DROP TABLE #tmpSubsToDelete;
	CREATE TABLE #tmpSubsToDelete (rootSubscriberID int PRIMARY KEY);

	DECLARE @batchIDToRemove TABLE (batchID int PRIMARY KEY);
	DECLARE @batchID int, @orgID int = dbo.fn_getOrgIDFromSiteID(@siteID);

	INSERT INTO @batchIDToRemove (batchID)
	SELECT aib.batchID 
	FROM sub_aajImportBatches aib
	INNER JOIN sub_aajImports ai ON ai.importID = aib.importID
	WHERE ai.siteID = @siteID 
	AND ai.importID = @importID 
	AND ai.isBackedOut = 0;

	INSERT INTO #tmpSubsToDelete (rootSubscriberID)
	SELECT distinct ss.rootSubscriberID
	FROM @batchIDToRemove as b
	INNER JOIN dbo.tr_batchTransactions as bt on bt.orgID = @orgID 
		AND bt.batchID = b.batchID
	INNER JOIN dbo.tr_transactions as t on t.ownedByOrgID = @orgID 
		AND t.transactionID = bt.transactionID
		AND t.typeID = 5
	INNER JOIN dbo.tr_relationships as tr on tr.orgID = @orgID 
		AND tr.transactionID = t.transactionID
	INNER JOIN dbo.tr_transactions as att on att.ownedByOrgID = @orgID 
		AND att.transactionID = tr.appliedToTransactionID
		AND att.typeID = 1
	INNER JOIN dbo.tr_applications as ta on ta.orgID = @orgID
		AND ta.transactionID = att.transactionID
		AND ta.itemType = 'Dues'
	INNER JOIN dbo.sub_subscribers as ss on ss.orgID = @orgID
		AND ss.subscriberID = ta.itemID;
	
	BEGIN TRAN;
		update ta 
		set ta.[status] = 'D'
		FROM #tmpSubsToDelete as sd
		INNER JOIN dbo.sub_subscribers as ss on ss.orgID = @orgID
			AND ss.rootSubscriberID = sd.rootSubscriberID
		INNER JOIN dbo.tr_applications as ta on ta.orgID = @orgID
			AND ta.itemID = ss.subscriberID
			AND ta.itemType = 'Dues';

		-- wipe statusHistory table for subtype
		delete sh
		FROM #tmpSubsToDelete as sd
		INNER JOIN dbo.sub_subscribers as ss on ss.orgID = @orgID
			AND ss.rootSubscriberID = sd.rootSubscriberID
		INNER JOIN dbo.sub_statusHistory as sh on sh.subscriberID = ss.subscriberID;

		-- wipe paymentStatusHistory table for subtype
		delete psh
		FROM #tmpSubsToDelete as sd
		INNER JOIN dbo.sub_subscribers as ss on ss.orgID = @orgID
			AND ss.rootSubscriberID = sd.rootSubscriberID
		INNER JOIN dbo.sub_paymentStatusHistory as psh on psh.subscriberID = ss.subscriberID;

		-- wipe actual subscribers
		delete ss
		FROM #tmpSubsToDelete sd
		INNER JOIN dbo.sub_subscribers as ss on ss.orgID = @orgID 
			AND ss.rootSubscriberID = sd.rootSubscriberID;

		-- VOID ALL TRANSACTIONS TIED TO A BATCH
		SELECT @batchID = min(batchID) FROM @batchIDToRemove;
		WHILE @batchID IS NOT NULL BEGIN
			EXEC dbo.tr_voidBatchTransactions @batchID=@batchID, @mode='A', @commit=1, @recordedByMemberID=@recordedByMemberID;
			SELECT @batchID = min(batchID) FROM @batchIDToRemove WHERE batchID > @batchID;
		END

		UPDATE dbo.sub_aajImports 
		SET isBackedOut = 1 
		WHERE siteID = @siteID
		AND importID = @importID;
	COMMIT TRAN;

	IF OBJECT_ID('tempdb..#tmpSubsToDelete') IS NOT NULL 
		DROP TABLE #tmpSubsToDelete;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
