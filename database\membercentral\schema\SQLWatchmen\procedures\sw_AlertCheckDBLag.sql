ALTER PROCEDURE [dbo].[sw_AlertCheckDBLag]
(
	@CheckDBLagAlertThreshold INT = NULL,
	@CheckDBLagExcludedDatabases NVARCHAR(MAX) = NULL,
	@MailProfileName NVARCHAR(MAX) = NULL,
	@AlertEmail NVARCHAR(MAX) = NULL,
	@Company NVARCHAR(MAX) = NULL,
	@Configuration NVARCHAR(MAX) = NULL
)
AS
BEGIN
	SET NOCOUNT ON;

    IF @CheckDBLagAlertThreshold IS NULL
    BEGIN
        SET @CheckDBLagAlertThreshold = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'CheckDBLagAlertThreshold')
    END

    IF @CheckDBLagExcludedDatabases IS NULL
    BEGIN
        SET @CheckDBLagExcludedDatabases = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'CheckDBLagExcludedDatabases')
    END

    IF OBJECT_ID('tempdb..#CheckDBLagAlert') IS NOT NULL
    BEGIN
        DROP TABLE #CheckDBLagAlert
    END

    CREATE TABLE #AlertCheckDBLagDatabases
    (
        [ID] INT IDENTITY(1,1) NOT NULL,
        [Database] NVARCHAR(128) NOT NULL
    )

    INSERT INTO #AlertCheckDBLagDatabases ([Database])
    SELECT [name] FROM [master].[sys].[databases] WHERE [database_id] <> 2 AND (',' + @CheckDBLagExcludedDatabases + ',' NOT LIKE '%,' + [name] + ',%')

    CREATE TABLE #AlertCheckDBLagDBInfo 
    (
        [ParentObject] NVARCHAR(128) NOT NULL,
        [Object] NVARCHAR(128) NOT NULL,
        [Field] NVARCHAR(128) NOT NULL,
        [Value] NVARCHAR(128) NOT NULL
    )

    CREATE TABLE #CheckDBLagAlert
    (
        [Database] NVARCHAR(128) NOT NULL,
        [LastCheckDBDate] DATETIME NOT NULL,
        [CheckDBAgeHours] INT NOT NULL
    )

    DECLARE @RowCount INT = 1
    DECLARE @Database NVARCHAR(128)
    DECLARE @SQL NVARCHAR(MAX)

    WHILE @RowCount <= (SELECT COUNT(*) FROM #AlertCheckDBLagDatabases)
    BEGIN 
        SELECT @Database = [Database] FROM #AlertCheckDBLagDatabases WHERE [ID] = @RowCount

        SET @SQL = 'INSERT INTO #AlertCheckDBLagDBInfo EXEC(''DBCC DBINFO (''''' + @Database + ''''') WITH TABLERESULTS;'')'
        EXEC [master].[dbo].[sp_executesql] @SQL

        INSERT INTO #CheckDBLagAlert ([Database], [LastCheckDBDate], [CheckDBAgeHours])
        SELECT
            @Database,
            [Value] AS [LastCheckDBDate],
            DATEDIFF(HOUR, [Value], GETDATE()) AS [CheckDBAgeHours]
        FROM #AlertCheckDBLagDBInfo
        WHERE [Field] = 'dbi_dbccLastKnownGood'

        TRUNCATE TABLE #AlertCheckDBLagDBInfo
        SET @RowCount = @RowCount + 1
    END

    IF EXISTS (SELECT * FROM #CheckDBLagAlert WHERE [CheckDBAgeHours] > @CheckDBLagAlertThreshold)
    BEGIN

        IF @MailProfileName IS NULL
		BEGIN
			SELECT @MailProfileName = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'MailProfileName'
		END

		IF @AlertEmail IS NULL
		BEGIN
			SELECT @AlertEmail = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'AlertEmail'
		END

		IF @Company IS NULL
		BEGIN
			SELECT @Company = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Company'
		END

		IF @Configuration IS NULL
		BEGIN
			SELECT @Configuration = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Configuration'
		END
        
        DECLARE @Subject NVARCHAR(255)
        DECLARE @Message NVARCHAR(4000)

        SET @Subject = 'CheckDB Lag Alert'
        SET @Message = 'The following databases have not had a CheckDB run in over ' + CAST(@CheckDBLagAlertThreshold AS NVARCHAR) + ' hours:' + CHAR(13) + CHAR(10) + CHAR(13) + CHAR(10)

        SELECT 
            @Message += [Database] + ' | ' + CAST([CheckDBAgeHours] AS NVARCHAR) + ' hours' + CHAR(13) + CHAR(10)
        FROM #CheckDBLagAlert
        WHERE [CheckDBAgeHours] > @CheckDBLagAlertThreshold

        INSERT INTO [SQLWatchmen].[dbo].[AlertLog] ([Alert]) VALUES ('CheckDBLag')

        EXEC msdb.dbo.sp_send_dbmail 
            @profile_name = @MailProfileName,
            @recipients = @AlertEmail,
            @subject = @Subject,
            @body = @Message

    END

    IF OBJECT_ID('tempdb..#CheckDBLagAlert') IS NOT NULL
    BEGIN
        DROP TABLE #CheckDBLagAlert
    END

END
GO
