----------------------------------------------------------------------------------------------------
/* Query #42: PASSED */
ALTER PROCEDURE [dbo].[sw_BackupDatabases]
(
    @BackupDatabasesDatabases nvarchar(max) = NULL,
    @BackupDatabasesDirectory nvarchar(max) = NULL,
    @BackupDatabasesVerify nvarchar(max) = NULL,
    @BackupDatabasesCleanupTime int = NULL,
    @BackupDatabasesCleanupMode nvarchar(max) = NULL,
    @BackupDatabasesCompress nvarchar(max) = NULL,
    @BackupDatabasesCheckSum nvarchar(max) = NULL,
    @BackupDatabasesBlockSize int = NULL,
    @BackupDatabasesBufferCount int = NULL,
    @BackupDatabasesMaxTransferSize int = NULL,
    @BackupDatabasesNumberOfFiles int = NULL,
    @BackupDatabasesMinBackupSizeForMultipleFiles int = NULL,
    @BackupDatabasesMaxFileSize int = NULL,
    @BackupDatabasesURL nvarchar(max) = NULL,
    @BackupDatabasesCredential nvarchar(max) = NULL
)
AS 
BEGIN
    SET NOCOUNT ON

    IF @BackupDatabasesDatabases IS NULL
    BEGIN
        SET @BackupDatabasesDatabases = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupDatabasesDatabases')
    END

    IF (@BackupDatabasesDirectory IS NOT NULL) AND ((SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = @BackupDatabasesDirectory) IS NOT NULL)
    BEGIN
        SET @BackupDatabasesDirectory = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = @BackupDatabasesDirectory)
    END

    IF @BackupDatabasesDirectory IS NULL
    BEGIN
        SET @BackupDatabasesDirectory = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'PrimaryBackupLocation')
    END

    IF @BackupDatabasesVerify IS NULL
    BEGIN
        SET @BackupDatabasesVerify = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupDatabasesVerify')
    END

    IF @BackupDatabasesCleanupTime IS NULL
    BEGIN
        SET @BackupDatabasesCleanupTime = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupDatabasesCleanupTime')
    END

    IF @BackupDatabasesCleanupMode IS NULL
    BEGIN
        SET @BackupDatabasesCleanupMode = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupDatabasesCleanupMode')
    END

    IF @BackupDatabasesCompress IS NULL
    BEGIN
        SET @BackupDatabasesCompress = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupDatabasesCompress')
    END

    IF @BackupDatabasesCheckSum IS NULL
    BEGIN
        SET @BackupDatabasesCheckSum = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupDatabasesCheckSum')
    END

    IF @BackupDatabasesBlockSize IS NULL
    BEGIN
        SET @BackupDatabasesBlockSize = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupDatabasesBlockSize')
    END

    IF @BackupDatabasesBufferCount IS NULL
    BEGIN
        SET @BackupDatabasesBufferCount = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupDatabasesBufferCount')
    END

    IF @BackupDatabasesMaxTransferSize IS NULL
    BEGIN
        SET @BackupDatabasesMaxTransferSize = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupDatabasesMaxTransferSize')
    END

    IF @BackupDatabasesNumberOfFiles IS NULL
    BEGIN
        SET @BackupDatabasesNumberOfFiles = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupDatabasesNumberOfFiles')
    END

    IF @BackupDatabasesMinBackupSizeForMultipleFiles IS NULL
    BEGIN
        SET @BackupDatabasesMinBackupSizeForMultipleFiles = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupDatabasesMinBackupSizeForMultipleFiles')
    END

    IF @BackupDatabasesMaxFileSize IS NULL
    BEGIN
        SET @BackupDatabasesMaxFileSize = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupDatabasesMaxFileSize')
    END

    IF @BackupDatabasesURL IS NULL
    BEGIN
        SET @BackupDatabasesURL = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupDatabasesURL')
    END

    IF @BackupDatabasesCredential IS NULL
    BEGIN
        SET @BackupDatabasesCredential = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupDatabasesCredential')
    END

    EXEC DatabaseBackup
        @Databases = @BackupDatabasesDatabases,
        @Directory = @BackupDatabasesDirectory,
        @BackupType = 'FULL',
        @Verify = @BackupDatabasesVerify,
        @CleanupTime = @BackupDatabasesCleanupTime,
        @CleanupMode = @BackupDatabasesCleanupMode,
        @Compress = @BackupDatabasesCompress,
        @CheckSum = @BackupDatabasesCheckSum,
        @BlockSize = @BackupDatabasesBlockSize,
        @BufferCount = @BackupDatabasesBufferCount,
        @MaxTransferSize = @BackupDatabasesMaxTransferSize,
        @NumberOfFiles = @BackupDatabasesNumberOfFiles,
        @MinBackupSizeForMultipleFiles = @BackupDatabasesMinBackupSizeForMultipleFiles,
        @MaxFileSize = @BackupDatabasesMaxFileSize,
        @URL = @BackupDatabasesURL,
        @Credential = @BackupDatabasesCredential,
        @LogToTable = 'Y'

END
GO
