<cfoutput>
	<meta charset="utf-8">
	<title>#event.getValue('mc_pageDefinition.pagetitle',event.getValue('mc_siteInfo.sitename'))#</title>
	<!--Css File Attachments-->
	#application.objCMS.getBootstrapHeadHTML()#
	#application.objCMS.getResponsiveHeadHTML()#
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
	<meta name="msapplication-config" content="/images/favicon/browserconfig.xml" />
	<link rel="apple-touch-icon" href="/images/favicon/apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="96x96"  href="/images/favicon/android-chrome-96x96.png">
	<link rel="icon" type="image/png" sizes="32x32" href="/images/favicon/favicon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="/images/favicon/favicon-16x16.png">
	<link rel="shortcut icon"  href="/images/favicon.ico"/>
	<link rel="manifest" href="/images/favicon/site.webmanifest">
	<link rel="mask-icon" href="/images/safari-pinned-tab.svg" >
	<link rel="stylesheet" href="/css/main.css">
	<link href="/assets/common/javascript/owlCarousel/211/owl.carousel.min.css" rel="stylesheet" type="text/css">
	<link href="/assets/common/javascript/owlCarousel/211/owl.theme.default.min.css" rel="stylesheet" type="text/css">
	 <!-- Font CSS -->
      <link rel="preconnect" href="https://fonts.googleapis.com">
      <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
      <link href="https://fonts.googleapis.com/css2?family=Roboto+Slab:wght@100;200;300;400;500;600;700;800;900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap" rel="stylesheet">
      
      <!-- 
         font-family: 'Roboto', sans-serif;
         font-family: 'Roboto Slab', serif;
      -->
	<!-- Font Awesome CSS -->
	#application.objCMS.getFontAwesomeHTML(includeVersion4Support=false)#
	<!-- custom css files -->
	<link href="/css/stylesheet.css" rel="stylesheet" type="text/css">
	<link href="/css/responsive.css" rel="stylesheet" type="text/css">
	<link rel="stylesheet" href="/css/print.css" media="print">
	#application.objCMS.getSiteCustomCSS(siteID=arguments.event.getValue('mc_siteInfo.siteID'))#
</cfoutput>