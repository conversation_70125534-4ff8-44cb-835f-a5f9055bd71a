----------------------------------------------------------------------------------------------------
/* Query #44: PASSED */
ALTER PROCEDURE [dbo].[sw_CheckDatabaseIntegrity]
(
    @CheckDatabaseIntegrityDatabases nvarchar(max) = NULL,
    @CheckDatabaseIntegrityMaxDOP int = NULL,
    @CheckDatabaseIntegrityTimeLimit int = NULL,
    @CheckDatabaseIntegrityDatabaseOrder nvarchar(max) = NULL
)
AS
BEGIN
    IF @CheckDatabaseIntegrityDatabases IS NULL
    BEGIN
        SET @CheckDatabaseIntegrityDatabases = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'CheckDatabaseIntegrityDatabases')
    END

    IF @CheckDatabaseIntegrityMaxDOP IS NULL
    BEGIN
        SET @CheckDatabaseIntegrityMaxDOP = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'CheckDatabaseIntegrityMaxDOP')
    END

    IF @CheckDatabaseIntegrityTimeLimit IS NULL
    BEGIN
        SET @CheckDatabaseIntegrityTimeLimit = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'CheckDatabaseIntegrityTimeLimit')
    END

    IF @CheckDatabaseIntegrityDatabaseOrder IS NULL
    BEGIN
        SET @CheckDatabaseIntegrityDatabaseOrder = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'CheckDatabaseIntegrityDatabaseOrder')
    END

    EXEC DatabaseIntegrityCheck
        @Databases = @CheckDatabaseIntegrityDatabases,
        @MaxDOP = @CheckDatabaseIntegrityMaxDOP,
        @TimeLimit = @CheckDatabaseIntegrityTimeLimit,
        @DatabaseOrder = @CheckDatabaseIntegrityDatabaseOrder,
        @LogToTable  = 'Y'
END
GO
