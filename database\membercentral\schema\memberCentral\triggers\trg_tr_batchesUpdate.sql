CREATE TRIGGER trg_tr_batchesUpdate ON dbo.tr_batches
AFTER UPDATE 
AS 

SET NOCOUNT ON
BEGIN TRY

	IF NOT EXISTS (SELECT * FROM inserted) RETURN	

	IF UPDATE(depositDate) or UPDATE(statusID) BEGIN
		INSERT INTO platformQueue.dbo.queue_transactionLimitFlagging (transactionID, orgID, siteID)
		SELECT distinct bt.transactionID, bt.orgID, t.recordedOnSiteID
		FROM dbo.tr_batchTransactions as bt
		INNER JOIN dbo.tr_transactions as t on t.ownedByOrgID = bt.orgID and t.transactionID = bt.transactionID
		INNER JOIN Inserted as I ON bt.batchID = I.batchID
		INNER JOIN Deleted as D ON bt.batchID = D.batchID
		WHERE D.depositDate <> I.depositDate
		OR (D.statusID <> I.statusID AND I.statusID <> 4)
	END

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO
