/* Query #28: PASSED */
CREATE   PROCEDURE [dbo].[sw_AlertBlocking]
(
    @BlockingAlertInterval SMALLINT = NULL,
    @LogBlockingThreshold SMALLINT = NULL,
    @MailProfileName VARCHAR(128) = NULL,
	@AlertEmail VARCHAR(128) = NULL,
	@Company VARCHAR(128) = NULL,
	@Configuration VARCHAR(128) = NULL
)
AS
BEGIN  
    SET NOCOUNT ON

    IF @BlockingAlertInterval IS NULL
    BEGIN
        SET @BlockingAlertInterval = (SELECT CAST([Value] AS SMALLINT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BlockingAlertInterval')
    END

    DECLARE @DateTimeCutoff DATETIME
    SET @DateTimeCutoff = DATEADD(MINUTE, -@BlockingAlertInterval, GETDATE())
    IF (SELECT [DateTime] FROM [SQLWatchmen].[dbo].[AlertLog] WHERE [Alert] = 'Blocking' AND [DateTime] > @DateTimeCutoff) IS NOT NULL
    BEGIN
        RETURN
    END

    IF (SELECT COUNT(*) FROM [SQLWatchmen].[dbo].[BlockingLog] WHERE [DateTime] > @DateTimeCutoff) = 0
    BEGIN
        RETURN
    END

    IF @LogBlockingThreshold IS NULL
    BEGIN
        SET @LogBlockingThreshold = (SELECT CAST([Value] AS SMALLINT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'LogBlockingThreshold')
    END

    IF @MailProfileName IS NULL
    BEGIN
        SET @MailProfileName = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'MailProfileName')
    END

    IF @AlertEmail IS NULL
    BEGIN  
        SET @AlertEmail = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'AlertEmail')
    END

    IF @Company IS NULL
    BEGIN
        SET @Company = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Company')
    END

    IF @Configuration IS NULL
    BEGIN
        SET @Configuration = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Configuration')
    END

    DECLARE @Subject nvarchar(255) = N'Blocking Detected | ' + @Configuration + N' | ' + @Company
    DECLARE @Body nvarchar(MAX)

    SET @Body = 'At least one session has been blocked for more than the designated threshold of ' + CAST(@LogBlockingThreshold AS NVARCHAR) + ' minutes.' + CHAR(13) + CHAR(10)
            + 'Please refer to the details listed below:' + CHAR(13) + CHAR(10) +
            'Log Date Time | Session ID | Start Time | Blocking Session ID | Total Time (Seconds)' + CHAR(13) + CHAR(10)
    SELECT @Body += CAST(x.[DateTime] AS NVARCHAR) + ' | ' + CAST([SessionID] AS NVARCHAR) + ' | ' + 
        CAST([StartTime] AS NVARCHAR) + ' | ' + CAST([BlockingSessionID] AS NVARCHAR) + ' | ' + CAST([TotalTimeSeconds] AS NVARCHAR) + CHAR(13) + CHAR(10)
        FROM (SELECT [DateTime], [SessionID], [StartTime], [BlockingSessionID], [TotalTimeSeconds] FROM [SQLWatchmen].[dbo].[BlockingLog]) AS x

		print  'sending mail'

    EXEC msdb.dbo.sp_send_dbmail
        @profile_name = @MailProfileName,
        @recipients = @AlertEmail,
        @subject = @Subject,
        @body = @Body

    INSERT INTO [SQLWatchmen].[dbo].[AlertLog] ([Alert]) VALUES ('Blocking')

END
GO
