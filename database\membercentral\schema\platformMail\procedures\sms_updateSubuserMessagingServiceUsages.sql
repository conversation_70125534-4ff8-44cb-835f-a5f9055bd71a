USE platformMail
GO

CREATE PROC dbo.sms_updateSubuserMessagingServiceUsages
@messagingServiceID int,
@siteID int,
@controllingSiteResourceID int,
@name varchar(max),
@status char(1),
@verificationRequired int,
@usageTypeCode varchar(500),
@referenceID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @usageTypeID INT;

	SELECT TOP 1  @usageTypeID = t.usageTypeID 
			FROM dbo.sms_usageTypes t
			INNER JOIN sms_subuserMessagingServiceAllowedUsages smsau 
				ON t.usageTypeID = smsau.usageTypeID
				AND t.usageTypeCode = @usageTypeCode
			INNER JOIN sms_subuserMessagingServices sms 
				ON smsau.siteID = @siteID
				AND sms.isActive = 1
				AND smsau.siteID = @siteID
				AND sms.messagingServiceID = @messagingServiceID;

	IF EXISTS (SELECT * FROM dbo.sms_subuserMessagingServiceUsages 
		WHERE 
		messagingServiceID = @messagingServiceID AND 
		siteID = @siteID AND controllingSiteResourceID  = @controllingSiteResourceID AND 
		usageTypeID  =  @usageTypeID AND 
		referenceID = @referenceID)
	BEGIN
		UPDATE dbo.sms_subuserMessagingServiceUsages
		SET status = @status
		WHERE messagingServiceID = @messagingServiceID AND siteID = @siteID AND controllingSiteResourceID = @controllingSiteResourceID AND
		referenceID = @referenceID AND usageTypeID  =  @usageTypeID;
	END
	ELSE 
	BEGIN
		INSERT INTO dbo.sms_subuserMessagingServiceUsages 
		VALUES (@messagingServiceID,@siteID,@controllingSiteResourceID,@name,@status,@verificationRequired,@usageTypeID,@referenceID);
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO