CREATE PROC dbo.queue_invNumPrefix_download
@status varchar(60),
@csvfilename varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @selectsql varchar(max) = '';

	SELECT @selectsql = 'SELECT ''invNumPrefix'' as queueType, qs.queueStatus, qi.itemID, qi.itemGroupUID, qi.invoiceID,
		qi.orgID, qi.prefix, qi.dateAdded, qi.dateUpdated, ROW_NUMBER() OVER(order by qi.dateAdded, qi.itemID) as mcCSVorder
		*FROM* platformQueue.dbo.queue_invNumPrefix as qi
		INNER JOIN platformQueue.dbo.tblQueueStatuses as qs on qs.queuestatusID = qi.statusID and qs.queueStatus = ''' + @status + '''';

	EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@csvfilename, @returnColumns=0;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
