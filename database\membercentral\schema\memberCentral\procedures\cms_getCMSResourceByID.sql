ALTER PROC dbo.cms_getCMSResourceByID
@siteID int,
@siteResourceID int,
@languageID int,
@memberID int,
@ovrMode varchar(30)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLAR<PERSON> @activeMemberID int, @viewFunctionID int, @editFunctionID int, @templateID int, @modeID int, @ovrModeID int, 
		@siteCode varchar(10), @mainZoneID int, @usetemplateID int, @usemodeID int, @useZoneID int, @groupPrintID int;

	IF OBJECT_ID('tempdb..#layoutInfo') IS NOT NULL
		DROP TABLE #layoutInfo;
	IF OBJECT_ID('tempdb..#tmpCMSZoneAssignments') IS NOT NULL
		DROP TABLE #tmpCMSZoneAssignments;
	IF OBJECT_ID('tempdb..#tmpSRForSite') IS NOT NULL
		DROP TABLE #tmpSRForSite;
	CREATE TABLE #layoutInfo (templateID int, templateTypeName varchar(100), modeID int, modeName varchar(100), 
		templateFilename varchar(100), sitecode varchar(10), orgcode varchar(10), siteResourceID int);
	CREATE TABLE #tmpCMSZoneAssignments (autoid int IDENTITY(1,1), zoneID int, siteResourceID int, resourceTypeID int, 
		sortOrder int, dataLevel int, viewRightPrintID int, editContentRightPrintID int);
	CREATE TABLE #tmpSRForSite (siteid int, siteResourceID int, resourceTypeClassName varchar(100), applicationInstanceID int, 
		applicationInstanceName varchar(100), applicationTypeName varchar(100), applicationWidgetInstanceID int,
		applicationInstanceResourceID int, applicationWidgetTypeName varchar(100), applicationWidgetInstanceName varchar(100),
		contentID int);

	select @activeMemberID = dbo.fn_getActiveMemberID(@memberID);
	select @siteCode = dbo.fn_getSiteCodeFromSiteID(@siteID);
	select TOP 1 @usetemplateID = ovtemplateID from dbo.cms_pageSections ps where siteID = @siteID and parentSectionID is null;
	select @usemodeID = modeID from dbo.cms_pageModes where modeName = @ovrMode;
	select @mainZoneID = zoneid from dbo.cms_pageZones where zoneName = 'Main';
	select @viewFunctionID = functionID from dbo.cms_siteResourceFunctions where functionName = 'view';
	select @editFunctionID = functionID from dbo.cms_siteResourceFunctions where functionName = 'editContent';

	IF @activeMemberID > 0
		select @groupPrintID = groupPrintID 
		from dbo.ams_members
		where memberID = @activeMemberID;

	IF @groupPrintID IS NULL
		select @groupPrintID = o.publicGroupPrintID
		from dbo.organizations as o
		inner join dbo.sites as s on s.orgID = o.orgID 
			and s.siteID = @siteID;

	-- if usetemplateid is null then use template 1 - platform default
	IF @usetemplateID IS NULL 
		select @usetemplateID = templateID
		from dbo.cms_pageTemplates 
		where siteID is null 
		and templateFilename = 'DefaultTemplate'
		and [status] = 'A';
		
	IF @ovrMode = 'stream' BEGIN
		set @useZoneID = @mainZoneID;

		select @usetemplateID = isnull(p.ovTemplateID,@useTemplateID)
		from dbo.cms_pageZonesResources as pzr
		inner join dbo.cms_pageZones as pz on pzr.zoneID = pz.zoneID
			and pzr.siteResourceID = @siteResourceID
			and pz.zoneID = @useZoneID
		inner join dbo.cms_pages as p on p.pageID = pzr.pageID and p.siteID = @siteID
		inner join dbo.cms_pageLanguages as pl ON p.pageid = pl.pageID and pl.languageID = @languageID;
	END

	insert into #layoutInfo (templateID, templateTypeName, modeID, modeName, templateFilename, sitecode, orgcode, siteResourceID)
	select pSp.templateID, ptt.templateTypeName, pSp.modeID, pm.modeName, pt.templateFilename, s2.sitecode, o.orgcode, pt.siteResourceID
	from (select @usetemplateID as templateID, @usemodeID as modeID) as pSp
	inner join dbo.cms_pageTemplates as pt on pSp.templateID = pt.templateid
	inner join dbo.cms_pageTemplateTypes ptt on pt.templateTypeID = ptt.templateTypeID
	inner join dbo.cms_pageModes as pm on pSp.modeID = pm.modeID
	left outer join dbo.sites as s2 on s2.siteid = pt.siteid
	left outer join dbo.organizations as o on s2.orgid = o.orgid;

	-- find all zone assignments for page, not considering pageMode
	insert into #tmpCMSZoneAssignments (zoneid, siteResourceID, resourceTypeID, sortOrder, dataLevel)
	select @mainZoneID, siteResourceID, resourceTypeID, 1 as sortOrder, 0 as dataLevel
	from dbo.cms_siteResources
	where siteResourceID = @siteResourceID
	and siteID = @siteID
	and siteResourceStatusID = 1;

	IF @@ROWCOUNT = 0 BEGIN
		select cast('<pageStructure />' as xml) as pageStructureXML;
		GOTO on_done;
	END

	-- update view and editContentPerms
	update za 
	set za.viewRightPrintID = srfrp_view.rightPrintID, 
		za.editContentRightPrintID = srfrp_editContent.rightPrintID
	from #tmpCMSZoneAssignments za
	left outer join dbo.cache_perms_siteResourceFunctionRightPrints srfrp_view on srfrp_view.siteID = @siteID
		and srfrp_view.siteResourceID = za.siteResourceID 
		and srfrp_view.functionID = @viewFunctionID
	left outer join dbo.cache_perms_siteResourceFunctionRightPrints srfrp_editContent on srfrp_editContent.siteID = @siteID
		and srfrp_editContent.siteResourceID = za.siteResourceID 
		and srfrp_editContent.functionID = @editFunctionID;

	-- use site default language
	SELECT @languageID = defaultLanguageID from dbo.sites where siteCode = @sitecode;

	-- populate #tmpSRForSite
	EXEC dbo.cms_getCMSResourcesForSite @siteID=@siteID;
	
	-- Now that we know template and mode, figure out page pod assignments 
	select cast(isNull((
		select page.pageID, page.pageName, page.allowReturnAfterLogin, page.siteResourceID as pageSiteResourceID, page.pageDirectives, page.dateUnavailable, 
			isnull(s.siteID,0) as siteID,
			isnull(s.siteCode,'') as siteCode,
			isnull(s.orgID,0) as orgID,
			isnull(o.orgCode,'') as orgCode,
			page.sectionname, page.sectionCode, 0 as isVisible, @languageID as pageLanguageID,
			page.sectionBreadcrumb, 
			coalesce(siteResourceDetails.applicationWidgetInstanceName,siteResourceDetails.applicationInstanceName,'') as pageTitle, 
			'' as pageDesc, 
			'' as keywords, 
			ISNULL(layout.templateFileName,'DefaultTemplate') as layoutFileName,
			ISNULL(layout.templateID,'') as templateID,
			ISNULL(layout.siteResourceID,'') as templateSiteResourceID,
			ISNULL(layout.templateTypeName,'') as templateTypeName,
			ISNULL(layout.modeName,'Normal') as layoutMode, 
			ISNULL(layout.sitecode,'') as layoutSiteCode, ISNULL(layout.orgcode,'') as layoutOrgCode, 
			pageZone.zoneName, 
			siteResource.siteResourceID,
			CASE WHEN EXISTS (
				select gprp.groupRightPrintID
				from dbo.cache_perms_siteResourceFunctionRightPrints srfrp
				inner join dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
					and gprp.rightPrintID = srfrp.rightPrintID
					and gprp.groupPrintID = @groupPrintID
				where srfrp.siteID = @siteID
				and srfrp.siteresourceID = siteResource.siteResourceID
				and srfrp.functionID = 4
				)
				THEN 1
			ELSE 0
			END as allowed,
			ISNULL(srt.resourceType,'') as resourceType,
			ISNULL(siteResourceDetails.resourceTypeClassName,'') as resourceClass,
			ISNULL(siteResourceDetails.applicationInstanceID,'') as appInstanceID, 
			ISNULL(siteResourceDetails.applicationInstanceName,'') as appInstanceName, 
			ISNULL(siteResourceDetails.applicationTypeName,'') as appTypeName, 
			ISNULL(siteResourceDetails.applicationWidgetInstanceID,'') as appWidgetInstanceID, 
			ISNULL(siteResourceDetails.applicationInstanceResourceID,'') as appInstanceResourceID, 
			ISNULL(siteResourceDetails.applicationWidgetTypeName,'') as appWidgetTypeName, 
			ISNULL(siteResourceDetails.applicationWidgetInstanceName,'') as appWidgetInstanceName,
			ISNULL(siteResourceDetails.contentID,'') as contentID,
			ISNULL(siteResource.viewRightPrintID,'') as viewRightPrintID,
			ISNULL(siteResource.editContentRightPrintID,'') as editContentRightPrintID
		from (
			select '' as pageID, '' as pageName, 0 as siteResourceID, 0 as allowReturnAfterLogin, '' as sectionName, 
				'' as sectionCode, '' as sectionID, '' as pageDirectives, '' as dateUnavailable, '' as sectionBreadcrumb
		) as [page]
		inner join #layoutInfo as layout on 1=1
		inner join dbo.cms_pageTemplatesModesZones as ptmz on layout.modeid = ptmz.modeid and ptmz.templateID = layout.templateID
		inner join dbo.cms_pageZones as pageZone on pageZone.zoneid = ptmz.zoneid and isnull(@useZoneID,pageZone.zoneid) = ptmz.zoneid
		left outer join #tmpCMSZoneAssignments as [siteResource]
			inner join #tmpSRForSite as siteResourceDetails on siteResourceDetails.siteResourceID = siteResource.siteResourceID
			inner join dbo.sites as s on s.siteID = siteResourceDetails.siteID
			inner join dbo.organizations as o on o.orgID = s.orgID
			inner join dbo.cms_siteResourceTypes as srt on siteResource.resourceTypeID = srt.resourceTypeID
			on ptmz.zoneID = siteResource.zoneid and siteResource.siteResourceID = @siteResourceID
		order by siteResource.zoneid, siteResource.dataLevel desc, siteResource.sortOrder
		for xml auto, root('pageStructure')
	),'<pageStructure />') as xml) as pageStructureXML;

	on_done:
	IF OBJECT_ID('tempdb..#layoutInfo') IS NOT NULL
		DROP TABLE #layoutInfo;
	IF OBJECT_ID('tempdb..#tmpCMSZoneAssignments') IS NOT NULL
		DROP TABLE #tmpCMSZoneAssignments;
	IF OBJECT_ID('tempdb..#tmpSRForSite') IS NOT NULL
		DROP TABLE #tmpSRForSite;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
