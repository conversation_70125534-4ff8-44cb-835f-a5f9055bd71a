CREATE PROC dbo.TwilioMsgStatusTracking_sendMessage
@messageBody XML

AS

SET NOCOUNT ON;
BEGIN TRY

	EXEC dbo.sb_dialogpool_sendMessage N'TwilioMsgStatusTrackingInitiatorService', N'TwilioMsgStatusTrackingTargetService', 
		N'PlatformQueue/GeneralXMLEOSContract', N'PlatformQueue/GeneralXMLRequest', @messageBody;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO