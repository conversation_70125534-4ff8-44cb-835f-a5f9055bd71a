<cfscript>
	local.orgCode	= event.getValue('mc_siteInfo.orgCode');
	variables.applicationReservedURLParams 	= "";
	local.customPage.baseURL = "/?#getBaseQueryString(false)#";

	arguments.event.paramValue('ca','showList');
	arguments.event.paramValue('periodStartDate','#month(now())#/#day(now())#/#year(now())-1#');
	arguments.event.paramValue('periodEndDate',dateFormat(now(),'m/d/yyyy'));
	arguments.event.paramValue('membernumber','');

	local.periodStartDate = arguments.event.getValue('periodStartDate');
	local.periodEndDate = arguments.event.getValue('periodEndDate');
	local.membernumber = arguments.event.getValue('membernumber');
</cfscript>

<!--- default to show list of events --->
<cfset arguments.event.paramValue('panel','showList')>

<cfif arguments.event.getValue('panel') eq "viewCert">
	
	<cfscript>
	// get encrypted registrantid
	local.encryptedRID = arguments.event.getValue('rid','');
	
	// change xPcmKx to % (case sensitive), decode, fromBase64, and decrypt
	try { local.decryptedRID = val(decrypt(toString(toBinary(URLDecode(replace(local.encryptedRID,"xPcmKx","%","ALL")))),"TRiaL_SMiTH")); } 
	catch (any e) { local.decryptedRID = 0; }
	
	// generate certificate for registrantID
	local.strCertificate = CreateObject("component","model.admin.events.certificate").generateCertificate(registrantID=local.decryptedRID);
	</cfscript>
	
	<!--- redirect to pdf --->
	<cfif len(local.strCertificate.certificateURL)>
		<cflocation url="#local.strCertificate.certificateURL#" addtoken="no">
	<cfelse>
		<cflocation url="/?pg=MyCLEHistory&panel=certErr&mode=direct" addtoken="no">
	</cfif>
	
<cfelseif arguments.event.getValue('panel') eq "certErr">
	<cfoutput>
	<div class="tsAppHeading">My CLE History</div>
	<br/>
	<div class="tsAppBodyText">
		<b>Sorry...</b>, we were unable to generate a certificate at this time.<br/><br/>
		If you continue to see this message, please contact CAAA for assistance.
	</div>
	</cfoutput>

<cfelse>
	<!--- CLE history based on event registration with credit selections --->
	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCLE">
		select a.authorityID, a.authorityName as authorityName, eco.ApprovalNum, 
			r.attended,e.eventid,evr.startDate,evr.endDate,r.registrantID, r.dateRegistered, 
			cl.contentTitle, rc.creditValueAwarded, isnull(ast.ovTypeName,cat.typeName) as creditType,
			datepart(year,r.dateRegistered) as CLEYear
		from dbo.ev_registrants as r
		inner join dbo.ev_registration as evr on evr.registrationID = r.registrationID AND r.recordedOnSiteID = evr.siteID
		inner join dbo.ev_events as e on e.eventid = evr.eventid AND e.siteID = evr.siteID
		inner join dbo.cms_contentLanguages as cl on cl.contentID = e.eventContentID and cl.languageID = 1
		inner join dbo.crd_requests as rc on rc.registrantID = r.registrantID and rc.creditAwarded = 1 and r.status='A'
		inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
		inner join dbo.crd_offerings as eco on eco.offeringID = ect.offeringID
		inner join dbo.crd_authoritySponsorTypes as ast on ast.astid = ect.astid
		inner join dbo.crd_authoritySponsors as ecas on ecas.asid = ast.asid
		inner join dbo.crd_authorities as a on a.authorityID = ecas.authorityID	
		inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID 
		inner join dbo.ams_members as m1 on m1.memberID = r.memberID	
		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
			inner join dbo.ams_members as m on m.memberID = m1.activeMemberID and m.orgID = #arguments.event.getValue('mc_siteinfo.orgID')# 
			<cfif local.membernumber NEQ ''>
			AND m.membernumber=<cfqueryparam value="#local.membernumber#" cfsqltype="CF_SQL_VARCHAR"> 	
			</cfif>
		<cfelse>
			inner join dbo.ams_members as m on m.memberID = m1.activeMemberID
			and m.memberID = <cfqueryparam value="#session.cfcUser.memberData.memberID#" cfsqltype="CF_SQL_INTEGER">
		</cfif>
		WHERE 1=1  
		AND a.authorityName LIKE  '%State Bar Of California%'	
		<cfif len(trim(local.periodStartDate))>
			AND r.dateRegistered >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#dateformat(local.periodStartDate,"m/d/yyyy")# 00:00:00.000">	
		</cfif> 
		<cfif len(trim(local.periodEndDate))>
			AND r.dateRegistered <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#dateformat(local.periodEndDate,"m/d/yyyy")# 23:59:59.997">
		</cfif> 
		ORDER BY r.dateRegistered desc, e.eventid, a.authorityName, creditType
	</cfquery> 
	<cfquery name="local.qryCLETotals" dbtype="query">
		select CLEYear, creditType,authorityName, sum(creditValueAwarded) as totalCLE
		from [local].qryCLE
		group by CLEYear, creditType, authorityName
		order by CLEYear, totalCLE
	</cfquery>

	<!--- seminarweb history --->	
	<cfset local.qrySWP = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(local.orgCode).qryAssociation>
	<cfset local.showSW = false>
	<cfset local.depoMemberDataID = val(session.cfcUser.memberData.depomemberdataid)>
	<cfset local.memberIDToUse = session.cfcUser.memberData.memberID>
	<cfif (application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)) and len(trim(local.membernumber))>
		<cfset local.qryGetDepoMemberData = application.objCustomPageUtils.mem_DepoMemberData(memberNumber=local.membernumber, orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
		<cfset local.depoMemberDataID = val(local.qryGetDepoMemberData.depomemberdataid)>
		<cfset local.memberIDToUse = application.objMember.getMemberIDByMemberNumber(memberNumber=local.membernumber, orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
	</cfif>

	<cfif local.depoMemberDataID>
		<cfstoredproc procedure="sw_getEnrollmentHistory" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.memberIDToUse#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.orgCode#">
			<cfprocresult name="local.qrySWL" resultset="1">
			<cfprocresult name="local.qrySWOD" resultset="2">
			<cfprocresult name="local.qryCertPrograms" resultset="3">
		</cfstoredproc>
		<cfset local.showSW = true>		
	</cfif>

	<cfsavecontent variable="local.cleCSS">
		<cfoutput>
		<style type="text/css">
		##clehistory th, ##swlhistory th, ##swodhistory th { text-align:left; border-bottom:1px solid ##666; }
		</style>
		<script language="JavaScript">
			function viewEVCert(rid) {
				var certURL = '/?pg=myCLEHistory&panel=viewCert&mode=stream&rid=' + rid;
				window.open(certURL,'ViewCertificate','width=990,height=500');
			}
		</script>
		<cfif local.showSW>
			<script language="JavaScript">
			function viewCert(eId) {
				var certURL = '/?pg=semWebCatalog&panel=viewCert&mode=direct&eId=' + eId;
				window.open(certURL,'ViewCertificate','width=990,height=500');
			}
			</script>
		</cfif>
		</cfoutput>	
	</cfsavecontent>
	<cfhtmlhead text="#local.cleCSS#">

	<cfsavecontent variable="local.dataHead">
		<cfoutput>
		<script language="javascript">
			$(function(){
				mca_setupDatePickerRangeFields('periodStartDate','periodEndDate');
			});

			function _FB_validateForm(){
				<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
					if ($('##membernumber').val() == '') { 
						alert('Must enter MemberNumber before you can filter report.');
						return false;
					}
				</cfif>
				return true;
			}
		</script>
		<style type="text/css">
			##periodStartDate, ##periodEndDate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
			.creditTable td, .creditTable th { border:1px solid ##707070; border-collapse:collapse; padding:4px; }
			.balTable td { border:0px; padding:2px; }
		</style>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#application.objCommon.minText(local.dataHead)#">

	<cfform method="POST" action="#local.customPage.baseURL#" name="frmCLE" onsubmit="return _FB_validateForm();">
		<cfoutput>
			<span style="float:right;">
				<button class="btn" type="button" onClick="window.print();"><i class="icon-print"></i> Print</button>
			</span>
			<h3>SFTLA CLE History for #session.cfcuser.memberdata.firstname# #session.cfcuser.memberdata.middlename# #session.cfcuser.memberdata.lastname# #session.cfcuser.memberdata.suffix# </h3></br>

			<table cellpadding="4" cellspacing="0">
				<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
					<tr valign="top">
						<td colspan="4" >
							<b>MemberNumber:</b> &nbsp;
							<cfinput class="tsAppBodyText" type="text" name="membernumber" id="membernumber" value="#local.membernumber#" size="30" placeholder="Must enter MemberNumber.">
						</td>
					</tr>
				</cfif>
				<tr valign="top">
					<td>
						<b>Select your dates:</b> &nbsp;
						<cfinput type="text" name="periodStartDate" id="periodStartDate" value="#local.periodStartDate#" size="14"> 
						&nbsp;to&nbsp;
						<cfinput type="text" name="periodEndDate" id="periodEndDate" value="#local.periodEndDate#" size="14">
					</td>
					<td>
						<button class="btn" type="submit">Filter Report</button>
					</td>
				</tr>
			</table>
		</cfoutput>
	</cfform>
	<cfoutput>
	<!--- Live Conferences & Events --->
	<div><b>#local.qrySWP.brandConfTab#</b></div></cfoutput>	
	<cfif local.qryCLE.recordcount>	
		<cfset local.arr = createObject("java", "java.util.LinkedHashMap").init()/>
		<cfloop query="local.qryCLETotals">
			<cfset local.arr[local.qryCLETotals.cleYear][local.qryCLETotals.authorityName][local.qryCLETotals.creditType] = local.qryCLETotals.totalCLE>
		</cfloop>
		<cfoutput>
		<table cellpadding="2" cellspacing="0" border="0">
			<tr>
				<td class="tsAppBodyText">
					<div class="total">
						<b>Credit Totals</b>
					</div>
				</td>
			</tr>
			<tr>
				<td>
					<cfloop collection="#(local.arr)#" item="local.key">
						<div class="block">
							<div class="year tsAppBodyText">
								<b>#local.key#</b>
							</div>
							<cfloop collection="#local.arr[local.key]#" item="local.key1">
								<div class="authorityName tsAppBodyText">
									#local.key1#
								</div>
								<cfloop collection="#local.arr[local.key][local.key1]#" item="local.key2">
									<div class="creditType tsAppBodyText">
										#local.arr[local.key][local.key1][local.key2]# #local.key2#
									</div>
								</cfloop>
							</cfloop>
						</div>
					</cfloop>
				</td>
			</tr>
		</table>
		<br/>
		<table width="98%" cellpadding="2" cellspacing="0" border="0" id="clehistory">
		<tr class="tsAppBodyText">
			<th width="90">Date</th>
			<th>Title</th>
			<th colspan="2">Credit Awarded</th>
		</tr>
		</cfoutput>
		
		<cfset local.oddeven = 0>
		<cfoutput query="local.qryCLE" group="eventid">
			<cfset local.oddeven = local.oddeven + 1>
			<cfset local.rID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qryCLE.registrantID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
			<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
				<td class="tsAppBodyText">#dateformat(local.qryCLE.dateRegistered,"mm/dd/yyyy")#</td>
				<td class="tsAppBodyText">#local.qryCLE.contentTitle#</td>
				<td><a href="javascript:viewEVCert('#local.rID#');" title="Print Certificate"><i class="icon-print"></i></a></td>
				<td class="tsAppBodyText" nowrap>
						
					<table>
						<tr>
							<td>
								<cfoutput group="authorityName">
									#local.qryCLE.authorityName#<br/>
									<cfif len(local.qryCLE.ApprovalNum)>
										Approval Number: #local.qryCLE.ApprovalNum#<br/>
									</cfif>
									<cfoutput>
										#local.qryCLE.creditValueAwarded# #local.qryCLE.creditType#<br/>
									</cfoutput>
									<br>
								</cfoutput>
							</td>
						</tr>
					</table>
				</td>
			</tr>	
		</cfoutput>	
		<cfoutput>
		</table>
		</cfoutput>
	<cfelse>
		<cfoutput>
		<div class="tsAppBodyText">
			There are no Live Conferences & Events to display.
		</div>
		</cfoutput>
	</cfif>

	<cfoutput><br/><br/></cfoutput>
	<!--- Live Webinars --->
	<cfif local.showSW>
	<cfquery name="local.qrySWLOrder"   dbtype="query">
		SELECT * FROM  [local].qrySWL  ORDER BY dateStart DESC
	</cfquery>
		<cfif local.qrySWLOrder.recordcount>
			<cfoutput>
				<div><b>#local.qrySWP.brandSWLTab#</b></div><br/>
				<table width="98%" cellpadding="2" cellspacing="0" border="0" id="swlhistory">
					<tr class="tsAppBodyText">
						<th width="90">Date</th>
						<th>Title</th>
						<th>Status</th>
					</tr>
					<cfloop query="local.qrySWLOrder">
						<cfset local.eID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qrySWLOrder.enrollmentID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
						<tr valign="top">
							<td class="tsAppBodyText">#DateFormat(local.qrySWLOrder.dateStart,'m/d/yyyy')#</td>
							<td class="tsAppBodyText">#encodeForHTML(local.qrySWLOrder.seminarName)#</td>
							<td class="tsAppBodyText" width="120">
								<cfif len(local.qrySWLOrder.dateCompleted) and local.qrySWLOrder.passed is 1 and local.qrySWLOrder.offerCertificate>
									<a href="javascript:viewCert('#local.eID#')">View certificate(s)</a>
								<cfelseif len(local.qrySWLOrder.dateCompleted) and local.qrySWLOrder.passed is 1 and not local.qrySWLOrder.offerCertificate>
									Completed
								<cfelseif len(local.qrySWLOrder.dateCompleted) and local.qrySWLOrder.passed is 0>
									Failed
								<cfelseif now() lt local.qrySWLOrder.dateStart>
									Not yet begun
								<cfelse>
									Did not attend
								</cfif>
							</td>
						</tr>
					</cfloop>
				</table>
				<br/><br/>
			</cfoutput>
		</cfif>

		<!--- SWOD --->
		<!--- Self-Paced Online Seminars--->
		<cfoutput>
			<cfif local.qrySWOD.recordcount>
				<div><b>#local.qrySWP.brandSWODTab#</b></div><br/>
				<table width="98%" cellpadding="2" cellspacing="0" border="0" id="swodhistory">
				<tr class="tsAppBodyText">
					<th>Title</th>
					<th>Status</th>
				</tr>
				<cfloop query="local.qrySWOD">
					<cfset local.eID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qrySWOD.enrollmentID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
					<tr valign="top">
						<td class="tsAppBodyText">#encodeForHTML(local.qrySWOD.seminarName)#</td>
						<td class="tsAppBodyText" width="140">
							<cfif len(local.qrySWOD.dateCompleted) is 0>
								<cfif local.qrySWOD.isPublished and local.qrySWOD.preReqFulfilled>
									<a href="/?pg=swOnDemandPlayer&seminarID=#local.qrySWOD.seminarID#&enrollmentID=#local.qrySWOD.enrollmentID#&orgCode=#arguments.event.getValue('mc_siteinfo.orgCode')#" target="_blank">Begin</a>
								<cfelseif local.qrySWOD.isPublished>
									Awaiting Prereqs
								<cfelse>
									Not available
								</cfif>
							<cfelse>
								<cfif local.qrySWOD.isPublished>
									<a href="/?pg=swOnDemandPlayer&seminarID=#local.qrySWOD.seminarID#&enrollmentID=#local.qrySWOD.enrollmentID#&orgCode=#arguments.event.getValue('mc_siteinfo.orgCode')#" target="_blank">Review</a> |
								</cfif>
								<cfif local.qrySWOD.passed and local.qrySWOD.offerCertificate>
									<a href="javascript:viewCert('#local.eID#');">Certificate</a>
								<cfelseif local.qrySWOD.passed and not local.qrySWOD.offerCertificate>
									Completed
								<cfelseif not local.qrySWOD.passed>
									Failed
								</cfif>
							</cfif>
						</td>
					</tr>
				</cfloop>
				</table>
				<br/><br/>
			</cfif>	
		</cfoutput>
	<cfelse>
		<cfoutput>Nothing to report during the selected period.</cfoutput>
	</cfif>
</cfif>