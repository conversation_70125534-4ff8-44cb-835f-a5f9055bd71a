<cfcomponent extends="model.AppLoader" output="no">
	<cfset defaultEvent = "controller">
	<cfset variables.appResourceType = "FileShare2">
	<cfset variables.fileShareSettings = structNew()/>
	<cfset variables.applicationReservedURLParams = "fsAction,fsSectionID,fsDocumentID,lh,un,tab">
	
	<cffunction name="init" access="public" returntype="void" output="false">
		<cfset variables.fileShareSettings = getFileShareSettings(this.appInstanceID)/>
		<cfset this.fileShareSettings = variables.fileShareSettings />
	</cffunction>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
	
		<cfset var local = structNew()>

		<cfset init()>
		<cfset buildAppRightsStruct(memberid=application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.orgID),siteid=variables.fileShareSettings.siteid)>
		<cfset local.rc = arguments.event.getCollection()>
		<cfset local.returnStruct = structNew()>
		<cfset local.viewToUse = "fileShare2/notFound"/>
		<!--- Redirect if invalid tab param found --->
		<cfset local.allowedTabs = "B,S,A,UD,U,PLF">
		<cfif arguments.event.valueExists('tab') and NOT listfindnocase(local.allowedTabs,arguments.event.getValue('tab'))>
			<cflocation url="/?#getBaseQueryString(false)#" addtoken="no">
		</cfif>

		<cfset local.returnStruct.actionStruct = structNew()>
		<cfif isFileShareValid()>

			<cfset local.returnStruct.fsAction = arguments.event.getValue("fsAction","showBrowse")/>
			<cfswitch expression="#local.returnStruct.fsAction#">
				<cfcase value="showSection">
					<cfset local.returnStruct.actionStruct = actionShowSection(Event)/>
					<cfset local.viewToUse = local.returnStruct.actionStruct.view />
				</cfcase>
				<cfcase value="showBrowse">
					<cfset local.returnStruct.actionStruct = actionShowBrowse(Event)/>
					<cfset local.viewToUse = local.returnStruct.actionStruct.view />
				</cfcase>
				<cfcase value="getFileList">
					<cfset local.returnStruct.actionStruct = actionGetFileList(Event)/>
					<cfset local.viewToUse = local.returnStruct.actionStruct.view />
				</cfcase>
				<cfcase value="getFoldersXML">
					<cfset local.returnStruct.actionStruct = actionGetFoldersXML(Event)/>
					<cfset local.viewToUse = local.returnStruct.actionStruct.view />
				</cfcase>
				<cfcase value="getFilesXML">
					<cfset local.returnStruct.actionStruct = actionGetFilesXML(Event)/>
					<cfset local.viewToUse = local.returnStruct.actionStruct.view />
				</cfcase>
				<cfcase value="viewDocument">
					<cfset local.returnStruct.actionStruct = actionViewDocument(Event)/>
					<cfset local.viewToUse = local.returnStruct.actionStruct.view />
				</cfcase>
				<cfcase value="addDocument">
					<cfset local.returnStruct.actionStruct = actionAddDocument(Event)/>
					<cfset local.viewToUse = local.returnStruct.actionStruct.view />
				</cfcase>
				<cfcase value="addDocuments">
					<cfset local.returnStruct.actionStruct = actionAddDocuments(Event)/>
					<cfset local.viewToUse = local.returnStruct.actionStruct.view />
				</cfcase>
				<cfcase value="moveDocument">
					<cfset local.returnStruct.actionStruct = actionMoveDocument(Event)/>
					<cfset local.viewToUse = local.returnStruct.actionStruct.view />
				</cfcase>
				<cfcase value="copyDocument">
					<cfset local.returnStruct.actionStruct = actionCopyDocument(Event)/>
					<cfset local.viewToUse = local.returnStruct.actionStruct.view />
				</cfcase>
				<cfcase value="editDocument">
					<cfset local.returnStruct.actionStruct = actionEditDocument(Event)/>
					<cfset local.viewToUse = local.returnStruct.actionStruct.view />
				</cfcase>
				<cfcase value="deleteDocument">
					<cfset local.returnStruct.actionStruct = actionDeleteDocument(Event)>
					<cfset local.viewToUse = local.returnStruct.actionStruct.view>
				</cfcase>
				<cfcase value="hideDocument">
					<cfset local.returnStruct.actionStruct = actionHideDocument(Event)>
					<cfset local.viewToUse = local.returnStruct.actionStruct.view>
				</cfcase>
				<cfcase value="unhideDocument">
					<cfset local.returnStruct.actionStruct = actionUnhideDocument(Event)>
					<cfset local.viewToUse = local.returnStruct.actionStruct.view>
				</cfcase>
				<cfcase value="downloadDocument">
					<cfset local.returnStruct.actionStruct = actionDownloadDocument(Event)>
					<cfset local.viewToUse = local.returnStruct.actionStruct.view>
				</cfcase>
				<cfcase value="addCategory">
					<cfset local.returnStruct.actionStruct = actionAddCategory(Event)>
					<cfset local.viewToUse = local.returnStruct.actionStruct.view>
				</cfcase>
			</cfswitch>
		</cfif>

		<cfif listFirst(local.viewToUse,"/") eq "fileshare2">
			<cfif (application.objCMS.getTemplateSetting(arguments.event,"supportsBootstrap") eq "true")>
				<cfset local.viewDirectory = "responsive">
			<cfelse>
				<cfset local.viewDirectory = "default">
			</cfif>
			<cfset local.viewToUse = listInsertAt(local.viewToUse, 2, local.viewDirectory, "/")>
		</cfif>
		<cfset local.returnStruct.fileShareSettings = variables.fileShareSettings>
		<cfset local.returnStruct.appRightsStruct 	= variables.appRightsStruct>
		<cfset local.returnStruct.baseQueryString 	= getBaseQueryString(false)>
		<cfset local.returnStruct.appBaseLink 			= getAppBaseLink(variables.fileShareSettings.applicationInstanceID,variables.fileShareSettings.siteID)>
		<cfset local.sectionToLoad = arguments.event.getValue("fsSectionID",variables.fileShareSettings.rootSectionID)>
		
		<!--- record app hit --->
		<cfset application.objPlatformStats.recordAppHit(appname="fileshare2",appsection="")>
		
		<cfreturn returnAppStruct(local.returnStruct,local.viewToUse)>
	</cffunction>

	<cffunction name="actionShowSection" access="private" returntype="struct" output="false">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>
		<cfset local.sectionToShow = arguments.event.getValue('fsSectionID',variables.fileShareSettings.rootSectionID)>
		<cfset local.actionReturnStruct = structNew()>
		<cfif listFind(valuelist(variables.fileShareSettings.qrySections.sectioniD),local.sectionToShow)>
			<cfset local.actionReturnStruct.sectionToShow = local.sectionToShow>
			<cfset local.actionReturnStruct.view = "fileshare2/list">
			<cfset local.actionReturnStruct.bucketID = variables.fileShareSettings.BucketID>
			<cfset local.actionReturnStruct.gridURL = "/?event=cms.showresource&resid=#this.siteResourceID#&fsAction=getFileList&mode=stream&fsSectionID=">
			<cfset local.actionReturnStruct.foldersURL = "/?event=cms.showresource&resid=#this.siteResourceID#&fsAction=getFoldersXML&mode=stream&fsSectionID=">
			<cfset local.actionReturnStruct.filesURL = "/?event=cms.showresource&resid=#this.siteResourceID#&fsAction=getFilesXML&mode=stream&fsSectionID=">
		<cfelse>
			<cfset local.actionReturnStruct.view = "fileshare2/notfound">
		</cfif>

		<cfreturn local.actionReturnStruct>
	</cffunction>

	<cffunction name="actionViewDocument" access="private" returntype="struct" output="false">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>
		
		<cfset local.sectionToShow = arguments.event.getValue('fsSectionID',variables.fileShareSettings.rootSectionID)>
		<cfset local.actionReturnStruct = structNew()>
		<cfif listFind(valuelist(variables.fileShareSettings.qrySections.sectionID),local.sectionToShow)>
			<cfset local.actionReturnStruct.sectionToShow = local.sectionToShow>
			<cfset local.actionReturnStruct.view = "fileshare2/viewDocument">
			<cfset local.actionReturnStruct.bucketID = variables.fileShareSettings.BucketID>
			<cfset local.actionReturnStruct.showSectionURL = "/?#getBaseQueryString(false)#&fsAction=showSection&fsSectionID=#local.sectionToShow#">
			<cfset local.actionReturnStruct.getDocData = CreateObject("component","model.system.platform.document").getDocumentData(documentID=arguments.event.getValue('fsDocumentID'))>
			
			<cfif variables.fileShareSettings.showVersioning>
					<cfset local.actionReturnStruct.getDocumentVersions = CreateObject("component","model.admin.common.modules.documentDetails.documentDetails").getDocumentVersions(local.actionReturnStruct.getDocData.documentLanguageID)>
			</cfif>
		<cfelse>
			<cfset local.actionReturnStruct.view = "fileshare2/notfound">
		</cfif>

		<cfreturn local.actionReturnStruct>
	</cffunction>

	<cffunction name="actionAddDocument" access="private" returntype="struct" output="false">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>
		<cfset local.rc = arguments.event.getCollection()>
		<cfset local.sectionToShow = arguments.event.getValue('fsSectionID',variables.fileShareSettings.rootSectionID)>
		<cfset local.actionReturnStruct = structNew()>

		<cfset local.actionReturnStruct.tabValue = 'UD'>
		
		<cfif listFind(valuelist(variables.fileShareSettings.qrySections.sectioniD),local.sectionToShow)>
			<cfset local.actionReturnStruct.qryGetCategoryTrees = getCategoryTrees() />
			<cfset local.actionReturnStruct.sectionToShow = local.sectionToShow>
			<cfset local.actionReturnStruct.view = "fileshare2/editDocument">
			<cfset local.actionReturnStruct.bucketID = variables.fileShareSettings.BucketID>
			<cfset local.actionReturnStruct.PG = "#arguments.event.getValue('PG')#">
			<cfif arguments.event.getValue('COMMPG', '') NEQ ''>
				<cfset local.actionReturnStruct.PG = "#arguments.event.getValue('PG')#&commpg=#arguments.event.getValue('COMMPG')#">
			</cfif>			
			<cfset local.actionReturnStruct.baseURL = "/?pg=#local.actionReturnStruct.PG#&panel=#arguments.event.getValue('from','browse')#&byt=#arguments.event.getValue('byT',0)#&catid=#arguments.event.getValue('catID',0)#">
			<cfset local.actionReturnStruct.formPostURL = "/?#getBaseQueryString(false)#&fsAction=addDocument">
			<cfset local.actionReturnStruct.showSectionURL = "/?#getBaseQueryString(false)#&fsAction=showSection&fsSectionID=#local.sectionToShow#">
			<cfset local.actionReturnStruct.editURL = "/?#getBaseQueryString(false)#&fsAction=editDocument&lang=en">
			<cfset local.actionReturnStruct.getDocData = structNew()>
			<cfset local.actionReturnStruct.getDocData.documentID = 0>
			<cfset local.actionReturnStruct.getDocData.siteResourceID = 0>
			<cfset local.actionReturnStruct.getDocData.sectionID = local.actionReturnStruct.sectionToShow>
			<cfset local.actionReturnStruct.getDocData.languageID = #session.mcstruct.languageID#>
			<cfset local.actionReturnStruct.getDocData.docTitle = "">
			<cfset local.actionReturnStruct.getDocData.docDesc = "">
			<cfset local.actionReturnStruct.getDocData.fileName = "">
			<cfset local.actionReturnStruct.getDocData.dateCreated = "">
			<cfset local.actionReturnStruct.getDocData.dateModified = "">
			<cfset local.actionReturnStruct.getDocData.fileExt = "">
			<cfset local.actionReturnStruct.getDocData.publicationDate = "">
			<cfset local.actionReturnStruct.getDocData.author = "">
			<cfset local.actionReturnStruct.getDocData.contributorMemberID = "#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getvalue("mc_pageDefinition.orgID"))#">
			<cfset local.actionReturnStruct.qryFSColumns = getExtraFSColumns(variables.fileShareSettings.siteResourceID)>
			<cfloop query="local.actionReturnStruct.qryFSColumns">
				<cfset local.actionReturnStruct.getExtraDocData["fs_#local.actionReturnStruct.qryFSColumns.columnID#"] = "">
			</cfloop>
			<cfloop query="local.actionReturnStruct.qryGetCategoryTrees">
				<cfset local.actionReturnStruct.getExtraDocData["s_#local.actionReturnStruct.qryGetCategoryTrees.categoryTreeID#ID"] = "">
			</cfloop>
			<cfif variables.appRightsStruct.fsAddDocuments and isdefined("local.rc.fsDocumentSaved")>
				<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
				<cfscript>
					// check to see if there is a new file to upload --------------------------------------------
					if(arguments.event.getTrimValue('newFile') NEQ "" ){
						// if yes then set fileToUpload to the form variable newFile ------------------------------
						arguments.event.setValue('fileToUpload','newFile');
						// pre set the fileUploaded variable to TRUE ----------------------------------------------
						local.fileUploaded = TRUE;
						// try to upload the file to the proper destination ---------------------------------------
						try {
							local.newFile = local.objDocument.uploadFile("form.newFile");
							if (local.newFile.uploadComplete){
								local.objDocument.forceFileExtentionIfBlank(local.newFile);
								arguments.event.setValue('fsFileName',local.newFile.clientFile);
								arguments.event.setValue('fsfileExt',local.newFile.clientFileExt);
							}
							else{	local.fileUploaded = FALSE; }
						} 
						catch(any excpt) {
							// if if fails to upload the set the fileUploaded flag to FALSE -------------------------
							local.fileUploaded = FALSE;
						}
					}
					if( local.fileUploaded ){
						
						if (len(arguments.event.getTrimValue('fsPublicationDate',''))) {
							local.insertDocResults = local.objDocument.insertDocument(
								siteID=variables.fileShareSettings.siteID,
								resourceType='ApplicationCreatedDocument',
								parentSiteResourceID=variables.fileShareSettings.siteResourceID,
								sectionID=arguments.event.getValue('fsSectionID'),
								docTitle=arguments.event.getValue('fsdocTitle'),
								docDesc=arguments.event.getValue('fsdocDesc'),
								author=arguments.event.getValue('fsAuthor'),
								fileData=local.newFile,
								isActive=1,
								contributorMemberID=arguments.event.getValue('fsContributorID'),
								recordedByMemberID=application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.orgID),
								publicationDate=arguments.event.getValue('fsPublicationDate'));
								}					
						else {
							local.insertDocResults = local.objDocument.insertDocument(
								siteID=variables.fileShareSettings.siteID,
								resourceType='ApplicationCreatedDocument',
								parentSiteResourceID=variables.fileShareSettings.siteResourceID,
								sectionID=arguments.event.getValue('fsSectionID'),
								docTitle=arguments.event.getValue('fsdocTitle'),
								docDesc=arguments.event.getValue('fsdocDesc'),
								author=arguments.event.getValue('fsAuthor'),
								fileData=local.newFile,
								isActive=1,
								contributorMemberID=arguments.event.getValue('fsContributorID'),
								recordedByMemberID=application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.orgID));
						}
						
						arguments.event.setValue('fsDocumentID',local.insertDocResults.documentID);
						linkDocViewRightsToFileShare(local.insertDocResults.documentID);
					}	
					else{
						local.actionReturnStruct.displayMessage = local.newFile.ReasonText;
						local.actionReturnStruct.view = "fileshare2/fileEcho";
					}
				</cfscript>

				<cfset local.actionReturnStruct.getDocData = local.objDocument.getDocumentData(arguments.event.getValue('fsDocumentID'))>
				<cfscript>
					if (variables.fileShareSettings.notifyOnAdd eq 1)
					{
						local.emailBody = "#session.cfcUser.memberdata.firstname# #session.cfcUser.memberdata.lastname# (#session.cfcUser.memberdata.memberNumber#) has added a document #chr(13)#  #(application.objPlatform.isRequestSecure() ? 'https' : 'http')#://#application.objPlatform.getCurrentHostname()#/#local.actionReturnStruct.editURL#&fsDocumentID=#local.rc.fsDocumentID# #chr(13)##chr(13)# ";
						local.from = #session.cfcUser.memberdata.email#;
						if (len(local.from) eq 0) 
							local.from = '<EMAIL>';
	
						fssendemail(event=arguments.event, siteNotifyEmail=arguments.event.getValue('mc_siteInfo.defaultAdminEmails',''), 
							from=local.from, subject="#variables.fileShareSettings.applicationInstanceName# document added", 
							body=local.emailBody);
					}
				</cfscript>

				<!--- Update the categories for the documents--->
				<cfloop query="local.actionReturnStruct.qryGetCategoryTrees">
					<cfset local.categoryID = arguments.event.getValue('s_#local.actionReturnStruct.qryGetCategoryTrees.categoryTreeID#ID', '') />
					<cfif local.categoryID NEQ "">
						<cfloop list="#local.categoryID#" index="local.insertCatID">
							<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.saveFieldSets">
								INSERT INTO dbo.cms_categorySiteResources (CategoryID, siteResourceID)
								VALUES (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.insertCatID#">, <cfqueryparam cfsqltype="cf_sql_integer" value="#local.actionReturnStruct.getDocData.siteResourceID#">)					
							</cfquery>
						</cfloop>
					</cfif>
				</cfloop>								

				<!--- Update the custom fields on the fileshare application --->
				<cfloop query="local.actionReturnStruct.qryFSColumns">
					<cfset local.columnValue = arguments.event.getValue('fs_#local.actionReturnStruct.qryFSColumns.columnID#', '') />
					<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="cms_saveSiteResourceData">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.actionReturnStruct.getDocData.siteResourceID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.actionReturnStruct.qryFSColumns.columnID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
						<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.columnValue#">
					</cfstoredproc>						
				</cfloop>
				
				<!--- Load the extra data so the category trees will be set --->
				<cfset local.actionReturnStruct.getExtraDocData = getExtraDocData(local.actionReturnStruct.getDocData.siteResourceID) />
				<cfloop query="local.actionReturnStruct.qryFSColumns">
					<cfset local.actionReturnStruct.getExtraDocData["fs_#local.actionReturnStruct.qryFSColumns.columnID#"] = getExtraFSColumnData(local.actionReturnStruct.getDocData.siteResourceID, local.actionReturnStruct.qryFSColumns.columnID)>
				</cfloop>				
				
				<cfset local.actionReturnStruct.msg = 1>
				<cfset local.actionReturnStruct.formPostURL = "/?#getBaseQueryString(false)#&fsAction=editDocument&fsDocumentID=#arguments.event.getValue('fsDocumentID',0)#">
			</cfif>
		<cfelse>
			<cfset local.actionReturnStruct.view = "fileshare2/notfound">
		</cfif>
		<cfreturn local.actionReturnStruct>
	</cffunction>

	<cffunction name="actionAddDocuments" access="private" returntype="struct" output="false">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>
		<cfset local.rc = arguments.event.getCollection()>
		<cfset local.sectionToShow = arguments.event.getValue('fsSectionID',variables.fileShareSettings.rootSectionID)>
		<cfset local.actionReturnStruct = structNew()>
		<cfset local.actionReturnStruct.tabValue = 'UD'>
		
		<cfif listFind(valuelist(variables.fileShareSettings.qrySections.sectioniD),local.sectionToShow)>
			<cfset local.actionReturnStruct.qryGetCategoryTrees = getCategoryTrees() />
			<cfset local.actionReturnStruct.sectionToShow = local.sectionToShow>
			<cfset local.actionReturnStruct.view = "fileshare2/addDocuments">
			<cfset local.actionReturnStruct.PG = "#arguments.event.getValue('PG')#">
			<cfif arguments.event.getValue('COMMPG', '') NEQ ''>
				<cfset local.actionReturnStruct.PG = "#arguments.event.getValue('PG')#&commpg=#arguments.event.getValue('COMMPG')#">
			</cfif>			
			<cfset local.actionReturnStruct.bucketID = variables.fileShareSettings.BucketID>
			<cfset local.actionReturnStruct.baseURL = "/?pg=#local.actionReturnStruct.PG#&panel=#arguments.event.getValue('from','browse')#&byt=#arguments.event.getValue('byT',0)#&catid=#arguments.event.getValue('catID',0)#">
			<cfset local.actionReturnStruct.formPostURL = "/?#getBaseQueryString(false)#">
			<cfset local.actionReturnStruct.uploadURL = "/?pg=#local.actionReturnStruct.PG#&panel=uploadsEdit">
			<cfset local.actionReturnStruct.uploadSingleURL = "/?pg=#local.actionReturnStruct.PG#&fsAction=addDocument">
			<cfset local.actionReturnStruct.showSectionURL = "/?#getBaseQueryString(false)#&fsAction=showSection&fsSectionID=#local.sectionToShow#">
			<cfset local.actionReturnStruct.getDocData = structNew()>
			<cfset local.actionReturnStruct.getDocData.documentID = 0>
			<cfset local.actionReturnStruct.getDocData.sectionID = local.actionReturnStruct.sectionToShow>
			<cfset local.actionReturnStruct.getDocData.languageID = #session.mcstruct.languageID#>
			<cfset local.actionReturnStruct.getDocData.docTitle = "">
			<cfset local.actionReturnStruct.getDocData.docDesc = "">
			<cfset local.actionReturnStruct.getDocData.fileName = "">
			<cfset local.actionReturnStruct.getDocData.dateCreated = "">
			<cfset local.actionReturnStruct.getDocData.dateModified = "">
			<cfset local.actionReturnStruct.getDocData.fileExt = "">
			<cfset local.actionReturnStruct.getDocData.publicationDate = "">
			<cfset local.actionReturnStruct.getDocData.author = "">
			<cfset local.actionReturnStruct.getDocData.contributorMemberID = "#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getvalue("mc_pageDefinition.orgID"))#">
			<cfloop query="local.actionReturnStruct.qryGetCategoryTrees">
				<cfset local.actionReturnStruct.getExtraDocData["s_#local.actionReturnStruct.qryGetCategoryTrees.categoryTreeID#ID"] = "">
			</cfloop>
		<cfelse>
			<cfset local.actionReturnStruct.view = "fileshare2/notfound">
		</cfif>
		<cfreturn local.actionReturnStruct>
	</cffunction>

	<!--- This function should be called through the remoteProxy application --->
	<cffunction name="insertDocuments" access="public" output="false" returntype="void" hint="Save form">
		<cfargument name="Event" type="any">

		<cfscript>
			var local 				= structNew();
			local.data	 			= "";

			local.objDocument = CreateObject("component","model.system.platform.document");
			local.rc = arguments.event.getCollection();
		</cfscript>
		<cfset local.actionReturnStruct.editURL = "/?#getBaseQueryString(false)#&fsAction=editDocument&lang=en">
		<cfset local.actionReturnStruct.notifyURL = "/?#getAppBaseLink(applicationInstanceID=variables.fileShareSettings.applicationInstanceID, siteID=variables.fileShareSettings.siteID)#&fsAction=editDocument&lang=en">


		<cfif arguments.event.getTrimValue('filename') NEQ "">
			<cfset local.actionReturnStruct.qryGetCategoryTrees = getCategoryTrees() />
			<cfscript>
				arguments.event.setValue('fsdocTitle', '');
				arguments.event.setValue('fsdocDesc', '');
				arguments.event.setValue('fsPublicationDate', '');
							
				// check to see if there is a new file to upload --------------------------------------------
				if(arguments.event.getTrimValue('filename') NEQ "" ){
					// if yes then set fileToUpload to the form variable newFile ------------------------------
					arguments.event.setValue('fileToUpload','filename');
					// pre set the fileUploaded variable to TRUE ----------------------------------------------
					local.fileUploaded = TRUE;
					// try to upload the file to the proper destination ---------------------------------------
					try {
						local.newFile = local.objDocument.uploadFile("form.filename");
						if (local.newFile.uploadComplete){
							local.objDocument.forceFileExtentionIfBlank(local.newFile);
							arguments.event.setValue('fsFileName',local.newFile.clientFile);
							arguments.event.setValue('fsfileExt',local.newFile.clientFileExt);
						}
						else{	local.fileUploaded = FALSE; }
					} 
					catch(any excpt) {
						// if if fails to upload the set the fileUploaded flag to FALSE -------------------------
						local.fileUploaded = FALSE;
					}
				}
				if( local.fileUploaded ){
					
					if (len(arguments.event.getTrimValue('fsPublicationDate',''))) {
						local.insertDocResults = local.objDocument.insertDocument(
							siteID=variables.fileShareSettings.siteID,
							resourceType='ApplicationCreatedDocument',
							parentSiteResourceID=variables.fileShareSettings.siteResourceID,
							sectionID=variables.fileShareSettings.rootSectionID,
							docTitle=arguments.event.getValue('fsFileName'),
							docDesc=arguments.event.getValue('fsdocDesc'),
							author=arguments.event.getValue('fsAuthor'),
							fileData=local.newFile,
							isActive=1,
							contributorMemberID=arguments.event.getValue('fsContributorID'),
							recordedByMemberID=arguments.event.getValue('memberID'),
							publicationDate=arguments.event.getValue('fsPublicationDate'));
							}					
					else {
						local.insertDocResults = local.objDocument.insertDocument(
							siteID=variables.fileShareSettings.siteID,
							resourceType='ApplicationCreatedDocument',
							parentSiteResourceID=variables.fileShareSettings.siteResourceID,
							sectionID=variables.fileShareSettings.rootSectionID,
							docTitle=arguments.event.getValue('fsFileName'),
							docDesc=arguments.event.getValue('fsdocDesc'),
							author=arguments.event.getValue('fsAuthor'),
							fileData=local.newFile,
							isActive=1,
							contributorMemberID=arguments.event.getValue('fsContributorID'),
							recordedByMemberID=arguments.event.getValue('memberID'));
					}
					
					arguments.event.setValue('fsDocumentID',local.insertDocResults.documentID);
					linkDocViewRightsToFileShare(local.insertDocResults.documentID);
				}	
				else{
					local.actionReturnStruct.displayMessage = local.newFile.ReasonText;
					local.actionReturnStruct.view = "fileshare2/fileEcho";
				}
			</cfscript>
			<cfset local.actionReturnStruct.getDocData = local.objDocument.getDocumentData(arguments.event.getValue('fsDocumentID'))>
			<cfscript>
				if (variables.fileShareSettings.notifyOnAdd eq 1)
				{
					local.emailBody = "#session.cfcUser.memberdata.firstname# #session.cfcUser.memberdata.lastname# (#session.cfcUser.memberdata.memberNumber#) has added a document #chr(13)#  #(application.objPlatform.isRequestSecure() ? 'https' : 'http')#://#application.objPlatform.getCurrentHostname()#/#local.actionReturnStruct.notifyURL#&fsDocumentID=#local.rc.fsDocumentID# #chr(13)##chr(13)# ";
					local.from = #session.cfcUser.memberdata.email#;
					if (len(local.from) eq 0) 
						local.from = '<EMAIL>';

					fssendemail(event=arguments.event, siteNotifyEmail=arguments.event.getValue('mc_siteInfo.defaultAdminEmails',''), 
						from=local.from, subject="#variables.fileShareSettings.applicationInstanceName# document added", 
						body=local.emailBody);
				}
			</cfscript>
						
			<!--- Update the categories for the documents--->
			<cfloop query="local.actionReturnStruct.qryGetCategoryTrees">
				<cfset local.categoryID = arguments.event.getValue('s_#local.actionReturnStruct.qryGetCategoryTrees.categoryTreeID#ID', '') />
				<cfif local.categoryID NEQ "">
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.saveFieldSets">
						INSERT INTO dbo.cms_categorySiteResources (CategoryID, siteResourceID)
						VALUES (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.categoryID#">, <cfqueryparam cfsqltype="cf_sql_integer" value="#local.actionReturnStruct.getDocData.siteResourceID#">)					
					</cfquery>
				</cfif>
			</cfloop>				
		</cfif>
	
	</cffunction>	

	<cffunction name="actionGetFoldersXML" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			
			local.actionReturnStruct = structNew();
			local.actionReturnStruct.view = "fileshare2/foldersXML";
			
			arguments.event.paramValue('lh',1);
			arguments.event.paramValue('posStart',0);
			arguments.event.paramValue('orderby',1);
			arguments.event.setValue('orderby',int(val(arguments.event.getValue('orderby'))));
			arguments.event.paramValue('count',50);
			arguments.event.setValue('count',int(val(arguments.event.getValue('count'))));
			arguments.event.paramValue('direct',"asc");
			if (not listFindnocase("asc,des",arguments.event.getValue('direct'))) arguments.event.setValue('direct','asc');
			
			local.sectionToShow = arguments.event.getValue('fsSectionID',variables.fileShareSettings.rootSectionID);
		</cfscript>

		<cfquery name="local.sectionStr" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int, @startSectionID int, @userCreatedSectionResourceTypeID int,
				@startSectionPath varchar(max), @startSectionPathExpanded varchar(max);
			SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#variables.fileShareSettings.siteid#">;
			SET @startSectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.sectionToShow#">;

			SELECT @startSectionPath=thePath, @startSectionPathExpanded=thePathExpanded
			FROM dbo.cms_pageSections
			WHERE siteID = @siteID
			AND sectionID = @startSectionID;

			SELECT ps.sectionID, ps.sectionName, ps.parentSectionID, ps.siteResourceID,
				thePath = replace(ps.thePath,@startSectionPath,'0001'), 
				thePathExpanded = case
					when ps.sectionID = @startSectionID then ''
					when len(@startSectionPathExpanded) > 0 then replace(ps.thePathExpanded, @startSectionPathExpanded + ' \ ','')
					else ps.thePathExpanded
				end
			FROM dbo.cache_cms_recursivePageSections AS rps
			INNER JOIN dbo.cms_pageSections AS ps ON ps.sectionID = rps.startSectionID
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = ps.siteResourceID
				AND sr.siteResourceStatusID = 1
				AND sr.siteID = @siteID
			WHERE rps.sectionID = @startSectionID
			AND rps.siteID = @siteID
			ORDER BY ps.thePath;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @groupPrintID INT, @siteID INT, @sectionID INT, @viewFunctionID INT;
			SET @viewFunctionID = 4;
			SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#variables.fileShareSettings.siteid#">;
			SET @sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.sectionToShow#">;
			SELECT @groupPrintID = groupPrintID FROM dbo.ams_members WHERE memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.orgID)#">;

			IF @groupPrintID IS NULL
				select @groupPrintID = o.publicGroupPrintID
				from dbo.organizations as o
				inner join dbo.sites as s on s.orgID = o.orgID 
					and s.siteID = @siteID;

			SELECT tmp.* FROM (
				SELECT 
					docs.documentId, docs.sectionID, docs.siteid, docs.siteResourceID, l.docTitle, l.docDesc, 
					docResource.resourceTypeID, v.filename, v.fileExt, v.publicationDate, v.dateCreated, 
					v.dateModified
				FROM cms_documents docs 	
					INNER JOIN cms_documentLanguages l on docs.documentID = l.documentID
					INNER JOIN cms_documentVersions v on l.documentLanguageID = v.documentLanguageID AND v.isActive = 1
					INNER JOIN cms_siteResources docResource on docs.siteResourceID = docResource.siteResourceID
						AND docs.siteID = @siteID
						AND docResource.isVisible = 1
					INNER JOIN dbo.cache_cms_recursivePageSections AS rps ON rps.startSectionID = docs.sectionID
						AND rps.siteID = @siteID
						AND rps.sectionID = @sectionID
					INNER JOIN cms_siteResourceTypes docResourceType on docResource.resourceTypeID = docResourceType.resourceTypeID
					INNER JOIN dbo.cms_siteResourceStatuses docResourceStatus on docResourceStatus.siteResourceStatusID = docResource.siteResourceStatusID
						AND docResourceStatus.siteResourceStatusDesc = 'Active'
					LEFT OUTER JOIN cms_siteResources AppResource
						INNER JOIN cms_siteResourceTypes AppResourceTypes on AppResourceTypes.resourceTypeID = AppResource.resourceTypeID
						INNER JOIN cms_siteResourceTypeClasses AppResourceTypeClasses on AppResourceTypeClasses.resourceTypeClassID = AppResourceTypes.resourceTypeClassID
							AND AppResourceTypeClasses.resourceTypeClassName = 'application'
						INNER JOIN cms_applicationInstances ai on ai.siteResourceID = AppResource.siteResourceID
							INNER JOIN cms_applicationTypes at on ai.applicationTypeID = at.applicationTypeID
					ON AppResource.parentSiteResourceID = docResource.siteResourceID	
			) tmp
			INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints AS srfrp ON srfrp.siteID = @siteID
				AND srfrp.siteresourceID = tmp.siteResourceID
				AND srfrp.functionID = @viewFunctionID
			INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp ON gprp.siteID = @siteID
				AND gprp.rightPrintID = srfrp.rightPrintID
				AND gprp.groupPrintID = @groupPrintID
			WHERE tmp.siteID = @siteID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.sectionTree = structNew()>
		<cfloop query="local.sectionStr">
			<cfset local.currentBranch = local.sectionTree>
			<cfset local.sectionDepth = listlen(local.sectionStr.thePath,".")>
			<cfset local.currentDepth = 0>
			<cfset local.pathSoFar = "">
			<cfset local.searchPath = arrayNew(1)>			
			<cfloop list="#local.sectionStr.thePath#" delimiters="." index="local.currentSection">
				<cfset local.currentDepth = local.currentDepth + 1>
				<cfif local.currentDepth is not 1>
					<cfif not structKeyExists(local.currentBranch,local.currentSection)>
						<cfset local.currentBranch[local.currentSection] = StructNew()>
					</cfif>
					<cfset local.currentBranch = local.currentBranch[local.currentSection]>
				</cfif> 				
 				<cfif local.currentDepth is local.sectionDepth>					
					<cfquery name="local.sectionData" dbtype="query">
						SELECT * 
						FROM [local].data
						WHERE sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.sectionStr.sectionID#">
					</cfquery>
					<cfset local.currentBranch.childCount 			= local.sectionData.recordCount />
					<cfset local.currentBranch.parentSectionID 	= local.sectionStr.parentSectionID />
					<cfset local.currentBranch.sectionID 				= local.sectionStr.sectionID />
					<cfif local.sectionStr.thePath EQ '0001'>
						<cfset local.currentBranch.sectionName 			= 'Fileshare Home' />
					<cfelse>
						<cfset local.currentBranch.sectionName 			= local.sectionStr.sectionName />
					</cfif>
					<cfset local.currentBranch.siteResourceID 	= local.sectionStr.siteResourceID />
					<cfset local.currentBranch.thePath 					= local.sectionStr.thePath />
					<cfset local.currentBranch.thePathExpanded 	= local.sectionStr.thePathExpanded />
				</cfif>
			</cfloop>
		</cfloop>
		
		

		<cfsavecontent variable="local.actionReturnStruct.returnXML">
			<cfoutput>
				<rows parent="0">
	 				<head>
						<column align="left" sort="str" type="tree" width="*">Sections</column>
						<beforeInit>
							<call command="attachEvent"><param>onXLE</param><param>doLoadPages</param></call>
							<call command="attachEvent"><param>onRowSelect</param><param>doOnRowSelect</param></call>
						</beforeInit>						
						<settings>
							<colwidth>px</colwidth>
						</settings>
					</head>
					#createTreeXML(local.sectionTree)#
				</rows>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn local.actionReturnStruct />	
	</cffunction>
	
	<cffunction name="createTreeXML" access="private" returntype="string">
		<cfargument name="sectionStruct" type="struct" required="true">
		<cfset var local = structNew()>
 		<cfsavecontent variable="local.resultXML">
			<cfoutput>
				<row id="#arguments.sectionStruct.sectionID#" open="1">
					<cell image="folder.png"> #xmlFormat(arguments.sectionStruct.sectionName)# (#arguments.sectionStruct.childCount#)</cell>
					<cfset local.keyList = listsort(StructKeyList(arguments.sectionStruct),"textnocase")>
					<cfloop index="local.currentKey" list="#local.keyList#">
						<cfif isStruct(arguments.sectionStruct[local.currentKey])>
							#createTreeXML(arguments.sectionStruct[local.currentKey])# 
						</cfif>
					</cfloop>					
				</row>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.resultXML>
 	</cffunction>

	<cffunction name="actionGetFilesXML" access="private" returntype="struct" output="false">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.actionReturnStruct = structNew();
			local.actionReturnStruct.view = "fileshare2/foldersXML";
			
			// GRID SETTINGS ---------------------------------------------------------------------------- ::
			arguments.event.setValue('direct',arguments.event.getValue('direct2','asc'));
			arguments.event.setValue('lh',int(val(arguments.event.getValue('lh2',0))));
			arguments.event.setValue('orderby',int(val(arguments.event.getValue('orderby2',1))));
			
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('posStart',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('count',50))));
			// ------------------------------------------------------------------------------------------ ::
			
			local.sectionToShow = arguments.event.getValue('fsSectionID',0);
			
			arguments.event.paramValue('srID',0);
			arguments.event.paramValue('ssID',0);
			
		</cfscript>
		
		<cfif arguments.event.getValue('posStart') IS 0>
			<cfquery name="local.qryDocCount" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @groupPrintID INT, @siteID INT, @sectionID INT, @viewFunctionID int;
				SET @viewFunctionID = 4;
				SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#variables.fileShareSettings.siteid#">;
				SET @sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.sectionToShow#">;
				SELECT @groupPrintID = groupPrintID FROM dbo.ams_members WHERE memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.orgID)#">;

				IF @groupPrintID IS NULL
					select @groupPrintID = o.publicGroupPrintID
					from dbo.organizations as o
					inner join dbo.sites as s on s.orgID = o.orgID 
						and s.siteID = @siteID;

				SELECT COUNT(*) as theCount
				FROM (
					SELECT tmp2.*
					FROM (
						SELECT 
							docs.documentId, docs.siteResourceID,docs.siteID,
							docResource.resourceTypeID
						FROM dbo.cms_documents docs
							inner join cms_siteResources docResource on docs.siteResourceID = docResource.siteResourceID
								and docs.sectionID = @sectionID
								and docs.siteID = @siteID
								and docResource.isVisible=1
							inner join cms_siteResourceTypes docResourceType on docResource.resourceTypeID = docResourceType.resourceTypeID
							inner join dbo.cms_siteResourceStatuses docResourceStatus on docResourceStatus.siteResourceStatusID = docResource.siteResourceStatusID
								and docResourceStatus.siteResourceStatusDesc = 'Active'
							left outer join cms_siteResources AppResource
								inner join cms_siteResourceTypes AppResourceTypes on AppResourceTypes.resourceTypeID = AppResource.resourceTypeID
								inner join cms_siteResourceTypeClasses AppResourceTypeClasses on AppResourceTypeClasses.resourceTypeClassID = AppResourceTypes.resourceTypeClassID
									and AppResourceTypeClasses.resourceTypeClassName = 'application'
								inner join cms_applicationInstances ai on ai.siteResourceID = AppResource.siteResourceID
								inner join cms_applicationTypes at on ai.applicationTypeID = at.applicationTypeID
							on AppResource.parentSiteResourceID = docResource.siteResourceID
					) tmp2
					INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints AS srfrp ON srfrp.siteID = @siteID
						AND srfrp.siteresourceID = tmp2.siteResourceID
						AND srfrp.functionID = @viewFunctionID
					INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp ON gprp.siteID = @siteID
						AND gprp.rightPrintID = srfrp.rightPrintID
						AND gprp.groupPrintID = @groupPrintID
					WHERE tmp2.siteID = @siteID 
				) AS tmp;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfset local.totalCount = local.qryDocCount.theCount>
		<cfelse>
			<cfset local.totalCount = "">
		</cfif>

		<!--- handle ordering --->
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"documentID")>
		
		<cfquery name="local.qryData" datasource="#application.dsn.memberCentral.dsn#" result="local.qryStat">			
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @groupPrintID INT, @siteID INT, @sectionID INT, @viewFunctionID int;
			SET @viewFunctionID = 4;
			SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#variables.fileShareSettings.siteid#">;
			SET @sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.sectionToShow#">;
			SELECT @groupPrintID = groupPrintID FROM dbo.ams_members WHERE memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.orgID)#">;

			IF @groupPrintID IS NULL
				select @groupPrintID = o.publicGroupPrintID
				from dbo.organizations as o
				inner join dbo.sites as s on s.orgID = o.orgID 
					and s.siteID = @siteID;

			SELECT tmp.*
			FROM (
				SELECT tmp2.*, ROW_NUMBER() OVER (ORDER BY docTitle asc) as row  FROM (
					SELECT 
						docResourceStatus.siteResourceStatusDesc as status,
						docResourceStatus.siteResourceStatusID	as statusID,
						docResource.resourceTypeID,
						dl.documentID, docs.siteID, docs.siteResourceID, dl.docTitle, dl.docDesc, dl.languageID, l.languageCode,
						v.filename, v.fileExt, v.publicationDate, v.dateCreated, v.dateModified,
						m3.firstName, m3.lastName
					FROM dbo.cms_documents docs
						INNER JOIN cms_documentLanguages dl on docs.documentID = dl.documentID
						INNER JOIN cms_documentVersions v on dl.documentLanguageID = v.documentLanguageID AND v.isActive = 1
						INNER JOIN cms_languages l on dl.languageID = l.languageID
						inner join cms_siteResources docResource on docs.siteResourceID = docResource.siteResourceID
							and docs.sectionID = @sectionID
							and docs.siteID = @siteID
							and docResource.isVisible=1
						inner join cms_siteResourceTypes docResourceType on docResource.resourceTypeID = docResourceType.resourceTypeID
						inner join dbo.cms_siteResourceStatuses docResourceStatus on docResourceStatus.siteResourceStatusID = docResource.siteResourceStatusID
							and docResourceStatus.siteResourceStatusDesc = 'Active'
						left outer join cms_siteResources AppResource
							inner join cms_siteResourceTypes AppResourceTypes on AppResourceTypes.resourceTypeID = AppResource.resourceTypeID
							inner join cms_siteResourceTypeClasses AppResourceTypeClasses on AppResourceTypeClasses.resourceTypeClassID = AppResourceTypes.resourceTypeClassID
								and AppResourceTypeClasses.resourceTypeClassName = 'application'
							inner join cms_applicationInstances ai on ai.siteResourceID = AppResource.siteResourceID
							inner join cms_applicationTypes at on ai.applicationTypeID = at.applicationTypeID
						on AppResource.parentSiteResourceID = docResource.siteResourceID
						INNER JOIN dbo.ams_members contributors ON contributors.memberID = v.contributorMemberID
						INNER JOIN dbo.ams_members m3 on contributors.activeMemberid = m3.memberID
				) tmp2
				INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints AS srfrp ON srfrp.siteID = @siteID
					AND srfrp.siteresourceID = tmp2.siteResourceID
					AND srfrp.functionID = @viewFunctionID
				INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp ON gprp.siteID = @siteID
					AND gprp.rightPrintID = srfrp.rightPrintID
					AND gprp.groupPrintID = @groupPrintID
				WHERE tmp2.siteID = @siteID 
			) AS tmp 
			WHERE row > #arguments.event.getValue('posStart')# AND row <= #arguments.event.getValue('posStart') + arguments.event.getValue('count')#
			order by row;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfsavecontent variable="local.actionReturnStruct.returnXML">
			<cfoutput>
			<rows total_count="#local.totalCount#" pos="#arguments.event.getValue('posStart')#">
			<cfif arguments.event.getValue('lh') is 1 and arguments.event.getValue('posStart') is 0>
				<head>
					<column align="center" sort="na" type="img" width="22">Title</column>
					<column align="left" sort="na" type="link" width="*">##cspan</column>
					<column align="left" sort="na" type="ro" width="125">Contributor</column>
					<column align="center" sort="na" type="img" width="75">Modified</column>
					<column align="center" sort="na" type="img" width="75">Issue</column>
					<afterInit>
						<call command="enableSmartRendering"><param>true</param></call>
					</afterInit>
					<settings>
						<colwidth>px</colwidth>
					</settings>
				</head>
			</cfif>
			<cfloop query="local.qryData">
				<cfswitch expression="#local.qryData.fileExt#">
					<cfcase value="doc,docx"><cfset local.fileImg = "/assets/common/images/fileExts/doc.png"></cfcase>
					<cfcase value="html"><cfset local.fileImg = "/assets/common/images/fileExts/html.png"></cfcase>
					<cfcase value="pdf"><cfset local.fileImg = "/assets/common/images/fileExts/pdf.png"></cfcase>
					<cfcase value="ppt,pptx"><cfset local.fileImg = "/assets/common/images/fileExts/ppt.png"></cfcase>
					<cfcase value="txt"><cfset local.fileImg = "/assets/common/images/fileExts/txt.png"></cfcase>
					<cfcase value="xls,xlsx"><cfset local.fileImg = "/assets/common/images/fileExts/xls.png"></cfcase>
					<cfdefaultcase><cfset local.fileImg = "/assets/common/images/fileExts/file.png"></cfdefaultcase>
				</cfswitch>
				<row id='#local.qryData.documentID#'>
					<!--- icon --->
					<cell>#local.fileImg#^^</cell>
					<!--- filename --->
					<cell title="#xmlformat(local.qryData.FileName)#">#xmlformat(local.qryData.docTitle)#^javascript:mcg_viewDocument(#local.qryData.documentID#,"#local.qryData.languageCode#")^_self</cell>
					<!--- Contributor --->
					<cell>#local.qryData.firstName# #local.qryData.lastName#</cell>
					<!--- dateModified --->
					<cell type="ro">#dateFormat(local.qryData.dateModified,"m/d/yyyy")#</cell>					
					<cell type="ro">Issue</cell>					
				</row>
			</cfloop>
			</rows>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.actionReturnStruct />	
	</cffunction>

	<cffunction name="actionDeleteDocument" access="private" returntype="void" output="false">
		<cfargument name="Event" type="any" required="yes"/>

		<cfset var local = structNew()>
		<cfset local.actionReturnStruct = structNew()>
		<cfset local.catId = arguments.event.getValue('catID', '') />
		<cfset local.actionReturnStruct.bucketID = variables.fileShareSettings.BucketID>
		<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
		<cfset local.getDocData = local.objDocument.getDocumentData(arguments.event.getValue('fsDocumentID'))>
		<cfif variables.appRightsStruct.fsDeleteAny or (variables.appRightsStruct.fsDeleteOwn and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (local.getDocData.memberid eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.orgID)))>
			<cfset local.objDocument.deleteDocument(siteID=arguments.event.getValue('mc_siteInfo.siteID'), documentID=arguments.event.getValue('fsDocumentID'))>
			<cfstoredproc datasource="#application.dsn.platformstatsMC.dsn#" procedure="act_recordLog">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.masterOrgID)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="remove">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#variables.fileShareSettings.applicationTypeID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#variables.fileShareSettings.applicationInstanceID#">
			</cfstoredproc>		
		</cfif>
		
		<cfset local.actionReturnStruct.showSectionURL = "/?#getBaseQueryString(false)#">

		<cflocation addtoken="false" url="#local.actionReturnStruct.showSectionURL#">
	</cffunction>
	
	<cffunction name="actionHideDocument" access="private" returntype="void" output="false">
		<cfargument name="Event" type="any" required="yes"/>

		<cfset var local = structNew()>
		<cfset local.actionReturnStruct = structNew()>
		<cfset local.catId = arguments.event.getValue('catID', '') />
		<cfset local.actionReturnStruct.bucketID = variables.fileShareSettings.BucketID>
		<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
		<cfset local.getDocData = local.objDocument.getDocumentData(arguments.event.getValue('fsDocumentID'))>
		<cfif variables.appRightsStruct.fsDeleteAny or (variables.appRightsStruct.fsDeleteOwn and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (local.getDocData.memberid eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.orgID)))>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.siteResourceID">
				SELECT d.siteResourceID, srs.siteResourceStatusDesc
				FROM dbo.cms_documents d
				INNER JOIN dbo.cms_siteResources as docSR ON d.siteResourceID = docSR.siteResourceID
				INNER JOIN dbo.cms_siteResourceStatuses as srs on docSR.siteResourceStatusID = srs.siteResourceStatusID 			
				WHERE d.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#variables.fileShareSettings.siteID#">
				AND d.documentID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('fsDocumentID')#">
			</cfquery>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#"  procedure="cms_updateSiteResourceStatus">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="Inactive">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.siteResourceID.siteResourceID#">
			</cfstoredproc>
		</cfif>

		<cfset local.actionReturnStruct.showSectionURL = "/?#getBaseQueryString(false)#">

		<cflocation addtoken="false" url="#local.actionReturnStruct.showSectionURL#">
	</cffunction>
	
	<cffunction name="actionUnhideDocument" access="private" returntype="void" output="false">
		<cfargument name="Event" type="any" required="yes"/>

		<cfset var local = structNew()>
		<cfset local.actionReturnStruct = structNew()>
		<cfset local.catId = arguments.event.getValue('catID', '') />
		<cfset local.actionReturnStruct.bucketID = variables.fileShareSettings.BucketID>
		<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
		<cfset local.getDocData = local.objDocument.getDocumentData(arguments.event.getValue('fsDocumentID'))>

		<cfif variables.appRightsStruct.fsDeleteAny or (variables.appRightsStruct.fsDeleteOwn and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (local.getDocData.memberid eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.orgID)))>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.siteResourceID">
				SELECT d.siteResourceID, srs.siteResourceStatusDesc
				FROM dbo.cms_documents d
				INNER JOIN dbo.cms_siteResources as docSR ON d.siteResourceID = docSR.siteResourceID
				INNER JOIN dbo.cms_siteResourceStatuses as srs on docSR.siteResourceStatusID = srs.siteResourceStatusID 			
				WHERE d.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#variables.fileShareSettings.siteID#">
				AND d.documentID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('fsDocumentID')#">
			</cfquery>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="cms_updateSiteResourceStatus">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="Active">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.siteResourceID.siteResourceID#">
			</cfstoredproc>
		</cfif>
		
		<cfif local.catID EQ "">
			<cfset local.actionReturnStruct.showSectionURL = "/?#getBaseQueryString(false)#&fsAction=showSection">
		<cfelse>
			<cfset local.actionReturnStruct.showSectionURL = "/?#getBaseQueryString(false)#">
		</cfif>

		<cflocation addtoken="false" url="#local.actionReturnStruct.showSectionURL#">
	</cffunction>
	
	<cffunction name="linkDocViewRightsToFileShare" access="private" returntype="void" output="no">
		<cfargument name="documentID" type="numeric" required="true">

		<cfset var local = structNew()>
		
		<cfquery name="local.qryGetInfo" datasource="#application.dsn.membercentral.dsn#">
			select siteid, siteResourceID
			from dbo.cms_documents
			where documentid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.documentID#">
		</cfquery>
		<cfstoredproc procedure="cms_createSiteResourceRight" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam cfsqltype="cf_sql_integer" value="#local.qryGetInfo.siteid#">
			<cfprocparam cfsqltype="cf_sql_integer" value="#local.qryGetInfo.siteResourceID#">
			<cfprocparam cfsqltype="cf_sql_bit" value="1">
			<cfprocparam cfsqltype="cf_sql_varchar" value="4">
			<cfprocparam cfsqltype="cf_sql_integer" null="yes">
			<cfprocparam cfsqltype="cf_sql_integer" null="yes">
			<cfprocparam cfsqltype="cf_sql_integer" value="#variables.fileShareSettings.siteresourceID#">
			<cfprocparam cfsqltype="cf_sql_integer" value="4">
		</cfstoredproc>
	</cffunction>

	<cffunction name="actionMoveDocument" access="private" returntype="struct">
		<cfargument name="Event" type="any" required="yes"/>

		<cfset var local = structNew()>
		<cfset local.rc = arguments.event.getCollection()>
		<cfset local.sectionToShow = arguments.event.getValue('fsSectionID',variables.fileShareSettings.rootSectionID)>
		<cfset local.actionReturnStruct = structNew()>
							
		<cfif listFind(valuelist(variables.fileShareSettings.qrySections.sectionID),local.sectionToShow)>
			<cfset local.actionReturnStruct.qryGetCategoryTrees = getCategoryTrees() />
			<cfset local.actionReturnStruct.sectionToShow = local.sectionToShow>
			<cfset local.actionReturnStruct.view = "fileshare2/moveDocument">
			<cfset local.actionReturnStruct.bucketID = variables.fileShareSettings.BucketID>
			<cfset local.actionReturnStruct.PG = "#arguments.event.getValue('PG')#">
			<cfif arguments.event.getValue('COMMPG', '') NEQ ''>
				<cfset local.actionReturnStruct.PG = "#arguments.event.getValue('PG')#&commpg=#arguments.event.getValue('COMMPG')#">
			</cfif>			
			<cfset local.actionReturnStruct.baseURL = "/?pg=#local.actionReturnStruct.PG#&panel=#arguments.event.getValue('panel','browse')#&byt=#arguments.event.getValue('byT',0)#&catid=#arguments.event.getValue('catID',0)#">
			<cfset local.actionReturnStruct.formPostURL = "/?#getBaseQueryString(false)#&fsAction=moveDocument&fsDocumentID=#arguments.event.getValue('fsDocumentID',0)#">
			<cfset local.actionReturnStruct.showSectionURL = "/?#getBaseQueryString(false)#&fsAction=showSection&fsSectionID=#local.sectionToShow#">
			<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
			<cfset local.actionReturnStruct.getDocData = local.objDocument.getDocumentData(documentID=arguments.event.getValue('fsDocumentID'))>
			<cfset local.actionReturnStruct.getExtraDocData = getExtraDocData(local.actionReturnStruct.getDocData.siteResourceID) />
			<cfset local.actionReturnStruct.qryFSColumns = getExtraFSColumns(variables.fileShareSettings.siteResourceID)>
			<cfloop query="local.actionReturnStruct.qryFSColumns">
				<cfset local.actionReturnStruct.getExtraDocData["fs_#local.actionReturnStruct.qryFSColumns.columnID#"] = getExtraFSColumnData(local.actionReturnStruct.getDocData.siteResourceID, local.actionReturnStruct.qryFSColumns.columnID)>
			</cfloop>
			<cfset local.actionReturnStruct.qryGetOtherFileShares = getOtherFileShares(local.actionReturnStruct.getDocData.sectionID) />

			<cfif variables.appRightsStruct.fsDeleteAny or (variables.appRightsStruct.fsDeleteOwn and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (local.actionReturnStruct.getDocData.memberid eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.orgID)))>
				<cfset local.canDelete = true>
			<cfelse>
				<cfset local.canDelete = false>
			</cfif>
			
			<cfif variables.appRightsStruct.fsReuploadAny or (variables.appRightsStruct.fsReuploadOwn and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (local.actionReturnStruct.getDocData.memberid eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.orgID)))>
				<cfset local.canReupload = true>
			<cfelse>
				<cfset local.canReupload = false>
			</cfif>
			
			<cfif variables.appRightsStruct.fsEditAnyMetadata or (variables.appRightsStruct.fsEditOwnMetadata and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (local.actionReturnStruct.getDocData.memberid eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.orgID)))>
				<cfset local.canEditMetaData = true>
			<cfelse>
				<cfset local.canEditMetaData = false>
			</cfif>

			<cfif isdefined("local.rc.fsDocumentSaved") and local.rc.fsDocumentID neq 0>
	
			
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.status">
					UPDATE	dbo.cms_documents set 
						sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.rc.newrootsectionid#">
					WHERE		sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.rc.fssectionid#">
						AND 	documentID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.rc.fsDocumentID#">
				</cfquery>				
					
				<!--- remove all the categories for the documents--->
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.deleteCategoryForSRID">
					delete from dbo.cms_categorySiteResources
					WHERE siteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.actionReturnStruct.getDocData.siteResourceID#">
				</cfquery>

				<!--- copy custom data from file share data from --->
				<cfset copyExtraFSColumns(local.actionReturnStruct.getDocData.siteResourceID, local.rc.newrootsectionid, local.rc.fssectionid) />
				
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.deleteCategoryForSRID">
					delete from dbo.cms_categorySiteResources
					WHERE siteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.actionReturnStruct.getDocData.siteResourceID#">
				</cfquery>
				
				<!--- record in activity log --->
				<cfstoredproc datasource="#application.dsn.platformstatsMC.dsn#" procedure="act_recordLog">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.masterOrgID)#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="moved to different fileshare">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#variables.fileShareSettings.applicationTypeID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#variables.fileShareSettings.applicationInstanceID#">
				</cfstoredproc>		
					
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryPage">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @siteID int = <cfqueryparam value="#variables.fileShareSettings.siteid#" cfsqltype="CF_SQL_INTEGER">;

					select p.pagename, fs.applicationInstanceID
					from dbo.fs_fileShare fs
					inner join dbo.cms_applicationInstances as ai on ai.siteID = @siteID
						and ai.applicationInstanceID = fs.applicationInstanceID
					inner join dbo.cms_siteResources as sr on sr.siteID = @siteID
						and ai.siteResourceID = sr.siteResourceID
						and sr.siteResourceStatusID = 1
					inner join dbo.cms_pages as p on p.siteID = @siteID 
						and p.siteresourceid = sr.parentsiteresourceid
					where fs.rootSectionID =  <cfqueryparam cfsqltype="cf_sql_integer" value="#local.rc.newrootsectionid#">;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfset local.appBaseLink = getAppBaseLink(local.qryPage.applicationInstanceID,variables.fileShareSettings.siteid)>			
				
				<cfset local.redirectURL = "/?#local.appBaseLink#&panel=#arguments.event.getValue('panel','browse')#&fsAction=editDocument&lang=en&fsDocumentID=#local.rc.fsDocumentID#">
			</cfif>
			
		<cfelse>
			<cfset local.actionReturnStruct.view = "fileshare2/notfound">
		</cfif>
		<cfreturn local.actionReturnStruct>
	</cffunction>

	<cffunction name="moveDocument" access="public" output="false" returntype="struct" hint="Marks file in database as Inactive">
		<cfargument name="newRootSectionID" type="numeric" required="true">
		<cfargument name="fsSectionID" type="numeric" required="true">
		<cfargument name="fsDocumentID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()/>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDocument">
			SELECT siteResourceID 
			FROM dbo.cms_documents
			WHERE sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fsSectionID#">
			AND documentID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fsDocumentID#">
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.status">
			UPDATE dbo.cms_documents
			set sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.newRootSectionID#">
			WHERE sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fsSectionID#">
			AND documentID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fsDocumentID#">
		</cfquery>

		<!--- Update the categories for the documents--->
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.deleteCategoryForSRID">
			delete from dbo.cms_categorySiteResources
			WHERE siteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryDocument.siteResourceID#">
		</cfquery>

		<!--- copy custom data from file share data from --->
		<cfset copyExtraFSColumns(local.qryDocument.siteResourceID, arguments.newRootSectionID, arguments.fsSectionID) />

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryPage">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">;

			select p.pagename, fs.applicationInstanceID
			from dbo.fs_fileShare fs
			inner join dbo.cms_applicationInstances as ai on ai.siteID = @siteID
				and ai.applicationInstanceID = fs.applicationInstanceID
			inner join dbo.cms_siteResources as sr on sr.siteID = @siteID
				and ai.siteResourceID = sr.siteResourceID
				and sr.siteResourceStatusID = 1
			inner join dbo.cms_pages as p on p.siteID = @siteID 
				and p.siteresourceid = sr.parentsiteresourceid
			where fs.rootSectionID =  <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.newRootSectionID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.redirectURL = "">

		<cfset local.appBaseLink = getAppBaseLink(local.qryPage.applicationInstanceID,arguments.siteID)>

		<cfset local.html = "/?#local.appBaseLink#&panel=browse&fsAction=editDocument&lang=en&fsDocumentID=#arguments.fsDocumentID#">

		<cfset local.strResult = StructNew()>
		<cfset local.strResult.success = true>
		<cfset local.strResult.documentID = arguments.fsDocumentID>
		<cfset local.strResult.st = local.html>

		<cfreturn local.strResult>
	</cffunction>

	<cffunction name="actionCopyDocument" access="private" returntype="struct">
		<cfargument name="Event" type="any" required="yes"/>

		<cfset var local = structNew()>
		<cfset local.rc = arguments.event.getCollection()>
		<cfset local.sectionToShow = arguments.event.getValue('fsSectionID',variables.fileShareSettings.rootSectionID)>
		<cfset local.actionReturnStruct = structNew()>
							
		<cfif listFind(valuelist(variables.fileShareSettings.qrySections.sectionID),local.sectionToShow)>
			<cfset local.actionReturnStruct.qryGetCategoryTrees = getCategoryTrees() />
			<cfset local.actionReturnStruct.sectionToShow = local.sectionToShow>
			<cfset local.actionReturnStruct.view = "fileshare2/copyDocument">
			<cfset local.actionReturnStruct.bucketID = variables.fileShareSettings.BucketID>
			<cfset local.actionReturnStruct.PG = "#arguments.event.getValue('PG')#">
			<cfif arguments.event.getValue('COMMPG', '') NEQ ''>
				<cfset local.actionReturnStruct.PG = "#arguments.event.getValue('PG')#&commpg=#arguments.event.getValue('COMMPG')#">
			</cfif>			
			<cfset local.actionReturnStruct.baseURL = "/?pg=#local.actionReturnStruct.PG#&panel=#arguments.event.getValue('panel','browse')#&byt=#arguments.event.getValue('byT',0)#&catid=#arguments.event.getValue('catID',0)#">
			<cfset local.actionReturnStruct.formPostURL = "/?#getBaseQueryString(false)#&fsAction=copyDocument&fsDocumentID=#arguments.event.getValue('fsDocumentID',0)#">
			<cfset local.actionReturnStruct.showSectionURL = "/?#getBaseQueryString(false)#&fsAction=showSection&fsSectionID=#local.sectionToShow#">
			<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
			<cfset local.actionReturnStruct.getDocData = local.objDocument.getDocumentData(documentID=arguments.event.getValue('fsDocumentID'))>
			<cfset local.actionReturnStruct.getExtraDocData = getExtraDocData(local.actionReturnStruct.getDocData.siteResourceID) />
			<cfset local.actionReturnStruct.qryFSColumns = getExtraFSColumns(variables.fileShareSettings.siteResourceID)>
			<cfloop query="local.actionReturnStruct.qryFSColumns">
				<cfset local.actionReturnStruct.getExtraDocData["fs_#local.actionReturnStruct.qryFSColumns.columnID#"] = getExtraFSColumnData(local.actionReturnStruct.getDocData.siteResourceID, local.actionReturnStruct.qryFSColumns.columnID)>
			</cfloop>
			<cfset local.actionReturnStruct.qryGetOtherFileShares = getOtherFileShares(local.actionReturnStruct.getDocData.sectionID) />

			<cfif variables.appRightsStruct.fsDeleteAny or (variables.appRightsStruct.fsDeleteOwn and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (local.actionReturnStruct.getDocData.memberid eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.orgID)))>
				<cfset local.canDelete = true>
			<cfelse>
				<cfset local.canDelete = false>
			</cfif>
			
			<cfif variables.appRightsStruct.fsReuploadAny or (variables.appRightsStruct.fsReuploadOwn and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (local.actionReturnStruct.getDocData.memberid eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.orgID)))>
				<cfset local.canReupload = true>
			<cfelse>
				<cfset local.canReupload = false>
			</cfif>
			
			<cfif variables.appRightsStruct.fsEditAnyMetadata or (variables.appRightsStruct.fsEditOwnMetadata and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (local.actionReturnStruct.getDocData.memberid eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.orgID)))>
				<cfset local.canEditMetaData = true>
			<cfelse>
				<cfset local.canEditMetaData = false>
			</cfif>
			
		<cfelse>
			<cfset local.actionReturnStruct.view = "fileshare2/notfound">
		</cfif>
		<cfreturn local.actionReturnStruct>
	</cffunction>

	<cffunction name="copyDocument" access="public" output="false" returntype="struct" hint="Copies document to new fileshare instance">
		<cfargument name="newRootSectionID" type="numeric" required="true">
		<cfargument name="fsSectionID" type="numeric" required="true">
		<cfargument name="fsDocumentID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		
		<cfset var local = structNew()/>
		<cfset local.objDocument = CreateObject("component","model.system.platform.document")>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDocument">
			SELECT siteResourceID
			FROM dbo.cms_documents
			WHERE sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fsSectionID#">
			AND documentID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fsDocumentID#">
		</cfquery>

		<cfset local.originalDocument = local.objDocument.getDocumentData(arguments.fsDocumentID)>

		<cfset local.copyDocResults = local.objDocument.copyDocument(
			sourceDocumentID=arguments.fsDocumentID,
			destinationSiteID=local.originalDocument.siteID,
			destinationSectionID=arguments.newRootSectionID,
			contributorMemberID=local.originalDocument.ContributorMemberID,
			recordedByMemberID=application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=local.originalDocument.orgID)
		)>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryFS">
			SELECT ai.siteResourceID
			FROM dbo.fs_fileshare fs
			inner join dbo.cms_applicationInstances ai on ai.applicationInstanceID = fs.applicationInstanceID
			WHERE rootSectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.newRootSectionID#">
		</cfquery>

		<cfif val(local.qryFS.siteResourceID)>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.siteResourceID">
				UPDATE dbo.cms_siteResources
				SET	parentsiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryFS.siteResourceID#">
				WHERE siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.copyDocResults.documentSiteResourceID#">
			</cfquery>
		</cfif>

		<cfquery name="local.qryGetInfo" datasource="#application.dsn.membercentral.dsn#">
			select siteID, siteResourceID
			from dbo.cms_documents
			where documentid = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.copyDocResults.documentID#">
		</cfquery>

		<cfstoredproc procedure="cms_createSiteResourceRight" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam cfsqltype="cf_sql_integer" value="#local.qryGetInfo.siteid#">
			<cfprocparam cfsqltype="cf_sql_integer" value="#local.qryGetInfo.siteResourceID#">
			<cfprocparam cfsqltype="cf_sql_bit" value="1">
			<cfprocparam cfsqltype="cf_sql_varchar" value="4">
			<cfprocparam cfsqltype="cf_sql_integer" null="yes">
			<cfprocparam cfsqltype="cf_sql_integer" null="yes">
			<cfprocparam cfsqltype="cf_sql_integer" value="#local.qryFS.siteResourceID#">
			<cfprocparam cfsqltype="cf_sql_integer" value="4">
		</cfstoredproc>

		<!--- copy custom data from file share data from --->
		<cfset copyExtraFSColumns(local.qryDocument.siteResourceID, arguments.newRootSectionID, arguments.fsSectionID, local.copyDocResults.documentSiteResourceID) />

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryPage">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">;

			select p.pagename, fs.applicationInstanceID
			from dbo.fs_fileShare fs
			inner join dbo.cms_applicationInstances as ai on ai.siteID = @siteID
				and ai.applicationInstanceID = fs.applicationInstanceID
			inner join dbo.cms_siteResources as sr on sr.siteID = @siteID
				and ai.siteResourceID = sr.siteResourceID
				and sr.siteResourceStatusID = 1
			inner join dbo.cms_pages as p on p.siteID = @siteID 
				and p.siteresourceid = sr.parentsiteresourceid
			where fs.rootSectionID =  <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.newRootSectionID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.appBaseLink = getAppBaseLink(local.qryPage.applicationInstanceID,arguments.siteID)>

		<cfset local.strResult = StructNew()>
		<cfset local.strResult.success = true>
		<cfset local.strResult.documentID = local.copyDocResults.documentID>
		<cfset local.strResult.st = "/?#local.appBaseLink#&panel=browse&fsAction=editDocument&lang=en&fsDocumentID=#local.copyDocResults.documentID#">

		<cfreturn local.strResult>
	</cffunction>

	<cffunction name="actionEditDocument" access="private" returntype="struct" output="false">
		<cfargument name="Event" type="any" required="yes"/>

		<cfset var local = structNew()>
		<cfset local.rc = arguments.event.getCollection()>
		<cfset local.sectionToShow = arguments.event.getValue('fsSectionID',variables.fileShareSettings.rootSectionID)>
		<cfset local.actionReturnStruct = structNew()>
		<cfset local.actionReturnStruct.tabValue = 'UD'>
							
		<cfif listFind(valuelist(variables.fileShareSettings.qrySections.sectionID),local.sectionToShow)>
			<cfset local.actionReturnStruct.qryGetFileShareShowSettings = getFileShareShowSettings(variables.fileShareSettings.fileshareID)>			
			<cfset local.actionReturnStruct.qryGetCategoryTrees = getCategoryTrees() />
			<cfset local.actionReturnStruct.sectionToShow = local.sectionToShow>
			
			<cfset local.actionReturnStruct.view = "fileshare2/editDocument">
			<cfset local.actionReturnStruct.bucketID = variables.fileShareSettings.BucketID>
			<cfset local.actionReturnStruct.PG = "#arguments.event.getValue('PG')#">
			<cfif arguments.event.getValue('COMMPG', '') NEQ ''>
				<cfset local.actionReturnStruct.PG = "#arguments.event.getValue('PG')#&commpg=#arguments.event.getValue('COMMPG')#">
			</cfif>			
			<cfset local.actionReturnStruct.baseURL = "/?pg=#local.actionReturnStruct.PG#&panel=#arguments.event.getValue('panel','browse')#&byt=#arguments.event.getValue('byT',0)#&catid=#arguments.event.getValue('catID',0)#">
			<cfset local.actionReturnStruct.formPostURL = "/?#getBaseQueryString(false)#&fsAction=editDocument&fsDocumentID=#arguments.event.getValue('fsDocumentID',0)#">
			<cfset local.actionReturnStruct.showSectionURL = "/?#getBaseQueryString(false)#&fsAction=showSection&fsSectionID=#local.sectionToShow#">
			<cfset local.actionReturnStruct.editURL = "/?#getBaseQueryString(false)#&fsAction=editDocument&lang=en">
			<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
			<cfset local.actionReturnStruct.getDocData = local.objDocument.getDocumentData(documentID=arguments.event.getValue('fsDocumentID'))>
			<cfset local.actionReturnStruct.getExtraDocData = getExtraDocData(local.actionReturnStruct.getDocData.siteResourceID) />
			<cfset local.actionReturnStruct.qryFSColumns = getExtraFSColumns(variables.fileShareSettings.siteResourceID)>
			<cfloop query="local.actionReturnStruct.qryFSColumns">
				<cfset local.actionReturnStruct.getExtraDocData["fs_#local.actionReturnStruct.qryFSColumns.columnID#"] = getExtraFSColumnData(local.actionReturnStruct.getDocData.siteResourceID, local.actionReturnStruct.qryFSColumns.columnID)>
			</cfloop>

			<cfif variables.appRightsStruct.fsDeleteAny or (variables.appRightsStruct.fsDeleteOwn and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (local.actionReturnStruct.getDocData.memberid eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.orgID)))>
				<cfset local.canDelete = true>
			<cfelse>
				<cfset local.canDelete = false>
			</cfif>
			
			<cfif variables.appRightsStruct.fsReuploadAny or (variables.appRightsStruct.fsReuploadOwn and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (local.actionReturnStruct.getDocData.memberid eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.orgID)))>
				<cfset local.canReupload = true>
			<cfelse>
				<cfset local.canReupload = false>
			</cfif>
			
			<cfif variables.appRightsStruct.fsEditAnyMetadata or (variables.appRightsStruct.fsEditOwnMetadata and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (local.actionReturnStruct.getDocData.memberid eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.orgID)))>
				<cfset local.canEditMetaData = true>
			<cfelse>
				<cfset local.canEditMetaData = false>
			</cfif>

			<cfif isdefined("local.rc.fsDocumentSaved") and local.rc.fsDocumentID neq 0>
				<cfscript>
					local.DocID = arguments.event.getValue("fsDocumentID");
					local.newContributorMemberID = application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.orgID);
					
					// check to see if there is a new file to upload --------------------------------------------
					if( arguments.event.getTrimValue('newFile', '') NEQ "" ){
						if( local.canReupload ){
							// if yes then set fileToUpload to the form variable newFile ------------------------------
							arguments.event.setValue('fileToUpload','newFile');
							// pre set the fileUploaded variable to TRUE ----------------------------------------------
							local.fileUploaded = TRUE;
							// try to upload the file to the proper destination ---------------------------------------
							try { local.newFile = local.objDocument.uploadFile("form.newFile"); } 
								// if if fails to upload the set the fileUploaded flag to FALSE -------------------------
								catch(any excpt) { local.fileUploaded = FALSE; }
							// if file was uploaded then get the files new source data --------------------------------
							if( local.fileUploaded ){
								local.objDocument.forceFileExtentionIfBlank(local.newFile);
								local.strArgs = {   documentID = arguments.event.getValue('fsDocumentID'),
													siteID = arguments.event.getValue('mc_siteInfo.siteID'),
													author = arguments.event.getValue('fsAuthor'),
													docTitle = arguments.event.getValue('fsDocTitle'),
													docDesc = arguments.event.getValue('fsDocDesc'),
													fileData = local.newFile,
													sectionID = arguments.event.getValue('fsSectionID'),
													contributorMemberID = arguments.event.getValue('fsContributorID'),
													recordedByMemberID = application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=event.getvalue('mc_pageDefinition.orgID')),
													newFileUploaded = 1,
													languageID = arguments.event.getValue('lan',session.mcstruct.languageID),
													oldFileExt=local.actionReturnStruct.getDocData.fileExt	};

								if (len(arguments.event.getTrimValue('fsPublicationDate',''))) {
									local.strArgs.publicationDate = arguments.event.getValue('fsPublicationDate');
								}

								local.loadDocument = local.objDocument.updateDocument(argumentCollection=local.strArgs);
								
								arguments.event.setValue('fsFileName',local.newFile.clientFile);
								arguments.event.setValue('fsfileExt',local.newFile.clientFileExt);
								local.newContributorMemberID = application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.orgID);
	
							}
							else{
								// error in upload - locate to message page and apply message ---------------------------
								application.objCommon.redirect('#this.link.message#&message=3');
							}
						}
					}
					if (local.canEditMetaData) {
						local.fileData = { clientFile=arguments.event.getValue('fsfileName'), serverFileExt=arguments.event.getValue('fsfileExt') };
						local.strArgs = {	documentID=arguments.event.getValue('fsDocumentID'),
											siteID=arguments.event.getValue('mc_siteInfo.siteID'),
											author=arguments.event.getValue('fsAuthor'),
											docTitle=arguments.event.getValue('fsdocTitle'),
											docDesc=arguments.event.getValue('fsdocDesc'),
											fileData=local.fileData,
											sectionID=arguments.event.getValue('fsSectionID'),
											contributorMemberID=arguments.event.getValue('fsContributorID'),
											recordedByMemberID=application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=event.getvalue('mc_pageDefinition.orgID')),
											newFileUploaded=0,
											languageID=arguments.event.getValue('lan',session.mcstruct.languageID) };

						if (len(arguments.event.getTrimValue('fsPublicationDate',''))) {
							local.strArgs.publicationDate =arguments.event.getValue('fsPublicationDate');
						}

						local.loadDocument = local.objDocument.updateDocument(argumentCollection=local.strArgs);
					}
					
					if (variables.fileShareSettings.notifyOnUpdate eq 1)
					{
						local.emailBody = "#session.cfcUser.memberdata.firstname# #session.cfcUser.memberdata.lastname# (#session.cfcUser.memberdata.memberNumber#) has updated a document #chr(13)#  #(application.objPlatform.isRequestSecure() ? 'https' : 'http')#://#application.objPlatform.getCurrentHostname()#/#local.actionReturnStruct.editURL#&fsDocumentID=#local.rc.fsDocumentID# #chr(13)##chr(13)# ";
						local.from = #session.cfcUser.memberdata.email#;
						if (len(local.from) eq 0) 
							local.from = '<EMAIL>';
	
						fssendemail(event=arguments.event, siteNotifyEmail=arguments.event.getValue('mc_siteInfo.defaultAdminEmails',''), 
							from=local.from, subject="#variables.fileShareSettings.applicationInstanceName# document updated", 
							body=local.emailBody);
					}
				</cfscript>

				<!--- Update the categories for the documents: ------------------------------------------------------------------------->
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.deleteCategoryForSRID">
					delete from dbo.cms_categorySiteResources
					WHERE siteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.actionReturnStruct.getDocData.siteResourceID#">
				</cfquery>
				
				<cfloop query="local.actionReturnStruct.qryGetCategoryTrees">
					<cfset local.categoryID = arguments.event.getValue('s_#local.actionReturnStruct.qryGetCategoryTrees.categoryTreeID#ID', '') />
					<cfif local.categoryID NEQ "">
						<cfloop list="#local.categoryID#" index="local.insertCatID">
							<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.saveFieldSets">
								INSERT INTO dbo.cms_categorySiteResources (CategoryID, siteResourceID)
								VALUES (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.insertCatID#">, <cfqueryparam cfsqltype="cf_sql_integer" value="#local.actionReturnStruct.getDocData.siteResourceID#">)					
							</cfquery>
						</cfloop>
					</cfif>
				</cfloop>				
				
				
				<!--- Update the custom fields on the fileshare application: ------------------------------------------------------------>
				<cfloop query="local.actionReturnStruct.qryFSColumns">
					<cfset local.columnValue = arguments.event.getValue('fs_#local.actionReturnStruct.qryFSColumns.columnID#', '') />

					<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="cms_deleteSiteResourceData">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.actionReturnStruct.getDocData.siteResourceID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.actionReturnStruct.qryFSColumns.columnID#">
					</cfstoredproc>						
					
					<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="cms_saveSiteResourceData">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.actionReturnStruct.getDocData.siteResourceID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.actionReturnStruct.qryFSColumns.columnID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
						<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.columnValue#">
					</cfstoredproc>						
					
					<cfset local.actionReturnStruct.getExtraDocData["fs_#local.actionReturnStruct.qryFSColumns.columnID#"] = getExtraFSColumnData(local.actionReturnStruct.getDocData.siteResourceID, local.actionReturnStruct.qryFSColumns.columnID)>
					
				</cfloop>
				
				<!--- record in activity log --->
				<cfstoredproc datasource="#application.dsn.platformstatsMC.dsn#" procedure="act_recordLog">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.masterOrgID)#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="update">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#variables.fileShareSettings.applicationTypeID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#variables.fileShareSettings.applicationInstanceID#">
				</cfstoredproc>						
					
				
				<cfset local.actionReturnStruct.msg = 2>
				<cfif len(arguments.event.getValue('lang') eq 0)>
					<cfset arguments.event.setValue('lang',session.mcstruct.languageCode)>
				</cfif>
				<cfset local.actionReturnStruct.getDocData = local.objDocument.getDocumentData(documentID=arguments.event.getValue('fsDocumentID'))>
				<cfset local.actionReturnStruct.getExtraDocData = getExtraDocData(local.actionReturnStruct.getDocData.siteResourceID) />
				<!--- get extra columns again --->
				<cfloop query="local.actionReturnStruct.qryFSColumns">
					<cfset local.actionReturnStruct.getExtraDocData["fs_#local.actionReturnStruct.qryFSColumns.columnID#"] = getExtraFSColumnData(local.actionReturnStruct.getDocData.siteResourceID, local.actionReturnStruct.qryFSColumns.columnID)>
				</cfloop>
				
			</cfif>
			
		<cfelse>
			<cfset local.actionReturnStruct.view = "fileshare2/notfound">
		</cfif>
		<cfreturn local.actionReturnStruct>
	</cffunction>

	<cffunction name="addSection" access="private" returntype="numeric">
		<cfargument name="sectionName" type="String" required="true">
		<cfargument name="parentSectionID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.insertSection">
			SET NOCOUNT ON 
			DECLARE  	@sectionResourceTypeID int, 
								@sectionID int,
								@sectionName varchar(50),
								@siteID int,
								@parentSectionID int,
								@inheritPlacements bit
								
			SELECT @sectionResourceTypeID = dbo.fn_getResourceTypeID('UserCreatedSection') 
			SELECT @sectionName 					= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.sectionName#">
			SELECT @siteID 								= <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			SELECT @parentSectionID 			= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.parentSectionID#">
			SELECT @inheritPlacements			= 1
			EXEC 
				dbo.cms_createPageSection 
					@siteID 								= @siteID, 
					@sectionResourceTypeID 	= @sectionResourceTypeID, 
					@ovTemplateID 					= null,
					@ovTemplateIDMobile 					= null,
					@ovModeID 							= null, 
					@parentSectionID 				= @parentSectionID, 
					@sectionName 						= @sectionName, 
					@sectionCode 						= null,
					@sectionBreadcrumb 					= null,
					@inheritPlacements			= @inheritPlacements,
					@sectionID 							= @sectionID OUTPUT

			select @sectionID as sectionID
			SET NOCOUNT OFF	
		</cfquery>
		<cfif local.insertSection.sectionID gt 0>
			<cfset init()>
			<cfset local.returnInt = local.insertSection.sectionID>
		<cfelse>
			<cfset local.returnInt = arguments.parentSectionID>
		</cfif>
		<cfreturn local.returnInt>
	</cffunction>

	<cffunction name="actionAddCategory" access="private" returntype="struct" output="false">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>
		<cfset local.sectionToShow = arguments.event.getValue('fsSectionID',variables.fileShareSettings.rootSectionID)>
		<cfset local.actionReturnStruct = structNew()>
		<cfset local.actionReturnStruct.bucketID = variables.fileShareSettings.BucketID>		
		<cfset local.actionReturnStruct.controllingSRID = this.siteResourceID>
		<cfset local.actionReturnStruct.showAll = true>
		<cfset local.actionReturnStruct.catID = 0>
		<cfif (application.objCMS.getTemplateSetting(arguments.event,"supportsBootstrap") eq "true")>
			<cfset local.actionReturnStruct.view = "category/editCategoryResponsive">
		<cfelse>
			<cfset local.actionReturnStruct.view = "category/editCategoryDefault">
		</cfif>

		<cfreturn local.actionReturnStruct>
	</cffunction>

	<cffunction name="isFileShareValid" access="private" output="false" returntype="boolean">
		<cfreturn (structKeyExists(variables.fileShareSettings,"fileShareID") and isnumeric(variables.fileShareSettings.fileShareID))>
	</cffunction>

	<cffunction name="setInstanceSettings" access="public" returntype="void">
		<cfargument name="instanceSettings" type="struct" required="yes">
		<cfset variables.fileShareSettings = arguments.instanceSettings>
	</cffunction>

	<cffunction name="getFileShareSettings" access="public" returntype="struct">
		<cfargument name="applicationInstanceID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		
		<cfset local.fileShareSettings = structNew()>
		
		<cfquery name="local.getFileShareSettings" datasource="#application.dsn.membercentral.dsn#">
			select 
				sr.resourceTypeID,
				fs.fileShareID,
				fs.recordsPerPage,
				fs.rootSectionID,
				fs.showPublicationDate,
				fs.requirePublicationDate,
				fs.isNetworkWide,
				fs.showVersioning,
				fs.showAuthor,
				fs.requireAuthor,
				fs.showContributedBy,
				fs.showFirm,
				fs.authorLabel,
				fs.showTags,
				fs.alwaysShowFolders,
				fs.showTagsAsLinks,
				fs.showAddress,
				fs.showCustomFields,
				fs.showBrowseTitle,
				fs.showInteriorText,
				fs.showDocDownloadCountToMembers,
				fs.notifyEmails,
				fs.notifyOnAdd,
				fs.notifyOnUpdate,
				fs.isMultipleSelectSearch,
				fs.showShareButton,
				ai.siteresourceID,
				ai.applicationInstanceID,
				ai.applicationTypeID,
				ai.siteID, 
				ai.applicationInstanceName,
				s.siteCode,
				s.orgID as masterOrgID,
				o.orgID,
				o.orgCode
			from fs_fileShare fs
			inner join cms_applicationInstances ai on ai.applicationInstanceID = fs.applicationInstanceID 
				and ai.applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.applicationInstanceID#">
			inner join cms_siteResources sr on ai.siteResourceID = sr.siteResourceID
			inner join sites s on ai.siteID = s.siteID
			inner join organizations o on s.orgID = o.orgID
		</cfquery>

		<cfset variables.appResourceTypeID = local.getFileShareSettings.resourceTypeID/>
		<cfloop index="local.thisField" list="#local.getFileShareSettings.columnList#">
			<cfset local.fileShareSettings [local.thisField] = local.getFileShareSettings[local.thisField]/>
		</cfloop>

		<cfquery name="local.fileShareSettings.qrySections" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int, @startSectionID int, @startSectionPath varchar(max), @startSectionPathExpanded varchar(max);
			SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#val(local.fileShareSettings.siteID)#">;
			SET @startSectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#val(local.fileShareSettings.rootSectionID)#">;

			SELECT @startSectionPath=thePath, @startSectionPathExpanded=thePathExpanded
			FROM dbo.cms_pageSections
			WHERE siteID = @siteID
			AND sectionID = @startSectionID;

			SELECT ps.sectionID, ps.sectionName,
				thePath = replace(ps.thePath,@startSectionPath,'0001'), 
				thePathExpanded = case
					when ps.sectionID = @startSectionID then ''
					when len(@startSectionPathExpanded) > 0 then replace(ps.thePathExpanded, @startSectionPathExpanded + ' \ ','')
					else ps.thePathExpanded
				end
			FROM dbo.cache_cms_recursivePageSections rps
			INNER JOIN dbo.cms_pageSections AS ps ON ps.sectionID = rps.startSectionID
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = ps.siteResourceID
				AND sr.siteResourceStatusID = 1
				AND sr.siteID = @siteID
			WHERE rps.sectionID = @startSectionID
			AND rps.siteID = @siteID
			ORDER BY ps.thePath;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="local.qryBucketID" datasource="#application.dsn.tlasites_search.dsn#">
			select top 1 sb.bucketID
			from dbo.tblSearchBuckets as sb
			inner join dbo.tblSearchBucketTypes as sbt on sbt.bucketTypeID = sb.bucketTypeID and sbt.bucketType = 'FileShare2'
			where sb.siteid = <cfqueryparam cfsqltype="cf_sql_integer" value="#val(local.fileShareSettings.siteID)#">
			and sb.bucketSettings.value('(//@fileShareID)[1]','varchar(50)') = '#val(local.fileShareSettings.fileShareID)#'
		</cfquery>
		
		<cfset local.fileShareSettings.customFields = getExtraFSColumns(containerSiteResourceID=val(local.fileShareSettings.siteResourceID))>
		
		<cfset local.fileShareSettings["BucketID"] = val(local.qryBucketID.BucketID)>

		<cfreturn local.fileShareSettings/>
	</cffunction>
	
	<cffunction name="getLatestUploads" access="package" returntype="struct">
		<cfargument name="siteID" type="numeric" required="yes"/>
		<cfargument name="rootSectionID" type="numeric" required="yes"/>
		<cfargument name="memberID" type="numeric" required="yes"/>
		<cfargument name="restrictToMemberID" type="numeric" required="yes"/>
		
		<cfset var local = structNew()>
		
		<cfset local.fileShareSettings = structNew()>
		
		<cfquery name="local.fileShareSettings.files" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @siteID int = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">;

			SELECT TOP (5)
				m3.firstName,m3.lastName,m3.memberID,
				docs.documentID, docs.sectionID, docs.siteID, docs.siteResourceID, l.docTitle,
				l.docDesc, v.filename, v.fileExt, v.dateCreated
			FROM cms_documents docs 
			INNER JOIN cms_documentLanguages l on docs.documentID = l.documentID
			INNER JOIN cms_documentVersions v on l.documentLanguageID = v.documentLanguageID AND v.isActive = 1
			INNER JOIN cms_siteResources docResource on docs.siteResourceID = docResource.siteResourceID
				AND docs.siteID = @siteID
				AND docResource.isVisible = 1
			INNER JOIN dbo.cms_siteResourceStatuses srs ON docResource.siteResourceStatusID = srs.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
			INNER JOIN dbo.ams_members contributor on v.contributorMemberID = contributor.memberID
			<cfif arguments.restrictToMemberID gt 0>
				AND contributor.memberID = <cfqueryparam value="#arguments.restrictToMemberID#" cfsqltype="CF_SQL_INTEGER">
			</cfif>
			INNER JOIN dbo.ams_members m3 on contributor.activeMemberid = m3.memberID
			INNER JOIN dbo.cache_cms_recursivePageSections AS rps ON rps.startSectionID = docs.sectionID
				AND rps.siteID = @siteID
				AND rps.sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rootSectionID#">
			INNER JOIN dbo.cms_siteResourceTypes docResourceType on docResource.resourceTypeID = docResourceType.resourceTypeID
			INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp on srfrp.siteID = @siteID
				AND docResource.siteResourceID = srfrp.siteResourceID 
			INNER JOIN dbo.cms_siteResourceFunctions srf ON srfrp.functionID = srf.functionID
				and srf.functionName = 'view'
			INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp on srfrp.siteID = @siteID
				AND srfrp.rightPrintID = gprp.rightPrintID
			INNER JOIN dbo.ams_members m ON m.groupPrintID = gprp.groupPrintID
				AND m.activeMemberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">
			LEFT OUTER JOIN cms_siteResources AppResource
				INNER JOIN cms_siteResourceTypes AppResourceTypes on AppResourceTypes.resourceTypeID = AppResource.resourceTypeID
				INNER JOIN cms_siteResourceTypeClasses AppResourceTypeClasses on AppResourceTypeClasses.resourceTypeClassID = AppResourceTypes.resourceTypeClassID
					AND AppResourceTypeClasses.resourceTypeClassName = 'application'
				INNER JOIN cms_applicationInstances ai on ai.siteResourceID = AppResource.siteResourceID
				INNER JOIN cms_applicationTypes at on ai.applicationTypeID = at.applicationTypeID
			ON AppResource.siteID = @siteID and AppResource.parentSiteResourceID = docResource.siteResourceID	
			ORDER BY docs.dateCreated desc;
		</cfquery>

		<cfreturn local.fileShareSettings>
	</cffunction>

	<cffunction name="createAppInstance" access="public" output="true" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="baseLink" type="string">
		<cfargument name="appInfo" type="query">
		<cfargument name="returnToAppAdmin" type="boolean" required="false" default="0">	
		<cfargument name="isFromCommunity" type="boolean" required="false" default="0">	

		<cfscript>
			var local 											= structNew();		
			// SET EVENT SPECIFICATION ----------------------------------------------
			local.appInfo 									= arguments.appInfo;
			variables.isCommunityReady 			= XMLSearch(local.appInfo.settingsXML,"string(//setting[@name='isCommunityReady']/@value)");
			variables.isMultiInstanceReady 	= XMLSearch(local.appInfo.settingsXML,"string(//setting[@name='isMultiInstanceReady']/@value)");
			arguments.event.paramValue('appTypeID','0');
			// LOAD OBJECTS ---------------------------------------------------------
			local.objAppCreation 						= CreateObject("component","model.admin.pages.appCreationProcess");
			// call the contruct to do all the page validation and form params ------
			contructAppInstanceForm(arguments.event,local.appInfo);			
		</cfscript>
		<cfif cgi.request_method eq "POST" AND NOT arguments.event.getValue('error.formErrors')>
			<cftry>
				<cfif variables.isCommunityReady AND arguments.event.getValue('deployToComm','0')>
					<cfset local.viewRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Community", functionName="view")>
					<cfset local.participateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Community", functionName="participate")>
					<cfset local.fsAddDocumentsRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="FileShare2", functionName="fsAddDocuments")>
					<cfset local.fsEditOwnMetadataRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="FileShare2", functionName="fsEditOwnMetadata")>
					<cfset local.fsReuploadOwnRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="FileShare2", functionName="fsReuploadOwn")>
					<cfset local.fsAddSubFolderRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="FileShare2", functionName="fsAddSubFolder")>
					<cfset local.fsDeleteOwnRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="FileShare2", functionName="fsDeleteOwn")>
				</cfif>

				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.createApp">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						DECLARE @siteID	int, @languageID int, @sectionID int, @isVisible bit, @pageName varchar(50), 
							@pageTitle varchar(200), @pagedesc varchar(400), @zoneID int, @pageTemplateID int,
							@pageModeID int, @pgResourceTypeID int, @pgParentResourceID int,  @allowReturnAfterLogin bit,
							@applicationInstanceName varchar(100), @applicationInstanceDesc varchar(200),
							@applicationInstanceID int, @siteResourceID int, @pageID int, @commSRID int;
		     
						SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;
						SET @languageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('lid')#">;
						SET @isVisible = 1;
						SELECT @pageName = dbo.fn_regexReplace(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('pageName')#">,'[^A-Z0-9\-]+','');
						SET @pageTitle = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('pageTitle')#">;
						SET @pagedesc = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('pageDesc')#">;
						SELECT @zoneID = dbo.fn_getZoneID('Main');
						SET @pageTemplateID = NULL;
						SET @pageModeID = <cfif arguments.event.getValue('pageModeID','0') EQ 0>NULL<cfelse><cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('pageModeID')#"></cfif>;
						SET @allowReturnAfterLogin = 1;
						SET @applicationInstanceName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('appInstanceName')#">;
						SET @applicationInstanceDesc = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('appInstanceDesc')#">;
					
						<cfif variables.isCommunityReady AND arguments.event.getValue('deployToComm','0')>
							SET @commSRID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('commSRID')#">;
							
							SELECT @sectionID = comm.rootSectionID 
							FROM dbo.comm_communities comm 
							INNER JOIN dbo.cms_applicationInstances ai on comm.applicationInstanceID = ai.applicationInstanceID
								AND ai.siteResourceID = @commSRID
							AND ai.siteID = @siteID;

							SELECT @pgResourceTypeID = dbo.fn_getResourceTypeID('ApplicationSubPage');
							SET @pgParentResourceID = @commSRID;
						<cfelse>
							SET @sectionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('sectionID')#">
							SELECT @pgResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedPage')
							SET @pgParentResourceID = NULL;
						</cfif>

						BEGIN TRAN;
							EXEC dbo.cms_createApplicationInstanceFileShare2 @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, 
								@isVisible=@isVisible, @pageName=@pageName, @pageTitle=@pageTitle, @pagedesc=@pagedesc,
								@zoneID=@zoneID, @pageTemplateID=@pageTemplateID, @pageModeID=@pageModeID,
								@pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=@pgParentResourceID,
								@allowReturnAfterLogin=@allowReturnAfterLogin, @applicationInstanceName=@applicationInstanceName,
								@applicationInstanceDesc=@applicationInstanceDesc, @applicationInstanceID=@applicationInstanceID OUTPUT,
								@siteResourceID=@siteResourceID OUTPUT, @pageID=@pageID OUTPUT;

							<cfif variables.isCommunityReady AND arguments.event.getValue('deployToComm','0')>
								DECLARE @viewFunctionID int, @participateFunctionID int, @functionIDList varchar(max),
									@fsAddDocumentsFunctionID int, @fsEditOwnMetadataFunctionID int, @fsReuploadOwnFunctionID int, 
									@fsDeleteOwnFunctionID int, @fsAddSubFolderFunctionID int, @FileShareResourceTypeID int;
								
								SET @viewFunctionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.viewRFID#">;
								SET @participateFunctionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.participateRFID#">;
								SET @fsAddDocumentsFunctionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.fsAddDocumentsRFID#">;
								SET @fsEditOwnMetadataFunctionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.fsEditOwnMetadataRFID#">;
								SET @fsReuploadOwnFunctionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.fsReuploadOwnRFID#">;
								SET @fsAddSubFolderFunctionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.fsAddSubFolderRFID#">;
								SET @fsDeleteOwnFunctionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.fsDeleteOwnRFID#">;

								EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1, 
									@functionIDList=@viewFunctionID, @roleID=NULL, @groupID=NULL, @inheritedRightsResourceID=@commSRID, 
									@inheritedRightsFunctionID=@viewFunctionID;

								SET @functionIDList = NULL;
								SELECT @functionIDList = CONCAT(@fsAddDocumentsFunctionID,',',@fsEditOwnMetadataFunctionID,',',@fsReuploadOwnFunctionID,',',
															@fsAddSubFolderFunctionID,',',@fsDeleteOwnFunctionID);

								EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1, 
									@functionIDList=@functionIDList, @roleID=NULL, @groupID=NULL, @inheritedRightsResourceID=@commSRID, 
									@inheritedRightsFunctionID=@participateFunctionID;
							</cfif>
						COMMIT TRAN;

						SELECT @applicationInstanceID as applicationInstanceID, @siteResourceID as siteResourceID, @pageID as pageID;
						
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
				<cfset local.message = 1>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch)>
					<cfset local.message = 2>
				</cfcatch>
			</cftry>
			<cfif arguments.isFromCommunity>
				<cfoutput>
					<script language="javascript">
						top.reloadCommSubPagesTable();
						top.MCModalUtils.hideModal();
					</script>
				</cfoutput>
			<cfelseif arguments.returnToAppAdmin>
				<cfoutput>
					<script language="javascript">
						top.reloadFSTable();
						top.MCModalUtils.hideModal();
					</script>
				</cfoutput>
			<cfelse>	
				<cfoutput>
					<script language="javascript">
						top.reloadPageTable();
						top.MCModalUtils.hideModal();
					</script>
				</cfoutput>
			</cfif>	
		<cfelse>
			<cfoutput>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.getMode">
					select dbo.fn_getmodeId('Full') as pageModeID
				</cfquery>
				<cfset arguments.event.setValue('pageModeID',local.getMode.pageModeID)>
				<cfset showAppInstanceForm(arguments.event,local.appInfo)>
			</cfoutput>
		</cfif>		
	</cffunction>

	<cffunction name="getListSideBarCount" access="public" returntype="numeric" output="no">
		<cfargument name="daysToLookBack" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfif arguments.daysToLookBack gt 0>
			<cfset local.daysToLookBack = -1 * arguments.daysToLookBack>
		<cfelse>
			<cfset local.daysToLookBack = arguments.daysToLookBack>
		</cfif>
		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			select docs.documentID
			from cms_documents docs
			inner join dbo.cms_documentLanguages dl on docs.documentID = dl.documentID
			inner join dbo.cms_documentVersions dv on dl.documentLanguageID = dv.documentLanguageID
			INNER JOIN dbo.cache_cms_recursivePageSections AS rps ON rps.startSectionID = docs.sectionID
				AND rps.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#variables.fileShareSettings.siteID#">
				AND rps.sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#variables.fileShareSettings.rootSectionID#">
			INNER JOIN dbo.cms_pageSections AS ps ON ps.sectionID = rps.startSectionID
			INNER JOIN dbo.cms_siteResources sr ON ps.siteResourceID = sr.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses srs ON sr.siteResourceStatusID = srs.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
			INNER JOIN dbo.cms_siteResources sr2 ON docs.siteResourceID = sr2.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses srs2 ON sr2.siteResourceStatusID = srs2.siteResourceStatusID and srs2.siteResourceStatusDesc = 'Active'
			where dv.DateModified > DateAdd(day, #local.daysToLookBack#, getDate())
		</cfquery>
		<cfreturn local.data.recordCount>
	</cffunction>	
	
	<cffunction name="getFileShareShowSettings" access="public" returntype="Query" output="no">
		<cfargument name="fileShareID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfquery name="local.qryGetFileShareSettings" datasource="#application.dsn.membercentral.dsn#">								
			select	
				fs.fileShareID,
				fs.showResultSorting,
				fs.defaultResultSorting
			from 
				dbo.fs_fileShare fs
			where
				fs.fileShareID = <cfqueryparam value="#val(arguments.fileShareID)#" cfsqltype="cf_sql_integer">
		</cfquery>	
		<cfreturn local.qryGetFileShareSettings>
	</cffunction>	

	<!--- This function handles the actions for both the uploaded documents and browsing the JRC. --->
	<cffunction name="actionShowBrowse" access="private" returntype="struct" output="false">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>
		<cfset local.rc = arguments.event.getCollection()>		
		<cfset local.sectionToShow = arguments.event.getValue('fsSectionID',variables.fileShareSettings.rootSectionID)>
		<cfset local.actionReturnStruct = structNew()>
		<cfif listFind(valuelist(variables.fileShareSettings.qrySections.sectioniD),local.sectionToShow)>

			<cfset local.actionReturnStruct.sectionToShow = local.sectionToShow>
			<cfset local.actionReturnStruct.view = "fileshare2/browse">
			<cfset local.actionReturnStruct.bucketID = variables.fileShareSettings.BucketID>
			<cfset local.actionReturnStruct.PG = arguments.event.getValue('PG')>
			<!--- If in community add extra page variable  --->
			<cfif arguments.event.getValue('COMMPG', '') NEQ ''>
				<cfset local.actionReturnStruct.PG = "#arguments.event.getValue('PG')#&commpg=#arguments.event.getValue('COMMPG')#">
			</cfif>
			<cfset local.actionReturnStruct.sectionURL = "/?#getBaseQueryString(false)#&fsAction=showSection">
			<cfset local.actionReturnStruct.editURL = "/?#getBaseQueryString(false)#&fsAction=editDocument&lang=en">
			<cfset local.actionReturnStruct.uploadSingleURL = "/?#getBaseQueryString(false)#&fsAction=addDocument">
			<cfset local.actionReturnStruct.uploadMultipleURL = "/?#getBaseQueryString(false)#&fsAction=addDocuments">
			<cfset local.actionReturnStruct.uploadsURL = "/?pg=#local.actionReturnStruct.PG#&panel=uploads">
			<cfset local.actionReturnStruct.uploadURL = "/?pg=#local.actionReturnStruct.PG#&panel=uploadsEdit">
			<cfset local.actionReturnStruct.baseURL = "/?pg=#local.actionReturnStruct.PG#">
			<cfset local.actionReturnStruct.notifyURL = "/?pg=#local.actionReturnStruct.PG#&panel=sendNote">
			<cfset local.actionReturnStruct.formPostURL = "/?#getBaseQueryString(false)#">
			<cfset local.actionReturnStruct.qryCartCount = CreateObject("component","model.viewCart.viewCart").getDocuments(
				depomemberdataid=session.cfcuser.memberdata.depoMemberDataID,
				membertype=val(application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='MemberType')),
				billingstate=application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='billingState'),
				billingzip=application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='billingZip'),
				orgcode=variables.fileShareSettings.orgcode)>
			<cfset local.actionReturnStruct.qryFSColumns = getExtraFSColumns(variables.fileShareSettings.siteResourceID)>			
			<cfset local.actionReturnStruct.qryGetFileShareShowSettings = getFileShareShowSettings(variables.fileShareSettings.fileshareID)>
			
			<cfscript>
			local.actionReturnStruct.AbsoluteMaxPerPage = variables.fileShareSettings.recordsPerPage;
			
			// Force language for now
			arguments.event.setValue('lang','en');					
				
			// Search ID
			local.actionReturnStruct.searchid = arguments.event.getValue("searchid","0");

			// Sort Type
			local.actionReturnStruct.sortType = arguments.event.getValue("sortType","");
			
			// Sort Order
			local.actionReturnStruct.sortOrder = arguments.event.getValue("sortOrder","");	
			
			// Current Sort Type
			local.actionReturnStruct.currSort = arguments.event.getValue("currSort",local.actionReturnStruct.sortType);	
			
			// Current Order
			local.actionReturnStruct.currOrder = arguments.event.getValue("currOrder",local.actionReturnStruct.sortOrder);						

			// Panel we came from
			local.actionReturnStruct.from = arguments.event.getValue("from","browse");
			
			// Only show a letter?
			local.actionReturnStruct.letter = arguments.event.getValue("ltr","");
			if (len(local.actionReturnStruct.letter) is 0 or asc(lcase(local.actionReturnStruct.letter)) lt 97 or asc(lcase(local.actionReturnStruct.letter)) gt 122) local.actionReturnStruct.letter = "";
			
			// Starting page to display
			local.actionReturnStruct.startrow = arguments.event.getValue("startrow","");
			if (NOT IsNumeric(local.actionReturnStruct.startrow)) local.actionReturnStruct.startrow = 1;
			
			// Panel List
			local.validPanelList = "uploadsEdit,browse,search,uploads,tagged,admin,sendNote";	
			
			// Show by tree
			local.actionReturnStruct.byTreeID = int(val(arguments.event.getValue("byT", 0)));
			local.actionReturnStruct.Panel = arguments.event.getValue("panel","browse");	
			if ( NOT listfindNoCase(local.validPanelList,local.actionReturnStruct.Panel)) local.actionReturnStruct.Panel = "browse";
			
			// set browselink
			local.actionReturnStruct.browseLink = "/?pg=#local.actionReturnStruct.PG#&panel=browse";
			if (local.actionReturnStruct.searchid NEQ "" AND local.actionReturnStruct.searchid GT 0)
			{
				local.actionReturnStruct.browseLink = local.actionReturnStruct.browseLink & "&searchid=#local.actionReturnStruct.searchid#";
			}
			local.actionReturnStruct.uploadsEditLink = "/?pg=#local.actionReturnStruct.PG#&panel=uploadsEdit";
			
			// validate categoryid
			local.actionReturnStruct.parentCategoryID = event.getValue("catID", 0);
			if (NOT IsNumeric(local.actionReturnStruct.parentCategoryID)) local.actionReturnStruct.parentCategoryID = 0;
			
			// validate byT
			local.actionReturnStruct.qryGetCategoryTrees = getCategoryTrees();
			if (NOT isValid("integer",local.actionReturnStruct.byTreeID)) local.actionReturnStruct.byTreeID = 0;
			if (local.actionReturnStruct.byTreeID eq 0) local.actionReturnStruct.byTreeID = local.actionReturnStruct.qryGetCategoryTrees.categoryTreeID; 
			
			local.actionReturnStruct.qryCurrentCategoryTree = getCurrentCategoryTree(variables.fileShareSettings.siteid,variables.fileShareSettings.siteResourceID,local.actionReturnStruct.byTreeID);
			local.actionReturnStruct.qryGetSubCategories = getSubCategoriesForCatalog(local.actionReturnStruct.parentCategoryID,local.actionReturnStruct.byTreeID,local.actionReturnStruct.letter, local.actionReturnStruct.searchid);
			local.actionReturnStruct.qryGetCategoryPathUp = getCategoryPathUp(local.actionReturnStruct.parentCategoryID,local.actionReturnStruct.byTreeID);
			local.actionReturnStruct.numDocsInSubCats = getDocumentCount(local.actionReturnStruct.qryGetSubCategories);
			
			if (local.actionReturnStruct.Panel EQ "uploadsEdit"){
				local.actionReturnStruct.tabValue = arguments.event.getValue('tab','U');
				local.actionReturnStruct.qryGetUploadedDocuments = getDocumentsWithNoDescription(memberID=session.cfcuser.memberdata.memberid, startRow=local.actionReturnStruct.startrow, maxPerPage=local.actionReturnStruct.AbsoluteMaxPerPage);
				local.actionReturnStruct.documentCount = val(local.actionReturnStruct.qryGetUploadedDocuments.totalDocumentCount);

				local.actionReturnStruct.MaxPerPage = iif(local.actionReturnStruct.documentCount gt local.actionReturnStruct.AbsoluteMaxPerPage,local.actionReturnStruct.AbsoluteMaxPerPage,local.actionReturnStruct.documentCount);
				if (local.actionReturnStruct.qryGetUploadedDocuments.recordCount mod 2 is not 0) 
					local.actionReturnStruct.MaxPerColumn = ceiling(local.actionReturnStruct.qryGetUploadedDocuments.recordCount / 2);
				else 
					local.actionReturnStruct.MaxPerColumn = int(local.actionReturnStruct.qryGetUploadedDocuments.recordCount / 2);
				if (local.actionReturnStruct.AbsoluteMaxPerPage gt 0) {
					local.actionReturnStruct.NumTotalPages = Ceiling(local.actionReturnStruct.documentCount / local.actionReturnStruct.AbsoluteMaxPerPage);
					local.actionReturnStruct.NumCurrentPage = int((int(local.actionReturnStruct.startrow) + local.actionReturnStruct.AbsoluteMaxPerPage - 1) / local.actionReturnStruct.AbsoluteMaxPerPage);
				} else {
					local.actionReturnStruct.NumTotalPages = 1;
					local.actionReturnStruct.NumCurrentPage = 1;
				}
				local.actionReturnStruct.tmpSort = ArrayNew(1);
				ArrayAppend(local.actionReturnStruct.tmpSort,"rank|Relevance");
				ArrayAppend(local.actionReturnStruct.tmpSort,"createDate|Create Date");
				ArrayAppend(local.actionReturnStruct.tmpSort,"reviseDate|Revise Date");
				ArrayAppend(local.actionReturnStruct.tmpSort,"source|Source");
				
				
			}
			else if (local.actionReturnStruct.Panel EQ "browse"){
				local.actionReturnStruct.tabValue = arguments.event.getValue('tab','B');
				local.actionReturnStruct.documentCount = getCategoryDocumentsCount(variables.fileShareSettings.siteid,local.actionReturnStruct.parentCategoryID,session.cfcuser.memberdata.memberid, local.actionReturnStruct.searchid);
				local.actionReturnStruct.qryGetCategoryDocuments = getCategoryDocuments(variables.fileShareSettings.siteid,local.actionReturnStruct.parentCategoryID,session.cfcuser.memberdata.memberid, local.actionReturnStruct.startrow, local.actionReturnStruct.AbsoluteMaxPerPage, variables.fileShareSettings.fileShareID, local.actionReturnStruct.currSort, local.actionReturnStruct.currOrder, local.actionReturnStruct.qryGetFileShareShowSettings, local.actionReturnStruct.searchid);
				
				local.actionReturnStruct.MaxPerPage = iif(local.actionReturnStruct.documentCount gt local.actionReturnStruct.AbsoluteMaxPerPage,local.actionReturnStruct.AbsoluteMaxPerPage,local.actionReturnStruct.documentCount);
				if (local.actionReturnStruct.qryGetSubCategories.recordCount mod 2 is not 0) 
					local.actionReturnStruct.MaxPerColumn = ceiling(local.actionReturnStruct.qryGetSubCategories.recordCount / 2);
				else 
					local.actionReturnStruct.MaxPerColumn = int(local.actionReturnStruct.qryGetSubCategories.recordCount / 2);
				if (local.actionReturnStruct.AbsoluteMaxPerPage gt 0) {
					local.actionReturnStruct.NumTotalPages = Ceiling(local.actionReturnStruct.documentCount / local.actionReturnStruct.AbsoluteMaxPerPage);
					if (local.actionReturnStruct.NumTotalPages gt 0)
						local.actionReturnStruct.NumCurrentPage = int((int(local.actionReturnStruct.startrow) + local.actionReturnStruct.AbsoluteMaxPerPage - 1) / local.actionReturnStruct.AbsoluteMaxPerPage);
					else {
						local.actionReturnStruct.NumTotalPages = 1;
						local.actionReturnStruct.NumCurrentPage = 1;
					}

				} else {
					local.actionReturnStruct.NumTotalPages = 1;
					local.actionReturnStruct.NumCurrentPage = 1;
				}
				local.actionReturnStruct.tmpSort = ArrayNew(1);
				ArrayAppend(local.actionReturnStruct.tmpSort,"rank|Relevance");
				ArrayAppend(local.actionReturnStruct.tmpSort,"createDate|Create Date");
				ArrayAppend(local.actionReturnStruct.tmpSort,"reviseDate|Revise Date");
				ArrayAppend(local.actionReturnStruct.tmpSort,"source|Source");
			}

			if (local.actionReturnStruct.Panel EQ "search"){
				local.actionReturnStruct.tabValue = 'S';
				local.actionReturnStruct.documentCount = getCategoryDocumentsCount(variables.fileShareSettings.siteid,local.actionReturnStruct.parentCategoryID,session.cfcuser.memberdata.memberid, local.actionReturnStruct.searchid);
				local.actionReturnStruct.qryGetCategoryDocuments = getCategoryDocuments(variables.fileShareSettings.siteid,local.actionReturnStruct.parentCategoryID,session.cfcuser.memberdata.memberid, local.actionReturnStruct.startrow, local.actionReturnStruct.AbsoluteMaxPerPage, variables.fileShareSettings.fileShareID, local.actionReturnStruct.currSort, local.actionReturnStruct.currOrder, local.actionReturnStruct.qryGetFileShareShowSettings, local.actionReturnStruct.searchid);
				
				local.actionReturnStruct.MaxPerPage = iif(local.actionReturnStruct.documentCount gt local.actionReturnStruct.AbsoluteMaxPerPage,local.actionReturnStruct.AbsoluteMaxPerPage,local.actionReturnStruct.documentCount);
				if (local.actionReturnStruct.qryGetSubCategories.recordCount mod 2 is not 0) 
					local.actionReturnStruct.MaxPerColumn = ceiling(local.actionReturnStruct.qryGetSubCategories.recordCount / 2);
				else 
					local.actionReturnStruct.MaxPerColumn = int(local.actionReturnStruct.qryGetSubCategories.recordCount / 2);
				if (local.actionReturnStruct.AbsoluteMaxPerPage gt 0) {
					local.actionReturnStruct.NumTotalPages = Ceiling(local.actionReturnStruct.documentCount / local.actionReturnStruct.AbsoluteMaxPerPage);
					local.actionReturnStruct.NumCurrentPage = int((int(local.actionReturnStruct.startrow) + local.actionReturnStruct.AbsoluteMaxPerPage - 1) / local.actionReturnStruct.AbsoluteMaxPerPage);
				} else {
					local.actionReturnStruct.NumTotalPages = 1;
					local.actionReturnStruct.NumCurrentPage = 1;
				}
				local.actionReturnStruct.tmpSort = ArrayNew(1);
				ArrayAppend(local.actionReturnStruct.tmpSort,"rank|Relevance");
				ArrayAppend(local.actionReturnStruct.tmpSort,"createDate|Create Date");
				ArrayAppend(local.actionReturnStruct.tmpSort,"reviseDate|Revise Datee");
				ArrayAppend(local.actionReturnStruct.tmpSort,"source|Source");

			}			
			if (local.actionReturnStruct.Panel EQ "uploads"){
				local.actionReturnStruct.tabValue = 'UD';
			}
			if (local.actionReturnStruct.Panel EQ "tagged"){
				local.actionReturnStruct.tabValue = 'C';
			}
			if (local.actionReturnStruct.Panel EQ "admin"){
				local.actionReturnStruct.qryAdminNotes = getAdminNotes();
				local.actionReturnStruct.tabValue = 'A';
			}
			if (local.actionReturnStruct.Panel EQ "sendNote"){
				if (IsNumeric(arguments.event.getValue('fsDocumentID')) AND arguments.event.getValue('fsDocumentID') GT 0 ) {
					
					local.objDocument = CreateObject("component","model.system.platform.document");
					local.actionReturnStruct.getDocData = local.objDocument.getDocumentData(documentID=arguments.event.getValue('fsDocumentID'));
					local.actionReturnStruct.getExtraDocData = getExtraDocData(local.actionReturnStruct.getDocData.siteResourceID);
					local.actionReturnStruct.tabValue = 'SN';
					
					if (IsDefined("local.rc.fsNoteSaved") and local.rc.fsDocumentID neq 0)
					{
						// Save the note somewhere and send it to all JRC admins
						insertAdminNote (documentID=arguments.event.getValue('fsDocumentID'), note=local.rc.note);
						
						local.emailBody = "#session.cfcUser.memberdata.firstname# #session.cfcUser.memberdata.lastname# (#session.cfcUser.memberdata.memberNumber#) has sent a notification on document #chr(13)#  #(application.objPlatform.isRequestSecure() ? 'https' : 'http')#://#application.objPlatform.getCurrentHostname()#/#local.actionReturnStruct.editURL#&fsDocumentID=#local.rc.fsDocumentID# #chr(13)##chr(13)# Note: " & local.rc.note;
						local.from = #session.cfcUser.memberdata.email#;
						if (len(local.from) eq 0) 
							local.from = '<EMAIL>';

						fssendemail(event=arguments.event, siteNotifyEmail=arguments.event.getValue('mc_siteInfo.defaultAdminEmails',''), 
							from=local.from, subject="#variables.fileShareSettings.applicationInstanceName# admin notification", 
							body=local.emailBody);
								 
						local.actionReturnStruct.msg = 1;
					}
				}
				else {
					application.objCommon.redirect('/?pg=#local.actionReturnStruct.PG#');
				}
			}

			// If a search id is provided then mark the search tab as active.
			if (local.actionReturnStruct.searchid NEQ "" AND local.actionReturnStruct.searchid GT 0)
			{
				local.actionReturnStruct.tabValue = 'S';
			}
			</cfscript>						

			<cfset local.actionReturnStruct.getTags = this.getTags />
			<cfset local.actionReturnStruct.getIcon = this.getIcon />
			<cfset local.actionReturnStruct.getFileDescription = this.getFileDescription />

			<cfset local.actionReturnStruct.methods = this />			

			
		<cfelse>
			<cfset local.actionReturnStruct.view = "fileshare2/notfound">
		</cfif>

		<cfreturn local.actionReturnStruct>
	</cffunction>

	<cffunction name="getOtherFileShares" returntype="query" output="no">
		<cfargument name="rootSectionID" type="numeric" required="yes">

		<cfset var local = structNew()>
		
		<!--- get category trees --->
		<cfquery name="local.getOtherFileShares" datasource="#application.dsn.membercentral.dsn#">
			select 
				fs.rootSectionID,
				ai.applicationInstanceName + case WHEN communityInstances.applicationInstanceName is not null THEN ' (' + communityInstances.applicationInstanceName + ')' ELSE '' END as applicationInstanceName
			from dbo.fs_fileShare fs
			inner join dbo.cms_applicationInstances ai
				on ai.applicationInstanceID = fs.applicationInstanceID and ai.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#variables.fileShareSettings.siteID#">
			inner join dbo.cms_siteResources sr on ai.siteResourceID = sr.siteResourceID
			inner join dbo.cms_siteResources AS parentResource ON parentResource.siteResourceID = sr.parentSiteResourceID
			left outer join dbo.cms_siteResources AS grandparentResource
				inner join dbo.cms_applicationInstances AS CommunityInstances on communityInstances.siteResourceID = grandParentResource.siteResourceID
				on grandparentResource.siteResourceID = parentResource.parentSiteResourceID
			where rootSectionID <> <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rootSectionID#">
			order by 2
		</cfquery>
			
		<cfreturn local.getOtherFileShares>
	</cffunction>

	<cffunction name="getCategoryTrees" returntype="query" output="no">
		<cfset var local = structNew()>
		
		<!--- get category trees --->
		<cfquery name="local.qryCategoryTrees" datasource="#application.dsn.membercentral.dsn#">
			SELECT  ct.categoryTreeID, ct.siteID, ct.siteResourceID, ct.categoryTreeName, ct.categoryTreeDesc, ct.categoryTreeCode, ct.controllingSiteResourceID
			FROM    cms_categoryTrees ct
			INNER JOIN cms_siteResources sr on sr.siteResourceID = ct.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses srs ON sr.siteResourceStatusID = srs.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
			WHERE   ct.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#variables.fileShareSettings.siteid#">	
			AND 	ct.controllingSiteResourceID =  <cfqueryparam cfsqltype="cf_sql_integer" value="#variables.fileShareSettings.siteResourceID#">			
			order by sortOrder
		</cfquery>
	
		<cfreturn local.qryCategoryTrees>
	</cffunction>

	<cffunction name="getCurrentCategoryTree" returntype="query" output="no">
		<cfargument name="siteID" required="Yes" type="numeric">
		<cfargument name="siteResourceID" required="Yes" type="numeric">
		<cfargument name="categoryTreeID" required="Yes" type="numeric">
		
		<cfset var local = structNew()>
		
		<!--- get category trees --->
		<cfquery name="local.qryCurrentCategoryTree" datasource="#application.dsn.membercentral.dsn#">
			SELECT  categoryTreeID, siteID, siteResourceID, categoryTreeName, categoryTreeDesc, categoryTreeCode, controllingSiteResourceID
			FROM    cms_categoryTrees
			WHERE   siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">	
			AND 	controllingSiteResourceID =  <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteResourceID#">
			AND 	categoryTreeID =  <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.categoryTreeID#">
		</cfquery>
	
		<cfreturn local.qryCurrentCategoryTree>
	</cffunction>

	<cffunction name="getCategoryPathUp" returntype="query" output="no">
		<cfargument name="parentCategoryID" required="Yes" type="numeric">
		<cfargument name="categoryTreeID" required="Yes" type="numeric">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.qryGetCategoryPathUp" datasource="#application.dsn.membercentral.dsn#">
			select * 
			from dbo.fn_cms_categoryPathUp(<cfqueryparam value="#arguments.parentCategoryID#" cfsqltype="CF_SQL_INTEGER">,<cfqueryparam value="#val(arguments.categoryTreeID)#" cfsqltype="CF_SQL_INTEGER">)
			order by Depth
		</cfquery>	

		<cfreturn local.qryGetCategoryPathUp>
	</cffunction>
	
	<cffunction name="getSubCategoriesForCatalog" returntype="query" output="no">
		<cfargument name="parentCategoryID" required="Yes" type="numeric">
		<cfargument name="categoryTreeID" required="Yes" type="numeric">
		<cfargument name="letter" required="Yes" type="string">
		<cfargument name="searchID" required="no" type="numeric">

		<cfset var local = structNew()>
		
		<cfquery name="local.qryGetSubCategories" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @categoryTreeID int = <cfqueryparam value="#arguments.categoryTreeID#" cfsqltype="CF_SQL_INTEGER">;
			declare @activeSRS int, @inactiveSRS int;
			select @activeSRS = siteResourceStatusID from dbo.cms_siteResourceStatuses where siteResourceStatusDesc = 'Active';
			select @inactiveSRS = siteResourceStatusID from dbo.cms_siteResourceStatuses where siteResourceStatusDesc = 'Inactive';

			IF OBJECT_ID('tempdb..##subcategories') IS NOT NULL 
				DROP TABLE ##subcategories;
			CREATE TABLE ##subcategories (topLevelCategoryID int PRIMARY KEY, documentcount int);

			WITH Categories AS (
				select c.categoryID, c.CategoryName, c.categoryID as topLevelCategoryID, c.parentCategoryID
				FROM dbo.cms_categories c
				INNER JOIN dbo.cms_categoryTrees ct on ct.categoryTreeID = c.categoryTreeID
				INNER JOIN dbo.cms_siteResources sr on sr.siteResourceID = ct.siteResourceID and sr.siteResourceStatusID = @activeSRS
				WHERE c.categoryTreeID = @categoryTreeID
				<cfif arguments.parentCategoryID is 0>
					and c.parentCategoryID IS NULL
				<cfelse>
					and c.parentCategoryID = <cfqueryparam value="#arguments.parentCategoryID#" cfsqltype="CF_SQL_INTEGER">
				</cfif>
				and c.isActive = 1
					UNION ALL
				select c.categoryID, c.CategoryName, cat.topLevelCategoryID, c.parentCategoryID
				FROM dbo.cms_categories c
				INNER JOIN dbo.cms_categoryTrees ct on ct.categoryTreeID = c.categoryTreeID
				INNER JOIN dbo.cms_siteResources sr on sr.siteResourceID = ct.siteResourceID and sr.siteResourceStatusID = @activeSRS
				INNER JOIN Categories as cat ON c.parentCategoryID = cat.categoryID
				WHERE c.categoryTreeID = @categoryTreeID
				and c.isActive = 1
			)
			insert into ##subcategories (topLevelCategoryID, documentcount)
			select sc.topLevelCategoryID, count(*)
			from Categories sc
			INNER JOIN dbo.cms_categorySiteResources csr on csr.categoryID = sc.categoryID
			<cfif len(arguments.searchid) and arguments.searchid gt 0>
				inner join searchMC.dbo.tblSearchSiteResourceCache ssr on ssr.searchid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.searchID#">
					and ssr.siteResourceid = csr.siteResourceID
			</cfif>						
			<!--- If you have fsDeleteAny it implies you are an administrator --->
			INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = csr.siteResourceID
			<cfif variables.appRightsStruct.fsDeleteAny>
				AND sr.siteResourceStatusID IN (@activeSRS,@inactiveSRS)
			<cfelse>
				AND sr.siteResourceStatusID = @activeSRS
			</cfif>						
			group by topLevelCategoryID;

			SELECT c.categoryID, c.categoryName, c.parentCategoryID, isnull(sc.DocumentCount,0) as DocumentCount,
				(select count(categoryID) from dbo.cms_categories where parentCategoryID = c.categoryID) as categoryCount, c.sortOrder
			FROM dbo.cms_categories AS c
			LEFT OUTER JOIN ##subcategories sc on sc.topLevelCategoryID = c.categoryID
			WHERE c.categoryTreeID = @categoryTreeID
			AND c.isActive = 1
			<cfif arguments.parentCategoryID is 0>
				and c.parentCategoryID IS NULL
			<cfelse>
				and c.parentCategoryID = <cfqueryparam value="#arguments.parentCategoryID#" cfsqltype="CF_SQL_INTEGER">
			</cfif>
			<cfif len(arguments.searchid) and arguments.searchid gt 0>
				and sc.DocumentCount > 0
			</cfif>						
			ORDER BY c.sortOrder

			IF OBJECT_ID('tempdb..##subcategories') IS NOT NULL 
				DROP TABLE ##subcategories;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;	
		</cfquery>

		<cfreturn local.qryGetSubCategories>
	</cffunction>
	
	<cffunction name="getCategoryDocumentsCount" returntype="numeric" output="no">
		<cfargument name="siteID" required="Yes" type="numeric">
		<cfargument name="CategoryID" required="Yes" type="numeric">
		<cfargument name="memberID" required="Yes" type="numeric">
		<cfargument name="searchID" required="no" type="numeric">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.qryGetCategoryDocumentsCount" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int, @memberID int, @docRFID int, @useMemberID int, @rtID int, 
				@applicationInstanceID int, @categoryID int, @viewFunctionID int, @publicGroupPrintID int;			
					
			set @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;
			set @memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">;
			set @applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#variables.fileShareSettings.applicationInstanceID#">;
			set @categoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.categoryID#">;
			set @docRFID = 4;
			set @rtID = dbo.fn_getResourceTypeID('Community');
			set @viewFunctionID = 4;

			if @publicGroupPrintID is null
				select @publicGroupPrintID = o.publicGroupPrintID
				from dbo.organizations o
				inner join sites s on s.orgID = o.orgID and s.siteID = @siteID;

			IF dbo.fn_isSuperUser(@memberID) = 1
				set @useMemberID = @memberID;

			SELECT @useMemberID = coalesce(@useMemberID,fileShareOrgMember.memberID,0)
			FROM dbo.sites s
			INNER JOIN dbo.networksites ns ON s.siteID = ns.siteID AND ns.isLoginNetwork = 1
			INNER JOIN dbo.networksites allSitesInNetwork ON ns.networkID = allSitesInNetwork.networkID AND allSitesInNetwork.isLoginNetwork = 1
			INNER JOIN dbo.sites fileShareSite ON allSitesInNetwork.siteID = fileShareSite.siteID
			INNER JOIN dbo.cms_applicationInstances ai ON ai.siteID = fileShareSite.siteID
			INNER JOIN dbo.cms_siteResources appSR ON ai.siteResourceID = appSR.siteResourceID
			INNER JOIN dbo.cms_siteResources AS parentResource ON parentResource.siteResourceID = appSR.parentSiteResourceID
			INNER JOIN dbo.fs_fileshare fs ON ai.applicationInstanceID = fs.applicationInstanceID
				AND fs.applicationInstanceID = @applicationInstanceID
			left outer join dbo.cms_siteResources AS grandparentResource
				inner join dbo.cms_applicationInstances AS CommunityInstances on communityInstances.siteResourceID = grandParentResource.siteResourceID
				on grandparentResource.siteResourceID = parentResource.parentSiteResourceID
					and grandparentResource.resourceTypeID = @rtID
			LEFT OUTER JOIN dbo.ams_members m
				INNER JOIN dbo.ams_memberNetworkProfiles mnp ON m.activeMemberID = mnp.memberID
					AND m.memberID = @memberID
					AND mnp.status = 'A'
				INNER JOIN dbo.ams_memberNetworkProfiles allLinkedMembers ON mnp.profileID = allLinkedMembers.profileID
					AND allLinkedMembers.status = 'A'
				INNER JOIN dbo.ams_members fileShareOrgMember ON allLinkedMembers.memberID = fileShareOrgMember.memberID
				ON fileShareOrgMember.orgID = fileShareSite.orgID
			WHERE s.siteID = @siteID;

			SELECT COUNT(*) as theCount
			FROM (
				SELECT docs.*
				FROM(
					SELECT distinct @useMemberID as memberID,
						ai.applicationInstanceID, ai.siteID, 
						fs.fileShareID, fs.rootSectionID, fs.showPUblicationDate, 
						sections.sectionID, sections.sectionName, sections.ovModeID, sections.ovTemplateID, sections.parentSectionID, 
						sections.siteResourceID as sectionSiteResourceID,
						'' as SectionPath,
						fileShareName = s.siteName + ' - ' + ai.applicationInstanceName + case  WHEN communityInstances.applicationInstanceName is not null THEN ' (' + communityInstances.applicationInstanceName + ')' ELSE '' END,
						documents.documentID, documents.siteResourceID as docSiteResourceID, v.contributorMemberID, documents.redirectID, 
						l.docTitle, l.docDesc, v.fileName, v.fileExt, v.publicationDate, v.dateCreated, l.documentLanguageID,
						v.dateModified, v.author, contributors2.firstName, contributors2.lastName, docSR.resourceTypeID
					FROM dbo.sites s
					INNER JOIN dbo.networksites ns ON s.siteID = ns.siteID AND s.siteID = @siteID AND ns.isLoginNetwork = 1
					INNER JOIN dbo.networksites allSitesInNetwork ON ns.networkID = allSitesInNetwork.networkID AND allSitesInNetwork.isLoginNetwork = 1
					INNER JOIN dbo.sites fileShareSite ON allSitesInNetwork.siteID = fileShareSite.siteID
					INNER JOIN dbo.cms_applicationInstances ai ON ai.siteID = fileShareSite.siteID
					INNER JOIN dbo.cms_siteResources appSR ON ai.siteResourceID = appSR.siteResourceID
					INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp on srfrp.siteResourceID = appSR.siteResourceID
						and srfrp.functionID = @viewFunctionID and srfrp.siteID = @siteID
					INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
						AND srfrp.rightPrintID = gprp.rightPrintID
						<cfif arguments.memberID EQ 0>
							and gprp.groupPrintID = @publicGroupPrintID
						<cfelse>
							INNER JOIN dbo.ams_members rightsMem on rightsMem.groupPrintID = gprp.groupPrintID
								and (rightsMem.memberID = @memberID)
						</cfif>						
					INNER JOIN dbo.cms_siteResources AS parentResource ON parentResource.siteResourceID = appSR.parentSiteResourceID
					left outer join dbo.cms_siteResources AS grandparentResource
						inner join dbo.cms_applicationInstances AS CommunityInstances on communityInstances.siteResourceID = grandParentResource.siteResourceID
						on grandparentResource.siteResourceID = parentResource.parentSiteResourceID and grandparentResource.resourceTypeID = @rtID
					INNER JOIN dbo.fs_fileshare fs ON ai.applicationInstanceID = fs.applicationInstanceID
						AND fs.applicationInstanceID = @applicationInstanceID
					INNER JOIN dbo.cms_documents documents ON documents.sectionID = fs.rootSectionID
					INNER JOIN dbo.cms_pageSections sections on sections.sectionID = fs.rootSectionID
					INNER JOIN dbo.cms_documentLanguages l on documents.documentID = l.documentID
					INNER JOIN dbo.cms_documentVersions v on l.documentLanguageID = v.documentLanguageID AND v.isActive = 1
					INNER JOIN dbo.cms_siteResources as docSR ON documents.siteResourceID = docSR.siteResourceID
					<!--- If you have fsDeleteAny it implies you are an administrator --->
					<cfif variables.appRightsStruct.fsDeleteAny>
						AND docSR.siteResourceStatusID in (1,2)
					<cfelse>
						AND docSR.siteResourceStatusID = 1
					</cfif>								
					INNER JOIN dbo.ams_members contributors ON contributors.memberID = v.contributorMemberID
					INNER JOIN dbo.ams_members contributors2 ON contributors2.memberID = contributors.activeMemberID
					INNER JOIN dbo.cms_categorySiteResources csr ON csr.siteResourceID = documents.siteResourceID
						AND csr.categoryID = @categoryID
					<cfif len(arguments.searchid) and arguments.searchid gt 0>
						inner join searchMC.dbo.tblSearchSiteResourceCache ssr on ssr.searchid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.searchID#">
							and ssr.siteResourceid = documents.siteResourceID			
					</cfif>										
				) docs
			) AS myCount;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;	
		</cfquery>

		<cfreturn local.qryGetCategoryDocumentsCount.theCount>
	</cffunction>

	<cffunction name="getCategoryDocuments" returntype="query" output="no">
		<cfargument name="siteID" required="Yes" type="numeric">
		<cfargument name="CategoryID" required="Yes" type="numeric">
		<cfargument name="memberID" required="Yes" type="numeric">
		<cfargument name="startRow" required="Yes" type="numeric">
		<cfargument name="MaxPerPage" required="Yes" type="numeric">
		<cfargument name="fileShareID" required="Yes" type="numeric">
		<cfargument name="currSort" required="Yes" type="string" >
		<cfargument name="currOrder" required="Yes" type="string" >
		<cfargument name="qryGetFileShareSettings" required="Yes" type="query">
		<cfargument name="searchID" required="no" type="numeric">
		
		<cfset var local = structNew()>							

		<cfswitch expression="#arguments.currSort#">
			<cfcase value="date">
				<cfset local.sqlSortType = "order by docs.publicationDate #arguments.currOrder#">
			</cfcase>
			<cfcase value="title">
				<cfset local.sqlSortType = "order by docs.docTitle #arguments.currOrder#">
			</cfcase>						
			<cfdefaultcase>							
				<cfset local.sqlSortType = "order by docs.dateCreated desc">	
				
				<cfif arguments.qryGetFileShareSettings.recordCount and val(arguments.qryGetFileShareSettings.showResultSorting) and len(trim(arguments.qryGetFileShareSettings.defaultResultSorting))>
					<cfset arguments.sortType = arguments.qryGetFileShareSettings.defaultResultSorting>
					<cfswitch expression="#arguments.qryGetFileShareSettings.defaultResultSorting#">
						<cfcase value="date">
							<cfset arguments.currOrder  = "DESC">	
							<cfset local.sqlSortType = "order by docs.publicationDate #arguments.currOrder#">
						</cfcase>
						<cfcase value="title">
							<cfset arguments.currOrder  = "ASC">	
							<cfset local.sqlSortType = "order by docs.docTitle #arguments.currOrder#">
						</cfcase>
					</cfswitch>
				</cfif>									
			</cfdefaultcase>
		</cfswitch>	
			
		<cfquery name="local.qryGetCategoryDocuments" datasource="#application.dsn.membercentral.dsn#" result="local.qryGetCategoryDocumentsStats">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int, @memberID int, @commRTID int, @useMemberID int, @docRFID int, @applicationInstanceID int, 
				@categoryID int, @viewFunctionID int, @startRow int, @endRow int, @publicGroupPrintID int;	

			set @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;
			set @memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">;
			set @commRTID = dbo.fn_getResourceTypeID('Community');
			set @docRFID = 4; 
			set @applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#variables.fileShareSettings.applicationInstanceID#">;
			set @categoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.categoryID#">;
			set @viewFunctionID = 4;
			set @startRow = <cfqueryparam value="#arguments.startrow#" cfsqltype="CF_SQL_INTEGER">;
			set @endRow = <cfqueryparam value="#(arguments.startrow + arguments.maxPerPage -1)#" cfsqltype="CF_SQL_INTEGER">;

			if @publicGroupPrintID is null
				select @publicGroupPrintID = o.publicGroupPrintID
				from dbo.organizations o
				inner join sites s on s.orgID = o.orgID and s.siteID = @siteID;

			IF dbo.fn_isSuperUser(@memberID) = 1
				set @useMemberID = @memberID;

			IF OBJECT_ID('tempdb..##tmpDocs') IS NOT NULL 
				DROP TABLE ##tmpDocs

			SELECT @useMemberID = coalesce(@useMemberID,fileShareOrgMember.memberID,0)
			FROM dbo.sites s
			INNER JOIN dbo.networksites ns ON s.siteID = ns.siteID AND s.siteID = @siteID AND ns.isLoginNetwork = 1
			INNER JOIN dbo.networksites allSitesInNetwork ON ns.networkID = allSitesInNetwork.networkID AND allSitesInNetwork.isLoginNetwork = 1
			INNER JOIN dbo.sites fileShareSite ON allSitesInNetwork.siteID = fileShareSite.siteID
			INNER JOIN dbo.cms_applicationInstances ai ON ai.siteID = fileShareSite.siteID
			INNER JOIN dbo.cms_siteResources appSR ON ai.siteResourceID = appSR.siteResourceID
			INNER JOIN dbo.cms_siteResources AS parentResource ON parentResource.siteResourceID = appSR.parentSiteResourceID
			left outer join dbo.cms_siteResources AS grandparentResource
				inner join dbo.cms_applicationInstances AS CommunityInstances on communityInstances.siteResourceID = grandParentResource.siteResourceID
			on grandparentResource.siteResourceID = parentResource.parentSiteResourceID
				and grandparentResource.resourceTypeID = @commRTID						
			INNER JOIN dbo.fs_fileshare fs ON ai.applicationInstanceID = fs.applicationInstanceID
					AND fs.applicationInstanceID = @applicationInstanceID
			LEFT OUTER JOIN dbo.ams_members m
				INNER JOIN dbo.ams_memberNetworkProfiles mnp ON m.activeMemberID = mnp.memberID
					AND m.memberID = @memberID
					AND mnp.status = 'A'
				INNER JOIN dbo.ams_memberNetworkProfiles allLinkedMembers ON mnp.profileID = allLinkedMembers.profileID
					AND allLinkedMembers.status = 'A'
				INNER JOIN dbo.ams_members fileShareOrgMember ON allLinkedMembers.memberID = fileShareOrgMember.memberID
			ON fileShareOrgMember.orgID = fileShareSite.orgID 

			SELECT distinct @useMemberid as memberID,
				ai.applicationInstanceID, ai.siteID, 
				fs.fileShareID, fs.rootSectionID, fs.showPUblicationDate, 
				sections.sectionID, sections.sectionName, sections.ovModeID, sections.ovTemplateID, sections.parentSectionID, 
				sections.siteResourceID as sectionSiteResourceID,
				'' as SectionPath,
				fileShareName = s.siteName + ' - ' + ai.applicationInstanceName + case  WHEN communityInstances.applicationInstanceName is not null THEN ' (' + communityInstances.applicationInstanceName + ')' ELSE '' END,
				documents.documentID, documents.siteResourceID as docSiteResourceID, v.contributorMemberID, documents.redirectID, 
				l.docTitle, l.docDesc, v.fileName, v.fileExt, v.publicationDate, v.dateCreated, l.documentLanguageID,
				v.dateModified, v.author, contributors2.firstName, contributors2.lastName, contributors.company, docSR.resourceTypeID,
				srs.siteResourceStatusDesc
			into ##tmpDocs
			FROM dbo.sites s
			INNER JOIN dbo.networksites ns ON s.siteID = ns.siteID AND s.siteID = @siteID AND ns.isLoginNetwork = 1
			INNER JOIN dbo.networksites allSitesInNetwork ON ns.networkID = allSitesInNetwork.networkID AND allSitesInNetwork.isLoginNetwork = 1
			INNER JOIN dbo.sites fileShareSite ON allSitesInNetwork.siteID = fileShareSite.siteID
			INNER JOIN dbo.cms_applicationInstances ai ON ai.siteID = fileShareSite.siteID
			INNER JOIN dbo.cms_siteResources appSR ON ai.siteResourceID = appSR.siteResourceID
			INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp on srfrp.siteID = @siteID
				and srfrp.siteResourceID = appSR.siteResourceID
				and srfrp.functionID = @viewFunctionID
			INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID 
				and srfrp.rightPrintID = gprp.rightPrintID
			<cfif arguments.memberID EQ 0>
				and gprp.groupPrintID = @publicGroupPrintID
			<cfelse>
				INNER JOIN dbo.ams_members rightsMem on rightsMem.groupPrintID = gprp.groupPrintID
					and (rightsMem.memberID = @memberID)
			</cfif>
			INNER JOIN dbo.cms_siteResources AS parentResource ON parentResource.siteResourceID = appSR.parentSiteResourceID
			left outer join dbo.cms_siteResources AS grandparentResource
				inner join dbo.cms_applicationInstances AS CommunityInstances on communityInstances.siteResourceID = grandParentResource.siteResourceID
				on grandparentResource.siteResourceID = parentResource.parentSiteResourceID and grandparentResource.resourceTypeID = @commRTID						
			INNER JOIN dbo.fs_fileshare fs ON ai.applicationInstanceID = fs.applicationInstanceID
				AND fs.applicationInstanceID = @applicationInstanceID
			INNER JOIN dbo.cms_documents documents ON documents.sectionID = fs.rootSectionID
			INNER JOIN dbo.cms_pageSections sections on sections.sectionID = fs.rootSectionID
			INNER JOIN dbo.cms_documentLanguages l on documents.documentID = l.documentID
			INNER JOIN dbo.cms_documentVersions v on l.documentLanguageID = v.documentLanguageID AND v.isActive = 1
			INNER JOIN dbo.cms_siteResources as docSR ON documents.siteResourceID = docSR.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses as srs on docSR.siteResourceStatusID = srs.siteResourceStatusID 
				<!--- If you have fsDeleteAny it implies you are an administrator --->
				<cfif variables.appRightsStruct.fsDeleteAny >
					AND srs.siteResourceStatusDesc in ('Active', 'Inactive')
				<cfelse>
					AND srs.siteResourceStatusDesc in ('Active')
				</cfif>
			INNER JOIN dbo.ams_members contributors ON contributors.memberID = v.contributorMemberID
			INNER JOIN dbo.ams_members contributors2 ON contributors2.memberID = contributors.activeMemberID
			INNER JOIN dbo.cms_categorySiteResources csr ON csr.siteResourceID = documents.siteResourceID
				AND csr.categoryID = @categoryID
			<cfif len(arguments.searchid) and arguments.searchid gt 0>
				inner join searchMC.dbo.tblSearchSiteResourceCache ssr on ssr.searchid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.searchID#">
					and ssr.siteResourceid = documents.siteResourceID			
			</cfif>;					

			SELECT tmp.*, (
					select count(dh.docHitID) 
					FROM dbo.cms_documents d  
					INNER JOIN dbo.cms_documentLanguages l on d.documentID = l.documentID
					INNER JOIN dbo.cms_documentVersions v on l.documentLanguageID = v.documentLanguageID
					INNER JOIN platformstatsMC.dbo.statsDocumentHits dh on dh.documentVersionID = v.documentVersionID and dh.siteID=@siteID
					WHERE d.documentID = tmp.documentID
				) as downloadCount						
			FROM (
				SELECT docs.*, ROW_NUMBER() OVER (#local.sqlSortType#) as row
				FROM ##tmpDocs as docs
			) AS tmp 
			WHERE row between @startRow and @endRow
			order by row;

			IF OBJECT_ID('tempdb..##tmpDocs') IS NOT NULL 
				DROP TABLE ##tmpDocs;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;	
		</cfquery>

		<cfreturn local.qryGetCategoryDocuments>
	</cffunction>

	<cffunction name="getExtraDocData" returntype="struct" output="no">
		<cfargument name="siteResourceID" required="Yes" type="numeric">
		
		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		
		<cfquery name="local.qryExtraDocData" datasource="#application.dsn.membercentral.dsn#">
			select ct.categoryTreeID, ct.categoryTreeName, STRING_AGG(csr.categoryID,', ') as categoryID
			from dbo.cms_categorySiteResources csr
			INNER JOIN dbo.cms_categories c on c.categoryID = csr.categoryID
			INNER JOIN dbo.cms_categoryTrees ct on c.categoryTreeID = ct.categoryTreeID
			where csr.siteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteResourceID#">			
			group by  ct.categoryTreeID, ct.categoryTreeName
		</cfquery>	

		<cfloop query="local.qryExtraDocData">
			<cfset local.data["s_#local.qryExtraDocData.categoryTreeID#ID"] = local.qryExtraDocData.categoryID>	
		</cfloop>
		
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="getExtraFSColumns" returntype="query" output="no">
		<cfargument name="containerSiteResourceID" required="Yes" type="numeric">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.qryExtraDocData" datasource="#application.dsn.membercentral.dsn#">
			SELECT 
				c.columnID,c.siteID,c.containerSiteREsourceID,c.columnName,c.columnOrder,c.dataTypeID,c.displayTypeID,c.columnDesc,
				displayType.displayTypeCode,
				dataType.dataTypeCode
			FROM dbo.cms_siteResourceDataColumns c
			INNER JOIN dbo.ams_memberDataColumnDisplayTypes displayType ON c.displayTypeID = displayType.displayTypeID
			INNER JOIN dbo.ams_memberDataColumnDataTypes dataType ON c.dataTypeID = dataType.dataTypeID
			WHERE containerSiteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.containerSiteResourceID#">			
			ORDER BY c.columnOrder
		</cfquery>	

		<cfreturn local.qryExtraDocData>
	</cffunction>
	
	<cffunction name="getExtraFSColumnData" returntype="string" output="no">
		<cfargument name="itemSiteResourceID" required="Yes" type="numeric">
		<cfargument name="columnId" required="Yes" type="numeric">
		
		<cfset var local = structNew()>
		<cfset local.returnData = '' />
		
		<cfset local.qryExtraDocData = this.getCustomColumnData(itemSiteREsourceID=arguments.itemSiteResourceID,columnID=arguments.columnID) />
		
		<cfif local.qryExtraDocData.recordCount>
			<cfswitch expression="#local.qryExtraDocData.dataTypeID#">
				<!--- STRING --->
				<cfcase value="1"><cfset local.returnData = local.qryExtraDocData.columnValueSTRING /></cfcase>
				<!--- DECIMAL2 --->
				<cfcase value="2"><cfset local.returnData = local.qryExtraDocData.columnValueDECIMAL2 /></cfcase>
				<!--- INTEGER --->
				<cfcase value="3"><cfset local.returnData = local.qryExtraDocData.columnValueINTEGER /></cfcase>
				<!--- DATE --->
				<cfcase value="4"><cfset local.returnData = local.qryExtraDocData.columnvalueDATE /></cfcase>
				<!--- BIT --->
				<cfcase value="5"><cfset local.returnData = local.qryExtraDocData.columnValueBIT /></cfcase>
				<cfdefaultcase> </cfdefaultcase>
			</cfswitch>
		</cfif>
		<cfreturn local.returnData />
	</cffunction>
	
	<cffunction name="getExtraFSColumnDisplay" returntype="string" output="no">
		<cfargument name="itemSiteResourceID" required="Yes" type="numeric">
		<cfargument name="columnId" required="Yes" type="numeric">
		
		<cfset var local = structNew()>
		<cfset local.returnData = '' />
		
		<cfset local.qryExtraDocData = this.getCustomColumnData(itemSiteREsourceID=arguments.itemSiteResourceID,columnID=arguments.columnID) />
		
		<cfif local.qryExtraDocData.recordCount>
			<cfswitch expression="#local.qryExtraDocData.dataTypeID#">
				<!--- STRING --->
				<cfcase value="1"><cfset local.returnData = local.qryExtraDocData.columnValueSTRING /></cfcase>
				<!--- DECIMAL2 --->
				<cfcase value="2"><cfset local.returnData = local.qryExtraDocData.columnValueDECIMAL2 /></cfcase>
				<!--- INTEGER --->
				<cfcase value="3"><cfset local.returnData = local.qryExtraDocData.columnValueINTEGER /></cfcase>
				<!--- DATE --->
				<cfcase value="4"><cfset local.returnData = local.qryExtraDocData.columnvalueDATE /></cfcase>
				<!--- BIT --->
				<cfcase value="5">
					<cfif local.qryExtraDocData.columnValueBIT>
						<cfset local.returnData = True />
					<cfelse>
						<cfset local.returnData = False />
					</cfif>
				</cfcase>
				<cfdefaultcase> </cfdefaultcase>
			</cfswitch>
		</cfif>
		<cfreturn local.returnData />
	</cffunction>
	
	<cffunction name="getCustomColumnData" access="public" returntype="query" output="no">
		<cfargument name="itemSiteResourceID" required="Yes" type="numeric">
		<cfargument name="columnId" required="Yes" type="numeric">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			select 
				c.columnID, c.siteID, c.containerSiteResourceID, c.columnName, c.columnOrder, c.dataTypeID, c.displayTypeID, c.ColumnDesc,
				dcv.valueID,dcv.columnValueString, dcv.columnValueDecimal2, dcv.columnValueInteger, dcv.columnvalueDate, dcv.columnValueBit, 
				dcv.columnValueSiteResourceID, rd.dataID, rd.itemSiteResourceID
			from dbo.cms_siteResourceDataColumns as c
			left outer join cms_siteResourceDataColumnValues dcv on dcv.columnID = c.columnID
			left outer join cms_siteResourceData rd on rd.valueID = dcv.valueID
			where rd.itemSiteResourceID =  <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.itemSiteResourceID#">		
			and c.columnID =  <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.columnId#">		
		</cfquery>	
		
		<cfreturn local.data />
	</cffunction>
	
	<cffunction name="getAdditionalColumnValues" access="public" returntype="query">
		<cfargument name="columnID" type="numeric" required="true">
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.data">
			SELECT
				valueID,
				columnID,
				columnValueSiteResourceID,
				columnValueString,
				columnValueDecimal2,
				columnValueInteger,
				columnValueDate,
				columnValueBit
			FROM cms_siteResourceDataColumnValues
			WHERE columnID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.columnID#">
		</cfquery>
		
		<cfreturn local.data />
	</cffunction>
	
	<cffunction name="copyExtraFSColumns" access="public" returntype="void" output="no">
		<cfargument name="documentSiteResourceID" required="Yes" type="numeric">
		<cfargument name="newRootSectionID" required="Yes" type="numeric">
		<cfargument name="fsSectionID" required="Yes" type="numeric">
		<cfargument name="newDocumentSiteResourceID" required="No" type="numeric" default="0">
		
		<!--- Copy all of the fileshare extra columns between file shares if the document is moved between them --->
		<!---	Given the 2 sections, lookup up the custom columns and copy the data from one file share to the other.	--->
		<cfset var local = structNew()>

		<cfset local.newDocumentSiteResourceID = arguments.documentSiteResourceID>		
		<cfif arguments.newDocumentSiteResourceID NEQ 0>
			<cfset local.newDocumentSiteResourceID = arguments.newDocumentSiteResourceID>
		</cfif>
		
		
		<cfquery name="local.columnsToCopy" datasource="#application.dsn.membercentral.dsn#">
				SELECT 
					f.rootSectionID as fsOldSectionID, c.columnID as fsOldColumnID, c.containerSiteREsourceID as fsOldSRID,c.columnName as oldColumnName,
					target.rootSectionID as fsNewSectionID, target.columnID as fsNewColumnID,target.containerSiteREsourceID as fsNewSRID
				from dbo.fs_fileShare f
				inner join cms_applicationInstances ai on ai.applicationInstanceID = f.applicationInstanceID
					and f.rootSectionID =  <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fsSectionID#">		
				inner join dbo.cms_siteResourceDataColumns c on ai.siteResourceID = c.containerSiteResourceID
				inner join (
					SELECT 
						f.rootSectionID, c.columnID,c.siteID,c.containerSiteREsourceID,c.columnName,c.columnOrder,c.dataTypeID,c.displayTypeID,c.columnDesc
					from dbo.fs_fileShare f
					inner join cms_applicationInstances ai on ai.applicationInstanceID = f.applicationInstanceID
						and f.rootSectionID =  <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.newRootSectionID#">		
					inner join dbo.cms_siteResourceDataColumns c on ai.siteResourceID = c.containerSiteResourceID
				) as target on target.columnName = c.columnName and target.dataTypeID = c.dataTypeID		

		</cfquery>	

		<!--- Update the custom fields on the fileshare application: ------------------------------------------------------------>
		<cfloop query="local.columnsToCopy">
			<cfset local.columnValue = this.getExtraFSColumnData(itemSiteREsourceID=arguments.documentSiteResourceID,columnID=local.columnsToCopy.fsOldColumnID) />
<!--- 
	Made the decision to leave the orphan data for now.  We may want to clean this up later.
	
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="cms_deleteSiteResourceData">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.documentSiteResourceID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.actionReturnStruct.qryFSColumns.columnID#">
			</cfstoredproc>						
 --->
			<cftry>
				<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="cms_saveSiteResourceData">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.newDocumentSiteResourceID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.columnsToCopy.fsNewColumnID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
					<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.columnValue#">
				</cfstoredproc>						
			<cfcatch>
			</cfcatch>
			</cftry>
		</cfloop>
	</cffunction>

	
	<cffunction name="getDocumentCount" returntype="numeric" output="no">
		<cfargument name="qryCategories" required="Yes" type="query">
		
		<cfset var local = structNew()>
		
		<cfset local.documentCount = 0 />
		<cfloop query="arguments.qryCategories">
			<cfset local.documentCount = local.documentCount + arguments.qryCategories.documentCount>
		</cfloop>

		<cfreturn local.documentCount>
	</cffunction>

	<cffunction name="getDocumentsWithNoDescription" returntype="query" output="no">
		<cfargument name="memberID" required="Yes" type="numeric">
		<cfargument name="startRow" required="Yes" type="numeric">
		<cfargument name="MaxPerPage" required="Yes" type="numeric">
		
		<cfset var qryGetDocuments = "">
		
		<cfquery name="qryGetDocuments" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @memberID int, @totalDocumentCount int;
					
			set @memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">;

			IF OBJECT_ID('tempdb..##tmpDocs') IS NOT NULL 
				DROP TABLE ##tmpDocs;
			CREATE TABLE ##tmpDocs (documentID int, siteResourceID int, documentLanguageID int, siteResourceStatusDesc varchar(100), docTitle varchar(255), 
				contributorMemberID int, dateCreated datetime, dateModified datetime, publicationDate datetime, author varchar(255), fileExt varchar(20), row int,
				INDEX IX_tmpDocs_row (row));
			
			INSERT INTO ##tmpDocs (documentID, siteResourceID, documentLanguageID, siteResourceStatusDesc, docTitle, 
				contributorMemberID, dateCreated, dateModified, publicationDate, author, fileExt, row)
			SELECT d.documentID, d.siteResourceID, dl.documentLanguageID, srs.siteResourceStatusDesc, dl.docTitle,
				dv.contributorMemberID, dv.dateCreated, dv.dateModified, dv.publicationDate, dv.author, dv.fileExt,
				ROW_NUMBER() OVER (ORDER BY dv.dateCreated desc) as row
			FROM dbo.fs_fileshare as fs
			INNER JOIN dbo.cms_documents as d ON d.sectionID = fs.rootSectionID
			INNER JOIN dbo.cms_documentLanguages as dl on d.documentID = dl.documentID and dl.docDesc = ''
			INNER JOIN dbo.cms_documentVersions as dv on dv.documentLanguageID = dl.documentLanguageID AND dv.isActive = 1
				<cfif variables.appRightsStruct.fsEditAnyMetaData>
					<!--- get all data --->
				<cfelse>
					<cfif variables.appRightsStruct.fsEditOwnMetaData>
						AND dv.contributorMemberID = @memberID
					<cfelse>
						AND dv.contributorMemberID = 0
					</cfif>					
				</cfif>
			INNER JOIN dbo.cms_siteResources as docSR ON d.siteResourceID = docSR.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = docSR.siteResourceStatusID 
				<!--- If you have fsDeleteAny it implies you are an administrator --->
				<cfif variables.appRightsStruct.fsDeleteAny>
					AND srs.siteResourceStatusDesc in ('Active','Inactive')
				<cfelse>
					AND srs.siteResourceStatusDesc = 'Active'
				</cfif>
			WHERE fs.applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#variables.fileShareSettings.applicationInstanceID#">
			OPTION(RECOMPILE);

			select @totalDocumentCount = @@ROWCOUNT;

			select tmp2.documentID, tmp2.siteResourceID, tmp2.documentLanguageID, tmp2.siteResourceStatusDesc, tmp2.docTitle, tmp2.contributorMemberID, 
				tmp2.dateCreated, tmp2.dateModified, tmp2.publicationDate, tmp2.author, tmp2.fileExt, contributors2.firstName, contributors2.lastName, 
				contributors2.company, @totalDocumentCount as totalDocumentCount
			from (
				SELECT documentID, siteResourceID, documentLanguageID, siteResourceStatusDesc, docTitle, contributorMemberID, dateCreated, dateModified, 
					publicationDate, author, fileExt, row
				FROM ##tmpDocs
				WHERE row between <cfqueryparam value="#arguments.startrow#" cfsqltype="CF_SQL_INTEGER"> and <cfqueryparam value="#(arguments.startrow + arguments.maxPerPage -1)#" cfsqltype="CF_SQL_INTEGER">
			) as tmp2
			INNER JOIN dbo.ams_members as contributors ON contributors.memberID = tmp2.contributorMemberID
			INNER JOIN dbo.ams_members as contributors2 ON contributors2.memberID = contributors.activeMemberID
			order by row;

			IF OBJECT_ID('tempdb..##tmpDocs') IS NOT NULL 
				DROP TABLE ##tmpDocs
		</cfquery>

		<cfreturn qryGetDocuments>
	</cffunction>

	<cffunction name="getTags" access="public" output="yes" returntype="string">
		<cfargument name="siteResourceID" required="yes" type="string">
		<cfargument name="showTagsAsLinks" required="no" type="string" default="0">
		<cfargument name="elementWrapperStart" required="no" type="string" default="">
		<cfargument name="elementWrapperEnd" required="no" type="string" default="">
		
		<cfset local.tag = "">
		<cfset local.Links = arguments.showTagsAsLinks>
		
		
		<cfquery name="local.qryTags" datasource="#application.dsn.membercentral.dsn#">
			SELECT c.categoryID, c.categoryName, c.categoryTreeID
			FROM dbo.cms_categories AS c 
			INNER JOIN dbo.cms_categoryTrees as t on c.categoryTreeID = t.categoryTreeID
			INNER JOIN dbo.cms_categorySiteResources csr on csr.categoryID = c.categoryID
			WHERE csr.siteResourceID = <cfqueryparam value="#arguments.siteResourceID#" cfsqltype="CF_SQL_INTEGER">
			order by t.sortOrder
		</cfquery>
	
		<cfloop query="local.qryTags">
			<cfif local.links EQ "0">
				<cfset local.tag = local.tag & arguments.elementWrapperStart & local.qryTags.categoryName & arguments.elementWrapperEnd>
			<cfelse>
				<cfset local.tag = local.tag & arguments.elementWrapperStart & '<a href="#dataStruct.browseLink#&catID=#local.qryTags.categoryID#&byT=#local.qryTags.categoryTreeID#">' & local.qryTags.categoryName & '</a>' & arguments.elementWrapperEnd>
			</cfif>
		</cfloop>
		
		<cfreturn local.tag>
	</cffunction>
	

	<cffunction name="getAddress" access="public" output="yes" retuntype="string">
		<cfargument name="memberid" required="yes" type="string">
		
		<cfquery name="local.qyrAdd" datasource="#application.dsn.membercentral.dsn#">
			select email  
			from dbo.ams_members m
			inner join dbo.ams_memberEmailTypes t on t.orgID = m.orgID
			inner join dbo.ams_memberEmails e on e.memberID = m.memberID
			where e.memberid = <cfqueryparam value="#arguments.memberid#" cfsqltype="CF_SQL_INTEGER"> 

		</cfquery>
		
		<cfreturn local.qyrAdd>
	</cffunction>
	
	<cffunction name="getIcon" access="public" output="yes" returntype="string">
		<cfargument name="fileExt" required="yes" type="string">
		
		<cfswitch expression="#fileExt#">
			<cfcase value="doc,docx"><cfset local.fileImg = "doc.png"></cfcase>
			<cfcase value="html"><cfset local.fileImg = "html.png"></cfcase>
			<cfcase value="pdf"><cfset local.fileImg = "pdf.png"></cfcase>
			<cfcase value="ppt,pptx"><cfset local.fileImg = "ppt.png"></cfcase>
			<cfcase value="txt"><cfset local.fileImg = "txt.png"></cfcase>
			<cfcase value="xls,xlsx"><cfset local.fileImg = "xls.png"></cfcase>
			<cfcase value="jpg,jpeg"><cfset local.fileImg = "jpg.png"></cfcase>
			<cfcase value="gif"><cfset local.fileImg = "gif.png"></cfcase>
			<cfcase value="zip"><cfset local.fileImg = "zip.png"></cfcase>
			<cfcase value="xml"><cfset local.fileImg = "xml.png"></cfcase>
			<cfcase value="png"><cfset local.fileImg = "png.png"></cfcase>
			<cfcase value="rtf"><cfset local.fileImg = "rtf.png"></cfcase>		
			<cfcase value="wpd"><cfset local.fileImg = "file.png"></cfcase>		
			<cfcase value="ptx"><cfset local.fileImg = "file.png"></cfcase>		
			<cfdefaultcase><cfset local.fileImg = "file.png"></cfdefaultcase>
		</cfswitch>
		<cfreturn local.fileImg>
	</cffunction>

	<cffunction name="getFileDescription" access="public" output="yes" returntype="string">
		<cfargument name="fileExt" required="yes" type="string">
		
		<cfswitch expression="#fileExt#">
			<cfcase value="doc,docx"><cfset local.desc = "Word Document"></cfcase>
			<cfcase value="html"><cfset local.desc = "Web Page"></cfcase>
			<cfcase value="pdf"><cfset local.desc = "PDF Document"></cfcase>
			<cfcase value="ppt,pptx"><cfset local.desc = "Power Point Document"></cfcase>
			<cfcase value="txt"><cfset local.desc = "Text Document"></cfcase>
			<cfcase value="xls,xlsx"><cfset local.desc = "Excel Document"></cfcase>
			<cfcase value="jpg,jpeg"><cfset local.desc = "JPG Image"></cfcase>
			<cfcase value="gif"><cfset local.desc = "GIF Image"></cfcase>
			<cfcase value="zip"><cfset local.desc = "Zip Archive"></cfcase>
			<cfcase value="xml"><cfset local.desc = "XML Document"></cfcase>
			<cfcase value="png"><cfset local.desc = "PNG Image"></cfcase>
			<cfcase value="rtf"><cfset local.desc = "Rich Text Document"></cfcase>		
			<cfcase value="wpd"><cfset local.desc = "Word Perfect Document"></cfcase>		
			<cfcase value="ptx"><cfset local.desc = "E-Transcript"></cfcase>		
			<cfdefaultcase><cfset local.desc = "Other File Type"></cfdefaultcase>
		</cfswitch>
		<cfreturn local.desc>
	</cffunction>

	<cffunction name="insertAdminNote" returntype="void" output="no">
		<cfargument name="documentID" required="Yes" type="numeric">
		<cfargument name="note" required="Yes" type="string">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.qryInsertNote" datasource="#application.dsn.membercentral.dsn#">
			insert into fs_adminNotes (fileShareID, memberID, documentID, Note)
			values ( <cfqueryparam cfsqltype="cf_sql_integer" value="#variables.fileShareSettings.fileShareID#">,
					<cfqueryparam cfsqltype="cf_sql_integer" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.fileShareSettings.orgID)#">,
					<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.documentID#">,
					<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.note#">)
		</cfquery>
	
		
	</cffunction>

	<cffunction name="getAdminNotes" returntype="query" output="no">
		<cfset var local = structNew()>
		
		<cfquery name="local.qryGetAdminNotes" datasource="#application.dsn.membercentral.dsn#">
			select n.fileShareNoteID, n.note, n.memberID, m3.FirstName, m3.LastName, m3.middleName, d.documentID, l.docTitle
			from fs_adminNotes n
			inner join ams_members m on m.memberID = n.memberID
			inner join dbo.ams_members m3 on m.activeMemberid = m3.memberID
			inner join cms_documents d on d.documentID = n.documentID
			inner join cms_documentLanguages l on d.documentID = l.documentID 
			
			where fileShareID = <cfqueryparam value="#variables.fileShareSettings.fileShareID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
	
		<cfreturn local.qryGetAdminNotes>
	</cffunction>

	<cffunction name="fssendemail" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="siteNotifyEmail" type="string" required="true">
		<cfargument name="from" type="string" required="true">
		<cfargument name="subject" type="string" required="true">
		<cfargument name="body" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.emailTo = "">
		<cfset local.arrEmailTo = []>
	
		<!--- Send to appropriate emails --->
		<cfif variables.fileShareSettings.notifyEmails NEQ "">
			<cfset local.emailTo = variables.fileShareSettings.notifyEmails>
		<cfelseif arguments.siteNotifyEmail NEQ "">
			<cfset local.emailTo = arguments.siteNotifyEmail>
		</cfif>
		
		<cfset local.toEmailArr = listToArray(local.emailTo,';')>
		<cfloop array="#local.toEmailArr#" item="local.thisEmail">
			<cfset ArrayAppend(local.arrEmailTo, { name:"", email:trim(local.thisEmail) })>
		</cfloop>

		<cfif arrayLen(local.arrEmailTo)>
			<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom')},
				emailto=local.arrEmailTo,
				emailreplyto=arguments.from,
				emailsubject=arguments.subject,
				emailtitle=arguments.subject,
				emailhtmlcontent=arguments.body,
				emailAttachments=[],
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				memberID=arguments.event.getValue('mc_siteinfo.sysmemberid'),
				messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="FILESHARE"),
				sendingSiteResourceID=this.siteResourceID)>
		</cfif>
	</cffunction>

</cfcomponent>