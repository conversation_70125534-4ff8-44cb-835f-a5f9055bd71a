<cfsavecontent variable="local.evhead">
	<cfoutput>

		<style type="text/css">
			##MyTELA .nav-tabs > .active > a, ##MyTELA .nav-tabs>.active>a:hover {
				color: ##ffffff!important;
				background: ##324B64!important;
				font-weight: normal;
				font-family: 'Montserrat';
				text-decoration: none;
				cursor:default;
				text-shadow: none;
				text-transform: uppercase;
			}
			##MyTELA .nav-tabs li.active a {
    			text-decoration: none!important;
			}
			##MyTELA .nav-tabs>li>a, ##MyTELA .nav-pills>li>a {
				padding: 7px 7px 7px 7px!important;
			}
			##MyTELA .nav-tabs > li > a {
				border: 1px solid transparent;
				border-radius: 4px 4px 0 0;
				line-height: 1.42857;
				margin-right: 2px;
			}
			##MyTELA .tab-content {
				border: 2px solid ##ddd;
				padding: 10px;
				margin-bottom: 20px;
				background: ##fff;
			}
			##MyTELA .tab-content .tab-pane,##boxCarousel {
				overflow-y: auto;
				min-height: 210px;
				max-height: 210px;
			}
			##MyTELA .nav {
				margin-bottom: 0px;
			}
			##MyTELA .nav-tabs li {
				margin-bottom: 0!important;
			}
			##MyTELA .nav-tabs>li:last-child>a {
				margin-right: auto!important;;
			}
			##MyTELA .nav-tabs a, ##MyTELA .nav-tabs a:hover {
    			color: ##324B64;
				background: ##EBEBEB !important;
				font-weight: lighter;
				text-decoration: none;
				font-family: 'Montserrat';
				text-shadow: none;
				text-transform: uppercase;
			}
			##MyTELA .nav-tabs{
				border-bottom: 0px;
			}
			##MyTELA .carousel,.carousel p{
				margin-bottom: 0!important;
			}
			##MyTELA .carousel {
				display: table-cell;
				vertical-align: middle;
			}
			##MyTELA ##boxCarousel{
				display:table;
				margin: 0 auto;
			}
			##MyTELA ##boxCarousel .carousel-inner>.next, .carousel-inner>.prev{
				top:unset;
			}
			.myInfo .HeaderText{color: ##324B64!important;}
			.renewTextWrap {
				border: 1.4px solid ##324B64;
				padding: 10px;
				margin-bottom: 10px;
				text-align: center;
			}
			
		</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.evhead#">
<cfscript>
	local.arrCustomFields = [];
	
	local.tmpField = { name="box1Title", type="STRING", desc="The tab title for box 1", value="TBD" };
	arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="box1Content", type="CONTENTOBJ", desc="The content for box 1", value="TBD" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="box2Title", type="STRING", desc="The tab title for box 2", value="TBD" };
	arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="box2Content", type="CONTENTOBJ", desc="The content for box 2", value="TBD" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="box3Title", type="STRING", desc="The tab title for box 3", value="TBD" };
	arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="box3Content", type="CONTENTOBJ", desc="The content for box 3", value="TBD" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="box4Title", type="STRING", desc="The tab title for box 4", value="TBD" };
	arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="box4Content", type="CONTENTOBJ", desc="The content for box 4", value="TBD" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="box5Title", type="STRING", desc="The tab title for box 5", value="TBD" };
	arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="box5Content", type="CONTENTOBJ", desc="The content for box 5", value="TBD" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="box6Title", type="STRING", desc="The tab title for box 6", value="TBD" };
	arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="box6Content", type="CONTENTOBJ", desc="The content for box 6", value="TBD" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="box7Title", type="STRING", desc="The tab title for box 7", value="TBD" };
	arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="box7Content", type="CONTENTOBJ", desc="The content for box 7", value="TBD" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="box8Title", type="STRING", desc="The tab title for box 8", value="TBD" };
	arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="box8Content", type="CONTENTOBJ", desc="The content for box 8", value="TBD" };
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="box8bTitle", type="STRING", desc="The tab title for box 8", value="TBD" };
	arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="box8bContent", type="CONTENTOBJ", desc="The content for box 8", value="TBD" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="box9Title", type="STRING", desc="The tab title for box 9", value="TBD" };
	arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="box9Content", type="CONTENTOBJ", desc="The content for box 9", value="TBD" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="RenewMsgContent", type="CONTENTOBJ", desc="The content of the permissioned box at the top of page", value="TBD" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="RenewMessageGroup", type="STRING", desc="UID of group who can view renewal message.", value="4D8C557E-4BF4-40CA-8DD7-5C1DF5845A67" };
	arrayAppend(local.arrCustomFields, local.tmpField);
							
	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);
	
 	local.qryGroupMembership = application.objMember.getMemberGroups(session.cfcuser.memberData.memberID, arguments.event.getValue('mc_siteInfo.ORGID'));

</cfscript>

	<cfset local.qryGroupMembership = application.objMember.getMemberGroups(session.cfcuser.memberData.memberID, arguments.event.getValue('mc_siteInfo.ORGID'))/>
	<cfset local.canViewRenewText = false>
	<cftry>
		
		<cfif len(trim(local.strPageFields.RenewMessageGroup))>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMemberGroup">
				select groupID
				from dbo.ams_groups
				where uid = <cfqueryparam value="#trim(local.strPageFields.RenewMessageGroup)#" cfsqltype="CF_SQL_VARCHAR"> 
			</cfquery>

			<cfquery dbtype="query" name="local.qryIsMemberGroupExist">
				select groupID
				from [local].qryGroupMembership
				where groupID =  <cfqueryparam value="#local.qryMemberGroup.groupID#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>
			<cfif local.qryIsMemberGroupExist.recordCount GT 0>
				<cfset local.canViewRenewText = true>
			</cfif>
		</cfif>
		
		<cfcatch type="any">
		</cfcatch>
	</cftry>

<cfoutput>

	<cfif local.canViewRenewText>
		<div class="renewTextWrap">#local.strPageFields.RenewMsgContent#</div>
	</cfif>

	<div id="MyTELA">
		<div class="row-fluid">			
			<div class="span1 myPhoto">					
				<cfif session.cfcuser.memberData.hasMemberPhotoThumb is 1>
					<img src="/memberphotosth/#LCASE(session.cfcUser.memberData.memberNumber)#.jpg?cb=#getTickCount()#" width="100px" >
				<cfelse>
					<img src="/assets/common/images/directory/default.jpg" width="100px" >
				</cfif>
			</div>
			<div class="span5 myInfo" style="margin-top: 5px;">
				<span class="HeaderText">#session.cfcUser.memberData.firstName# #session.cfcUser.memberData.middleName# #session.cfcUser.memberData.lastName#<cfif len(session.cfcUser.memberData.suffix)>, #session.cfcUser.memberData.suffix#</cfif></span><br />
				<span class="BodyText">
					<ul style="margin-left: 15px;" class="showBullets">
						<li><a href="/?pg=updateMember">Update My Profile</a></li>
						<li><a href="/?pg=updateMember##memberUpdateSectionLoginInfo">Change My Login</a></li>
						<li><a href="/?pg=updatemember&memaction=updatePhoto">Update My Photo</a></li>						
					</ul>
				</span>
			</div>
		</div>
		<div class="row-fluid">
				
			<div class="span4 MyTELARow">
				<ul class="nav nav-tabs" id="box1">
					<li class="active">
						<a href="##box1" data-toggle="tab">#ReReplace(ReReplace(local.strPageFields.box1Title,'<p>',''),'</p>','')#</a>
					</li>
				</ul>
				<div class="tab-content">
					<div class="tab-pane active" id="box1">
						#ReReplace(ReReplace(local.strPageFields.box1Content,'<p>',''),'</p>','')#
					</div>
				</div>
			</div>
			
			<div class="span4 MyTELARow">
				<ul class="nav nav-tabs" id="box2">
					<li class="active">
						<a href="##box2" data-toggle="tab">#ReReplace(ReReplace(local.strPageFields.box2Title,'<p>',''),'</p>','')#</a>
					</li>
				</ul>
				<div class="tab-content">
					<div class="tab-pane active" id="box2">
						#ReReplace(ReReplace(local.strPageFields.box2Content,'<p>',''),'</p>','')#
					</div>
				</div>
			</div>
			
			<div class="span4 MyTELARow">
				<ul class="nav nav-tabs" id="box3">
					<li class="active">
						<a href="##box3" data-toggle="tab">#ReReplace(ReReplace(local.strPageFields.box3Title,'<p>',''),'</p>','')#</a>
					</li>
				</ul>
				<div class="tab-content">
					<div class="tab-pane active" id="box3">									
						#ReReplace(ReReplace(local.strPageFields.box3Content,'<p>',''),'</p>','')#
					</div>
				</div>
			</div>
		</div>
		<div class="row-fluid">				
			<div class="span4 MyTELARow">
				<ul class="nav nav-tabs" id="box4">
					<li class="active">
						<a href="##box4" data-toggle="tab">#ReReplace(ReReplace(local.strPageFields.box4Title,'<p>',''),'</p>','')#</a>
					</li>
				</ul>
				<div class="tab-content">
					<div class="tab-pane active" id="box4">									
						#ReReplace(ReReplace(local.strPageFields.box4Content,'<p>',''),'</p>','')#
					</div>
				</div>
			</div>
			
			<div class="span4 MyTELARow">
				<ul class="nav nav-tabs" id="box5">
					<li class="active">
						<a href="##box5" data-toggle="tab" >#ReReplace(ReReplace(local.strPageFields.box5Title,'<p>',''),'</p>','')#</a>
					</li>
				</ul>
				<div class="tab-content">
					<div class="tab-pane active" id="box5">									
						#ReReplace(ReReplace(local.strPageFields.box5Content,'<p>',''),'</p>','')#
					</div>
				</div>
			</div>
			
			<div class="span4 MyTELARow">
				<ul class="nav nav-tabs" id="box6">
					<li class="active">
						<a href="##box6" data-toggle="tab" >#ReReplace(ReReplace(local.strPageFields.box6Title,'<p>',''),'</p>','')#</a>
					</li>
				</ul>
				<div class="tab-content">
					<div class="tab-pane active" id="box6">									
						#ReReplace(ReReplace(local.strPageFields.box6Content,'<p>',''),'</p>','')#
					</div>
				</div>
			</div>
		</div>
		<div class="row-fluid">				
			<div class="span4 MyTELARow">
				<ul class="nav nav-tabs" id="box7">
					<li class="active">
						<a href="##box7" data-toggle="tab" >#ReReplace(ReReplace(local.strPageFields.box7Title,'<p>',''),'</p>','')#</a>
					</li>
				</ul>
				<div class="tab-content">
					<div class="tab-pane active" id="box7">									
						#ReReplace(ReReplace(local.strPageFields.box7Content,'<p>',''),'</p>','')#
					</div>
				</div>
			</div>
			
			<div class="span4 MyTELARow">
				<ul class="nav nav-tabs" id="box8">
					<li class="active">
						<a href="##box8" data-toggle="tab" >#ReReplace(ReReplace(local.strPageFields.box8Title,'<p>',''),'</p>','')#</a>
					</li>
				</ul>
				<div class="tab-content">
					<div class="tab-pane active" id="box8">									
						#ReReplace(ReReplace(local.strPageFields.box8Content,'<p>',''),'</p>','')#
					</div>
				</div>
			</div>
			
			<div class="span4 MyTELARow">
				<ul class="nav nav-tabs" id="box9">
					<li class="active">
						<a href="##box9" data-toggle="tab" >#ReReplace(ReReplace(local.strPageFields.box9Title,'<p>',''),'</p>','')#</a>
					</li>			
				</ul>
				<div class="tab-content">
					<div class="tab-pane active" id="box9">
						<div id="boxCarousel">
							<div id="box9ContentCarousel" class="box9ContentCarousel carousel slide text-center">
								<!-- Carousel items -->								
								<div class="carousel-inner text-center">
									<cfif structKeyExists(local.strPageFields,"box9Content") AND len(trim(local.strPageFields.box9Content))>
										<cfloop list="#local.strPageFields.box9Content#" index="local.thisImage" delimiters="|">
											<div class="<cfif ListFirst(local.strPageFields.box9Content,'|') eq local.thisImage>active </cfif>item">
												<p>#ReReplace(ReReplace(local.thisImage,'<p>',''),'</p>','')#</p>
											</div>
										</cfloop>
									</cfif>
								</div>
							</div>
							<cfif structKeyExists(local.strPageFields,"box9Content") AND len(trim(local.strPageFields.box9Content))>
								<script type='text/javascript'>
									$(document).ready(function() {
										$('.box9ContentCarousel').carousel({
											interval: 3000
										})
									});    
								</script>
							</cfif>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</cfoutput>