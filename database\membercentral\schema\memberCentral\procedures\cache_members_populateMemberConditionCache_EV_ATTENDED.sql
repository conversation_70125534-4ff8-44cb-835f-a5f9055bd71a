ALTER PROC dbo.cache_members_populateMemberConditionCache_EV_ATTENDED

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	declare @orgID int;
	select top 1 @orgID = orgID from #tblCondALL;

	-- conditions with specific events selected
	insert into #cache_members_conditions_shouldbe
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblEvSplit as evsplit on evsplit.conditionID = tblc.conditionID
	inner join #tblEvSplitEvents as evevents on evevents.conditionID = evsplit.conditionID
	inner join dbo.ev_events as e on e.eventID = evevents.eventID
	inner join dbo.ev_registration as er on er.eventID = e.eventID and er.status = 'A' and er.siteID = e.siteID
	inner join dbo.ev_registrants as reg on reg.registrationID = er.registrationID and reg.status = 'A'
		and reg.attended = 1
	inner join dbo.ams_members as m2 on m2.memberid = reg.memberid and m2.orgID = @orgID
	inner join #tblMCQCondCacheMem as m on m.memberID = m2.activeMemberID
	where tblc.subProc = 'EV_ATTENDED'
	and evsplit.evFilterOption = 'event'
	and 1 = case
		when evsplit.evRoleIDs is null then 1
		when evsplit.evRoleIDs = '0' and not exists (select 1 from dbo.ev_registrantCategories where registrantID = reg.registrantID) then 1
		when exists (select 1 from #tblEvSplitRoles as evRoles inner join dbo.ev_registrantCategories as rc on rc.categoryID = evRoles.categoryID 
					where evRoles.conditionID = tblc.conditionID and rc.registrantID = reg.registrantID and evRoles.hasRolesFilter = 1 and evRoles.hasNoRoleFilter = 0) then 1
		when exists (select 1 from #tblEvSplitRoles as evRoles where evRoles.conditionID = tblc.conditionID and evRoles.hasRolesFilter = 1 and evRoles.hasNoRoleFilter = 1)
			and (
				exists (select 1 from #tblEvSplitRoles as evRoles inner join dbo.ev_registrantCategories as rc on rc.categoryID = evRoles.categoryID 
						where evRoles.conditionID = tblc.conditionID and rc.registrantID = reg.registrantID)
				or not exists (select 1 from dbo.ev_registrantCategories where registrantID = reg.registrantID)
			)
			then 1 
		else 0
		end
	OPTION (RECOMPILE);


	-- conditions without specific events selected
	insert into #cache_members_conditions_shouldbe
	select distinct m.memberid, tblc.conditionID
	from #tblCondALL as tblc
	inner join #tblEvSplit as evsplit on evsplit.conditionID = tblc.conditionID
	inner join dbo.ev_events as e on e.siteID = evsplit.evSiteID and e.status in ('A','I')
	inner join dbo.cache_calendarEvents as cache on cache.eventID = e.eventID and cache.calendarID = cache.sourceCalendarID
	inner join dbo.ev_registration as er on er.eventID = e.eventID and er.status = 'A' and er.siteID = e.siteID
	inner join dbo.ev_registrationTypes as ert on er.registrationTypeID = ert.registrationTypeID and ert.registrationTypeID = 1
	inner join dbo.sites as s on s.siteID = evsplit.evSiteID
	inner join dbo.ev_times as times on times.eventID = e.eventID and times.timeZoneID = s.defaultTimeZoneID
	left outer join dbo.ev_times as lockTimes
		inner join dbo.timeZones as lockTimeZones on lockTimeZones.timeZoneID = lockTimes.timeZoneID
		on lockTimes.eventID = e.eventID and lockTimes.timeZoneID = e.lockTimeZoneID
	inner join dbo.cms_contentLanguages as ec on ec.contentID = e.eventContentID and ec.languageID = 1
	inner join dbo.ev_registrants as reg on reg.registrationID = er.registrationID and reg.status = 'A'
		and reg.attended = 1
	inner join dbo.ams_members as m2 on m2.memberid = reg.memberid and m2.orgID = @orgID
	inner join #tblMCQCondCacheMem as m on m.memberID = m2.activeMemberID
	where tblc.subProc = 'EV_ATTENDED'
	and evsplit.evFilterOption = 'filter'
	and 1 = case
		when evsplit.evCalendarIDs is null then 1
		when cache.calendarID in (select calendarID from #tblEvSplitCalendars where conditionID = tblc.conditionID) then 1
		else 0
		end
	and 1 = case
		when evsplit.evCategoryIDs is null then 1
		when cache.categoryID in (select categoryID from #tblEvSplitCategories where conditionID = tblc.conditionID) then 1
		else 0
		end
	and 1 = case
		when evsplit.evRoleIDs is null then 1
		when evsplit.evRoleIDs = '0' and not exists (select 1 from dbo.ev_registrantCategories where registrantID = reg.registrantID) then 1
		when exists (select 1 from #tblEvSplitRoles as evRoles inner join dbo.ev_registrantCategories as rc on rc.categoryID = evRoles.categoryID 
					where evRoles.conditionID = tblc.conditionID and rc.registrantID = reg.registrantID and evRoles.hasRolesFilter = 1 and evRoles.hasNoRoleFilter = 0) then 1
		when exists (select 1 from #tblEvSplitRoles as evRoles where evRoles.conditionID = tblc.conditionID and evRoles.hasRolesFilter = 1 and evRoles.hasNoRoleFilter = 1)
			and (
				exists (select 1 from #tblEvSplitRoles as evRoles inner join dbo.ev_registrantCategories as rc on rc.categoryID = evRoles.categoryID 
						where evRoles.conditionID = tblc.conditionID and rc.registrantID = reg.registrantID)
				or not exists (select 1 from dbo.ev_registrantCategories where registrantID = reg.registrantID)
			)
			then 1 
		else 0
		end
	and 1 = case
		when evsplit.evEventTitle = '' then 1
		when ec.contentTitle like '%'+replace(evsplit.evEventTitle,'_','\_')+'%' ESCAPE('\') then 1
		else 0
		end
	and 1 = case
		when evsplit.evEventType = 'main' and NOT EXISTS (select se.eventID from dbo.ev_subevents se where se.eventID = e.eventID) then 1
		when evsplit.evEventType = 'sub' and EXISTS (select se.eventID from dbo.ev_subevents se where se.eventID = e.eventID) then 1
		when evsplit.evEventType is null or evsplit.evEventType = 'all' then 1
		else 0
		end
	and 1 = case
		when evsplit.evReportCode = '' then 1
		when e.reportCode = evsplit.evReportCode then 1
		else 0
		end
	and 1 = case
		when evsplit.evStartLower is null and evsplit.evStartUpper is null then 1
		when evsplit.evStartLower is not null and evsplit.evStartUpper is not null and isnull(lockTimes.startTime,times.startTime) between evsplit.evStartLower and evsplit.evStartUpper then 1
		when evsplit.evStartLower is not null and evsplit.evStartUpper is null and isnull(lockTimes.startTime,times.startTime) >= evsplit.evStartLower then 1
		when evsplit.evStartLower is null and evsplit.evStartUpper is not null and isnull(lockTimes.startTime,times.startTime) <= evsplit.evStartUpper then 1
		else 0
		end
	OPTION (RECOMPILE);
		
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
