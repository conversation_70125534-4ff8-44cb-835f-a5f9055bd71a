----------------------------------------------------------------------------------------------------
/* Query #41: PASSED */
ALTER PROCEDURE [dbo].[sw_AlterRetentionPolicy]
(
    @RetentionPolicy NVARCHAR(128) = NULL,
    @Table NVARCHAR(128) = NULL,
    @Help BIT = 0
)
AS 
BEGIN

    IF @RetentionPolicy IS NULL OR @Table IS NULL OR @Help = 1
    BEGIN
        SELECT 'See Messages Tab for Help Info'
        SELECT 'Current Retention Policy'
        SELECT 
        OBJECT_NAME([major_id]) AS [Table],
        CAST([value] AS SMALLINT) AS [Retention Policy (Days)]
        FROM [SQLWatchmen].[sys].[tables] AS t 
        JOIN [SQLWatchmen].[sys].[extended_properties] AS ep ON ep.[major_id] = t.[object_id] 
        WHERE ep.[name] = N'Retention Policy'
        
        PRINT 
            'Stored Procedure sw_AlterRetentionPolicy' + CHAR(13) +
            '-------------------------------------------------------------------------------' + CHAR(13) +
            'Purpose: ' + CHAR(13) +
            CHAR(9) + 'To alter a table''s retention policy.' + CHAR(13) +
            'Parameters: ' + CHAR(13) +
            CHAR(9) + '@RetentionPolicy NVARCHAR(128) - The retention policy in days.' + CHAR(13) +
            CHAR(9) + '@Table NVARCHAR(128) - The table whose retention policy you want to change.' + CHAR(13) +
            CHAR(9) + '@Help BIT - Set to 1 to display this help message.' + CHAR(13) +
            'Note: ' + CHAR(13) +
            CHAR(9) + 'Only the tables shown in the select tab can have their retention policies changed using this stored procedure.' + CHAR(13) +
            CHAR(9) + 'System tables and CommandLog table are controlled by parameters in the Parameters table which can be changed using sw_AlterParameter' 
        RETURN
    END

    SELECT 
    'Previous Retention Policy',
    OBJECT_NAME([major_id]) AS [Table],
    CAST([value] AS SMALLINT) AS [Retention Policy (Days)]
    FROM [SQLWatchmen].[sys].[tables] AS t 
    JOIN [SQLWatchmen].[sys].[extended_properties] AS ep ON ep.[major_id] = t.[object_id] 
    WHERE ep.[name] = N'Retention Policy'
    AND OBJECT_NAME([major_id]) = @Table

    EXEC [SQLWatchmen].[dbo].[sp_updateextendedproperty] 
        @name = 'Retention Policy',
        @value = @RetentionPolicy,
        @level0type = N'SCHEMA', @level0name = N'dbo',
        @level1type = N'TABLE', @level1name = @Table

    SELECT 
    'New Retention Policy',
    OBJECT_NAME([major_id]) AS [Table],
    CAST([value] AS SMALLINT) AS [Retention Policy (Days)]
    FROM [SQLWatchmen].[sys].[tables] AS t 
    JOIN [SQLWatchmen].[sys].[extended_properties] AS ep ON ep.[major_id] = t.[object_id] 
    WHERE ep.[name] = N'Retention Policy'
    AND OBJECT_NAME([major_id]) = @Table

END
GO
