CREATE PROC dbo.queue_scheduledReport_grabForProcessing
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @nowDate datetime = getdate();
	EXEC dbo.queue_getQueueTypeID @queueType='scheduledReport', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	IF OBJECT_ID('tempdb..#tmpScheduledReport') IS NOT NULL
		DROP TABLE #tmpScheduledReport;
	CREATE TABLE #tmpScheduledReport (queueItemID int, srItemID int, reportID int, isReset bit, resetByMemberID int);

	-- dequeue in order of dateAdded.
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID, inserted.srItemID, inserted.reportID, inserted.isReset, inserted.resetByMemberID
		INTO #tmpScheduledReport
	FROM dbo.queue_scheduledReport as qid
	INNER JOIN (
		SELECT TOP 1 q.itemID 
		FROM dbo.queue_scheduledReport as q
		INNER JOIN membercentral.dbo.rpt_scheduledReports as r ON r.itemID = q.srItemID
			AND r.isRunning = 0
			AND len(r.toEmail) > 0
		WHERE q.statusID = @statusReady
		ORDER BY q.dateAdded, q.itemID
	) as batch on batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	-- mark scheduled report as running
	UPDATE r WITH (UPDLOCK, READPAST)
	SET r.isRunning = 1,
		r.dateLastUpdated = @nowDate	
	FROM #tmpScheduledReport AS tmp
	INNER JOIN membercentral.dbo.rpt_scheduledReports as r ON r.itemID = tmp.srItemID;

	-- return the report info
	SELECT tmp.queueItemID, tmp.srItemID, rpt.reportID, tmp.isReset, s.siteCode, tt.toolCFC, 
		r.reportAction, rpt.reportName, rpt.[uid] as reportUID, r.[fileName], r.toEmail, r.emailbody, 
		concat(m2.firstname,' ',m2.lastname) as resetByName
	FROM #tmpScheduledReport AS tmp
	INNER JOIN membercentral.dbo.rpt_scheduledReports as r ON r.itemID = tmp.srItemID
	INNER JOIN membercentral.dbo.rpt_SavedReports AS rpt ON rpt.reportID = r.reportID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = r.siteID
	INNER JOIN membercentral.dbo.admin_toolTypes AS tt ON tt.toolTypeID = rpt.toolTypeID
	LEFT OUTER JOIN membercentral.dbo.ams_members as m 
		INNER JOIN membercentral.dbo.ams_members as m2 on m2.orgID = m.orgID and m2.memberID = m.activeMemberID
		on m.memberID = tmp.resetByMemberID and tmp.isReset = 1;

	IF OBJECT_ID('tempdb..#tmpScheduledReport') IS NOT NULL
		DROP TABLE #tmpScheduledReport;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
