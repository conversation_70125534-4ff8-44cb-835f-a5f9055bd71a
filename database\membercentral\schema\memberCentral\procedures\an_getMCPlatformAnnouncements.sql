CREATE PROC dbo.an_getMCPlatformAnnouncements

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @centerID int;

	SELECT TOP 1 @centerID = c.centerID
	FROM dbo.an_centers AS c
	INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = c.applicationInstanceID
	WHERE ai.siteID = 1
	AND ai.applicationInstanceName = 'MCPlatformAnnouncements';

	EXEC dbo.an_getActiveNotices @centerID=@centerID, @languageID=1, @siteID=1, @memberID=0;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO