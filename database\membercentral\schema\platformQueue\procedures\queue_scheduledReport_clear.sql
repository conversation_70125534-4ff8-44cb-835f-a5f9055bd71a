CREATE PROC dbo.queue_scheduledReport_clear
@status varchar(60)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	delete qi
	from dbo.queue_scheduledReport as qi
	inner join dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.statusID and qs.queueStatus = @status;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO