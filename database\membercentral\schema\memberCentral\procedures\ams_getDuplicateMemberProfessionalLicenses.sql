CREATE PROC dbo.ams_getDuplicateMemberProfessionalLicenses
@orgID int,
@limitRowsCount int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @totalCount int;

	IF OBJECT_ID('tempdb..#tmpDupeProLicenses') IS NOT NULL 
		DROP TABLE #tmpDupeProLicenses;
	CREATE TABLE #tmpDupeProLicenses (PLName varchar(114), licenseNumber varchar(200), memberID int, rowID int);

	INSERT INTO #tmpDupeProLicenses (PLName, licenseNumber, memberID, rowID)
	SELECT mplt2.PLName, tmp.licenseNumber, m2.memberID,
		ROW_NUMBER() OVER(order by mplt2.PLName, tmp.licenseNumber, m2.lastname, m2.firstname, m2.membernumber)
	FROM (
		SELECT mpl.PLTypeID, mpl.licenseNumber
		FROM dbo.ams_memberProfessionalLicenseTypes AS mplt
		INNER JOIN dbo.ams_memberProfessionalLicenses AS mpl on mpl.PLTypeID = mplt.PLTypeID
			AND NULLIF(mpl.licenseNumber,'') IS NOT NULL
		INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID 
			AND m.memberID = mpl.memberID
			AND m.[status] <> 'D'
		WHERE mplt.orgID = @orgID
		AND mplt.alertDuplicates = 1
		GROUP BY mpl.PLTypeID, mpl.licenseNumber
		HAVING COUNT(*) > 1
	) AS tmp
	INNER JOIN dbo.ams_memberProfessionalLicenseTypes AS mplt2 ON mplt2.PLTypeID = tmp.PLTypeID
		AND mplt2.orgID = @orgID
	INNER JOIN dbo.ams_memberProfessionalLicenses AS mpl2 ON mpl2.plTypeID = tmp.PLTypeID
		AND mpl2.licenseNumber = tmp.licenseNumber
	INNER JOIN dbo.ams_members AS m2 ON m2.memberID = mpl2.memberID
		AND m2.memberID = m2.activeMemberID
		AND m2.[status] <> 'D'
		AND m2.orgID = @orgID;

	SET @totalCount = @@ROWCOUNT;

	SELECT tmp.PLName, tmp.licenseNumber, m.memberID, m.lastname + ', ' + m.firstname + ' (' + m.membernumber + ')' AS memberName, @totalCount AS totalCount
	FROM #tmpDupeProLicenses AS tmp
	INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID
		AND m.memberID = tmp.memberID
	WHERE tmp.rowID <= @limitRowsCount
	ORDER BY tmp.rowID;

	IF OBJECT_ID('tempdb..#tmpDupeProLicenses') IS NOT NULL 
		DROP TABLE #tmpDupeProLicenses;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO