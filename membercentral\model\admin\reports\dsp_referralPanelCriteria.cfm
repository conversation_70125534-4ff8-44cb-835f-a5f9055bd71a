<cfsavecontent variable="local.dataHead">
	<cfoutput>
	<script language="JavaScript">
		let rptReferralPanelsTable;

		function initRptReferralPanelsTable() {
			let domString = "<'row'<'col-sm-3 col-md-3'l><'col-sm-9 col-md-9'f>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";
			rptReferralPanelsTable = $('##rptReferralPanelsTable').DataTable({
				"processing": true,
				"serverSide": true,
				"pageLength": 10,
				"lengthMenu": [ 10, 20, 50, 100 ],
				"language": {
					"lengthMenu": "_MENU_",
					"emptyTable": "No Panels Selected"
				},
				"dom": domString,
				"ajax": { 
					"url": "#local.referralPanelList#",
					"type": "post"
				},
				"autoWidth": false,
				"columns": [
					{ "data": "thePathExpanded" },
					<cfif arguments.event.getValue('rohi',0) NEQ 1>
					{ "data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {						
								renderData += '<a href="javascript:removeReferralPanelFromReport('+ data.panelID+')" id="btnRemRptPanel'+data.panelID+'" class="btn btn-sm p-1 text-danger"><i class="fa-solid fa-trash-can"></i></a>';
							}
							return type === 'display' ? renderData : data;
						},
						"width": "10%",
						"className": "text-center",
						"orderable": false
					}
					</cfif>
				],
				"order": [[0, 'asc']]
			});
			<cfif arguments.event.getValue('rohi',0) NEQ 1>
				$( '##rptReferralPanelsTable_filter' ).append('<button type="button" name="btnAddCategory" class="btn btn-sm btn-secondary ml-4" onclick="addReferralPanelToReport();"><span class="btn-wrapper--icon"><i class="fa-regular fa-circle-plus"></i></span><span class="btn-wrapper--label">Add Referral Panel</span></button>');
			</cfif>
		}
		function reloadReferralPanelFilter() { 
			rptReferralPanelsTable.draw();
			top.rptHideAlert(); 
			top.$('##divReportShowScreen').html('').hide(); 
		}
		function addReferralPanelToReport() {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Select Referral Panels',
				iframe: true,
				contenturl: '#this.link.addReferralPanel#&rptId=#local.reportID#'
			});
		}
		function removeReferralPanelFromReport(rpid){
			var removeRptPanelResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					reloadReferralPanelFilter();
				} else {
					delCFElement.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
					alert('Unable to remove referral panel.');
				}	
			};

			let delCFElement = $('##btnRemRptPanel'+rpid);
			mca_initConfirmButton(delCFElement, function(){
				var objParams = { rptId:#local.reportID#, rptTT:'#local.reportTT#', rpid:rpid, csrID:#this.siteResourceID# };
				TS_AJX('ADMREPORTS','removeReferralPanelFilter',objParams,removeRptPanelResult,removeRptPanelResult,10000,removeRptPanelResult);
			});
		}

		$(function() {
			initRptReferralPanelsTable();
		});
	</script>
	</cfoutput>
</cfsavecontent>		
<cfhtmlhead text="#local.dataHead#">

<cfoutput>	
<div class="stepDIV mb-3">
	<cfif len(arguments.title)><h5>#arguments.title#</h5></cfif>
	<cfif len(arguments.desc)><div class="mb-2">#arguments.desc#</div></cfif>
	<table id="rptReferralPanelsTable" class="table table-sm table-striped table-bordered" style="width:100%">
		<thead>
			<tr>
				<th>Referral Panel</th>
				<cfif arguments.event.getValue('rohi',0) NEQ 1><th>Tools</th></cfif>
			</tr>
		</thead>
	</table>
</div>
</cfoutput>
