CREATE PROC dbo.ams_getInvalidMemberLinkedRecords
@orgID int,
@limitRowsCount int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @totalCount int;

	IF OBJECT_ID('tempdb..#tmpInvalidLinks') IS NOT NULL
		DROP TABLE #tmpInvalidLinks;
	CREATE TABLE #tmpInvalidLinks (RelationMasterRecordType varchar(100), RelationChildRecordType varchar(100),
		RelationTypeName varchar(100), ActualMasterRecordType varchar(100), ActualChildRecordType varchar(100),
		masterMemberID int, masterMemberName varchar(205), masterCompany varchar(200), childMemberID int, 
		childMemberName varchar(205), childCompany varchar(200));

	INSERT INTO #tmpInvalidLinks (RelationMasterRecordType, RelationChildRecordType, RelationTypeName,
		ActualMasterRecordType, ActualChildRecordType, masterMemberID, masterMemberName, masterCompany,
		childMemberID, childMemberName, childCompany)
	SELECT 
		ISNULL(rtMaster.recordTypeName,'[Unassigned]') AS RelationMasterRecordType,
		ISNULL(rtChild.recordTypeName,'[Unassigned]') AS RelationChildRecordType,
		rrt.relationshipTypeName AS RelationTypeName,
		ISNULL(rtActualMaster.recordTypeName,'[Unassigned]') AS ActualMasterRecordType,
		ISNULL(rtActualChild.recordTypeName,'[Unassigned]') AS ActualChildRecordType,
		mMaster.memberID AS masterMemberID,
		mMaster.lastname + ', ' + mMaster.firstname + ' (' + mMaster.membernumber + ')' as masterMemberName,
		mMaster.Company AS masterCompany,
		mChild.memberID AS childMemberID,
		mChild.lastname + ', ' + mChild.firstname + ' (' + mChild.membernumber + ')' as childMemberName,
		mChild.Company AS childCompany
	FROM dbo.ams_recordRelationships AS rr
	INNER JOIN dbo.ams_recordTypesRelationshipTypes AS rtrt ON rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID
	INNER JOIN dbo.ams_recordTypes AS rtMaster ON rtMaster.recordTypeID = rtrt.masterRecordTypeID
		AND rtMaster.orgID = @orgID
	INNER JOIN dbo.ams_recordTypes AS rtChild ON rtChild.recordTypeID = rtrt.childRecordTypeID
		AND rtChild.orgID = rtMaster.orgID
	INNER JOIN dbo.ams_recordRelationshipTypes AS rrt ON rrt.relationshipTypeID = rtrt.relationshipTypeID
		AND rrt.orgID = rtMaster.orgID
	INNER JOIN dbo.organizations AS o ON o.orgID = rrt.orgID
	INNER JOIN dbo.ams_members AS mMaster ON mMaster.activememberID = rr.masterMemberID
		AND mMaster.[status] <> 'D'
		AND mMaster.orgID = o.orgID
	LEFT OUTER JOIN dbo.ams_recordTypes AS rtActualMaster ON rtActualMaster.recordTypeID = mMaster.RecordTypeID
		AND rtActualMaster.orgID = o.orgID
	INNER JOIN dbo.ams_members AS mChild ON mChild.activememberID = rr.childMemberID
		AND mChild.[status] <> 'D'
		AND mChild.orgID = o.orgID
	LEFT OUTER JOIN dbo.ams_recordTypes AS rtActualChild ON rtActualChild.recordTypeID = mChild.RecordTypeID
		AND rtActualChild.orgID = o.orgID
	WHERE rr.isActive = 1
	AND rr.orgID = @orgID
	AND rtrt.isActive = 1
	AND (rtrt.masterRecordTypeID <> ISNULL(mMaster.recordTypeID,0) OR rtrt.childRecordTypeID <> ISNULL(mChild.recordTypeID,0));

	SELECT @totalCount = @@ROWCOUNT;

	WITH orderedRows AS (
		SELECT RelationMasterRecordType, RelationChildRecordType, RelationTypeName, ActualMasterRecordType, ActualChildRecordType,
			masterMemberID, masterMemberName, masterCompany, childMemberID, childMemberName, childCompany,
			ROW_NUMBER() OVER(ORDER BY RelationTypeName, RelationChildRecordType, RelationMasterRecordType, masterMemberName, childMemberName) AS rowID
		FROM #tmpInvalidLinks
	)
	SELECT RelationMasterRecordType, RelationChildRecordType, RelationTypeName, ActualMasterRecordType, ActualChildRecordType,
		masterMemberID, masterMemberName, masterCompany, childMemberID, childMemberName, childCompany, @totalCount AS totalCount
	FROM orderedRows
	WHERE rowID <= @limitRowsCount
	ORDER BY rowID;

	IF OBJECT_ID('tempdb..#tmpInvalidLinks') IS NOT NULL
		DROP TABLE #tmpInvalidLinks;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO