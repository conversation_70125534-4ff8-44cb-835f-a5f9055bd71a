----------------------------------------------------------------------------------------------------
/* Query #54: PASSED */
ALTER PROCEDURE [dbo].[sw_SendPageEmailCheck] 
(
	@MailProfileName varchar(128) = NULL,
	@PageEmail varchar(128) = NULL,
	@Company varchar(128) = NULL,
	@Configuration varchar(128) = NULL
)
AS
BEGIN
    SET NOCOUNT ON

    IF @MailProfileName IS NULL
    BEGIN
        SET @MailProfileName = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'MailProfileName')
    END

    IF @PageEmail IS NULL
    BEGIN
        SET @PageEmail = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'PageEmail')
    END

    IF @Company IS NULL
    BEGIN
        SET @Company = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Company')
    END

    IF @Configuration IS NULL
    BEGIN
        SET @Configuration = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Configuration')
    END

    DECLARE @Subject NVARCHAR(255)
    DECLARE @Body NVARCHAR(MAX)

    SET @Subject = N'Page Email Check | ' + @Configuration + N' | ' + @Company
    SET @Body = 'Page Email Check' + CHAR(13) + CHAR(10) +
        'Configuration: ' + @Configuration + CHAR(13) + CHAR(10) +
        'Company: ' + @Company + CHAR(13) + CHAR(10) +
        'Mail Profile Used: ' + @MailProfileName + CHAR(13) + CHAR(10) +
        'Page Email Address: ' + @PageEmail 

    EXEC msdb.dbo.sp_send_dbmail
        @profile_name = @MailProfileName,
        @recipients = @PageEmail,
        @subject = @Subject,
        @body = @Body

    INSERT INTO [SQLWatchmen].[dbo].[AlertLog] ([Alert]) VALUES ('Page Check')

END
GO
