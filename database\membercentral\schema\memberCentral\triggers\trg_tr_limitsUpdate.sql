CREATE TRIGGER trg_tr_limitsUpdate ON dbo.tr_limits
AFTER UPDATE 
AS 

SET NOCOUNT ON
BEGIN TRY

	IF NOT EXISTS (SELECT * FROM inserted) RETURN;

	-- when changing the amount of a GL in a limit schedule, flag all transactions in this GL and all GLs that follow for the scheduled date range.
	IF UPDATE(maxAmount) BEGIN
		INSERT INTO platformQueue.dbo.queue_transactionLimitFlagging (transactionID, orgID, siteID)
		select t.transactionID, ls.orgID, t.recordedOnSiteID
		from dbo.tr_limits as l
		INNER JOIN Inserted as I ON l.limitID = I.limitID
		INNER JOIN Deleted as D ON l.limitID = D.limitID
		INNER JOIN dbo.tr_limitSchedules as ls on ls.scheduleID = l.scheduleID
		INNER JOIN dbo.tr_transactions as t on t.ownedByOrgID = ls.orgID 
			AND t.creditGLAccountID in (
				select l.glaccountID
					union
				select l2.glaccountID
				from dbo.tr_limits as l2
				where l2.scheduleID = l.scheduleID
				and l2.glOrder > l.glOrder
			)
		WHERE D.maxAmount <> I.maxAmount
		and t.dateRecorded between ls.startdate and ls.enddate;
	END

	-- When changing the order of GLs in a limit schedule, flag all transactions in either of the GLs affected for the schedule date range.
	IF UPDATE(glOrder) BEGIN
		INSERT INTO platformQueue.dbo.queue_transactionLimitFlagging (transactionID, orgID, siteID)
		select t.transactionID, ls.orgID, t.recordedOnSiteID
		from dbo.tr_limits as l
		INNER JOIN Inserted as I ON l.limitID = I.limitID
		INNER JOIN Deleted as D ON l.limitID = D.limitID
		INNER JOIN dbo.tr_limitSchedules as ls on ls.scheduleID = l.scheduleID
		INNER JOIN dbo.tr_transactions as t on t.ownedByOrgID = ls.orgID AND t.creditGLAccountID = l.glaccountID
		WHERE D.glOrder <> I.glOrder
		and t.dateRecorded between ls.startdate and ls.enddate;
	END

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO
