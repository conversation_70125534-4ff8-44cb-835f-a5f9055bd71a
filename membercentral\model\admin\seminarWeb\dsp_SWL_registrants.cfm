<cfoutput>
<div class="toolButtonBar">
	<div><a href="javascript:filterSWLRegistrants();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter registrants."><i class="fa-regular fa-filter"></i> Filter Registrants</a></div>
	<div><a href="javascript:exportSWLRegistrants();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to download registrants."><i class="fa-regular fa-download"></i> Download Registrants</a></div>
</div>

<div id="divFilterRegistrantsForm" class="d-none">
	<div class="mt-2 mb-4">
		<form name="frmRegistrantFilter" id="frmRegistrantFilter" onsubmit="dofilterSWLRegistrants(); return false;">
			<div class="row">
				<div class="col">
					<div class="card card-box">
						<div class="card-header py-1 bg-light">
							<div class="card-header--title font-weight-bold font-size-lg">Registrant Search</div>
						</div>
						<div class="card-body">
							<div class="row">
								<div class="col-sm-12 font-weight-bold">Program Filters</div>
							</div>
							<div class="row mt-2">
								<div class="col-md-6">
									<div class="card card-box">
										<div class="card-body p-3">
											<div class="form-row">
												<div class="col-xl-6 col-md-12">
													<div class="form-group">
														<div class="form-label-group">
															<div class="input-group dateFieldHolder">
																<input type="text" name="frpDateFrom" id="frpDateFrom" value="#local.pDateFrom#" class="form-control dateControl">
																<div class="input-group-append">
																	<span class="input-group-text cursor-pointer calendar-button" data-target="frpDateFrom"><i class="fa-solid fa-calendar"></i></span>
																	<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('frpDateFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
																</div>
																<label for="frpDateFrom">Start Date From</label>
															</div>
														</div>
													</div>
												</div>

												<div class="col-xl-6 col-md-12">
													<div class="form-group">
														<div class="form-label-group">
															<div class="input-group dateFieldHolder">
																<input type="text" name="frpDateTo" id="frpDateTo" value="#local.pDateTo#" class="form-control dateControl">
																<div class="input-group-append">
																	<span class="input-group-text cursor-pointer calendar-button" data-target="frpDateTo"><i class="fa-solid fa-calendar"></i></span>
																	<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('frpDateTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
																</div>
																<label for="frpDateTo">Start Date To</label>
															</div>
														</div>
													</div>
												</div>
											</div>

											<div class="row mt-0">
												<div class="col-sm-3 pr-sm-0 font-weight-bold">
													Next
												</div>
												<div class="col-sm-9 pl-sm-0">
													<a href="javascript:quickFilterSWLRegPrograms(7);" class="badge badge-neutral-second text-second mr-1">1w</a>
													<a href="javascript:quickFilterSWLRegPrograms(14);" class="badge badge-neutral-second text-second mr-1">2w</a>
													<a href="javascript:quickFilterSWLRegPrograms(21);" class="badge badge-neutral-second text-second mr-1">3w</a>
													<a href="javascript:quickFilterSWLRegPrograms(30);" class="badge badge-neutral-second text-second mr-1">1m</a>
													<a href="javascript:quickFilterSWLRegPrograms(60);" class="badge badge-neutral-second text-second mr-1">2m</a>
													<a href="javascript:quickFilterSWLRegPrograms(90);" class="badge badge-neutral-second text-second mr-1">3m</a>
													<a href="javascript:quickFilterSWLRegPrograms(180);" class="badge badge-neutral-second text-second mr-1">6m</a>
													<a href="javascript:quickFilterSWLRegPrograms(365);" class="badge badge-neutral-second text-second">1y</a>
												</div>
											</div>
											<div class="row mt-1">
												<div class="col-sm-3 pr-sm-0 font-weight-bold">
													Last
												</div>
												<div class="col-sm-9 pl-sm-0">
													<a href="javascript:quickFilterSWLRegPrograms(-7);" class="badge badge-neutral-second text-second mr-1">1w</a>
													<a href="javascript:quickFilterSWLRegPrograms(-14);" class="badge badge-neutral-second text-second mr-1">2w</a>
													<a href="javascript:quickFilterSWLRegPrograms(-21);" class="badge badge-neutral-second text-second mr-1">3w</a>
													<a href="javascript:quickFilterSWLRegPrograms(-30);" class="badge badge-neutral-second text-second mr-1">1m</a>
													<a href="javascript:quickFilterSWLRegPrograms(-60);" class="badge badge-neutral-second text-second mr-1">2m</a>
													<a href="javascript:quickFilterSWLRegPrograms(-90);" class="badge badge-neutral-second text-second mr-1">3m</a>
													<a href="javascript:quickFilterSWLRegPrograms(-180);" class="badge badge-neutral-second text-second mr-1">6m</a>
													<a href="javascript:quickFilterSWLRegPrograms(-365);" class="badge badge-neutral-second text-second">1y</a>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div class="col-md-6 pt-2">
									<div class="form-row">
										<div class="col-xl-6 col-md-12">
											<div class="form-group">
												<div class="form-label-group mb-2">
													<input type="text" name="frpKeyword" id="frpKeyword" class="form-control" value="">
													<label for="frpKeyword">Program Name Contains...</label>
												</div>
											</div>
										</div>
										<div class="col-xl-6 col-md-12">
											<div class="form-group">
												<div class="form-label-group mb-2">
													<input type="text" name="frpProgramCode" id="frpProgramCode" class="form-control" value="">
													<label for="frpProgramCode">Program Code</label>
												</div>
											</div>
										</div>
									</div>
									<div class="form-row">
										<cfif local.participantData.handlesOwnPayment is 0>
											<div class="col-xl-6 col-md-12">
												<div class="form-group">
													<div class="form-label-group mb-2">
														<select name="frpPubType" id="frpPubType" class="form-control">
															<option value="PO">Publisher or Opted-In</option>
															<option value="P">Publisher</option>
															<option value="O">Opted-In</option>
														</select>
														<label for="frpPubType">Publisher/Opted-In</label>
													</div>
												</div>
											</div>
										<cfelse>
											<input type="hidden" name="frpPubType" value="P">
										</cfif>
										<div class="col-xl-6 col-md-12">
											<div class="form-group">
												<div class="form-label-group mb-2">
													<select name="frpStatus" id="frpStatus" class="form-control">
														<option value="1">Hide Inactive Programs</option>
														<option value="0">Show Inactive Programs</option>
													</select>
													<label for="frpStatus">Program Status</label>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>

							<div class="row mt-4">
								<div class="col-sm-12 font-weight-bold">Registrant Filters</div>
							</div>
							<div class="row mt-2">
								<div class="col-md-6">
									<div class="card card-box" style="height:100%">
										<div class="card-body p-3">
											<div class="form-row">
												<div class="col-xl-6 col-md-12">
													<div class="form-group">
														<div class="form-label-group">
															<div class="input-group dateFieldHolder">
																<input type="text" name="frrDateFrom" id="frrDateFrom" value="#local.rDateFrom#" class="form-control dateControl">
																<div class="input-group-append">
																	<span class="input-group-text cursor-pointer calendar-button" data-target="frrDateFrom"><i class="fa-solid fa-calendar"></i></span>
																	<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('frrDateFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
																</div>
																<label for="frrDateFrom">Registered From</label>
															</div>
														</div>
													</div>
												</div>
												<div class="col-xl-6 col-md-12">
													<div class="form-group">
														<div class="form-label-group">
															<div class="input-group dateFieldHolder">
																<input type="text" name="frrDateTo" id="frrDateTo" value="#local.rDateTo#" class="form-control dateControl">
																<div class="input-group-append">
																	<span class="input-group-text cursor-pointer calendar-button" data-target="frrDateTo"><i class="fa-solid fa-calendar"></i></span>
																	<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('frrDateTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
																</div>
																<label for="frrDateTo">Registered To</label>
															</div>
														</div>
													</div>
												</div>
											</div>
											<div class="row mt-3">
												<div class="col-sm-3 pr-sm-0 font-weight-bold">
													Last
												</div>
												<div class="col-sm-9 pl-sm-0">
													<a href="javascript:quickFilterSWLRegRegistrants(-7);" class="badge badge-neutral-second text-second mr-1">1w</a>
													<a href="javascript:quickFilterSWLRegRegistrants(-14);" class="badge badge-neutral-second text-second mr-1">2w</a>
													<a href="javascript:quickFilterSWLRegRegistrants(-21);" class="badge badge-neutral-second text-second mr-1">3w</a>
													<a href="javascript:quickFilterSWLRegRegistrants(-30);" class="badge badge-neutral-second text-second mr-1">1m</a>
													<a href="javascript:quickFilterSWLRegRegistrants(-60);" class="badge badge-neutral-second text-second mr-1">2m</a>
													<a href="javascript:quickFilterSWLRegRegistrants(-90);" class="badge badge-neutral-second text-second mr-1">3m</a>
													<a href="javascript:quickFilterSWLRegRegistrants(-180);" class="badge badge-neutral-second text-second mr-1">6m</a>
													<a href="javascript:quickFilterSWLRegRegistrants(-365);" class="badge badge-neutral-second text-second">1y</a>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div class="col-md-6 pt-2">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<select name="frrAttended" id="frrAttended" class="form-control">
												<option value="">Attended or Not Attended</option>
												<option value="1">Attended</option>
												<option value="0">Not Attended</option>
											</select>
											<label for="frrAttended">Attended/Not Attended</label>
										</div>
									</div>
									<div class="form-group">
										<div class="form-label-group mb-2">
											<select name="frrHideDeleted" id="frrHideDeleted" class="form-control">
												<option value="1">Hide Deleted Registrants</option>
												<option value="0">Show Deleted Registrants</option>
											</select>
											<label for="frrHideDeleted">Registrant Status</label>
										</div>
									</div>
									<div class="form-group">
										<div class="form-label-group mb-2">
											<select name="frrCredits" id="frrCredits" class="form-control">
												<option value="">Awarded Credits or No Credits</option>						
												<option value="1">Awarded Credits</option>
												<option value="0">Awarded no credits</option>
											</select>
											<label for="frrCredits">Awarded Credits/No Credits</label>
										</div>
									</div>
								</div>
							</div>
							<div class="row mt-3">
								<div class="col-sm-12">
									<button type="submit" class="btn btn-sm btn-primary"><i class="fa-light fa-filter"></i> Show Registrants</button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</form>
	</div>
</div>

<div class="row align-items-end">
	<div class="col">
		<h5>Registrants registered from <span id="SWLRegistrantGriddates">#local.rdateFrom# to #local.rdateTo#</span></h5>
	</div>
</div>

<table id="SWLRegistrantsListTable" class="table table-sm table-striped table-bordered" style="width:100%">
	<thead>
		<tr>
			<th>Registrant</th>
			<th>Registered</th>
			<th>Attended</th>
			<th>Credits</th>
			<th>Billed</th>
			<th>Due</th>
			<th>Actions</th>
		</tr>
	</thead>
</table>
</cfoutput>