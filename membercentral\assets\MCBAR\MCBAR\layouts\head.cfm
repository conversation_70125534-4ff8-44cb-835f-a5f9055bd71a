<cfoutput>
    <meta charset="UTF-8">
    <title>#event.getValue('mc_pageDefinition.pagetitle',event.getValue('mc_siteInfo.sitename'))#</title>		
    <!--Css File Attachments-->
    #application.objCMS.getBootstrapHeadHTML()#
    #application.objCMS.getResponsiveHeadHTML()#
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <link rel="icon" href="/images/favicon.ico" sizes="16x16">
    <link href="/assets/common/javascript/owlCarousel/234/owl.carousel.min.css" rel="stylesheet" type="text/css">
    <link href="/css/lightbox.css" rel="stylesheet" type="text/css">
    <link href="/css/jquery.mCustomScrollbar.min.css" rel="stylesheet" type="text/css">
    #application.objCMS.getFontAwesomeHTML(includeVersion4Support=false)#
    <!-- custom css files -->
    <link rel="stylesheet" href="https://use.typekit.net/rpq2sya.css">
    <link href="/css/main.css" rel="stylesheet" type="text/css">
    <link href="/css/stylesheet.css" rel="stylesheet" type="text/css">
    <link href="/css/responsive.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="/css/print.css" media="print">
    #application.objCMS.getSiteCustomCSS(siteID=arguments.event.getValue('mc_siteInfo.siteID'))#
</cfoutput>