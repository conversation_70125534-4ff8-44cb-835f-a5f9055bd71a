/* generic functions */
function viewCertificate(eid,swf) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Registrant Certificate',
		iframe: true,
		contenturl: link_viewcertificate + '&swtype=' + swf + '&eid=' + eid,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmSendCert1 :submit").click',
			extrabuttonlabel: 'Send',
			extrabuttoniconclass: 'fa-light fa-share'
		}
	});
}
function viewCommunication(pid,did,eid,swf) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Communication History',
		iframe: true,
		contenturl: link_viewcommunication + '&swtype=' + swf + '&pid=' + pid + '&did=' + did + '&eid=' + eid,
		strmodalfooter : {
			showclose: true,	
		}
	});
}
function resendInstructions(pid,did,eid,swf) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Resend Connection Instructions',
		iframe: true,
		contenturl: link_resendinstructions + '&swtype=' + swf + '&pid=' + pid + '&did=' + did + '&eid=' + eid,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmInstructions :submit").click',
			extrabuttonlabel: 'Resend Confirmation',
			extrabuttoniconclass: 'fa-light fa-share'
		}
	});
}
function manageCredit(pid,did,eid,ft,gridmode) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Manage Credit Selections',
		iframe: true,
		contenturl: link_managecredit + '&pid=' + pid + '&did=' + did + '&eid=' + eid + '&swType=' + ft + '&gridMode=' + gridmode,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto d-none',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmApplyCredit :submit").click',
			extrabuttonlabel: 'Save Credit Selections',
		}
	});
}
function editMember(m) {
	window.open(link_editmember + '&memberID=' + m,'_blank');
}
function copyProgram(pid) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Copy Program',
		iframe: true,
		contenturl: link_copyswprogram + '&ft=' + sw_itemtype + '&pid=' + pid,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary py-1 ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frm_copySWProgram :submit").click',
			extrabuttonlabel: 'Submit',
		}
	});
}
function deleteProgram(pid, enrolledCount) {
	
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: 'Delete Program',
			iframe: true,
			contenturl: link_deleteswprogram + '&pid='+ pid+ '&enrolledCount=' + enrolledCount+ '&ft=' + sw_itemtype,
			strmodalfooter: {
				classlist: 'd-flex',
				showclose: true,
				showextrabutton: true,
				extrabuttonclass: 'btn-danger ml-auto',
				extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frm_deleteSWProgram :submit").click',
				extrabuttonlabel: 'Confirm Deletion'
			
			}
		});
}
function dodeleteProgram(pid) {
	$('#btnMCModalSave').html('Please wait...').prop('disabled',true);
	var deleteProgramResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			MCModalUtils.hideModal();	
			switch (sw_itemtype.toLowerCase()) {
				case 'swl':
					swlProgramsTable.draw();
					break;
				case 'swod':
					swodProgramsTable.draw();
					break;
			}
		} else {
			alert('We were unable to remove this program.');
			$('#btnMCModalSave').html('Confirm Deletion').prop('disabled',false);
		}
	};
	
		var objParams = { seminarID:pid, programType:sw_itemtype };
		TS_AJX('ADMINSWCOMMON','deleteProgram',objParams,deleteProgramResult,deleteProgramResult,10000,deleteProgramResult);
	
}
function checkSWCreditEntry(cboxID,scID) {
	if($.inArray(scID.toString(), arrSCID_REQ) !== -1) $('#'+cboxID).prop("checked",true);
	if ($('#'+cboxID).is(':checked')) {
		$('#r'+scID).addClass('bg-darkyellow');
		$('#id'+scID).removeClass('d-none');
	} else {
		$('#r'+scID).removeClass('bg-darkyellow');
		$('#id'+scID).addClass('d-none');
	}
	return false;
}
function swapSWCreditRow(cboxID,scID) {
	if($.inArray(scID.toString(), arrSCID_REQ) !== -1) 
		$('#'+cboxID).prop("checked",true);
	if ($('#'+cboxID).is(':checked')) 
		$('#'+cboxID).prop("checked",false);
	else 
		$('#'+cboxID).prop("checked",true);
	return;
}
function validateSWCreditApply() {
	var missingID = false;
	mca_hideAlert('err_creditlink');
	for (var i=0; i < arrSCID.length; i++) {
		if ($('#frmcreditlink'+arrSCID[i]).is(':checked') && $('#scid_'+arrSCID[i]).length && $('#scid_'+arrSCID[i]).val().length == 0 && ($.inArray(arrSCID[i], arrSCID_IDREQ) !== -1) ) 
			missingID = true;
	}
	if (missingID) {
		mca_showAlert('err_creditlink', 'Required information for at least one jurisdiction you selected is missing.');
		return false;
	}
	return true;
}
function removeStatement(text, divClass = 'seminarSetupIssues') {
	$('.' + divClass + ' ul li').filter(function() {
		return $(this).text().includes(text);
	}).remove();
}
function addStatementIfNotExists(text, divClass = 'seminarSetupIssues') {
	var statementExists = false;
	$('.' + divClass + ' ul li').each(function() {
		if ($(this).text().includes(text)) {
			statementExists = true;
			return false; // Exit the loop if the issue statement already exists
		}
	});
	if (!statementExists) {
		$('.' + divClass + ' ul').append('<li>' + text + '</li>');
	}
}
function toggleSWProgramRegistrants(){
	var allow = $('span.toggleSWRegistrants').data('allow');
	var toggleText = allow?'allow':'disallow';
	var toggleAllowRegistrantsResult = function(r) {
		MCModalUtils.hideModal();
		if (r.success && r.success.toLowerCase() == 'true') {
			setToggleSWProgramRegistrantsButton(!allow);
			if (!allow) {
				addStatementIfNotExists('New registrations are disallowed on the Registrants tab.');
				removeStatement('New registrations are allowed on the Registrants tab.','seminarSetupStatements');
			} else {
				removeStatement('New registrations are disallowed on the Registrants tab.');
				addStatementIfNotExists('New registrations are allowed on the Registrants tab.','seminarSetupStatements');
			}

			// If no more issues, hide the entire alert
			if ($('.seminarSetupIssues ul li').length === 0) {
				$('.seminarSetupIssues').addClass('d-none');
				$('.seminarSetupStatements').removeClass('d-none');
			}
			else if($('.seminarSetupIssues').hasClass('d-none')) {
				$('.seminarSetupIssues').removeClass('d-none');
				$('.seminarSetupStatements').addClass('d-none');
			}
		}
		else { alert(r.errmsg && errmsg.length ? r.errmsg : 'We were unable to '+ toggleText +' new registrants for this program.'); }
	};
	var msg = 'Are you sure you want to '+ toggleText +' new registrants for this program?';
	MCModalUtils.showModal({
		verticallycentered: true,
		size: 'md',
		title: 'Confirmation Needed',
		strmodalbody: {
			content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>' + msg + '</span></div>'
		},
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttonlabel: 'Confirm'
		}
	});
	$('#btnMCModalSave').on('click', function(){
		$(this).prop('disabled', true).html('Processing...');
		var objParams = { seminarID:sw_seminarid, allowRegistrants:allow, programType:sw_itemtype };
		TS_AJX('ADMINSWCOMMON','toggleAllowRegistrants',objParams,toggleAllowRegistrantsResult,toggleAllowRegistrantsResult,10000,toggleAllowRegistrantsResult);
	});
	$('#MCModal').on('hidden.bs.modal', function() {
		$('#btnMCModalSave').off('click');
	});
}
function setToggleSWProgramRegistrantsButton(allow){	
	$('span.toggleSWRegistrants').text(allow?'Allow New Registrants':'Disallow New Registrants');
	$('span.toggleSWRegistrants').data('allow',allow);
	$('a.toggleSWRegistrants').prop('title','Click to '+ ( allow ? 'allow' : 'disallow' ) +' new registrants.').tooltip('dispose').tooltip();
}
function hideSWProgramPricingAlert() { mca_hideAlert('priceGrpErr'); }
function showSWProgramPricingAlert(msg) { mca_showAlert('priceGrpErr', msg); }
function saveSWCreditResult(ft,gridMode){
	reloadSWProgramRegGrid(ft,gridMode);
	MCModalUtils.hideModal();	
}
function reloadSWProgramRegGrid(ft,gridMode) {
	if(ft == "SWL" && gridMode == "grid")
		dofilterSWLProgramRegistrants();
	else if(ft == "SWL" && gridMode == "regsearchgrid")
		dofilterSWLRegistrants();
	else if(ft == "SWL" && gridMode == "reggrid")
		SWLRegistrantsListTable.draw();
	else if(ft == "SWOD" && gridMode == "grid")
		SWODRegistrantsListTable.draw();
	else if(ft == "SWOD" && gridMode == "regsearchgrid")
		dofilterSWODRegistrants();
	else if(ft == "SWOD" && gridMode == "reggrid")
		SWODRegistrantsListTable.draw();
	else if(ft == "SWB" && gridMode == "grid")
		SWBRegistrantsListTable.draw();
}
function getSWProgramID(){
	var programID = 0;
	if(sw_itemtype == "SWL"||sw_itemtype == "SWOD") programID = sw_seminarid;
	else if(sw_itemtype == "SWTL") programID = sw_titleid;
	else if(sw_itemtype == "SWB") programID = sw_bundleid;
	return programID;
}
function getSWProgramDisplayText(){
	var displayText = '';
	if(sw_itemtype == "SWL"||sw_itemtype == "SWOD"||sw_itemtype == "SWTL") displayText = 'Program';
	else if(sw_itemtype == "SWB") displayText = 'Bundle';
	return displayText;
}
function addSWPayment(po,ft) {
	if (sw_reggridmode == 'reggrid') 
		sw_itemtype = ft;

	mca_addPayment(po,link_addswpayment);
}
function closeAddPayment(po) {
	reloadSWProgramRegGrid(sw_itemtype,sw_reggridmode);
	if (Number(sw_hastransallocaterights) == 1)
		allocSWIndivPayment(po);
	else
		MCModalUtils.hideModal();
}
function allocSWIndivPayment(po) {
	mca_allocIndivPayment(po,link_allocateswpayment);
}
function closeAllocPayment() {
	reloadSWProgramRegGrid(sw_itemtype,sw_reggridmode);
	MCModalUtils.hideModal();
}
function getSWEditProgramLink() {
	var editProgramLink = '';
	if(sw_itemtype == "SWL") editProgramLink = link_editswlprogram;
	else if(sw_itemtype == "SWOD" || sw_itemtype == "SWTL") editProgramLink = link_editswodprogram;
	else if(sw_itemtype == "SWB") editProgramLink = link_editswbprogram;
	return editProgramLink;
}
function reloadSWEditProgram(tab,referenceType='') {
	if(referenceType=='swlProgram'){
		saveSWLRateCatalog('featureImage');
	}else if (referenceType=='swodProgram') {
		saveSWODRateCatalog('featureImage');
	}else if(referenceType=='swbProgram'){
		saveSWBRateCatalog('featureImage');
	}else{
		self.location.href = getSWEditProgramLink() + (typeof tab != "undefined" ? "&tab=" + tab : "");
	}
}
function manageSWProgramSettings(ft) {
	if (sw_lockprogramsettings) {
		switch(ft) {
			case 'swl':
				$('#divSWLTabContent .tab-pane').not('#pills-registrantsTab').find('input:not([type=hidden]),select,textarea,button').prop('disabled',true);
				break;
			case 'swod':
				$('#divSWODTabContent .tab-pane').not('#pills-registrantsTab').find('input:not([type=hidden]),select,textarea,button').prop('disabled',true);				
				break;
			case 'swb':
				$('#divSWBTabContent .tab-pane').not('#pills-registrantsTab').find('input:not([type=hidden]),select,textarea,button').prop('disabled',true);
				break;
		};

		if (sw_haslockprogramrights)
			$('.sw_locksettings').prop('disabled',false);
	}
}
function isSWProgramLocked() {
	return (typeof sw_lockprogramsettings != "undefined") ? sw_lockprogramsettings : false;
}
function changeSWRegistrantPrice(eid,ft) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Change Price for Registrant',
		iframe: true,
		contenturl: link_changeregprice + '&eid=' + eid + '&ft=' + ft,
		strmodalfooter : {
			classlist: 'text-right',
			showclose: false,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary',
			extrabuttononclickhandler: 'validateSWRegistrantPrice',
			extrabuttonlabel: 'Change Price',
		}
	});
}
function validateSWRegistrantPrice() {
	$('#MCModalBodyIframe')[0].contentWindow.validateSWRegistrantPriceChange();
}
function doChangeSWRegistrantPrice(obj) {
	var priceChangeResult = function(r) {
		top.MCModalUtils.hideModal();
		if (r.success && r.success.toLowerCase() == 'true') {
			if (obj.SWType == 'SWB') SWBRegistrantsListTable.draw();
			else if (obj.SWType == 'SWL') SWLRegistrantsListTable.draw(false);
			else if (obj.SWType == 'SWOD') SWODRegistrantsListTable.draw(false);
		}
		else if (r.success && r.success.toLowerCase() == 'false' && r.errmsg) { alert(r.errmsg); }
		else { alert('We were unable to change enrollment fees.'); }
	};

	$('#btnMCModalSave').prop('disabled',true).html('<i class="fa-light fa-circle-notch fa-spin"></i> Saving...');
	var objParams = { referenceID:obj.referenceID, newPrice:obj.newPrice, sendChangePriceEmail:obj.sendChangePriceEmail, paymentMethod:obj.paymentMethod, 
		paymentDescription:obj.paymentDescription, tspaymentMethod:obj.tspaymentMethod, SWType:obj.SWType };
	TS_AJX('ADMINSWCOMMON','changeRegistrantPrice',objParams,priceChangeResult,priceChangeResult,20000,priceChangeResult);
}
function exportSWProgramRegistrants() {
	$('div.divSWRegistrantsTool').addClass('d-none');
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Download Filtered Registrants',
		iframe: true,
		contenturl: link_exportswregprompt,
		strmodalfooter: {
			classlist: 'text-right',
			showclose: false,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.doExportSWProgramReg',
			extrabuttonlabel: 'Download CSV',
			extrabuttoniconclass: 'fa-light fa-file-csv'
		}
	});
	
	if(sw_itemtype.toLowerCase() == 'swl'){
		if(parseInt(swl_showDownloadAllRegistrantsForIsNATLE) || parseInt(swl_showDownloadAllRegistrantsForSyndicated)){
			$('#MCModalFooter').prepend('<button type="button" class="btn btn-sm btn-secondary float-left" id="exportSWLAllProgramReg" onclick="$(\'#MCModalBodyIframe\')[0].contentWindow.doExportSWLAllProgramReg();"><span class="superuser small"></span> Download All Registrants</button>');
		}
	}
}
function editSWProgram(pid,closeModal) {
	if (closeModal) MCModalUtils.hideModal();
	switch(sw_itemtype.toLowerCase()) {
		case 'swl':
			self.location.href = link_editswlprogram + '&pid=' + pid;
			break;
		case 'swod':
			self.location.href = link_editswodprogram + '&pid=' + pid;
			break;
		case 'swb':
			self.location.href = link_editswbprogram + '&pid=' + pid;
			break;
	};
}

/* SW Settings */
function showOrHideSWPmtSettings() {
	var am = $('input[name="handlesOwnPayment"]:checked').val();
	if (am == 1) $('#divPayMethods').removeClass('d-none');
	else $('#divPayMethods').addClass('d-none');
}
function validateSWParticipantDetails() {
	var arrReq = [];
	var emailRegEx = new RegExp(sw_emailregex,"gi");
	mca_hideAlert('err_swsettings');

	var am = $('input[name="handlesOwnPayment"]:checked').val();
	if (am == 1) {
		if (! $('input[name="swRegMPProfiles"]:checked').length) arrReq.push('Select at least one supported payment method.');
		if ($('#revenueGLAccountID').length && ($.trim($('#revenueGLAccountID').val()).length == 0 || $('#revenueGLAccountID').val() == 0)) arrReq.push('Select a Revenue GL Account.');
	}
	if(sw_hasManageSWSettingsAll){
		var supportEmail = $('#supportEmail').val();
		if($('#supportPhone').val() == '') arrReq[arrReq.length] = "Enter the telephone support number.";
		if (supportEmail.length == 0 || !(emailRegEx.test(supportEmail))) arrReq[arrReq.length] = 'Enter a valid e-mail support address.';
		if($('#supportHours').val() == '') arrReq[arrReq.length] = "Enter the association support hours.";

		if($("input:radio[name='isSelfReportCredit']:checked").length == 0) arrReq[arrReq.length] = "Show Self Reporting Credit Option?";
		if($("input:radio[name='showUSD']:checked").length == 0) arrReq[arrReq.length] = "Show USD next to the prices?";
		
		if($.trim($('#programSetupIssueEmail').val()).length == 0) arrReq[arrReq.length] = "Enter the e-mail recipient for program setup issues.";

		var marketingEmailRegEx = new RegExp("^([a-zA-Z0-9_\.\-])+$", "gi");
		var brandSWLMarketingEmail = $('#brandSWLMarketingEmail').val();
		if(brandSWLMarketingEmail != '' && !marketingEmailRegEx.test(brandSWLMarketingEmail)) arrReq[arrReq.length] = "Enter a valid string for the Marketing E-mail";
		if($('#orgIdentityID').val() == '') arrReq[arrReq.length] = "Select an Organization Identity";
	}

	if (arrReq.length) {
		mca_showAlert('err_swsettings', arrReq.join('<br/>'), true);
		return false;
	}

	$('#saveBtn').prop('disabled',true);
	return true;
}
function initSWFeaturedImageSettings() {
	if (sw_programftdimgconfigid > 0)
		onChangeSWSettingsFtdImgConfig(sw_programftdimgconfigid,['swProgramDetail','swProgramListings','swFeaturedProgramLanding','swOtherProgramDetail'],
			[sw_swprogramdetailftdimgsizeid,sw_swprogramlistingsftdimgsizeid,sw_swftdprogramlandingftdimgsizeid,sw_swotherprogramdetailftdimgsizeid]);
	if (sw_sponsorftdimgconfigid > 0)
		onChangeSWSettingsFtdImgConfig(sw_sponsorftdimgconfigid,['evSponsor','swSponsor'],[sw_evsponsorftdimgsizeid,sw_swsponsorftdimgsizeid]);
	if (sw_speakerftdimgconfigid > 0)
		onChangeSWSettingsFtdImgConfig(sw_speakerftdimgconfigid,'swSpeaker',sw_speakerftdimgsizeid);
}
function saveSWFeaturedImageSettings(sectionId) {
	var saveResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			onSaveSWFeaturedImageSettings(sectionId);
		} else {
			alert('We were unable to save featured image settings.');
		}
	};

	var arrReq = [];
	if(sectionId == 'program-images') {
		if ($('#swProgramFeatureImageConfigID').val() > 0 && $('.sw_programftdimagesizes').filter(function(){ return $(this).val() == 0; }).length > 0)
			arrReq.push('If selecting a program featured image config., you must select all the sizes');
	}
	if(sectionId == 'program-sponsorImages'){
		if ($('#sponsorFeatureImageConfigID').val() > 0 && ( $('#evSponsorFeatureImageSizeID').val() == 0 || $('#swSponsorFeatureImageSizeID').val() == 0 )) 
			arrReq.push('If selecting a sponsor featured image config., you must select both the sizes to show on Events detail and SeminarWeb detail');
	}
	if(sectionId == 'program-smImages'){
		if ($('#swSpeakerFeatureImageConfigID').val() == 0)
			arrReq.push('Select a Speaker Featured Image Configuration');
		if ($('#swSpeakerFeatureImageSizeID').val() == 0)
			arrReq.push('Select a Speaker Featured Image size to show on SeminarWeb detail');
	}
	
	if (arrReq.length) {
		alert(arrReq.join('\n'));
		return false;
	}

	$('#' + sectionId +'Body,#' + sectionId + 'SaveLoading').toggle();
	// Initialize objParams
	var objParams = { participantID: sw_participantid };

	// Populate objParams dynamically based on sectionId
	if (sectionId == 'program-images') {
		objParams.swProgramFeatureImageConfigID = $('#swProgramFeatureImageConfigID').val();
		objParams.swProgramDetailFeatureImageSizeID = $('#swProgramDetailFeatureImageSizeID').val();
		objParams.swProgramListingsFeatureImageSizeID = $('#swProgramListingsFeatureImageSizeID').val();
		objParams.swFeaturedProgramLandingFeatureImageSizeID = $('#swFeaturedProgramLandingFeatureImageSizeID').val();
		objParams.swOtherProgramDetailFeatureImageSizeID = $('#swOtherProgramDetailFeatureImageSizeID').val();
	} else if (sectionId == 'program-sponsorImages') {
		objParams.sponsorFeatureImageConfigID = $('#sponsorFeatureImageConfigID').val();
		objParams.evSponsorFeatureImageSizeID = $('#evSponsorFeatureImageSizeID').val();
		objParams.swSponsorFeatureImageSizeID = $('#swSponsorFeatureImageSizeID').val();
	} else if (sectionId == 'program-smImages') {
		objParams.speakerFeatureImageConfigID = $('#swSpeakerFeatureImageConfigID').val();
		objParams.speakerFeatureImageSizeID = $('#swSpeakerFeatureImageSizeID').val();
	}

	// Make the AJAX call
	TS_AJX('SEMWEBPARTICIPANTS', 'saveSWFeaturedImageSettings', objParams, saveResult, saveResult, 20000, saveResult);
}
function onSaveSWFeaturedImageSettings(sectionId) {
	$('#' + sectionId +'Body,#' + sectionId + 'SaveLoading').toggle();
	displaySavedResponse($('#'+sectionId));
	autoScrollToSection($('#'+sectionId));
	$('#' + sectionId + ' .card-footer .save-button').prop('disabled',false);
	if ($('#swProgramOldFeatureImageConfigID').val() != $('#swProgramFeatureImageConfigID').val())
		reloadSWFtdImgSettings();
}
function onChangeSWSettingsFtdImgConfig(cid,ft,sizeid) {
	if (typeof ft == 'object' && Array.isArray(ft))	
		$.each(ft, function (i, item) { $('#'+item+'FeatureImageSizeID').find('option[value!="0"]').remove(); });
	else 
		$('#'+ft+'FeatureImageSizeID').find('option[value!="0"]').remove();
	
	if (cid > 0) {
		if (typeof ft == 'object' && Array.isArray(ft))	
			$.each(ft, function (i, item) { $('#divSWFeaturedImageSizeRow_'+item).removeClass('d-none'); });

		var objParams = { featureImageConfigID:cid, limitWidth:400, limitHeight:400 };
		$.getJSON('/?event=proxy.ts_json&c=FTDIMAGES&m=getFeaturedImageSizesForConfigID', objParams)
			.done(function(r) { populateFeatureImageSizes(r,ft,sizeid); })
			.fail(populateFeatureImageSizes);
	} else {
		if (typeof ft == 'object' && Array.isArray(ft))	
			$.each(ft, function (i, item) { $('#divSWFeaturedImageSizeRow_'+item).addClass('d-none'); });
	}
}
function populateFeatureImageSizes(r,ft,sizeid) {
	if (r.success) {
		if(r.arrsizes && r.arrsizes.length) {
			$.each(r.arrsizes, function (i, item) {
				var optionText = item.featuredimagesizename + ' (' + item.width + ' x ' + item.height + ')';
				
				if (typeof ft == 'object' && Array.isArray(ft)) {
					$.each(ft, function (i,id) {
						$('#'+id+'FeatureImageSizeID').append( $('<option>', { value:item.featureImagesizeid, text:optionText }) );
					});
				} else {
					$('#'+ft+'FeatureImageSizeID').append( $('<option>', { value:item.featureImagesizeid, text:optionText }) );
				}
			});

			if (sizeid) {
				if (typeof sizeid == 'object' && Array.isArray(sizeid)) {
					$.each(ft, function (i, item) {
						$('#'+item+'FeatureImageSizeID').val(sizeid[i]);
					});
				} else {
					$('#'+ft+'FeatureImageSizeID').val(sizeid);
				}
			} 

			if (typeof ft == 'object' && Array.isArray(ft))
				$.each(ft, function (i, item) { $('#divNoEligibleSizeWarning_'+item).addClass('d-none'); });
			else
				$('#divNoEligibleSizeWarning_'+ft).addClass('d-none');

		} else {
			if (typeof ft == 'object' && Array.isArray(ft))
				$.each(ft, function (i, item) { $('#divNoEligibleSizeWarning_'+item).removeClass('d-none'); });
			else
				$('#divNoEligibleSizeWarning_'+ft).removeClass('d-none');
		}
	} else {
		alert('We were unable to load this featured image config sizes.');
	}
	return false;
}
function reloadSWFtdImgSettings() {
	self.location.href = sw_settingslink+'&tab=featuredImages&showBadge=fsdi';
}

/* SWL & SWOD Registrants Common Functions */
function getSWRegistrantsRowCount(ft) {
	let regRows = 0;
	switch (ft) {
		case 'SWL': regRows = SWLRegistrantsListTable.page.info().recordsTotal; break;
		case 'SWOD': regRows = SWODRegistrantsListTable.page.info().recordsTotal; break;
		default: regRows = mcg_g.getRowsNum();
	}
	return regRows;
}
function massEmailSWRegistrants(ft) {
	if(getSWRegistrantsRowCount(ft) == 0) {
		alert('There are no registrants to act upon.');
		return false;
	}
	var objForm = $('#frmRegistrantFilter');
	var massEmailRegLink = link_massemailreg + '&' + objForm.serialize();
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: 'E-mail Filtered Registrants',
		iframe: true,
		contenturl: massEmailRegLink,
		strmodalfooter: {
			classlist: 'd-none'
		}
	});
}

function massEmailCertificates(ft) {
	if(getSWRegistrantsRowCount(ft) == 0) {
		alert('There are no registrants to act upon.');
		return false;
	}
	var objForm =  $('#frmRegistrantFilter');
	
	var massEmailCertificateLink = link_massemailcert + '&'+ objForm.serialize();
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Email Certificates to Filtered Registrants',
		iframe: true,
		contenturl: massEmailCertificateLink,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmCertificates :submit").click',
			extrabuttonlabel: 'Send'
		}
	});
}

function massEmailresendInstructions(ft) {
	if(getSWRegistrantsRowCount(ft) == 0) {
		alert('There are no registrants to act upon.');
		return false;
	}
	var objForm =  $('#frmRegistrantFilter');
	
	var massEmailresendinstructionsLink = link_massemailresendinstructions + '&'+ objForm.serialize();
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Resend Connection Instructions to Filtered Registrants',
		iframe: true,
		contenturl: massEmailresendinstructionsLink,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmInstructions :submit").click',
			extrabuttonlabel: 'Resend Confirmation',
			extrabuttoniconclass: 'fa-light fa-share'
		}
	});
}
/* SWL & SWOD Credit Common Functions */
function reloadSWProgramCredit() {
	if (typeof link_editSWProgramCredit != "undefined") {
		if(programAdded)
			self.location.href = link_editSWProgramCredit + '&showBadge=credit&programAdded='+programAdded+'&lastIncompleteSectionIndex='+lastIncompleteSectionIndex;
		else self.location.href = link_editSWProgramCredit + '&showBadge=credit';
	}
	/* Credit Sponsor Admin - Credit Tab */
	else if (typeof sw_sponsorID != "undefined") {
		reloadCreditSponsorCreditTab();
	}
}
function addSWProgramCredit(sid) {
	if (isSWProgramLocked()) return false;

	var csaId = $('#addCSALinkID').val();
	mca_hideAlert('err_addcredit');

	if (csaId.length > 0 && csaId > 0) {
		var saveResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				reloadSWProgramCredit(); 
			} else {
				alert('We were unable to add this seminar credit. Try again.');
			}
		};
		var objParams = { CSALinkID:csaId, seminarID:sid };
		$('#btnAddCredit').attr('disabled','disabled');
		TS_AJX('ADMINSWCREDIT','addSeminarCredit',objParams,saveResult,saveResult,10000,saveResult);
	}	
	else{
		mca_showAlert('err_addcredit', 'Please choose a Sponsor/Authority.');
		return false;
	}		
}
function addSWProgramExpressCredit(sid) {
	if (isSWProgramLocked()) return false;

	var npId = $('#addProgramID').val();
	mca_hideAlert('err_addcredit');
	if (npId.length > 0 && npId > 0){
		var saveResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				reloadSWProgramCredit(); 
			} else {
				alert('We were unable to express add this seminar credit. Try again.');
			}
		};
		var objParams = { programID:npId, seminarID:sid };
		$('#btnAddCreditExp').attr('disabled','disabled');
		TS_AJX('ADMINSWCREDIT','addSeminarCreditExpress',objParams,saveResult,saveResult,10000,saveResult);
	}
}
function setExpressState() {
	if (isSWProgramLocked()) return false;

	var npId = $('#addProgramID').val();
	if (npId.length > 0 && npId > 0) 
		$('#btnAddCreditExp').prop('disabled',false);
	else 
		$('#btnAddCreditExp').prop('disabled',true);
}		
function uploadSWSeminarCreditForm(scId) {
	if (isSWProgramLocked()) return false;
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'md',
		title: 'Upload Form 1 PDF file',
		iframe: true,
		contenturl: link_getSWSeminarCreditFormUpload + '&scId=' + scId,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#uploadFileBtn").click',
			extrabuttonlabel: 'Upload PDF',
			extrabuttoniconclass: 'fa-light fa-file-arrow-up'
		}
	});
}
function viewSWSeminarCreditForm(scId) {
	if (isSWProgramLocked()) return false;
	window.open(link_viewSWSeminarCreditForm + '&scId=' + scId);
}
function deleteSWSeminarCreditForm(scId) {
	if (isSWProgramLocked()) return false;

	var deleteFormResult = function(r) {
		MCModalUtils.hideModal();
		if (r.success && r.success.toLowerCase() == 'true') { reloadSWProgramCredit(); }
		else { alert('Some error occurred while deleting this seminar credit form. Try again.'); }
	};
	MCModalUtils.showModal({
		verticallycentered: true,
		size: 'md',
		title: 'Confirmation Needed',
		strmodalbody: {
			content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>Are you sure you want to delete this form?</span></div>'
		},
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-outline-danger ml-auto',
			extrabuttonlabel: 'Confirm'
		}
	});
	$('#btnMCModalSave').on('click', function(){
		$(this).prop('disabled', true).html('Deleting...');
		var objParams = { seminarCreditID:scId };
		TS_AJX('ADMINSWCREDIT','deleteSeminarCreditForm',objParams,deleteFormResult,deleteFormResult,10000,deleteFormResult);
	});
	$('#MCModal').on('hidden.bs.modal', function() {
		$('#btnMCModalSave').off('click');
	});
}
function forceCreditPreselect(reqCr,preCrId) {
	if (isSWProgramLocked()) return false;
	if (reqCr.checked) $('#'+preCrId).prop("checked",true);
}
function setSWCreditRowBackground(scID,f) {
	if (isSWProgramLocked()) return false;
	$('div#credittxt' + scID).toggleClass('bg-neutral-first',f).toggleClass('bg-transparent',!f);
}
function editSWCreditRow(scID) {
	if (isSWProgramLocked()) return false;

	if (lastEditedCreditRow != null) {
		cancelSWCreditRow(lastEditedCreditRow);
	}
	lastEditedCreditRow = scID;
	$('div#credittxt' + scID).addClass('d-none');
	$('div#creditfrm' + scID).removeClass('d-none');
}
function cancelSWCreditRow(scID) {
	$('div#credittxt' + scID).removeClass('d-none');
	$('div#creditfrm' + scID).addClass('d-none');
}
function saveSWProgramCredit(scID) {
	var arrReq = [];
	mca_hideAlert('err_editcredit_'+scID);
	if (isSWProgramLocked()) return false;

	var saveCreditResult = function(r) {
		saveBtn.prop('disabled',false);
		saveBtn.find('.btn-wrapper--label').html('Save');

		if (r.success && r.success.toLowerCase() == 'true') {
			reloadSWProgramCredit();
		} else if (r.success && r.success.toLowerCase() == 'false' && r.errmsg && r.errmsg.length) {
			alert(r.errmsg);
		} else {
			alert('We were unable to save this program credit. Try again.');
		}
	};

	var objParams = { seminarCreditID:scID };
	var scIDStr = '';
	var inputType = '';
	var erMsg = '';
	var startDatePresent = false;
	var endDatePresent = false;
	var creditPresent = false;
	var completedByPresent = false;
	$('div#creditfrm'+scID+' .frmCredInput'+scID).each(function() {
		scIDStr = '_' + scID + '_';
		inputType = $(this).attr('type');
		if(inputType != 'checkbox' || (inputType == 'checkbox' && $(this).is(':checked'))){ 
			if(inputType != 'checkbox' && $(this).attr('datatype') == 'numeric' && isNaN($(this).val().trim())){
				arrReq[arrReq.length] = $(this).attr('msg');
			}
			objParams[$(this).attr('name').replace(scIDStr,'')] = $(this).val();
		}
	});
	if ($('#creditOfferedStartDate_' + scID + '_').length && $('#creditOfferedStartDate_' + scID + '_').val().trim() !== '') {
		startDatePresent = true;
	}
	if ($('#creditOfferedEndDate_'+ scID + '_').length && $('#creditOfferedEndDate_'+ scID + '_').val().trim() !== '') {
		endDatePresent = true;
	}
	if ($('#creditCompleteByDate_'+ scID + '_').length && $('#creditCompleteByDate_'+ scID + '_').val().trim() !== '') {
		completedByPresent = true;
	}
	$('input[id^="xcredit__' + scID + '"]').each(function(){ 
		if ($(this).attr('id').startsWith('xcredit__')) {
			if ($(this).val().trim() !== '') {
				creditPresent = true;
				return false; // Exit the loop early
			}
		}
	});
	var statusDropdown = $('#statusid_' + scID);
	var selectedOptionText = statusDropdown.find('option:selected').text().trim();
	if(selectedOptionText == 'Pending' || selectedOptionText == 'Self-Submitting' || selectedOptionText == 'Approved') {
		if(!startDatePresent && $('#creditOfferedStartDate_' + scID + '_').length) arrReq.push('Add a valid credit "Offered from" date.');
		if(!endDatePresent && $('#creditOfferedEndDate_'+ scID + '_').length) arrReq.push('Add a valid credit "Offered to" date.');
		if(!completedByPresent && $('#creditCompleteByDate_'+ scID + '_').length) arrReq.push('Add a valid credit "Complete by" date.');
		if(!creditPresent) arrReq.push('Add at least one "Credits offered" type.');
	}
	if (startDatePresent && endDatePresent && creditPresent) {
		if (selectedOptionText == 'Not Submitted') {
			arrReq.push('Please Select Approval Status');
		}
	}
	if (arrReq.length) {
		mca_showAlert('err_editcredit_'+scID, arrReq.join('<br/>'));
		return false;
	}
	var saveBtn = $('#btnSave' + scID);
	saveBtn.prop('disabled',true);
	saveBtn.find('.btn-wrapper--label').html('Saving...');
	$('#btnRemove' + scID).attr('disabled','disabled');
	TS_AJX('ADMINSWCREDIT','saveCreditForSWSeminar',objParams,saveCreditResult,saveCreditResult,10000,saveCreditResult);
}
function removeSWProgramCredit(scID) {
	if (isSWProgramLocked()) return false;

	var removeCreditResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			reloadSWProgramCredit();
		} else {
			alert('We were unable to remove this program credit. Try again.');
		}
	};

	MCModalUtils.showModal({
		verticallycentered: true,
		size: 'md',
		title: 'Confirmation Needed',
		strmodalbody: {
			content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>Are you sure you want to remove this credit?</span></div>'
		},
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-outline-danger ml-auto',
			extrabuttonlabel: 'Confirm'
		}
	});
	$('#btnMCModalSave').on('click', function(){
		MCModalUtils.hideModal();
		var objParams = { seminarCreditID:scID };
		$('#btnRemove' + scID + ' .btn-wrapper--label').text('Removing...');
		$('#btnRemove' + scID).attr('disabled','disabled');
		$('#btnSave' + scID).attr('disabled','disabled');
		TS_AJX('ADMINSWCREDIT','removeCreditFromSWSeminar',objParams,removeCreditResult,removeCreditResult,10000,removeCreditResult);
	});
	$('#MCModal').on('hidden.bs.modal', function() {
		$('#btnMCModalSave').off('click');
	});
}

/* SW Links Common Functions */
function hasSWProgramID(){
	if ((sw_itemtype == "SWOD" && swod_seminarid !== undefined) || (sw_itemtype == "SWTL" && sw_titleid !== undefined)) return true;
	return false;
}
function initSWLinksTable(){
	SWLinksTable = $('#SWLinksTable').DataTable({
		"processing": true,
		"serverSide": true,
		"paginate": false,
		"language": {
			"lengthMenu": "_MENU_"
		},
		"ajax": { 
			"url": link_SWLinks,
			"type": "post",
			"data": function(d) { 
				if (window.reorderData && window.reorderData.length > 0) { 
					d.reorderData = JSON.stringify(window.reorderData); 
					window.reorderData = [];
				} 
				return d; 
			}
		},
		"autoWidth": false,
		"columns": [
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<i class="fa-light fa-bars"></i>';

					}
					return type === 'display' ? renderData : data;
				},
				"width": "5%",
				"orderable": false
			},
			{
				"data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						renderData += data.linkName;
						renderData += '<div class="text-dim small"><a href="'+data.linkURL+'" target="_blank">'+data.linkURL+'</a></div>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "85%"
			},
			{ 
				"data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						if(!isSWProgramLocked()) {
							renderData += '<a href="#" class="btn btn-xs btn-outline-primary p-1 m-1" title="Edit Link" onclick="editSWLink('+data.linkID+');return false;"><i class="fa-solid fa-pencil"></i></a>';	
							renderData += '<a href="#" id="btnDel'+data.linkID+'" class="btn btn-xs btn-outline-danger p-1 m-1" title="Remove Link" onclick="removeSWLink('+data.linkID+');return false;"><i class="fa-solid fa-trash-can"></i></a>';
						}
					}
					return type === 'display' ? renderData : data;
				},
				"width": "10%",
				"className": "text-center align-top"
			}
		],
		"ordering": false,
		"searching": false,
		"rowReorder": {
			dataSrc: "columnid" 
		},
		"drawCallback": function() { 
			let selectElement = $('#toggleLink');
			let holderElement = $('#linksHolder');
			if (!$('#SWLinksTable .dataTables_empty').length){
				selectElement.prop('checked',true);
				selectElement.prop('disabled',true);
			}else{
				selectElement.prop('checked',false);
				if(!isSWProgramLocked())
				selectElement.prop('disabled',false);
				holderElement.addClass("d-none");
			}
		}
	});
	SWLinksTable.on('row-reorder', function (e, diff, edit) {
		let orderData = [];
		diff.forEach(function(item) {
			orderData.push({
				id: SWLinksTable.row(item.node).data().linkID,
				newOrder: item.newPosition
			});
		});
		window.reorderData = orderData;
	});
}
function editSWLink(id) {
	if (isSWProgramLocked()) return false;

	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: id > 0 ? 'Edit Link' : 'Add Link',
		iframe: true,
		contenturl: link_editSWLink + '&linkID=' + id,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.validateAndSaveSWLink',
			extrabuttonlabel: id > 0 ? 'Update' : 'Add',
		}
	});
}
function doSaveSWLink(objLink) {
	if (isSWProgramLocked()) return false;
	
	var saveResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			if (hasSWProgramID()){
				SWLinksTable.draw();
				MCModalUtils.hideModal();
			}
		} else {
			let objLinkForm = $("#MCModalBodyIframe")[0].contentWindow;
			objLinkForm.mca_showAlert('err_swlink', r.errmsg && r.errmsg.length ? r.errmsg : 'We ran into an error while saving link. Try again.');
			objLinkForm.toggleSWLinkSaveProgress(false);
		}
	};
	
	if (hasSWProgramID()) {
		var programID = getSWProgramID();
		var objParams = { programID:programID, programType:sw_itemtype, linkID:objLink.linkID, 
			linkURL:objLink.linkURL, linkName:objLink.linkName, linkDesc:objLink.linkDesc, 
			purchaseURL:objLink.purchaseURL };
		TS_AJX('ADMINSWCOMMON','saveProgramLink',objParams,saveResult,saveResult,20000,saveResult);
	}
}
function removeSWLink(id) {
	if (isSWProgramLocked()) return false;
	var removeData = function(r) {
		if (r.success && r.success.toLowerCase() == 'true' && hasSWProgramID()){
			SWLinksTable.draw();
		} else {
			delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
			alert('Some error occured while removing this link. Try again.');
		}
	};
	
	let delBtn = $('#btnDel'+id);
	mca_initConfirmButton(delBtn, function(){
		var objParams = { programID:getSWProgramID(), programType:sw_itemtype, linkID:id };
		TS_AJX('ADMINSWCOMMON','removeProgramLink',objParams,removeData,removeData,10000,removeData);
	});
}
/* SW Authors common functions */
function getSWAuthorLabels(atype) {
	var obj = {};
	if(atype.toLowerCase() == "speaker") {
		obj = { authorLabelSinglular:'Speaker', authorLabelPlural:'Speakers' };
	} else if(atype.toLowerCase() == "moderator") {
		obj = { authorLabelSinglular:'Moderator', authorLabelPlural:'Moderators' };
	}
	return obj;
}
function addSWProgramAuthor(atype) {
	if (isSWProgramLocked()) return false;

	var objAuthor = getSWAuthorLabels(atype);
	
	var result = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			var html = '<option value="">Choose '+ objAuthor.authorLabelSinglular +'</option><optgroup label="Create a New '+ objAuthor.authorLabelSinglular +'"><option value="0">New '+ objAuthor.authorLabelSinglular +'</option></optgroup>';
			if (r.arrorgauthors.length) {
				html += '<optgroup label="Previously Saved '+ objAuthor.authorLabelPlural +'">';
				for (var i=0; i<r.arrorgauthors.length; i++) {
					html += '<option value="'+r.arrorgauthors[i].authorid+'">'+r.arrorgauthors[i].authorname+'</option>';
				}
				html += '</optgroup>';
			}
			$('#fSWAuthor_'+atype).html(html);
		} else {
			alert('An error occured while loading '+ objAuthor.authorLabelPlural +'.');
		}
	};

	MCModalUtils.showModal({
		isslideout: true,
		size: 'md',
		title: 'Choose '+objAuthor.authorLabelSinglular+' for this Program',
		strmodalbody: {
			content: $('#mc_addSWProgramAuthorForm_'+atype).html(),
		},
		strmodalfooter : {
			classlist: 'd-flex',
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttonlabel: 'Continue'
		}
	});

	$('#btnMCModalSave').on('click', function(){
		doAddSWProgramAuthor(atype);
	});

	var objParams = { orgcode:sw_sitecode, programID:getSWProgramID(), programType:sw_itemtype, authorType:atype };
	TS_AJX('ADMINSWAUTHOR','getAvailableSWAuthorsToAdd',objParams,result,result,10000,result);
}
function doAddSWProgramAuthor(atype) {
	if (isSWProgramLocked()) return false;

	var objAuthor = getSWAuthorLabels(atype);
	mca_hideAlert('err_addswprogramauthor_'+atype);

	var addResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			MCModalUtils.hideModal();
			reloadSWAuthorGrids(atype);
			if (typeof window['onAddSWProgramAuthor'+atype] != 'undefined' && typeof window['onAddSWProgramAuthor'+atype] == 'function') {
				window['onAddSWProgramAuthor'+atype]();
			}
		}
		else {
			alert(r.errmsg ? r.errmsg : 'An error occured while adding this '+ objAuthor.authorLabelSinglular +' .');
		}
	};

	var authorID = $('#fSWAuthor_'+atype).val();
	if (authorID == "") {
		mca_showAlert('err_addswprogramauthor_'+atype, 'Select a '+ objAuthor.authorLabelSinglular +'.');
		return false;
	} else if (authorID == 0) {
		MCModalUtils.hideModal();
		$('#MCModal').on('hidden.bs.modal', function() { editSWAuthor(0,atype); });
	} else {
		$('#btnMCModalSave').prop('disabled',true);
		var objParams = { programID:getSWProgramID(), programType:sw_itemtype, authorID:authorID };
		TS_AJX('ADMINSWAUTHOR','addSWProgramAuthor',objParams,addResult,addResult,10000,addResult);
	}
}
function editSWAuthor(aid, atype) {
	if (isSWProgramLocked()) return false;

	var objAuthor = getSWAuthorLabels(atype);

	if (typeof isPublisher === 'undefined') {
		isPublisher = true;
	}

	var modalOptions = {
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: (aid > 0 ? 'Edit ' : 'Add ') + objAuthor.authorLabelSinglular,
		iframe: true,
		contenturl: link_editauthor + '&aid=' + aid + '&atype=' + atype,
	};

	if (isPublisher == true) {
		modalOptions.strmodalfooter = {
			classlist: 'd-flex',
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmAuthorDetails :submit").click',
			extrabuttonlabel: 'Save',
		};
	}

	MCModalUtils.showModal(modalOptions);
}

function reloadSWAuthorGrids(atype) {
	if(['SWL','SWOD'].indexOf(sw_itemtype) != -1) {
		window[sw_itemtype.toLowerCase()+'_'+atype.toLowerCase()+'_table'].draw();
		if (typeof window['onSaveSWProgramAuthor'+atype] != 'undefined' && typeof window['onSaveSWProgramAuthor'+atype] == 'function') 
			window['onSaveSWProgramAuthor'+atype]();
	}
	else swAuthorsTable.draw();
}
function editSWAuthorPhoto(aid,title,frmLink,fext,atype,editAuthorFn) {
	MCModalUtils.hideModal();
	$('#MCModal').on('hidden.bs.modal', function() {
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: title,
			iframe: true,
			contenturl: frmLink,
			strmodalfooter : {
				classlist: 'd-flex',
				showclose: true,
				showextrabutton: true,
				extrabuttonclass: 'btn-primary ml-auto',
				extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmFeaturedImage :submit").click',
				extrabuttonlabel: 'Upload',
				extrabuttoniconclass:'fa-light fa-file-upload'
			}
		});
		$('#MCModal').on('hidden.bs.modal', function() { editAuthorFn(aid,atype); });
	});
}
function removeSWProgramAuthor(aid,atype) {
	if (isSWProgramLocked()) return false;

	var removeResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			reloadSWAuthorGrids(atype);
			if (typeof window['onRemoveSWProgramAuthor'+atype] != 'undefined' && typeof window['onRemoveSWProgramAuthor'+atype] == 'function') {
				window['onRemoveSWProgramAuthor'+atype]();
			}
		} else {
			delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
			var objAuthor = getSWAuthorLabels(atype);
			alert(r.errmsg && r.errmsg.length ? r.errmsg : 'An error occured while removing this '+ objAuthor.authorLabelSinglular +'.');
		}
	};

	let delBtn = $('#btnDelSWProgramAuthor'+aid);
	mca_initConfirmButton(delBtn, function(){
		var objParams = { programID:getSWProgramID(), programType:sw_itemtype, authorID:aid };
		TS_AJX('ADMINSWAUTHOR','removeSWProgramAuthor',objParams,removeResult,removeResult,10000,removeResult);
	});
}
function sendSpeakerInstructions(aid,atype) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Send Speaker Instructions',
		iframe: true,
		contenturl: link_speakerSendInstr + '&aid=' + aid,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmInstructions :submit").click',
			extrabuttonlabel: 'Resend Confirmation',
			extrabuttoniconclass: 'fa-light fa-share'
		}
	});
}
function sendSpeakerInstructionsToAll() {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Email Instructions to All Speakers',
		iframe: true,
		contenturl: link_speakerSendInstr + '&all=1',
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmInstructions :submit").click',
			extrabuttonlabel: 'Send Speaker Instructions',
			extrabuttoniconclass: 'fa-light fa-share'
		}
	});
}
function getSpeakerEmailsSent(aid,atype) {
	var result = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') { 
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Speaker Email Logs',
				iframe: false,
				strmodalbody: { 
					content: r.speakerlogs
				},
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: true
				}
			});
		} else {
			var objAuthor = getSWAuthorLabels(atype);
			alert('We were unable to load '+ objAuthor.authorLabelSinglular +' email logs. Try again.');
		}
	};
	var objParams = { seminarID:sw_seminarid, authorID:aid };
	TS_AJX('ADMINSWAUTHOR','generateSpeakerLogPopup',objParams,result,result,10000,result);
}
function initSWLSpeakers() {
	swl_speaker_table = $('#swl_speaker_table').DataTable({
		"processing": true,
		"serverSide": true,
		"paginate": false,
		"language": {
			"lengthMenu": "_MENU_"
		},
		"ajax": { 
			"url": link_swspeakerslist,
			"type": "post",
			"data": function(d) { 
					if (window.reorderData && window.reorderData.length > 0) { 
						d.reorderData = JSON.stringify(window.reorderData); 
						window.reorderData = [];
					} 
					return d; 
			}
		},
		"autoWidth": false,
		"columns": [ 
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<i class="fa-light fa-bars"></i>';
						
					}
					return type === 'display' ? renderData : data;
				},
				"width": "5%",
				"orderable": false
			},
			{ "data": "speakerName", "width": "50%" },
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						if(data.emailCount > 0) renderData += '<a href="javascript:getSpeakerEmailsSent('+data.authorID+',\''+data.authorType+'\');">'+data.emailCount+'</a>';
						else renderData += '0';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "10%",
				"className": "text-center"
			},
			{ "data": "displayOnWebsite", "className": "text-center", "width": "15%", "orderable": false, },
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display' && !data.isSWProgramLocked) {
						if(!isPublisher) {
							renderData += '<a href="#" class="btn btn-xs btn-outline-primary p-1 m-1" onclick="editSWAuthor('+data.authorID+',\''+data.authorType+'\');"return false;" title="View '+data.authorLabel+'"><i class="fa-solid fa-eye"></i></a>';
						} else {
							renderData += '<a href="#" class="btn btn-xs btn-outline-primary p-1 m-1" onclick="editSWAuthor('+data.authorID+',\''+data.authorType+'\');return false;" title="Edit '+data.authorLabel+'"><i class="fa-solid fa-pencil"></i></a>';
							renderData += '<a href="javascrip:void(0);" id="btnDelSWProgramAuthor'+data.authorID+'" class="btn btn-xs btn-outline-danger p-1 m-1" onclick="removeSWProgramAuthor('+data.authorID+',\''+data.authorType+'\');" title="Remove '+data.authorLabel+'"><i class="fa-solid fa-trash-can"></i></a>';
							if(data.isOpen) {
								renderData += '<a href="#" class="btn btn-xs btn-outline-primary p-1 m-1'+(!data.authorIsSetup ? ' invisible' : '')+'" onclick="sendSpeakerInstructions('+data.authorID+',\''+data.authorType+'\');" title="Send Instructions"><i class="fa-solid fa-envelope"></i></a>';
								renderData += '<a href="#" class="btn btn-xs btn-outline-primary p-1 m-1'+(data.SWLCode != '' ? ' invisible' : '')+'" onclick="addSWLAuthorCode('+data.authorID+',\''+data.authorType+'\');" title="Needs Code"><i class="fa-solid fa-code"></i></a>';	
								renderData += '<a href="#" class="btn btn-xs btn-outline-primary p-1 m-1'+(!data.needsSetup ? ' invisible' : '')+'" onclick="addSWLProviderUserID('+data.authorID+',\''+data.authorType+'\');" title="Needs Setup"><i class="fa-solid fa-user"></i></a>';
							}
						}
					}
					return type === 'display' ? renderData : data;
				},
				"width": "20%",
				"className": "text-center"
			}
		],
		"ordering": false,
		"searching": false,
		"rowReorder": {
				dataSrc: "columnid" 
			},
		"drawCallback": function() {
			if($('#swl_speaker_table .dataTables_empty').length > 0 && programAdded)
				$('#nextButton').prop('disabled',true);
			else $('#nextButton').prop('disabled',false);
		}
	});
	swl_speaker_table.on('row-reorder', function (e, diff, edit) {
		let orderData = [];
		diff.forEach(function(item){
			orderData.push({
				id: swl_speaker_table.row(item.node).data().authorID,
				newOrder: item.newPosition
			});
		});
		window.reorderData = orderData;
	});
}
function initSWODModerators() {
	swod_moderator_table = $('#swod_moderator_table').DataTable({
		"processing": true,
		"serverSide": true,
		"paginate": false,
		"language": {
			"lengthMenu": "_MENU_",
			"info": "",
			"infoEmpty": ""
		},
		"ajax": { 
			"url": link_swodmoderatorslist,
			"type": "post",
			"data": function(d) { 
						if (window.reorderData && window.reorderData.length > 0) { 
							d.reorderData = JSON.stringify(window.reorderData); 
							window.reorderData = [];
						} 
						return d; 
				}
		},
		"autoWidth": false,
		"columns": [ 
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<i class="fa-light fa-bars"></i>';
						
					}
					return type === 'display' ? renderData : data;
				},
				"width": "5%",
				"orderable": false
			},
			{ "data": "speakerName", "width": "80%" },
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display' && !data.isSWProgramLocked) {
						renderData += '<a href="#" class="btn btn-xs btn-outline-primary p-1 m-1" onclick="editSWAuthor('+data.authorID+',\''+data.authorType+'\');return false;" title="Edit '+data.authorLabel+'"><i class="fa-solid fa-pencil"></i></a>';
						renderData += '<a href="javascript:void(0);" id="btnDelSWProgramAuthor'+data.authorID+'" class="btn btn-xs btn-outline-danger p-1 m-1" onclick="removeSWProgramAuthor('+data.authorID+',\''+data.authorType+'\');" title="Remove '+data.authorLabel+'"><i class="fa-solid fa-trash-can"></i></a>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "15%",
				"className": "text-center"
			}
		],
		"ordering": false,
		"searching": false,
		"rowReorder": {
				dataSrc: "columnid" 
			}
	});
	swod_moderator_table.on('row-reorder', function (e, diff, edit) {
		let orderData = [];
		diff.forEach(function(item){
			orderData.push({
				id: swod_moderator_table.row(item.node).data().authorID,
				newOrder: item.newPosition
			});
		});
		window.reorderData = orderData;
	});
}
function initSWODSpeakers() {
	swod_speaker_table = $('#swod_speaker_table').DataTable({
		"processing": true,
		"serverSide": true,
		"paginate": false,
		"language": {
			"lengthMenu": "_MENU_"
		},
		"ajax": { 
			"url": link_swodspeakerslist,
			"type": "post",
			"data": function(d) { 
					if (window.reorderData && window.reorderData.length > 0) { 
						d.reorderData = JSON.stringify(window.reorderData); 
						window.reorderData = [];
					} 
					return d; 
			}
		},
		"autoWidth": false,
		"columns": [ 
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<i class="fa-light fa-bars"></i>';
						
					}
					return type === 'display' ? renderData : data;
				},
				"width": "5%",
				"orderable": false
			},
			{ "data": "speakerName", "width": "65%" },
			{ "data": "displayOnWebsite", "className": "text-center", "width": "15%", "orderable": false, },
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display' && !data.isSWProgramLocked) {
						if(!isPublisher) {
							renderData += '<a href="#" class="btn btn-xs btn-outline-primary p-1 m-1" onclick="editSWAuthor('+data.authorID+',\''+data.authorType+'\');"return false;" title="View '+data.authorLabel+'"><i class="fa-solid fa-pencil"></i></a>';
						} else {
							renderData += '<a href="#" class="btn btn-xs btn-outline-primary p-1 m-1" onclick="editSWAuthor('+data.authorID+',\''+data.authorType+'\');return false;" title="Edit '+data.authorLabel+'"><i class="fa-solid fa-pencil"></i></a>';
							renderData += '<a href="javascript:void(0);" id="btnDelSWProgramAuthor'+data.authorID+'" class="btn btn-xs btn-outline-danger p-1 m-1" onclick="removeSWProgramAuthor('+data.authorID+',\''+data.authorType+'\');" title="Remove '+data.authorLabel+'"><i class="fa-solid fa-trash-can"></i></a>';
						}
					}
					return type === 'display' ? renderData : data;
				},
				"width": "15%",
				"className": "text-center"
			}
		],
		"ordering": false,
		"searching": false,
		"rowReorder": {
				dataSrc: "columnid" 
			}
	});
	swod_speaker_table.on('row-reorder', function (e, diff, edit) {
		let orderData = [];
		diff.forEach(function(item){
			orderData.push({
				id: swod_speaker_table.row(item.node).data().authorID,
				newOrder: item.newPosition
			});
		});
		window.reorderData = orderData;
	});
}

/* SW SUBJECTS common functions */
function addSWCategory() {
	if (isSWProgramLocked()) return false;

	mca_hideAlert('err_addsubject');
	var result = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			var html = '<option value="">-- Choose Subject Area --</option><optgroup label="Create a New Subject Area"><option value="0">New Subject Area</option></optgroup>';
			if (r.arrcategories.length) {
				html += '<optgroup label="Previously Saved Subject Areas">';
				for (var i=0; i<r.arrcategories.length; i++) {
					html += '<option value="'+r.arrcategories[i].categoryid+'">'+r.arrcategories[i].categoryname+'</option>';
				}
				html += '</optgroup>';
			}
			$('select#fSubjectArea').html(html);
			$('#divAddSubjectAreaForm').removeClass('d-none');
		} else {
			alert('An error occured while loading subject areas. Try again.');
		}
	};
	var objParams = { programID:getSWProgramID() };
	TS_AJX('ADMINSWCATEGORIES','getAvailableSWCategoriesToAdd',objParams,result,result,10000,result);
}
function doAddSWCategory() {
	if (isSWProgramLocked()) return false;

	var addResult = function(r) {
		$('#btnAddSubjectArea').prop('disabled',false);
		if (r.success && r.success.toLowerCase() == 'true') {
			reloadSWSubjects();
			$('#divAddSubjectAreaForm').addClass('d-none');
		} else {
			alert('An error occured while adding this subject area. Try again.');
		}
	};

	mca_hideAlert('err_addsubject');
	var categoryID = $('select#fSubjectArea').val();
	if (categoryID == "") {
		mca_showAlert('err_addsubject', 'Select a subject area');
		return false;
	} else if (categoryID == 0) {
		$('#divAddSubjectAreaForm').addClass('d-none');
		addNewSWCategory();
	} else {
		$('#btnAddSubjectArea').prop('disabled',true);
		var objParams = { programID:getSWProgramID(), programType:sw_itemtype, categoryID:categoryID };
		TS_AJX('ADMINSWCATEGORIES','addProgramCategory',objParams,addResult,addResult,10000,addResult);
	}
}
function addNewSWCategory() {
	if (isSWProgramLocked()) return false;
	
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'md',
		title: 'Create a New Subject Area',
		iframe: true,
		contenturl: link_addSWCategory,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmCategoryDetails :submit").click',
			extrabuttonlabel: 'Add'
		}
	});
}
function removeSWProgramCategory(cid) {
	if (isSWProgramLocked()) return false;

	var removeResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			reloadSWSubjects();
		} else {
			delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
			alert('Some error occured while removing this subject area. Try again.');
		}
	};
	if (!$('#divAddSubjectAreaForm').hasClass('d-none')) $('#divAddSubjectAreaForm').addClass('d-none');

	let delBtn = $('#btnDel'+cid);
	mca_initConfirmButton(delBtn, function(){
		var objParams = { programID:getSWProgramID(), programType:sw_itemtype, categoryID:cid };
		TS_AJX('ADMINSWCATEGORIES','removeProgramCategory',objParams,removeResult,removeResult,10000,removeResult);
	});
}
function initSWSubjectsTable(){
	SWSubjectsTable = $('#SWSubjectsTable').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 10,
		"lengthMenu": [ 10, 25, 50 ],
		"language": {
			"lengthMenu": "_MENU_"
		},
		"ajax": { 
			"url": categoriesListLink,
			"type": "post"
		},
		"autoWidth": false,
		"columns": [
			{ "data": "categoryName", "width": "90%" },
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						if(!data.isSWProgramLocked){
							renderData += '<a href="#" id="btnDel'+data.categoryID+'" class="btn btn-xs btn-outline-danger p-1 m-1" title="Remove Subject Area" onclick="removeSWProgramCategory('+data.categoryID+');return false;"><i class="fa-solid fa-trash-can"></i></a>';
						}							
					}
					return type === 'display' ? renderData : data;
				},
				"width": "10%",
				"className": "text-center",
				"orderable": false 
			}
		],
		"order": [[0, 'asc']]
	});
}
function cancelAddSubjectArea(){
	if ($('#divAddSubjectAreaForm').is(':visible')) {
		mca_hideAlert('err_addsubject');
		$('#fSubjectArea').val('');
		$('#divAddSubjectAreaForm').addClass('d-none');
	}
}
function reloadSWSubjects(swType='') {
	if(swType=='SWL' || swType=='SWOD'){
		getAvailableSWCategories();
	}else{
		SWSubjectsTable.draw();
	}
}
function getAvailableSWCategories() {
	if (isSWProgramLocked()) return false;
	
	var categoryResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			var html = '';
			if (r.arrcategories.length) {
				for (var i=0; i<r.arrcategories.length; i++) {
					var isSelected = r.arrcategories[i].isadded === 1 ? 'selected' : '';
					html += '<option value="'+r.arrcategories[i].categoryid+'" ' + isSelected + '>'+r.arrcategories[i].categoryname+'</option>';
				}
			}
			$('select#subjectArea').html(html);
			mca_setupSelect2ByID('subjectArea');
		} else {
			alert('An error occured while loading subject areas. Try again.');
		}
	};
	var objParams = { programID:getSWProgramID() };
	TS_AJX('ADMINSWCATEGORIES','getAvailableSWCategories',objParams,categoryResult,categoryResult,10000,categoryResult);
}
function addSWProgramCategory(data){
	if (isSWProgramLocked()) return false;

	var addResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
		} else {
			alert('An error occured while adding this subject area. Try again.');
		}
	};
	var objParams = { programID:getSWProgramID(), programType:sw_itemtype, categoryID:data.id };
	TS_AJX('ADMINSWCATEGORIES','addProgramCategory',objParams,addResult,addResult,10000,addResult);
}
function deleteSWProgramCategory(data){
	if (isSWProgramLocked()) return false;

	var removeResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
		} else {
			alert('An error occured while deleting this subject area. Try again.');
		}
	};
	var objParams = { programID:getSWProgramID(), programType:sw_itemtype, categoryID:data.id };
	TS_AJX('ADMINSWCATEGORIES','removeProgramCategory',objParams,removeResult,removeResult,10000,removeResult);
}

/* SW Forms common functions */
function reloadSWForms(lp) {
	window['SW_'+lp.toLowerCase()+'_FormsTable'].draw();
}
function initSWForms(ft,lp) {
	var link_SWExamTypeFormsList = link_SWFormsList + '&loadPoint='+lp;
	
	var dataTableSettings = {
		"processing": true,
		"serverSide": true,
		"paginate": false,
		"language": {
			"lengthMenu": "_MENU_"
		},
		"ajax": { 
			"url": link_SWExamTypeFormsList,
			"type": "post",
			"data": function(d) { 
				if (window.reorderData && window.reorderData.length > 0) { 
					d.reorderData = JSON.stringify(window.reorderData); 
					window.reorderData = [];
				} 
				return d; 
			}
		},
		"autoWidth": false,
		"columns": [
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<i class="fa-light fa-bars"></i>';

					}
					return type === 'display' ? renderData : data;
				},
				"width": "5%",
				"orderable": false
			}, 
			{ "data": "formTitle", "width": "85%" },
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display' && !data.isSWProgramLocked) {
						renderData += '<a href="#" title="Edit Settings" class="btn btn-xs text-primary p-1 mx-1" onclick="editSWForm('+data.seminarFormID+');return false;"><i class="fa-solid fa-pencil"></i></a>';
						renderData += '<a href="#" title="Remove From Program" id="btnDelSWForm'+data.seminarFormID+'" class="btn btn-xs text-danger p-1 mx-1" data-loadpoint="'+data.loadpoint+'" onclick="deleteSWForm('+data.seminarFormID+','+data.numResponses+');return false;"><i class="fa-solid fa-trash-can"></i></a>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "10%",
				"className": "text-center"
			}
		],
		"info": false,
		"ordering": false,
		"searching": false,
		"rowReorder": {
			dataSrc: "columnid" 
		},
		"initComplete": function() {
			var thisTable = this.DataTable();
			thisTable.on('row-reorder', function (e, diff, edit) {
				let orderData = [];
				diff.forEach(function(item) {
					orderData.push({
						id: thisTable.row(item.node).data().seminarFormID,
						newOrder: item.newPosition
					});
				});
				window.reorderData = orderData;
			});
		},
		"drawCallback": function(settings) {
			if(ft == 'SWOD' || ft == 'SWL') {
				let SWFormsTableName = 'SW_'+lp.toLowerCase()+'_FormsTable';
				let recordsTotal = window[SWFormsTableName].page.info().recordsTotal;
				let selectElement;
				let holderElement;
				if (lp.toLowerCase() == 'pretest') {
					selectElement = $('#preTestRequired');
					holderElement = $('#preTestRequiredHolder');
				} else if (lp.toLowerCase() == 'posttest') {
					selectElement = $('#examRequired');
					holderElement = $('#examRequiredHolder');
				} else {
					selectElement = $('#evaluationRequired');
					holderElement = $('#evaluationRequiredHolder');
				}
				if (recordsTotal) {
					selectElement.prop('checked',true);
					selectElement.prop('disabled',true);
				}
				else {
					selectElement.prop('checked',false);
					if ( !isSWProgramLocked() )
					selectElement.prop('disabled',false);
					holderElement.addClass("d-none");
				}
			}
		}
	};

	window['SW_'+lp.toLowerCase()+'_FormsTable'] = $('#SW_'+lp.toLowerCase()+'_FormsTable').DataTable(dataTableSettings);
}
function deleteSWForm(fid, numResponses) {
	if (isSWProgramLocked()) return false;

	if(numResponses > 0){
		var msg = '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"> \
					<span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-question-circle"></i></span> \
					<span><strong class="d-block">Are you sure you want to remove this form from this seminar?</strong> \
					This form has <strong>'+ numResponses +' responses</strong>. By continuing, we will <strong>inactivate</strong> those responses.<br/> \
					<strong>There is no reversal of this action</strong>.</span></div>';
		MCModalUtils.showModal({
			verticallycentered: true,
			size: 'lg',
			title: 'Confirmation Needed',
			strmodalbody: {
				content: msg
			},
			strmodalfooter : {
				classlist: 'd-flex',
				showclose: true,
				showextrabutton: true,
				extrabuttonclass: 'ml-auto btn-outline-danger',
				extrabuttonlabel: 'Remove'
			}
		});
		
		$('#btnMCModalSave').on('click', function(){
			doDeleteSWForm(fid,true);
		});
	} else {
		let delBtn = $('#btnDelSWForm'+fid);
		mca_initConfirmButton(delBtn, function(){
			doDeleteSWForm(fid, 0, delBtn);
		});
	}
}
function doDeleteSWForm(fid, isPopup, delElement){
	if (isSWProgramLocked()) return false;

	let loadPoint = delElement && delElement.length ? delElement.data('loadpoint') : $('#btnDelSWForm'+fid).data('loadpoint');

	var deleteSWFormResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			reloadSWForms(loadPoint);
			if(isPopup) MCModalUtils.hideModal();;
		} else { 
			if(delElement) delElement.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
			alert('An error occured while removing this form. Try again.');
		}
	};
	var objParams = { seminarID:sw_seminarid, seminarformID:fid };
	TS_AJX('ADMINSWFORM','removeSeminarForm',objParams,deleteSWFormResult,deleteSWFormResult,10000,deleteSWFormResult);
}
function doAddSWPreTest() {
	if (isSWProgramLocked()) return false;

	var addResult = function(r) {
		$('#btnAddPreTest').prop('disabled',false);
		if (r.success && r.success.toLowerCase() == 'true') {
			reloadSWForms('preTest');
			$('#fPreTest').val('');
			editSWForm(r.seminarformid);
		} else {
			alert('An error occured while adding this form to the program. Try again.');
		}
	};

	mca_hideAlert('err_addpretest');
	var arrReq = [];
	var formID = $('#fPreTest').val();
	if (formID == "") arrReq[arrReq.length] = 'Select a Pre-Test';

	if (arrReq.length) {
		mca_showAlert('err_addpretest', arrReq.join('<br/>'));
		return false;
	}

	$('#btnAddPreTest').prop('disabled',true);

	var objParams = { seminarID:sw_seminarid, formID:formID, loadPoint:'preTest' };
	TS_AJX('ADMINSWFORM','addSeminarForm',objParams,addResult,addResult,10000,addResult);
}
function doAddSWExam() {
	if (isSWProgramLocked()) return false;

	var addResult = function(r) {
		$('#btnAddExam').prop('disabled',false);
		if (r.success && r.success.toLowerCase() == 'true') {
			reloadSWForms('postTest');
			$('#fExam').val('');
			editSWForm(r.seminarformid);
		} else {
			alert('An error occured while adding this form to the program. Try again.');
		}
	};

	mca_hideAlert('err_addexam');
	var arrReq = [];
	var formID = $('#fExam').val();
	if (formID == "") arrReq[arrReq.length] = 'Select an Exam';

	if (arrReq.length) {
		mca_showAlert('err_addexam', arrReq.join('<br/>'));
		return false;
	}

	$('#btnAddExam').prop('disabled',true);

	var objParams = { seminarID:sw_seminarid, formID:formID, loadPoint:'postTest' };
	TS_AJX('ADMINSWFORM','addSeminarForm',objParams,addResult,addResult,10000,addResult);
}
function doAddSWEvaluation() {
	if (isSWProgramLocked()) return false;

	var addResult = function(r) {
		$('#btnAddEvaluation').prop('disabled',false);
		if (r.success && r.success.toLowerCase() == 'true') {
			reloadSWForms('evaluation');
			$('#fEvaluation').val('');
			editSWForm(r.seminarformid);
		} else {
			alert('An error occured while adding this evaluation to the program. Try again.');
		}
	};

	mca_hideAlert('err_addevaluation');
	var arrReq = [];
	var formID = $('select#fEvaluation').val();
	if (formID == "") arrReq[arrReq.length] = 'Select an Evaluation';

	if (arrReq.length) {
		mca_showAlert('err_addevaluation', arrReq.join('<br/>'));
		return false;
	}

	$('#btnAddEvaluation').prop('disabled',true);

	var objParams = { seminarID:sw_seminarid, formID:formID, loadPoint:'evaluation' };
	TS_AJX('ADMINSWFORM','addSeminarForm',objParams,addResult,addResult,10000,addResult);
}
function editSWForm(sfid) {
	if (isSWProgramLocked()) return false;

	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: sfid > 0 ? 'Edit Form' : 'Add Form',
		iframe: true,
		contenturl: link_editSWForm + '&sfid=' + sfid,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmSWForm :submit").click',
			extrabuttonlabel: sfid > 0 ? 'Save Details' : 'Add',
		}
	});
}

/* SW Rates common functions */
function manageSWRates() {
	hideSWProgramPricingAlert();
	$('.divSWRateTool').addClass('d-none');
	$('#divRatesGridContainer').removeClass('d-none');

	if ($('input[name="allowCatalog"]:checked').val() == 0){
		$('#divRatesGridContainer').addClass('d-none');
	}
}
function manageSWRateOptions(){
	manageSWRates();
	$('#divRateSelection').removeClass('d-none');
}
function saveSWRateOptions() {
	if (isSWProgramLocked()) return false;

	var saveRateOptionsResult = function(r) {
		$("#btnSaveSWProgramOption").prop('disabled',false).html('Save Changes');
		if (r.success && r.success.toLowerCase() == 'true') {
			if ($('#GLSaveWarning').length) {
				$('#GLSaveWarning').remove();
			}
			if(sw_itemtype == 'SWOD' && $('input[name="allowCatalog"]:checked').val() == 0){
				reloadSWRatesGrid();
			}
			$('#saveSWProgramOptionInfo').html('<span class="badge badge-success">Saved Successfully</span>').show().fadeOut(5000);
		} else {
			showSWProgramPricingAlert(r.err && r.err.length ? r.err : 'We were unable to save Rate options.');
		}
	};

	$("#btnSaveSWProgramOption").prop('disabled',true).html('Saving...');	
	var revGL = 0;
	if ($.trim($('#revenueGLAccountID').val()).length)
		revGL = $('#revenueGLAccountID').val();

	var strErr = '';
	var allowCatalog = $('input[name="allowCatalog"]:checked').val();
	if (allowCatalog == 1) {
		if ($('#dateCatalogStart').val().trim().length == 0)
			strErr += 'Enter the starting catalog availability date.<br/>';
		if ($('#dateCatalogEnd').val().trim().length == 0)
			strErr += 'Enter the ending catalog availability date.<br/>';
	} else {
		mca_clearDateRangeField('dateCatalogStart','dateCatalogEnd');
	}	
	if (strErr.length > 0) {
		var r = { "success":false, "err":strErr };
		saveRateOptionsResult(r);
		return false;
	} else {
		hideSWProgramPricingAlert();
		var objParams = { seminarID:sw_seminarid, 
			isPriceBasedOnActual:$('input[name=isPriceBasedOnActual]:checked').val(), revenueGLAccountID:revGL, 
			allowCatalog:$('input[name=allowCatalog]:checked').val(), dateCatalogStart:$('#dateCatalogStart').val(), 
			dateCatalogEnd:$('#dateCatalogEnd').val(), freeRateDisplay:$('input[name=freeRateDisplay]:checked').val() };
		TS_AJX((sw_itemtype == 'SWL') ? 'ADMINSWL' : 'ADMINSWOD','saveSeminarRateOptions',objParams,saveRateOptionsResult,saveRateOptionsResult,10000,saveRateOptionsResult);
	}
}
function addSyndicatedRate(){
	var maxRowCount = 4;
	
	$("table#syndRate #emptyRow").remove();
	var currentRowCount = $("table#syndRate tbody tr").length;
	
	if (currentRowCount < maxRowCount) {
		var newRow = `
			<tr>
				<td>${currentRowCount + 1}</td>
				<td><input type="text" name="syndGroup${currentRowCount + 1}" id="syndGroup${currentRowCount + 1}" value="" size="24" class="form-control form-control-sm"></td>
				<td><input type="text" name="syndPrice${currentRowCount + 1}" id="syndPrice${currentRowCount + 1}" value="" size="6" class="form-control form-control-sm syndPrice" onBlur="if (this.value != '') this.value=formatCurrency(this.value);"></td>
				<td><i class="fa-solid fa-circle-minus" onClick="removeSWRateSyndication(this)"></i></td>
			</tr>
		`;
		$("table#syndRate tbody").append(newRow);
	} else {
		alert("Maximum row count reached.");
	}
}
function removeSWRateSyndication(thisObj){
	$(thisObj).parent().parent().remove();
	if($("table#syndRate tbody tr").length==0){
		$("table#syndRate tbody").append('<tr id="emptyRow"><td colspan="4" class="text-center">No Rates Found</td></tr>');
	}
}
function saveSWSyndProgramRateOptions() {
	if (isSWProgramLocked()) return false;

	var saveRateOptionsResult = function(r) {
		$("#btnSaveSWProgramOption").prop('disabled',false).html('Save Changes');
		if (r.success && r.success.toLowerCase() == 'true'){
			$('#saveSWSyndRateOptsInfo').html('<span class="badge badge-success">Saved Successfully</span>').show().fadeOut(5000);
		} else {
			showSWProgramPricingAlert(r.err && r.err.length ? r.err : 'We were unable to save Rate options.');
		}
	};

	hideSWProgramPricingAlert();
	$("#btnSaveSWProgramOption").prop('disabled',true).html('Saving...');
	
	var objParams = { programID:getSWProgramID(), programType:sw_itemtype,
		isPriceBasedOnActual:$('input[name=isPriceBasedOnActual]:checked').val(), freeRateDisplay:$('input[name=freeRateDisplay]:checked').val() };
	TS_AJX('ADMINSWCOMMON','saveSWSyndProgramRateOptions',objParams,saveRateOptionsResult,saveRateOptionsResult,10000,saveRateOptionsResult);
}
function showSWSyndicationGroups(v) {
	if (v) $('#divSyndicationGroups').removeClass('d-none');
	else $('#divSyndicationGroups').addClass('d-none');
}
function manageSWSyndication(){
	if (isSWProgramLocked()) return false;

	manageSWRates();
	$('#divRatesGridContainer').addClass('d-none');
	$('#divSyndication').removeClass('d-none');
}
function saveSWRateSyndication() {
	if (isSWProgramLocked()) return false;

	var saveRateSyndResult = function(r) {
		$("#btnSaveSWProgramSyndication").prop('disabled',false).html('Save Changes');
		if (r.success && r.success.toLowerCase() == 'true'){
			$('#saveSWProgramSyndicationInfo').html('<span class="badge badge-success">Saved Successfully</span>').show().fadeOut(5000);
			if ($('#oldAllowSyndication').val() != allowSyndication) {
				reloadSWEditProgram('rates');
			}
		} else {
			showSWProgramPricingAlert(r.err && r.err.length ? r.err : 'We were unable to save Syndication Options.');
		}
	};

	$("#btnSaveSWProgramSyndication").prop('disabled',true).html('Saving...');
	var strErr = '';
	var xmlsynd = '<syndication>';
	var allowSyndication = $('input[name="allowSyndication"]:checked').val();
	if (allowSyndication == 1) {
		for (var i=1; i<=4; i++){
			if($('#syndGroup' + i) && $('#syndGroup' + i).val().trim() != '' && $('#syndPrice' + i).val().trim() == ''){
				strErr += 'Enter valid syndication prices.';
				break;
			} else if ($('#syndGroup' + i) && $('#syndGroup' + i).val().trim() == '' && formatCurrency($('#syndPrice' + i).val()) == '0.00') {
				$('#syndPrice' + i).val('');
			} else if ($('#syndGroup' + i) && $('#syndGroup' + i).val().trim() != '' && $('#syndPrice' + i).val().trim() != '') {
				xmlsynd = xmlsynd + '<pricegroup><group>' + $('#syndGroup' + i).val().trim() + '</group><price>' + formatCurrency($('#syndPrice' + i).val()).replace(/,/g,'') + '</price></pricegroup>';
			}
		}
	}
	xmlsynd = xmlsynd + '</syndication>';
	if (strErr.length > 0) {
		var r = { "success":false, "err":strErr };
		saveRateSyndResult(r);
		return false;
	} else {
		hideSWProgramPricingAlert();
		var objParams = { programID:getSWProgramID(), programType:sw_itemtype, xmlsynd:xmlsynd, allowSyndication:allowSyndication,
			pushDefaultPricingToOptIns:$('input[name="pushDefaultPricingToOptIns"]:checked').val(),
			allowOptInRateChange:$('input[name="allowOptInRateChange"]:checked').val() };
		TS_AJX('ADMINSWCOMMON','saveSWProgramRateSyndication',objParams,saveRateSyndResult,saveRateSyndResult,40000,saveRateSyndResult);
	}
}
function refreshSetupAlerts(swType){
	if(!$('#allowCatalogOption').length) return;

	if ($('#allowCatalogOption').prop('checked')) {
		removeStatement(notSoldInCatalogIssue);
		let dateStart = moment($('#dateStartCatalog').val(), 'M/D/YYYY');
		let dateEnd = moment($('#dateEndCatalog').val(), 'M/D/YYYY');
		let today = moment().startOf('day');
		
		if (dateEnd.isAfter(today)) 
			removeStatement(expiredDatesIssue);
		else addStatementIfNotExists(expiredDatesIssue);
		
		if (dateStart.isBefore(today) || dateStart.isSame(today)) 
			removeStatement(sellDatesInFutureIssue);
		else addStatementIfNotExists(sellDatesInFutureIssue);
		
		if ((dateEnd.isAfter(today)) && (dateStart.isBefore(today) || dateStart.isSame(today))) 
			validDate = true;
		
		if ($('#swProgramRatesTableCatalog tbody td.dataTables_empty').length == 0) {
			removeStatement(ratesIssue);
			validRate = true;
		} else {
			addStatementIfNotExists(ratesIssue);
		}
		if (swType != 'SWB' && bundleIsActive && validDate && validRate){
			addStatementIfNotExists(sellInCatalogWithActiveBundleSuccess + bundleLinks,'seminarSetupStatements');
			removeStatement(sellingSuccessStatement,'seminarSetupStatements');
			removeStatement(notSellInCatalogInactiveBundleStatement,'seminarSetupStatements');
		}
		else if(swType != 'SWB' && !bundleIsActive && validDate && validRate ){
			addStatementIfNotExists(sellingSuccessStatement,'seminarSetupStatements');
			removeStatement(notSellInCatalogInactiveBundleStatement,'seminarSetupStatements');
			removeStatement(sellInCatalogWithActiveBundleSuccess,'seminarSetupStatements');
		}
		if(swType == 'SWB' && validDate && validRate) {
			addStatementIfNotExists(sellingSuccessStatement,'seminarSetupStatements');
		}
	} else {
		if(swType != 'SWB' && !bundleIsActive) 
			addStatementIfNotExists(notSoldInCatalogIssue);
		else if(swType == 'SWB') {
			addStatementIfNotExists(notSoldInCatalogIssue);
			removeStatement(sellingSuccessStatement,'seminarSetupStatements');
		}
		removeStatement(expiredDatesIssue);
		removeStatement(ratesIssue);
		removeStatement(sellDatesInFutureIssue);
		if(swType != 'SWB' && bundleIsActive) {
			addStatementIfNotExists(notSellInCatalogInactiveBundleStatement + bundleLinks,'seminarSetupStatements');
			removeStatement(sellInCatalogWithActiveBundleSuccess,'seminarSetupStatements');
			removeStatement(sellingSuccessStatement,'seminarSetupStatements');
		}
	}
		
	// If no more issues, hide the entire alert
	if ($('.seminarSetupIssues ul li').length === 0) {
		$('.seminarSetupIssues').addClass('d-none');
		$('.seminarSetupStatements').removeClass('d-none');
	}
	else if($('.seminarSetupIssues').hasClass('d-none')) {
		$('.seminarSetupIssues').removeClass('d-none');
		$('.seminarSetupStatements').addClass('d-none');
	}
}
function initSWProgramRatesTable(swType, mode=''){
	let objFuncNames;

	switch(swType) {
		case "SWL":
		case "SWOD":
			objFuncNames = {
				rateGroupingEdit: 'editSWRateGrouping',
				rateGroupingRemove: 'removeSWRateGrouping',
				rateGroupingMove: 'moveSWRateGrouping',
				rateEdit: 'editSWRate',
				rateRemove: 'removeSWRate',
				rateCopy: 'copySWRate',
				groupRemove: 'removeSWMemberGroup'
			}
			break;
		case "SWB":
			objFuncNames = {
				rateGroupingEdit: 'editSWBRateGrouping',
				rateGroupingRemove: 'removeSWBRateGrouping',
				rateGroupingMove: 'moveSWBRateGrouping',
				rateEdit: 'editSWBRate',
				rateRemove: 'removeSWBRate',
				groupRemove: 'removeSWBMemberGroup'
			}
			break;
	}

	swProgramRatesTable = $('#swProgramRatesTable'+mode).DataTable({
		"processing": true,
		"serverSide": true,
		"paging": false,
		"info": false,
		"language": {
			"emptyTable": "No Rates Found."
		},
		"ajax": { 
			"url": link_listSWRates,
			"type": "post",
			"data": function(d) { 
					if (window.reorderData && window.reorderData.length > 0) { 
						d.reorderData = JSON.stringify(window.reorderData); 
						window.reorderData = [];
					} 
					return d; 
				}
		},
		"autoWidth": false,
		"columns": [
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						if (data.rowType == 'rate') {
							renderData += '<div class="row-drag-handle"><i class="fa-light fa-bars"></i></div>';
						}
					}
					return type === 'display' ? renderData : data;
				},
				"width": "5%",
				"orderable": false
			},
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let thisRowID = data['DT_RowId'];
					let renderData = '';
					if (type === 'display') {
						let cellPadding = data.level > 1 ? 24 * (data.level-1) : 0;
						let rowTypeIcon = '';

						if(data.rowType == 'rate') rowTypeIcon = '<i class="fas fa-money-bill text-goldenrod fa-fw pr-4"></i>';
						else if(data.rowType == 'group') rowTypeIcon = '<i class="fas fa-users text-green fa-fw pr-4"></i>';

						renderData += '<div style="padding-left:'+cellPadding+'px;">';
						if (data.hasChildren) {
							renderData += '<a href="javascript:toggleSWRatesGridParentRow(\''+thisRowID+'\');" id="displayLabel_'+thisRowID+'"><i class="'+ (data.rowType == 'rateGroup' ? 'fas fa-folder-minus' : 'far fa-minus-square') +' fa-fw rowToggleBtn pr-2"></i> '+ rowTypeIcon + data.displayName+'</a>';
						} else {
							renderData += '<span><i class="fas fa-folder-tree fa-fw pr-2 invisible"></i> '+ rowTypeIcon + data.displayName+'</span>';
						}
						renderData += '</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "60%",
				"className": "align-top"
			},
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					return type === 'display' ? (data.rowType == 'rate' ? data.rateFormatted : '') : data;
				},
				"width": "10%",
				"className": "text-right"
			},
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let thisRowID = data['DT_RowId'];
					let renderData = '';
					if (type === 'display') {
						switch(data.rowType) {
							case "rateGroup":
								if(data.hasChangePerms){
									renderData += '<a href="#" class="btn btn-xs p-1 mx-1 invisible"><i class="fas fa-users"></i></a>';
									renderData += '<a href="#" class="btn btn-xs text-primary p-1 mx-1" onclick="'+objFuncNames.rateGroupingEdit+'('+data.rateGroupingID+');return false;" title="Edit Rate Grouping"><i class="fas fa-pencil"></i></a>';
									renderData += '<a href="#" class="btn btn-xs text-danger p-1 mx-1" onclick="'+objFuncNames.rateGroupingRemove+'('+data.programID+','+data.rateGroupingID+');return false;" title="Remove Rate Grouping"><i class="fas fa-trash-alt"></i></a>';
									if(swType == 'SWL' || swType == 'SWOD'){
										renderData += '<a href="#" class="btn btn-xs p-1 mx-1 invisible"><i class="fas fa-copy"></i></a>';
									}
									renderData += '<a href="#" class="btn btn-xs text-green p-1 mx-1 rtRowMoveUp'+(data.canMoveUp ? "" : " invisible")+'" onclick="'+objFuncNames.rateGroupingMove+'('+data.rateGroupingID+',\''+thisRowID+'\',\''+data.parentRowID+'\',\'up\');return false;" title="Move Rate Grouping Up"><i class="fas fa-arrow-up"></i></a>';
									renderData += '<a href="#" class="btn btn-xs text-green p-1 mx-1 rtRowMoveDown'+(data.canMoveDown ? "" : " invisible")+'" onclick="'+objFuncNames.rateGroupingMove+'('+data.rateGroupingID+',\''+thisRowID+'\',\''+data.parentRowID+'\',\'down\');return false;" title="Move Rate Grouping Down"><i class="fas fa-arrow-down"></i></a>';
								}
								break;
							case "rate":
								if(data.canAddGroup) renderData += '<a href="#" class="btn btn-xs text-primary p-1 mx-1" onclick="launchSWShowRatePerms('+data.rateSRID+',\''+data.displayNameEncoded+'\');return false;" title="Add Group to Rate"><i class="fa fa-users"></i></a>';
								else renderData += '<a href="#" class="btn btn-xs p-1 mx-1 invisible"><i class="fas fa-users"></i></a>';
								
								if(data.hasChangePerms){
									renderData += '<a href="#" class="btn btn-xs text-primary p-1 mx-1" onclick="'+objFuncNames.rateEdit+'('+data.rateID+');return false;" title="Edit Rate"><i class="fas fa-pencil"></i></a>';
									renderData += '<a href="#" class="btn btn-xs text-danger p-1 mx-1" onclick="'+objFuncNames.rateRemove+'('+data.rateID+');return false;" title="Remove Rate"><i class="fas fa-trash-alt"></i></a>';
									if(swType == 'SWL' || swType == 'SWOD'){
										renderData += '<a href="#" class="btn btn-xs text-primary p-1 mx-1" onclick="'+objFuncNames.rateCopy+'('+data.rateID+');return false;" title="Copy Rate"><i class="fas fa-copy"></i></a>';
									}
									renderData += '<a href="#" class="btn btn-xs p-1 mx-1 invisible"><i class="fas fa-arrow-up"></i></a>';
									renderData += '<a href="#" class="btn btn-xs p-1 mx-1 invisible"><i class="fas fa-arrow-down"></i></a>';
								}
								break;
							case "group":
								if (data.canRemove) {
									renderData += '<a href="#" class="btn btn-xs p-1 mx-1 invisible"><i class="fas fa-users"></i></a>';
									renderData += '<a href="#" class="btn btn-xs p-1 mx-1 invisible"><i class="fas fa-pencil"></i></a>';
									renderData += '<a href="#" class="btn btn-xs text-danger p-1 mx-1" onclick="'+objFuncNames.groupRemove+'('+data.rateID+','+data.groupID+','+data.include+');return false;" title="Remove Group from Rate"><i class="fas fa-trash-alt"></i></a>';
									if(swType == 'SWL' || swType == 'SWOD'){
										renderData += '<a href="#" class="btn btn-xs p-1 mx-1 invisible"><i class="fas fa-copy"></i></a>';
									}
									renderData += '<a href="#" class="btn btn-xs p-1 mx-1 invisible"><i class="fas fa-arrow-up"></i></a>';
									renderData += '<a href="#" class="btn btn-xs p-1 mx-1 invisible"><i class="fas fa-arrow-down"></i></a>';
								}
								break;
						}
					}
					return type === 'display' ? renderData : data;
				},
				"className": "text-center align-top"
			}
		],
		"searching": false,
		"ordering": false,
		rowReorder: {
			dataSrc: "columnid",
			selector: '.row-drag-handle' 
		},
		"createdRow": function (row, data, index) {
			$(row).attr('data-rowType', data.rowType);
			$(row).attr('data-rategroupingid', data['rateGroupingID']);
				if (data.rowType !== 'rate') {
					
					$(row).find('.row-drag-handle').css('pointer-events', 'none');
				}
			if(data.rowType == 'rate' && !data.hasChangePerms){
				$(row).find('td:nth-child(3)').toggleClass('text-center text-left');
			}
		},
		"drawCallback": function(settings) {
			let rowsWithGroup = $('#swProgramRatesTable'+mode+' tbody tr[data-rowType="group"]');
			let saveSWProgramRate = false;
			
			if (rowsWithGroup.length > 0 && !$('#isPriceBasedOnActualOption').is(':checked')) {
				$('#isPriceBasedOnActualOption').prop("checked", true);
				saveSWProgramRate = true;
			} else if(rowsWithGroup.length == 0 && $('#isPriceBasedOnActualOption').is(':checked')){
				$('#isPriceBasedOnActualOption').prop("checked", false);
				saveSWProgramRate = true;
			}
			if (saveSWProgramRate) {
				switch(swType) {
					case 'SWL': saveSWLRateCatalog('',null,true); break;
					case 'SWOD': saveSWODRateCatalog('',null,true); break;
					case 'SWB': saveSWBRateCatalog('',null,true); break;
				}
			}
			if(typeof doRefreshSetupAlerts != "undefined" && doRefreshSetupAlerts == 1 && (swType == 'SWL' || swType == 'SWOD' || swType == 'SWB')){
				refreshSetupAlerts(swType);
				doRefreshSetupAlerts = false;
			}
		}
	});
	swProgramRatesTable.on('row-reorder', function (e, diff, edit) {
		let orderData = [];
		let isValidReorder = true;

		const draggedRow = swProgramRatesTable.row(edit.triggerRow.node()).data();
		const groupID = draggedRow.rateGroupingID;
		
		const reorderedRates = diff
			.map(d => swProgramRatesTable.row(d.node).data())
			.filter(d => d && d.rowType === 'rate');
		/*Check all reordered rows are within the same group */
		isValidReorder = reorderedRates.every(row => row.rateGroupingID === groupID);

		if (!isValidReorder) {
			window.reorderData = [];
			swProgramRatesTable.draw(false);
			alert("Can only reorder rate rows within the same rate group.");
			return;
		}
	let rateRowsInGroup = [];	
	swProgramRatesTable.rows().every(function (idx, tableLoop, rowLoop) {
		const d = this.data();
		if (d && d.rowType === 'rate' && d.rateGroupingID === groupID) {
			rateRowsInGroup.push({
				id: d.rateID,
				rowData: d,
				rowIndex: this.index()
			});
		}
	});	
	rateRowsInGroup.sort((a, b) => {
		const nodeA = swProgramRatesTable.row(a.rowIndex).node();
		const nodeB = swProgramRatesTable.row(b.rowIndex).node();
		return $(nodeA).index() - $(nodeB).index();
	});
	
	rateRowsInGroup.forEach((row, i) => {
		orderData.push({
			id: row.id,
			rateGroupingID: groupID,
			newOrder: i
		});
	});

	window.reorderData = orderData;
});
}
function reloadSWRatesGrid() {
	swProgramRatesTable.draw();
}
function toggleSWRatesGridParentRow(rowID) {	
	var swProgramRatesTableID = swProgramRatesTable.table().node().id;
	let rowType = $('#'+swProgramRatesTableID+' #'+rowID).attr('data-rowType');
	let rowToggleBtn = $('#'+swProgramRatesTableID+' #displayLabel_'+rowID+' i.rowToggleBtn');
	rowToggleBtn.toggleClass(rowType == 'rateGroup' ? 'fa-folder-plus fa-folder-minus' : 'fa-plus-square fa-minus-square');
	let showChildren = rowToggleBtn.hasClass(rowType == 'rateGroup' ? 'fa-folder-minus' : 'fa-minus-square');
	toggleSWRatesGridChildRows(rowID,showChildren);
}
function toggleSWRatesGridChildRows(rowID,f) {
	var swProgramRatesTableID = swProgramRatesTable.table().node().id;
	$('#'+swProgramRatesTableID+' tr.child-of-'+rowID).toggleClass('d-none',!f).each(function(i,thisRow) {
		let expandedIconClass = $(this).attr('data-rowType') == 'rateGroup' ? 'fa-folder-minus' : 'fa-minus-square';
		if ($(this).find('i.rowToggleBtn').hasClass(expandedIconClass)) toggleSWRatesGridChildRows($(this).attr('id'),f);
	});
}
function moveSWRatesGridRow(rowID,pRowID,dir) {
	var swProgramRatesTableID = swProgramRatesTable.table().node().id;
	let movingRow, targetRow;	

	if(dir == 'up'){
		movingRow = $('#'+swProgramRatesTableID+' #'+rowID);
		targetRow = movingRow.closest('tr').prevAll('tr.child-of-'+pRowID+':first');
	}
	else {
		movingRow = $('#'+swProgramRatesTableID+' #'+rowID).closest('tr').nextAll('tr.child-of-'+pRowID+':first'); /*next row will be moved to top*/
		targetRow = $('#'+swProgramRatesTableID+' #'+rowID);
	}

	let movingRowID = movingRow.attr('id');
	movingRow.addClass('moveRow-' + movingRowID);
	markSWAssocRateRows(movingRowID,movingRowID);

	let arrMoveRows = $('#'+swProgramRatesTableID+' tr.moveRow-'+movingRowID);
	arrMoveRows.remove().insertBefore(targetRow);
	$('#'+swProgramRatesTableID+' tr.moveRow-'+movingRowID).removeClass('moveRow-' + movingRowID);
	resetSWRatesGridMoveIcons(pRowID);
}
function markSWAssocRateRows(parentRowID,moveRowID) {
	var swProgramRatesTableID = swProgramRatesTable.table().node().id;
	let childRows = $('#'+swProgramRatesTableID+' tr.child-of-'+parentRowID);
	if (childRows.length) {
		childRows.addClass('moveRow-' + moveRowID).each(function() {
			markSWAssocRateRows($(this).attr('id'),moveRowID);
		});
	}
}
function resetSWRatesGridMoveIcons(pRowID) {
	var swProgramRatesTableID = swProgramRatesTable.table().node().id;
	let childRows = $('#'+swProgramRatesTableID+' tr.child-of-'+pRowID).not('.default-nogrouping');
	if (childRows.length > 1) {
		childRows.find('a.rtRowMoveUp,a.rtRowMoveDown').removeClass('invisible');
		childRows.find('a.rtRowMoveUp').first().addClass('invisible');
		childRows.find('a.rtRowMoveDown').last().addClass('invisible');
	} else {
		childRows.find('a.rtRowMoveUp,a.rtRowMoveDown').addClass('invisible');
	}
}
function launchSWShowRatePerms(rid,rt){
	if (isSWProgramLocked()) return false;
	mca_showPermissions(rid,rt,null,null,null,null,'Add Group for ' + rt,1,'swProgramRatePermClose');
}
function swProgramRatePermClose(){
	reloadSWRatesGrid();
	MCModalUtils.hideModal();
}
function editSWRateGrouping(rgid){
	if (isSWProgramLocked()) return false;

	mca_hideAlert('err_rategrouping');
	manageSWRates();
	var editRateGroupResult	= function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			let swRateGroupingTemplate = Handlebars.compile($('#mc_swRateGroupingTemplate').html());
			$('#MCModalBody').html(swRateGroupingTemplate({rateGroupingID:r.rategroupingid,rateGrouping:r.rategrouping}));
		} else {
			alert('We were unable to load rate grouping form. Try again.');
		}
	};

	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Edit Rate Grouping',
		strmodalfooter : {
			classlist: 'd-flex',
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: 'saveSWRateGrouping',
			extrabuttonlabel: 'Save',
		}
	});

	var objParams = { participantID:sw_participantid, rateGroupingID:rgid };
	TS_AJX('ADMINSWCOMMON','getSeminarRateGrouping',objParams,editRateGroupResult,editRateGroupResult,10000,editRateGroupResult);
}
function saveSWRateGrouping() {
	if (isSWProgramLocked()) return false;

	mca_hideAlert('err_rategrouping');
	var saveRateGroupResult	= function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			MCModalUtils.hideModal();
			manageSWRates();
			reloadSWRatesGrid();
		} else {
			alert(r.errmsg && r.errmsg.length ? r.errmsg : 'We were unable to save this rate grouping. Try again.');
			$('#btnMCModalSave').prop('disabled',false).html('Save');
		}
	};
	if($('#rateGrouping').val().trim()!=''){
		$('#btnMCModalSave').prop('disabled',true).html('Saving...');
		var objParams = { participantID:sw_participantid, rateGroupingID:$('#rateGroupingID').val(), rateGrouping:$('#rateGrouping').val(),
			seminarID:sw_seminarid, programType:sw_itemtype };
		TS_AJX('ADMINSWCOMMON','saveSeminarRateGrouping',objParams,saveRateGroupResult,saveRateGroupResult,10000,saveRateGroupResult);
	} else {
		mca_showAlert('err_rategrouping', 'Enter a name for this Rate Grouping.');
		return false;
	}
}
function removeSWRateGrouping(pid,rgid) {
	if (isSWProgramLocked()) return false;
	manageSWRates();

	MCModalUtils.showModal({
		verticallycentered: true,
		size: 'md',
		title: 'Remove Rate Grouping',
		strmodalbody: {
			content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-question-circle"></i></span><span>Are you sure you want to remove this rate grouping?<br/>Any rates in this rate grouping will be ungrouped.</span></div>',
		},
		strmodalfooter : {
			classlist: 'text-right',
			showclose: false,
			showextrabutton: true,
			extrabuttonclass: 'btn-outline-danger',
			extrabuttonlabel: 'Remove Rate Grouping',
		}
	});

	$('#btnMCModalSave').on('click', function(){
		doRemoveSWRateGrouping(pid,rgid);
	});
}
function doRemoveSWRateGrouping(pid,rgid) {
	if (isSWProgramLocked()) return false;

	var removeRateGroupingResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			MCModalUtils.hideModal();
			reloadSWRatesGrid();
		} else {
			alert('We were unable to remove this rate grouping.');
			$('#btnMCModalSave').prop('disabled',false).html('Remove Rate Grouping');
		}
	};
	$('#btnMCModalSave').prop('disabled',true).html('Removing...');
	var objParams = { participantID:sw_participantid, seminarID:pid, programType:sw_itemtype, rateGroupingID:rgid };
	TS_AJX('ADMINSWCOMMON','deleteSeminarRateGrouping',objParams,removeRateGroupingResult,removeRateGroupingResult,10000,removeRateGroupingResult);
}
function moveSWRateGrouping(rgID,rowID,pRowID,dir) {
	if (isSWProgramLocked()) return false;

	manageSWRates();
	var moveResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			moveSWRatesGridRow(rowID,pRowID,dir);
		}
	};
	var objParams = { participantID:sw_participantid, rateGroupingID:rgID, dir:dir };
	TS_AJX('ADMINSWCOMMON','doSeminarRateGroupingMove',objParams,moveResult,moveResult,10000,moveResult);
}
function editSWRate(rid){
	if (isSWProgramLocked()) return false;

	manageSWRates();
	
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: rid > 0 ? 'Edit Seminar Rate' : 'Add New Seminar Rate',
		iframe: true,
		contenturl: link_editSWRate + '&seminarID='+ sw_seminarid +'&rateID=' + rid + '&ft=' + sw_itemtype,
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmRate :submit").click',
			extrabuttonlabel: 'Save Rate',
		}
	});
}
function removeSWProgramPartner(seminarTitle) {
	if (isSWProgramLocked()) return false;

	MCModalUtils.showModal({
		verticallycentered: true,
		size: 'md',
		title: 'Confirmation Needed',
		strmodalbody: {
			content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning alert-dismissible fade show" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-exclamation-triangle"></i></span><span>You are about to opt out of <b>' + seminarTitle + '</b> by permanently removing it from your catalog.</span></div>',
		},
		strmodalfooter : {
			classlist: 'text-right',
			showclose: false,
			showextrabutton: true,
			extrabuttonclass: 'btn-outline-danger',
			extrabuttonlabel: 'Confirm Opt Out',
		}
	});
	$('#btnMCModalSave').on('click', function(){
		deleteSWProgramPartner(sw_sitecode);
	});
	// Re-enable the button when the modal is closed
	$('#MCModal').on('hidden.bs.modal', function() {
		$('#optOutOfProgram').prop('disabled', false);
	});
}
function removeSWBProgramPartner(seminarTitle) {
	if (isSWProgramLocked()) return false;

	MCModalUtils.showModal({
		verticallycentered: true,
		size: 'md',
		title: 'Confirmation Needed',
		strmodalbody: {
			content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning alert-dismissible fade show" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-exclamation-triangle"></i></span><span>You are about to opt out of <b>' + seminarTitle + '</b> by permanently removing it from your catalog.</span></div>',
		},
		strmodalfooter : {
			classlist: 'text-right',
			showclose: false,
			showextrabutton: true,
			extrabuttonclass: 'btn-outline-danger',
			extrabuttonlabel: 'Confirm Opt Out',
		}
	});
	$('#btnMCModalSave').on('click', function(){
		saveSWBOptOut(sw_sitecode);
	});
	// Re-enable the button when the modal is closed
	$('#MCModal').on('hidden.bs.modal', function() {
		$('#optOutOfProgram').prop('disabled', false);
	});
}
function removeSWRate(rid) {
	if (isSWProgramLocked()) return false;

	manageSWRates();
	MCModalUtils.showModal({
		verticallycentered: true,
		size: 'md',
		title: 'Remove Rate',
		strmodalbody: {
			content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning alert-dismissible fade show" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-question-circle"></i></span><span>Are you sure you want to remove this rate?</span></div>',
		},
		strmodalfooter : {
			classlist: 'text-right',
			showclose: false,
			showextrabutton: true,
			extrabuttonclass: 'btn-outline-danger',
			extrabuttonlabel: 'Remove Rate',
		}
	});

	$('#btnMCModalSave').on('click', function(){
		doRemoveSWRate(rid);
	});
}
function copySWRate(rid){
	if (isSWProgramLocked()) return false;

	manageSWRates();
	
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Copy Rate',
		iframe: true,
		contenturl: link_copySWRate + '&seminarID='+ sw_seminarid +'&rateID=' + rid + '&ft=' + sw_itemtype,
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmCopyRate :submit").click',
			extrabuttonlabel: 'Copy Rate',
		}
	});
}
function doRemoveSWRate(rid) {
	if (isSWProgramLocked()) return false;

	var removeRateResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			MCModalUtils.hideModal();
			reloadSWRatesGrid();
		} else {
			alert('We were unable to remove this rate.');
			$('#btnMCModalSave').prop('disabled',false).html('Remove Rate');
		}
	};
	$('#btnMCModalSave').prop('disabled',true).html('Removing...');
	var objParams = { participantID:sw_participantid, seminarID:sw_seminarid, programType:sw_itemtype, rateID:rid };
	TS_AJX('ADMINSWCOMMON','deleteSeminarRate',objParams,removeRateResult,removeRateResult,10000,removeRateResult);
}
function removeSWMemberGroup(rateid,grpid,inc) {
	if (isSWProgramLocked()) return false;

	manageSWRates();
	var removeMGData = function(mg) {
		if (mg.success && mg.success.toLowerCase() == 'true'){
			reloadSWRatesGrid();
		} else {
			alert('We were unable to remove this group from this rate.');
		}
	};
	var objParams = { participantID:sw_participantid, seminarID:sw_seminarid, programType:sw_itemtype, rateid:rateid, groupid:grpid, include:inc };
	TS_AJX('ADMINSWCOMMON','deleteMemberGroup',objParams,removeMGData,removeMGData,10000,removeMGData);
}
function copyRatesFromSWProgramPrompt(mode) {
	if (isSWProgramLocked()) return false;

	manageSWRates();
	let programDisplayText = getSWProgramDisplayText();
	let msg = '<div class="alert d-flex align-items-center pl-2 align-content-center alert-info fade show" role="alert">\
			<span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-solid fa-info-circle"></i></span>\
			<span>\
				<strong class="d-block">You are about to erase all current rates in this '+ programDisplayText.toLowerCase() +' and copy rates from another '+ programDisplayText.toLowerCase() +'.</strong> \
				'+(mode && mode == 'swsubmission' ? '' : 'Your action will NOT affect existing registrants for this '+ programDisplayText.toLowerCase() + '.') +'\
			</span>\
		</div>\
		';
	
	MCModalUtils.showModal({
		verticallycentered: true,
		size: 'lg',
		title: 'Confirmation Needed',
		strmodalbody: {
			content: msg
		},
		strmodalfooter : {
			classlist: 'text-right',
			showclose: false,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary',
			extrabuttonlabel: 'Choose '+ programDisplayText,
			extrabuttononclickhandler: 'copyRatesFromSWProgram'
		}
	});
}
function copyRatesFromSWProgram(){
	if (isSWProgramLocked()) return false;
	MCModalUtils.hideModal();
	if($('#btnCopyRateFromProgram').length){
		var sw_copyRate = 'copyForSWODProgram';
	} else {
		var sw_copyRate = '';
	}
	$('#MCModal').on('hidden.bs.modal', function() {
		let programDisplayText = getSWProgramDisplayText();
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: 'Select ' + programDisplayText + ' to Copy Rates From',
			iframe: true,
			contenturl: link_manageCopyRates + '&pid=' + getSWProgramID() + '&ft=' + sw_itemtype + '&copyAction=' + sw_copyRate,
			strmodalfooter : {
				classlist: 'd-none'
			}
		});
	});
}

/* SW Billing common functions */
function initSWBillingLogsTable(){
	swBillingLogsTable = $('#swBillingLogs').DataTable({
		"processing": true,
		"serverSide": true,
		"ajax": { "url": link_swBillingLogs, "type": "post" },
		"columns": [
			{ "data": "billamount", "width": "15%" },
			{ "data": "description", "width": "40%" },
			{ "data": "membername", "width": "20%" },
			{ "data": "datecreated", "width": "15%" },
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display' && data.candelete) {
						renderData = '<a id="a_rem_'+data.logid+'" href="#" onclick="removeSWBillingLog('+data.logid+');return false;" class="btn btn-sm px-0 mr-sm-2" tabindex="-1" data-confirm="0" role="button" aria-disabled="true" title="Remove Log Entry"><i class="fa-solid fa-trash-alt text-danger fa-lg"></i></a>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "10%",
				"orderable": false,
				"className": "text-center"
			}
		],
		"order": [[ 3, "desc" ]]
	});
}
function logSWBillingAction(logType, thisBtn){
	var targetControlName = $(thisBtn).data('target');

	var logActionResult = function(r) {
		$(thisBtn).prop('disabled',false);
		if (r.success && r.success.toLowerCase() == 'true') {
			$('#' + targetControlName).val('');
			$('#successBadge').show().fadeOut(5000);
			swBillingLogsTable.ajax.reload(null,false);
		} else {
			alert('An error occured while logging this action to the program. Try again.');
		}
	};

	var objParams = { programID:getSWProgramID(), programType:sw_itemtype, participantID:sw_participantid, logType:logType };
	
	var targetVal = trim($('#' + targetControlName).val() || '');
	if (!mca_validateInteger(targetVal) || targetVal == 0){
		$('#' + targetControlName).val('').focus();
		return false;
	}

	$(thisBtn).prop('disabled',true);

	if($.inArray(logType, ['setup','editing','syncing']) >= 0) objParams.minutes = targetVal;
	if(logType == 'form creation') objParams.formID = targetVal;

	TS_AJX('ADMINSWCOMMON','addSWBillingLog',objParams,logActionResult,logActionResult,10000,logActionResult);
}
function logSWBillingAdhocFee(thisBtn) {
	var logActionResult = function(r) {
		$(thisBtn).prop('disabled',false);
		if (r.success && r.success.toLowerCase() == 'true') {
			$('#adhocBillingAmt,#adhocBillingDetail').val('');
			$('#successBadge').show().fadeOut(5000);
			swBillingLogsTable.ajax.reload(null,false);
		} else {
			alert('An error occured while logging this action to the program. Try again.');
		}
	};

	$('#adhocBillingAmt,#adhocBillingDetail').removeClass('is-invalid');

	let billAmount = formatCurrencyAllowNeg($('#adhocBillingAmt').val()).replace(/,/g,'');
	let billDesc = $('#adhocBillingDetail').val().trim();

	if (billAmount == 0) $('#adhocBillingAmt').addClass('is-invalid').focus();
	if (billDesc == '') $('#adhocBillingDetail').addClass('is-invalid').focus();
	if (billAmount == 0 || billDesc == '') return false;

	$(thisBtn).prop('disabled',true);

	var objParams = { programID: getSWProgramID(), programType:sw_itemtype, participantID:sw_participantid, logType: 'adhoc', minutes: 0, formID: 0, billAmount: billAmount, billDesc: billDesc };
	TS_AJX('ADMINSWCOMMON','addSWBillingLog',objParams,logActionResult,logActionResult,10000,logActionResult);
}
function removeSWBillingLog(logID){
	var removeLogResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			swBillingLogsTable.ajax.reload(null,false);
		} else {
			alert('An error occured while removing this log entry. Try again.');
		}
	};

	var el = $('a#a_rem_'+logID);
	mca_initConfirmButton(el, function(){
		var objParams = { logID:logID };
		TS_AJX('ADMINSWCOMMON','removeSWBillingLog',objParams,removeLogResult,removeLogResult,10000,removeLogResult);
	},'<i class="fa-solid fa-trash-alt text-danger fa-lg"></i>','<span class="text-warning text-nowrap"><i class="fa-solid fa-info-circle"></i> Click to Confirm</span>','<span class="text-info text-nowrap"><i class="fa-solid fa-info-circle"></i> Removing...</span>');
}
function initSWBillingLogsTableForAssociations(){
	swBillingLogsTable = $('#swBillingLogs').DataTable({
		"processing": true,
		"serverSide": true,
		"ajax": {
			"url": link_swBillingLogs,
			"type": "post",
			"data": function(d) { d['fParticipantID'] = $('#fParticipantID').val() || 0; }
		},
		"columns": [
			{ "data": "billamount", "width": "15%" },
			{ "data": "description", "width": "30%" },
			{ "data": "sitecode", "width": "10%" },
			{ "data": "membername", "width": "20%" },
			{ "data": "datecreated", "width": "15%" },
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display' && data.candelete) {
						renderData = '<a id="a_rem_'+data.logid+'" href="#" onclick="removeSWBillingLog('+data.logid+');return false;" class="btn btn-sm px-0 mr-sm-2" tabindex="-1" data-confirm="0" role="button" aria-disabled="true" title="Remove Log Entry"><i class="fa-solid fa-trash-alt text-danger fa-lg"></i></a>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "10%",
				"orderable": false,
				"className": "text-center"
			}
		],
		"order": [[ 4, "desc" ]]
	});
}
function logSWBillingAdhocFeeForAssociation(thisBtn) {
	var logActionResult = function(r) {
		$(thisBtn).prop('disabled',false);
		if (r.success && r.success.toLowerCase() == 'true') {
			$('#adhocBillingAmt,#adhocBillingDetail').val('');
			$('#successBadge').show().fadeOut(5000);
			swBillingLogsTable.ajax.reload(null,false);
		} else {
			alert('An error occured while logging this discretionary fee for the association. Try again.');
		}
	};

	$('#fParticipantID,#adhocBillingAmt,#adhocBillingDetail').removeClass('is-invalid');
	
	let participantID = $('#fParticipantID').val();
	let billAmount = formatCurrencyAllowNeg($('#adhocBillingAmt').val()).replace(/,/g,'');
	let billDesc = $('#adhocBillingDetail').val().trim();

	if (participantID == 0) $('#fParticipantID').addClass('is-invalid').focus();
	if (billAmount == 0) $('#adhocBillingAmt').addClass('is-invalid').focus();
	if (billDesc == '') $('#adhocBillingDetail').addClass('is-invalid').focus();
	if (participantID == 0 || billAmount == 0 || billDesc == '') return false;

	$(thisBtn).prop('disabled',true);

	var objParams = { programID:0, programType:'', participantID:participantID, logType: 'adhoc', minutes: 0, formID: 0, billAmount: billAmount, billDesc: billDesc };
	TS_AJX('ADMINSWCOMMON','addSWBillingLog',objParams,logActionResult,logActionResult,10000,logActionResult);
}

/* SWL functions */
function initializeSWLProgramsTable() {
	let arrColumns = [];

	arrColumns.push(
		{
			"data": null,
			"render": function (data, type) {
				let renderData = '';
				if (type === 'display') {
					renderData = data.formattedDateStart + '<div class="pt-1 pl-2 small text-dim">' + data.formattedTimeStart + ' ' + timeZoneAbbr +'</div>';
				}
				return type === 'display' ? renderData : data;
			},
			"width": "15%",
			"className": "align-top"
		},
		{
			"data": null,
			"render": function (data, type) {
				let renderData = '';
				if (type === 'display') {
					renderData += '<div>';
					if (data.isFeatured)
						renderData +=  '<span id="featuredBadgeContainer_'+data.seminarID+'" class="float-right badge badge-info">Featured</span>';
					if (!data.isPublished)
						renderData +=  '<div class="badge badge-warning mb-1">Inactive</div>';
					renderData += '<div>' + data.seminarName + ' ' + (data.lockSettings ? '<span class="fa-solid fa-lock ml-1" style="font-size: 17px; color: grey;" title="Changes to this program are restricted by a Client Administrator."></span>' : '') + '</div>';
					if(data.seminarSubTitle.length) renderData += '<div class="text-dim small">'+data.seminarSubTitle+'</div>';
					renderData += '</div>';
				}
				return type === 'display' ? renderData : data;
			},
			"width": "55%",
			"className": "align-top"
		}
	);

	if (sw_hop == 0) {
		arrColumns.push(
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<div>';
						if (data.publisherOrgCode == sw_sitecode) {
							renderData += 'Publisher';
							if (data.isSyndicated)
								renderData += '<div class="text-dim small">SYNDICATED</div>';
						} else 
							renderData += '<div>Opt-In</div><div class="text-dim small">'+data.publisherOrgCode+'</div>';
						renderData += '</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "10%",
				"className": "align-top text-center",
				"orderable": false
			}
		);
	}

	arrColumns.push(
		{ "data": null,
			"render": function ( data, type, row, meta ) {
				let renderData = '';
				if (type === 'display') {
					renderData += '<a href="javascript:editSWLProgram('+data.seminarID+');" class="btn btn-xs btn-outline-primary p-1 m-1" title="View/Edit Program"><i class="fa-solid fa-pencil"></i></a>';
					if (data.copyProgramRights) {
						if (data.publisherOrgCode == sw_sitecode) {
							renderData += '<a href="javascript:copyProgram('+data.seminarID+');" class="btn btn-xs btn-outline-primary p-1 m-1" title="Copy Program"><i class="fa-solid fa-copy"></i></a>';
						} else {
							renderData += '<a class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-copy"></i></a>';
						}
					}
					if(data.isFeatured){
						renderData += '<a href="javascript:removeFromFeaturedPrograms('+data.seminarID+');" class="btn btn-xs btn-outline-first p-1 m-1" title="Remove from Featured Programs"><i class="fa-solid fa-check-circle swfeatured_icon_'+data.seminarID+'"></i></a>';
					}else{
						renderData += '<a href="javascript:addToFeaturedPrograms('+data.seminarID+');" class="btn btn-xs btn-outline-dark p-1 m-1" title="Add to Featured Programs"><i class="fa-solid fa-check-circle swfeatured_icon_'+data.seminarID+'"></i></a>';
					}
					if (data.deleteProgramRights) {
						if (data.publisherOrgCode == sw_sitecode) {
							renderData += '<a href="javascript:deleteProgram('+data.seminarID+','+data.enrolledCount+');" class="btn btn-xs btn-outline-danger p-1 m-1" title="Delete Program"><i class="fa-solid fa-trash-alt"></i></a>';
						} else {
							renderData += '<a class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-trash-alt"></i></a>';
						}
					}
					if(data.manageSWLRegistrantsSignUp == 1 || data.manageSWLRegistrantsAll == 1){
						renderData += '<a href="javascript:editSWLProgramRegistrants('+data.seminarID+');" class="btn btn-xs btn-outline-primary p-1 m-1" title="View Registrants"><i class="fa-solid fa-users"></i></a>';
					}
					renderData += '<span class="d-inline-flex ml-2 small text-first w-5">'+data.enrolledCount+'</span>';
				}
				return type === 'display' ? renderData : data;
			},
			"width": "20%",
			"className": "align-top text-center",
			"orderable": false
		}
	);

	swlProgramsTable = $('#swlProgramsTable').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 10,
		"lengthMenu": [ 10, 25, 50 ],
		"dom": "<'row'<'col-sm-3 col-md-3'l><'col-sm-9 col-md-9'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
		"ajax": { 
			"url": link_listswlprograms,
			"type": "post",
			"data": function(d) {
				$.each($('#frmFilter').serializeArray(),function() {
					d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
				});
			}
		},
		"autoWidth": false,
		"columns": arrColumns,
		"order": [[0, 'desc']],
		"searching": false
	});
}
function addSWLProgram() {
	if ($('#divAddProgramForm').length && $('#divAddProgramForm').hasClass('d-none')) {
		mca_hideAlert('err_swladdprogram');
		$('#swSeminarName').val('');
		mca_clearDateRangeField('swProgramStartTime','swProgramEndTime');
		mca_setupDateTimePickerRangeFields('swProgramStartTime','swProgramEndTime',5,0);

		$('div.divSWLTool').addClass('d-none');
		$('#divAddProgramForm').removeClass('d-none');
	}
}
function cancelAddSWLProgram(){
	if ($('#divAddProgramForm').is(':visible')) {
		mca_hideAlert('err_swladdprogram');
		$('#swSeminarName').val('');
		mca_clearDateRangeField('swProgramStartTime','swProgramEndTime');
		$('#divAddProgramForm').addClass('d-none');
	}
}
function cancelAddSWODProgram(){
	if ($('#divAddProgramForm').is(':visible')) {
		mca_hideAlert('err_swodaddprogram');
		$('#swSeminarName').val('');
		$('#divAddProgramForm').addClass('d-none');
	}
}
function cancelAddSWBProgram(){
	if ($('#divAddProgramForm').is(':visible')) {
		mca_hideAlert('err_swbaddprogram');
		$('#swBundleName').val('');
		if($('select#swFormat').length != 0){
			$('select#swFormat').val('');
		}
		$('#divAddProgramForm').addClass('d-none');
	}
}
function cancelAddSWCPProgram(){
	if ($('#divAddProgramForm').is(':visible')) {
		mca_hideAlert('err_swcpaddprogram');
		$('#programName').val('');
		$('#certificateMessage').val('');
		$('#divAddProgramForm').addClass('d-none');
	}
}
function doAddSWLProgram() {
	var addSWLProgramResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true' && r.seminarid){
			self.location.href = link_editswlprogram + '&pid=' + r.seminarid + '&programAdded=true';
		} else {
			alert('We were unable to create this SWL Program. Try again.');
			$('#btnAddSWLProgram').prop('disabled',false);
		}
	};

	var arrReq = [];
	mca_hideAlert('err_swladdprogram');
	var semname = $('#swSeminarName').val().trim();
	var datestart = $('#swProgramStartTime').val();
	var dateend = $('#swProgramEndTime').val();

	if (semname == '') arrReq[arrReq.length] = 'Enter the Seminar Name.';
	if (datestart == '') arrReq[arrReq.length] = 'Enter the Seminar Start Date.';
	if (dateend == '') arrReq[arrReq.length] = 'Enter the Seminar End Date.';

	if (arrReq.length) {
		mca_showAlert('err_swladdprogram', arrReq.join('<br/>'));
		return false;
	}

	$('#btnAddSWLProgram').prop('disabled',true);

	var objParams = { seminarName:semname, dateStart:datestart, dateEnd:dateend };
	TS_AJX('ADMINSWL','createSWLProgram',objParams,addSWLProgramResult,addSWLProgramResult,10000,addSWLProgramResult);
}
function editSWLProgram(pid) { 
	window.open(link_editswlprogram + '&pid=' + pid,'_blank'); 
}
function editSWLProgramRegistrants(pid) { 
	window.open(link_editswlprogram + '&pid=' + pid + '&tab=registrants','_blank'); 
}
function viewSWLProgress(pid,did,eid,gridmode){
	MCModalUtils.showModal({
		isslideout: true,
		size: 'xl',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Manage Progress for',
		iframe: true,
		contenturl: link_viewswlprogress + '&pid=' + pid + '&did=' + did + '&eid=' + eid + '&gridMode=' + gridmode
	});
}
function viewSWLCertificate(eid) {
	viewCertificate(eid,'SWL');
}
function viewSWLCommunication(pid,did,eid) {
	viewCommunication(pid,did,eid,'SWL');
}
function resendSWLInstructions(pid,did,eid) { 
	resendInstructions(pid,did,eid,'SWL');
}
function viewSWLMaterials(pid,did,eid,o) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Send Materials',
		iframe: true,
		contenturl: link_viewswlmaterials + '&pid=' + pid + '&did=' + did + '&eid=' + eid + '&org=' + o,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmMaterials :submit").click',
			extrabuttonlabel: 'Send'
		}
	});
}
function sendSWLReplay(pid,did,eid,o,name) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Send Replay Link to ' + name,
		iframe: true,
		contenturl: link_replaylink + '&pid=' + pid + '&did=' + did + '&eid=' + eid + '&org=' + o,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmReplay :submit").click',
			extrabuttonlabel: 'Send'
		}
	});
}

/* SWL Live Schedule Programs */
function initializeSWLScheduleProgramsTable() {
	swlProgramsScheduleTable = $('#swlScheduleProgramsTable').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 10,
		"lengthMenu": [ 10, 25, 50 ],
		"dom": "<'row'<'col-sm-3 col-md-3'l><'col-sm-9 col-md-9'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
		"ajax": { 
			"url": seminarsLink,
			"type": "post",
			"data": function(d) {
				$.each($('#frmFilter').serializeArray(),function() {
					d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
				});
			}
		},
		"autoWidth": false,
		"columns": [
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						renderData = data.dateStart + '<div class="pt-1 pl-2 small text-dim">'+data.timeStart+ ' ' + timeZoneAbbr +'</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "15%",
				"className": "align-top"
			},
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						renderData += data.orgcode;
						if (data.isNATLE)
							renderData +=  '<div class="pt-1 pl-2 small text-dim"><img src="/assets/common/images/seminarWeb/natle.png" width="37" height="12"></div>';
						renderData += '</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "12%",
				"className": "align-top"
			},
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						if (data.providerID)
							renderData += data.provider + '<div class="pt-1 pl-2 small text-dim">'+data.ZoomWebinarHostID+'</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "18%",
				"className": "align-top"
			},
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<div>';
						if (!data.isPublished)
							renderData +=  '<div class="badge badge-warning mb-1">Inactive</div>';
						renderData += '<div>' + data.seminarName + '</div>';
						if(data.seminarSubTitle.length) renderData += '<div class="text-dim small">'+data.seminarSubTitle+'</div>';
						renderData += '</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "45%",
				"className": "align-top"
			},
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<a href="'+mca_getSiteToolLink(data.orgcode,'SeminarWebAdmin','listSWL','editSWLProgram','pid='+data.seminarID)+'" title="Edit This Program" target="_swlProgram'+data.seminarID+'" class="btn btn-xs btn-outline-primary p-1 m-1"><i class="fa-solid fa-pencil"></i></a>';
						renderData += '<a href="'+mca_getSiteToolLink(data.orgcode,'SeminarWebAdmin','listSWL','editSWLProgram','pid='+data.seminarID+'&tab=registrants')+'" target="_swlProgramReg'+data.seminarID+'" title="Registrants" class="btn btn-xs btn-outline-primary p-1 m-1"><i class="fa-solid fa-users"></i></a> '+ data.enrollcount;
					}
					return type === 'display' ? renderData : data;
				},
				"width": "10%",
				"className": "align-top",
				"orderable": false
			}
			
		],
		"order": [[0, 'asc']],
		"searching": false
	});
}
function dofilterSWLProgramsSchedule(){
	$('#SWgriddates').html($('#fDateFrom').val() + ' to ' + $('#fDateTo').val());
	swlProgramsScheduleTable.draw();
}
function clearFilterSWLProgramsSchedule(){
	$('#frmFilter')[0].reset();
	$('#frmFilter [data-toggle="custom-select2"]').trigger('change.select2');
	dofilterSWLProgramsSchedule();
}
function filterSWLPrograms() {
	if ($('#divFilterForm').hasClass('d-none')) {
		$('div.divSWLTool').addClass('d-none');
		$('#divFilterForm').removeClass('d-none');
	}
}
function dofilterSWLPrograms() {	
	$('#SWgriddates').html($('#fDateFrom').val() + ' to ' + $('#fDateTo').val());
	if(sw_programgridmode == 'swlProgramScheduleGrid'){
		swlProgramsScheduleTable.draw();
	}else if(sw_programgridmode == 'swodProgramSearchGrid'){
		swodProgramsTable.draw();
	}else{
		swlProgramsTable.draw();
	}	
}
function clearFilterSWLPrograms(){
	$('#frmFilter')[0].reset();
	$('#frmFilter [data-toggle="custom-select2"]').trigger('change.select2');
	dofilterSWLPrograms();
}
function quickFilterSWLPrograms(d) {
	var now = new Date();
	var thisDate = new Date();
	var todayDate = new Date(thisDate.getFullYear(), thisDate.getMonth(), thisDate.getDate());

	var n = d * 1;
	if (n >= 0) {
		$('#fDateFrom').val(moment(todayDate).format('M/D/YYYY'));
		todayDate.setDate(now.getDate()+n);
		$('#fDateTo').val(moment(todayDate).format('M/D/YYYY'));
	} else {
		n = d * -1;
		$('#fDateTo').val(moment(todayDate).format('M/D/YYYY'));
		todayDate.setDate(now.getDate()-n);
		$('#fDateFrom').val(moment(todayDate).format('M/D/YYYY'));
	} 
	dofilterSWLPrograms();
}
function exportSWLPrograms() {
	if ($('#divExportForm').hasClass('d-none')) {
		$('div.divSWLTool').addClass('d-none');
		$('#divExportFormArea').html('<h5>Download Programs</h5>' + mca_getLoadingHTML());
		$('#divExportForm').removeClass('d-none');
		$('#divExportFormArea').load(link_exportswlprogram + '&' + $('#frmFilter').serialize());
	}
}
function doExportSWLPrograms(u) {
	self.location.href = '/tsdd/' + u;
	$('div.divSWLTool').addClass('d-none');
}
function filterSWLRegistrants() {
	if ($('#divFilterRegistrantsForm').hasClass('d-none')) {
		$('#divItemForm').addClass('d-none');
		$('#divFilterRegistrantsForm').removeClass('d-none');
	}
}
function dofilterSWLRegistrants() {	
	$('#SWLRegistrantGriddates').html($('#frrDateFrom').val() + ' to ' + $('#frrDateTo').val());
	SWLRegistrantsListTable.draw();
}
function quickFilterSWLRegPrograms(d) {
	var now = new Date();
	var thisDate = new Date();
	var todayDate = new Date(thisDate.getFullYear(), thisDate.getMonth(), thisDate.getDate());

	var n = d * 1;
	if(n >= 0){
		$('#frpDateFrom').val(moment(todayDate).format('M/D/YYYY'));
		todayDate.setDate(now.getDate()+n);
		$('#frpDateTo').val(moment(todayDate).format('M/D/YYYY')); 
	}else{
		n = d * -1;
		$('#frpDateTo').val(moment(todayDate).format('M/D/YYYY'));
		todayDate.setDate(now.getDate()-n);
		$('#frpDateFrom').val(moment(todayDate).format('M/D/YYYY'));
	} 
	dofilterSWLRegistrants();
}
function quickFilterSWLRegRegistrants(d) {
	var now = new Date();
	var thisDate = new Date();
	var todayDate = new Date(thisDate.getFullYear(), thisDate.getMonth(), thisDate.getDate());

	var n = d * 1;
	if(n >= 0){
		$('#frrDateFrom').val(moment(todayDate).format('M/D/YYYY'));
		todayDate.setDate(now.getDate()+n);
		$('#frrDateTo').val(moment(todayDate).format('M/D/YYYY')); 
	}else{
		n = d * -1;
		$('#frrDateTo').val(moment(todayDate).format('M/D/YYYY'));
		todayDate.setDate(now.getDate()-n);
		$('#frrDateFrom').val(moment(todayDate).format('M/D/YYYY'));
	} 
	dofilterSWLRegistrants();
}
function loadSWLRegistrantSearchTab() {
	mca_setupDatePickerRangeFields('rDateFrom','rDateTo');
	mca_setupCalendarIcons('frmRegistrantFilter');
	initSWLRegistrants();
}
function initSWLRegistrants() {
	let columnDefs = [];
	let filterID = sw_reggridmode == "reggrid" ? "frmSWLRegFilter" : "frmRegistrantFilter";	
	let domString = "<'row'<'col-sm-3 col-md-3'l><'col-sm-9 col-md-9'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";

	if(!Boolean(sw_showCreditCol)){
		columnDefs.push({"targets": 3, "searchable": false, "visible": false});
	}
	if(!Boolean(sw_showAttendanceCol)){
		columnDefs.push({"targets": 2, "searchable": false, "visible": false});
	}
	
	SWLRegistrantsListTable = $('#SWLRegistrantsListTable').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 10,
		"lengthMenu": [ 10, 25, 50 ],
		"dom": domString,
		"language": {
			"lengthMenu": "_MENU_",
			"emptyTable": "No Registrations found"
		},
		"ajax": { 
			"url": link_swlregistrantslist,
			"type": "post",
			"data": function(d) {
				$.each($('#'+filterID).serializeArray(),function() {
					d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
				});
			}
		},
		"autoWidth": false,
		"columnDefs": columnDefs,
		"columns": [
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						if(sw_reggridmode == "reggrid"){
							renderData += '<a href="javascript:editSWLProgram('+data.seminarid+');">'+data.seminarname+'</a>';
							if(data.seminarsubtitle.length){
								renderData += '<div class="text-dim small">'+data.seminarsubtitle+'</div>';
							}
						}else{
							if (!data.isActive) renderData += '<div><span class="badge badge-danger">Deleted</div>';
							if(data.memberid)
								renderData += '<a href="javascript:editMember('+data.memberid+');">'+data.lastname+', '+data.firstname+' ('+data.membernumber+')</a>';
							else
								renderData += data.lastname+', '+data.firstname;
							if (data.company.length)				
								renderData += '<div class="text-dim small">'+data.company+'</div>';
							if (sw_reggridmode == "regsearchgrid")	{
								renderData += '<div class="text-dim small">'+data.seminarname+'</div>';
								if(data.seminarsubtitle.length){
									renderData += '<div class="text-dim small">'+data.seminarsubtitle+'</div>';
								}
							}
							if (data.signuporgcode != sw_sitecode)				
								renderData += '<div class="text-dim small">'+data.signuporgcode+'</div>';
						}						
					}
					return type === 'display' ? renderData : data;
				},
				"className": "align-top"
			},
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<span>'+data.dateenrolled+'</span>';
						if (sw_reggridmode == "grid" && data.isswlproviderwarning)
							renderData += '<span class="float-right"><a href="javascript:addSWLProviderRegistrantID('+data.enrollmentid+');" class="btn btn-xs btn-outline-warning p-1 m-1" title="Add missing connection info"><i class="fa-solid fa-solid fa-exclamation-triangle"></i></a></span>';
					}
					return type === 'display' ? renderData : data;
				},
				"className": "align-top",
				"width": "12%"
			},
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						renderData += data.attended + (data.duration > 0 ? (' - ' + data.duration + ' min') : '');
					}
					return type === 'display' ? renderData : data;
				},
				"className": "align-top",
				"width": "10%",
				"orderable": false
			},		
			{ "data": "creditcount", 'className': 'align-top', "width": "7%", "orderable": false },
			{ "data": "amountbilled", 'className': 'text-right align-top', "width": "7%", "orderable": false },
			{ "data": "amountdue", 'className': 'text-right align-top', "width": "7%", "orderable": false },
			{ 
				"data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						if (data.showInstructionsCol) {
							if (data.hasviewswlinstructions) {
								renderData += '<a href="javascript:resendSWLInstructions('+data.seminarid+','+data.depomemberdataid+','+data.enrollmentid+');" class="btn btn-xs btn-outline-primary px-1 m-1" title="Resend Connection Instructions"><i class="fa-solid fa-envelope"></i></a>';
							} else {
								renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 text-muted disabled"><i class="fa-solid fa-envelope"></i></a>';
							}
						}

						if (data.showCreditCol) {
							if (data.hasmanageswlregcreditrights) {
								renderData += '<a href="javascript:manageCredit('+data.seminarid+','+data.depomemberdataid+','+data.enrollmentid+',\'SWL\',\''+sw_reggridmode+'\');" class="btn btn-xs btn-outline-warning px-1 m-1" title="Manage Credit"><i class="fa-solid fa-file-certificate"></i></a>';
							} else {
								renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 text-muted disabled"><i class="fa-solid fa-file-certificate"></i></a>';
							}
						}

						if (data.showCertificateCol) {
							if (data.hasviewswlcertificate) {
								renderData += '<a href="javascript:viewSWLCertificate('+data.enrollmentid+');" class="btn btn-xs btn-outline-primary px-1 m-1" title="View Certificate"><i class="fa-solid fa-certificate"></i></a>';
							} else {
								renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 text-muted disabled"><i class="fa-solid fa-certificate"></i></a>';
							}
						}

						if (data.showManageProgressCol) {
							if (data.hasviewswlprogress) {
								renderData += '<a href="javascript:viewSWLProgress('+data.seminarid+','+data.depomemberdataid+','+data.enrollmentid+',\'SWL\',\''+sw_reggridmode+'\');" class="btn btn-xs btn-outline-primary px-1 m-1" title="Manage Progress"><i class="fa-solid fa-eye"></i></a>';
							} else {
								renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 text-muted disabled"><i class="fa-solid fa-eye"></i></a>';
							}
						}

						if (data.hasviewswlcommunication) {
							renderData += '<a href="javascript:viewSWLCommunication('+data.seminarid+','+data.depomemberdataid+','+data.enrollmentid+');" class="btn btn-xs btn-outline-primary px-1 m-1" title="View Communications"><i class="fa-solid fa-table"></i></a>';
						} else {
							renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 text-muted disabled"><i class="fa-solid fa-table"></i></a>';
						}

						if (data.showMaterialsCol) {
							if (data.hasviewswlmaterials) {
								renderData += '<a href="javascript:viewSWLMaterials('+data.seminarid+','+data.depomemberdataid+','+data.enrollmentid+',\''+data.signuporgcode+'\');" class="btn btn-xs btn-outline-primary px-1 m-1" title="Send Materials"><i class="fa-solid fa-envelope-open-text"></i></a>';
							} else {
								renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 text-muted disabled"><i class="fa-solid fa-envelope-open-text"></i></a>';
							}
						}

						if (data.showReplayCol) {
							if (data.hassendswlreplay && data.canviewreplay) {
								renderData += '<a href="javascript:sendSWLReplay('+data.seminarid+','+data.depomemberdataid+','+data.enrollmentid+',\''+data.signuporgcode+'\',\'' + data.firstname + ' ' + data.firstname + '\');" class="btn btn-xs btn-outline-primary px-1 m-1" title="Send Replay Link"><i class="fa-solid fa-compact-disc"></i></a>';
							} else {
								renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 text-muted disabled"><i class="fa-solid fa-compact-disc"></i></a>';
							}
						}
		
						if (!data.assochandlesownpayment) {
							if(data.haschangeswregistrantprice) {
								renderData += '<a href="javascript:changeSWRegistrantPrice('+data.enrollmentid+',\'SWL\');" class="btn btn-xs btn-outline-primary p-1 m-1" title="Change Price"><i class="fa-solid fa-money-bill-alt text-green"></i></a>';
							} else {
								renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 text-muted disabled"><i class="fa-solid fa-money-bill-alt"></i></a>';
							}
						} else if(data.addpaymentencstring.length){
							renderData += '<a href="javascript:addSWPayment(\''+data.addpaymentencstring+'\',\'SWL\');" class="btn btn-xs btn-outline-primary p-1 m-1" title="Pay for Registration"><i class="fa-solid fa-money-bill-alt text-green"></i></a>';
						} else {
							renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 text-muted disabled"><i class="fa-solid fa-money-bill-alt"></i></a>';
						}
						
						if(data.hasIsFeeExemptRights) {
							if (data.showfeeexemptcol) {
								if(data.isfeeexempt) {
									renderData += '<a href="javascript:unMarkAsFeeExempt('+data.enrollmentid+',\'SWL\');" class="btn btn-xs btn-outline-dark px-1 m-1" title="Unmark the registrant as fee exempt"><i class="fa-solid fa-check-circle"></i></a>';
								} else {
									renderData += '<a href="javascript:markAsFeeExempt('+data.enrollmentid+',\'SWL\');" class="btn btn-xs btn-outline-dark text-muted p-1 m-1" title="Mark the registrant as fee exempt"><i class="fa-solid fa-check-circle"></i></a>';
								}
							} else {
								renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 text-muted disabled"><i class="fa-solid fa-check-circle"></i></a>';
							}
						}

						if(data.candelete) {
							renderData += '<a href="javascript:removeSWLRegistrant('+data.enrollmentid+');" class="btn btn-xs btn-outline-danger p-1 m-1" title="Cancel Registration"><i class="fa-solid fa-circle-minus"></i></a>';
						} else {
							renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 text-muted disabled"><i class="fa-solid fa-circle-minus"></i></a>';
						}
					}
					return type === 'display' ? renderData : data;
				},
				"className": "text-center align-top",
				"width": "25%",
				"orderable": false
			}
		],
		"order": [[1, 'desc']],
		"searching": false
	});
}
function exportSWLRegistrants() {
	$('#divFilterRegistrantsForm').addClass('d-none');
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Download Filtered Registrants',
		iframe: true,
		contenturl: link_exportswregprompt,
		strmodalfooter: {
			classlist: 'text-right',
			showclose: false,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.doExportSWProgramReg',
			extrabuttonlabel: 'Download CSV',
			extrabuttoniconclass: 'fa-light fa-file-csv'
		}
	});
}
function filterSWLProgramRegistrants() {
	if ($('#divFilterForm').hasClass('d-none')) {
		$('div.divSWRegistrantsTool').addClass('d-none');
		$('#divFilterForm').removeClass('d-none');
	}
}
function clearSWLRegFilters() {
	$('#frmRegistrantFilter')[0].reset();
	dofilterSWLProgramRegistrants();
}
function dofilterSWLProgramRegistrants() {	
	SWLRegistrantsListTable.draw();
}
function exportSWFormResponses() {
	if(getSWRegistrantsRowCount(sw_itemtype) == 0) {
		alert('There are no registrants to act upon.');
	} else if ($('#divFormResponseDownloadForm').hasClass('d-none')) {
		$('div.divSWRegistrantsTool').addClass('d-none');
		$('#divFormResponseDownloadForm').removeClass('d-none');
	}
}
function doExportSWFormResponses(type){
	mca_hideAlert('err_downloadformresponses');
	var formID = $('#formID').val();
	if (typeof formID == "undefined" || formID == 0) {
		mca_showAlert('err_downloadformresponses', 'Select an Evaluation/Exam');
	} else {
		$('#divFormResponseDownloadFormArea').addClass('d-none');
		$('#divFormResponseDownloadFormAreaLoading').removeClass('d-none').html('<h5>Download Evaluation/Exam Responses</h5>' + mca_getLoadingHTML());
		$('#divFormResponseDownloadFormAreaLoading').load(link_exportswformresponses + '&exportType=' + type + '&formID=' + formID + '&' + $('#frmRegistrantFilter').serialize());
	}
}
function downloadSWFormResponses(u) {
	self.location.href = '/tsdd/' + u;
	$('#divFormResponseDownloadFormArea').removeClass('d-none');
	$('div.divSWRegistrantsTool, #divFormResponseDownloadFormAreaLoading').addClass('d-none');
}
function setSaveSWLWebinar(b) {
	$('#createSWLWebinar').val(b);
}
function validateAndSaveSWLSeminar(callback) {
	mca_hideAlert('err_swldetails');
	$('#btnUpdateSWLProgram').prop('disabled',true);
	if ($('button.btnCreateWebinar').length)
		$('button.btnCreateWebinar').prop('disabled',true);

	var arrReq = [];
	if($('#seminarName').val().trim().length == 0)
		arrReq[arrReq.length] = 'Enter the seminar name.';

	var programCode = $('#programCode').val().trim();
	if(programCode.length == 0)
		arrReq[arrReq.length] = 'Enter a program code for this seminar.';

	var seminarDesc = "";
	if(CKEDITOR.instances['seminarDesc'] != null)
		seminarDesc = CKEDITOR.instances['seminarDesc'].getData().trim();
	else 
		seminarDesc = $('textarea[name="seminarDesc"]').val().trim();

	if(seminarDesc.length == 0)
		arrReq[arrReq.length] = 'Enter the seminar description.';
	if($('#dateStart').length && $('#dateStart').val().trim().length == 0)
		arrReq[arrReq.length] = 'Enter the seminar start date.';
	if($('#dateEnd').length && $('#dateEnd').val().trim().length == 0)
		arrReq[arrReq.length] = 'Enter the seminar end date.';
	
	if(arrReq.length) {
		showValidationAlertForSWLSeminar(arrReq.join('<br/>'));
	} else {
		var checkProgramCodeResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				var saveResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						let roomStatusChanged = !programAdded && $('#isOpen').length && $('#isOpen').val() !=  $('#isOpenOldVal').val();
						let reloadSWLProgram = false;
						// program locked / unlocked
						if($("#lockSWODProgramSettings").is(':checked') && !sw_lockprogramsettings) {
							sw_lockprogramsettings = true;
							reloadSWLProgram = true;
						} else if (!$("#lockSWODProgramSettings").is(':checked') && sw_lockprogramsettings) {
							reloadSWLProgram = true;
						} else if (roomStatusChanged || r.zoomwebinarcreated === true){
							reloadSWLProgram = true;
						}

						if (reloadSWLProgram) self.location.href = getSWEditProgramLink() + (programAdded ? '&programAdded=true' : '');

						if($('#isPublished').val() == 1) {
							removeStatement(publishedIssues);
							addStatementIfNotExists(publishedSuccessStatement,'seminarSetupStatements');
						}
						else {
							addStatementIfNotExists(publishedIssues);
							removeStatement(publishedSuccessStatement,'seminarSetupStatements');
						}
						// If no more issues, hide the entire alert
						if ($('.seminarSetupIssues ul li').length === 0) {
							$('.seminarSetupIssues').addClass('d-none');
							$('.seminarSetupStatements').removeClass('d-none');
						}
						else if($('.seminarSetupIssues').hasClass('d-none')) {
							$('.seminarSetupIssues').removeClass('d-none');
							$('.seminarSetupStatements').addClass('d-none');
						}
						if(!$("#program-basics .card-header:first #saveResponse").length)
							$("#program-basics .card-header:first .card-header--title").after('<span id="saveResponse"></span>');
						$('#program-basics .card-header:first #saveResponse').html('<span class="badge badge-success">SAVED SUCCESSFULLY</span>').addClass('text-success').show().fadeOut(5000);

						if(programAdded)
							$('#nextButton, #prevButton').prop('disabled',false);
						else
							$('#program-basics .card-footer .save-button').prop('disabled',false);

						refreshSWLSeminarTitleHeader(r);

						if (callback) {
							callback();
						}
					} else {
						var arrReq = [];
						arrReq.push(r.err && r.err.length ? r.err : 'We were unable to save the program details.');
						$('#err_swldetails').html(arrReq.join('<br/>')).removeClass('d-none');
						$('html,body').animate({scrollTop: $('#err_swldetails').offset().top-120},500);
						if(programAdded)
							$('#nextButton').prop('disabled',false);
						else
							$('#program-basics .card-footer .save-button').prop('disabled',false);
					}

					$('#frmSWLProgramDetails,#divSWProgramDetailsSaveLoading').toggle();

					if($('button.btnCreateWebinar').length)
						$('button.btnCreateWebinar').prop('disabled',false);
				}
				$('#frmSWLProgramDetails,#divSWProgramDetailsSaveLoading').toggle();

				var objParams = getSWLBasicsFormData();
				TS_AJX('ADMINSWL','updateSWLProgram',objParams,saveResult,saveResult,60000,saveResult);
			}
			else showValidationAlertForSWLSeminar('Program Code must be unique.');
		};

		var objParams = { programType:'SWL', programID:$('#seminarID').val(), programCode:programCode };
		TS_AJX('ADMINSWCOMMON','checkProgramCode',objParams,checkProgramCodeResult,checkProgramCodeResult,10000,checkProgramCodeResult);
	}

	return false;
}
function refreshSWLSeminarTitleHeader(r){
	var seminarName = $('#seminarName').val().trim();
	var seminarSubTitle = $('#seminarSubTitle').val();
	var hasSubTitle = seminarSubTitle.length > 0;
	$('h4#dispSeminarTitle').text(seminarName).removeClass('mb-1 mb-2').addClass(hasSubTitle ? 'mb-1' : 'mb-2');
	$('h5#dispSeminarSubTitle').text(seminarSubTitle).toggleClass('d-none',!hasSubTitle);

	if(r.dspstarttime && r.dspstarttime.length){
		$('span#dispSeminarStartTime').text(r.dspstarttime);
	}
}
function getSWLBasicsFormData(){
	var seminarDesc = "";
	if(CKEDITOR.instances['seminarDesc'] != null)
		seminarDesc = CKEDITOR.instances['seminarDesc'].getData().trim();
	else 
		seminarDesc = $('textarea[name="seminarDesc"]').val().trim();

	return { 
		seminarID:$('#seminarID').val(),
		seminarName:$('#seminarName').val().trim(),
		seminarSubTitle:$('#seminarSubTitle').val(),
		programCode:$('#programCode').val().trim(),
		seminarDesc:seminarDesc,
		isPublished:$("#isPublished").val(),
		dateStart:$("#dateStart").val(),
		dateEnd:$("#dateEnd").val(),
		lockSWLProgramSettings:$("#lockSWLProgramSettings:checked").length > 0,
		webinarHost:$('input[name="webinarHost"]:checked').val() ? $('input[name="webinarHost"]:checked').val() : '',
		createSWLWebinar:$('#createSWLWebinar').val(),
		agenda:$('#agenda').val(),
		isOpen:$('#isOpen').val(),
		zoomHostID:$('#zoomHostID').val() ? $('#zoomHostID').val() : ''
	}
}
function showValidationAlertForSWLSeminar(msg){
	$('#err_swldetails').html(msg).removeClass('d-none');
	$('html,body').animate({scrollTop: $('#err_swldetails').offset().top-120},500);
	if(programAdded)
		$('#nextButton').prop('disabled',false);
	else
		$('#program-basics .card-footer .save-button').prop('disabled',false);
	if ($('button.btnCreateWebinar').length) $('button.btnCreateWebinar').prop('disabled',false);
}
function uploadSWLReplayVideo() {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'md',
		title: 'Upload Replay Video',
		iframe: true,
		contenturl: link_uploadswlreplayvideo + '&pid=' + swl_seminarid,
		strmodalfooter : {
			classlist: 'text-right',
			showclose: true
		}
	});
}
function loadUploadSWLReplayVideoForm(obj) {
	if (isSWProgramLocked()) return false;
	var uploader = new plupload.Uploader({
		runtimes : 'html5',
		browse_button : 'btnSelectReplayFile',
		multi_selection: false,
		url : obj.uploadurl,
		multipart : true,
		init : {
			PostInit: function() {
				$('#selectedReplayFileName').val('');
				$('#btnUploadReplayFile').unbind().on('click',function(){
					$('#btnUploadReplayFile').prop('disabled',true);
					$('#selectedReplayFileName').append(mca_getLoadingHTML());
					uploader.start();
					return false;
				});
			},
			FilesAdded: function(up, files) {
				plupload.each(files, function(file) {
					$('#selectedReplayFileName').html('<div id="' + file.id + '"><b>' + file.name + ' (' + plupload.formatSize(file.size) + ')</b></div>');
				});
				$('#btnSelectReplayFile').prop('disabled',true);
				$('#btnUploadReplayFile').prop('disabled',false);
			},
			BeforeUpload: function(up, file) {
				var contentType = "application/octet-stream";
				var ext = file.name.substr(file.name.lastIndexOf('.')).toLowerCase();
				var filename = obj.seminarid + ext;
				
				up.settings.multipart_params = {
					'Filename': filename,
					'key': obj.objectkey,
					'AWSAccessKeyId': obj.accesskey,
					'acl': 'public-read',
					'content-type': contentType,
					'policy': obj.policy,
					'signature': obj.policysignature
				};
				// Next line needed to force the params to be passed along in the POST
				up.setOption('params', up.settings.multipart_params);
			},
			UploadComplete: function(up, files) {
				top.onUploadCompleteSWLReplayVideo();
			},
			Error: function(up, error) {
				if (error.message != 'File extension error.')
					$('#err_uploadreplayfile').html('We ran into an issue. ' + error.message).removeClass('d-none');
			}
		},
		filters : {
			max_file_size : '2048mb',
			mime_types: [ {title:"Streaming files", extensions:"mp4"} ]
		}
	});
	uploader.init();
}
function onUploadCompleteSWLReplayVideo(){
	var setUploadedFlagResult = function(r) {
		if (!r.success || r.success.toLowerCase() != 'true') {
			alert('An error occured while updating the seminar replay settings.');
		}
		else if(r.success && r.success.toLowerCase() == 'true' && r.replayvideolink && r.replayvideolink.length) {
			SWVideoPlayer.src(r.replayvideolink);
			hasReplayVideo = true;
			$('#hasReplayVideoUploaded').val(1);
			toggleReplayVideoPlayerDisplay();
		}
		top.MCModalUtils.hideModal();
		validateAndSaveSWLSeminarRecording();
	};

	var objParams = { seminarID:swl_seminarid };
	TS_AJX('ADMINSWL','setSWLReplayUploadedFlag',objParams,setUploadedFlagResult,setUploadedFlagResult,200000,setUploadedFlagResult);
}
function removeSWLReplayVideo(fid) {
	if (isSWProgramLocked()) return false;
	
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'md',
		title: 'Delete Replay Video?',
		strmodalbody: {
			content: '<div class="alert pl-2 align-content-center alert-danger mt-3" role="alert"><span class="font-size-lg d-40 mr-2 text-center"><i class="fa-regular fa-exclamation-circle"></i></span><span style="white-space: nowrap;"><strong>Confirmation needed</strong><br/>Are you sure you want to delete the replay video?</span></div>',
		},
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-danger ml-auto',
			extrabuttonlabel: 'Delete',
		}
	});
	$('#btnMCModalSave').on('click', function(){
		doRemoveSWLReplayVideo();
	});
}
function doRemoveSWLReplayVideo() {
	if (isSWProgramLocked()) return false;
	
	var deleteReplayVideoResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			SWVideoPlayer.src('');
			hasReplayVideo = false;
			top.$('#hasReplayVideoUploaded').val(0);
			toggleReplayVideoPlayerDisplay();
			setUpReplayButton();
		} else { 
			alert('We were unable to delete this file.');
		}
		top.MCModalUtils.hideModal();
		top.$('#btnRemoveSWLReplayVideo').prop('disabled',false);
	};

	top.$('#btnRemoveSWLReplayVideo').prop('disabled',true);
	var objParams = { seminarID:swl_seminarid };
	TS_AJX('ADMINSWL','deleteSWLReplayVideo',objParams,deleteReplayVideoResult,deleteReplayVideoResult,200000,deleteReplayVideoResult);
}
function convertToSWOD() {
	if (isSWProgramLocked()) return false;

	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Convert to OnDemand',
		iframe: true,
		contenturl: link_convertswltoswod + '&pid=' + swl_seminarid + '&mode=direct',
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frm_convertSWLToSWOD :submit").click',
			extrabuttonlabel: 'Convert to OnDemand',
		}
	});
}
function addSWLProviderRegistrantID(rid) {
	var addResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			reloadSWProgramRegGrid(sw_itemtype,sw_reggridmode);
		} else if (r.success && r.success.toLowerCase() == 'false' && r.errmsg) {
			alert(r.errmsg);
		} else {
			alert('We were unable to add the registrant to this seminar stream provider. Try again.');
		}
	};
	var objParams = { seminarID:swl_seminarid, registrantID:rid };
	TS_AJX('ADMINSWL','addSWLProviderRegistrantID',objParams,addResult,addResult,10000,addResult);
}
function addSWLProviderUserID(aid,atype) {
	var addResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			reloadSWAuthorGrids(atype);
		} else {
			alert('We were unable to add the speaker to this seminar stream provider. Try again.');
		}
	};
	var objParams = { programID:swl_seminarid, programType:'SWL', authorID:aid };
	TS_AJX('ADMINSWAUTHOR','addSWProgramAuthor',objParams,addResult,addResult,10000,addResult);
}
function addSWLAuthorCode(aid,atype) {
	var addResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			reloadSWAuthorGrids(atype);
		} else {
			alert('We were unable to add SWL Code. Try again.');
		}
	};
	var objParams = { seminarID:swl_seminarid, authorID:aid };
	TS_AJX('ADMINSWAUTHOR','addSeminarAuthorSWLCode',objParams,addResult,addResult,10000,addResult);
}
function getSWProgramPartners() {
	if (isSWProgramLocked()) return false;
	
	var partnerResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			var html = '';
			if (r.arroptinsoptouts.length) {
				for (var i=0; i<r.arroptinsoptouts.length; i++) {
					var isSelected = r.arroptinsoptouts[i].isadded === 1 ? 'selected' : '';
					html += '<option value="'+r.arroptinsoptouts[i].orgcode+'" ' + isSelected + ' isEnrollmentExist='+r.arroptinsoptouts[i].isenrollmentexist+'>'+r.arroptinsoptouts[i].description+'</option>';
				}
			}else{
				manageProgramSyndicationWithoutPartner();
			}
			$('select#programPartner').html(html);
			mca_setupSelect2ByID('programPartner');
			changeProgramPartnetEnrollChoiceColor();
		} else {
			alert('An error occured while loading Program Partner. Try again.');
		}
	};
	var objParams = { seminarID:getSWProgramID(), programType:sw_itemtype};
	TS_AJX('ADMINSWCOMMON','getAvailableSWProgramOptInAndOuts',objParams,partnerResult,partnerResult,10000,partnerResult);
}
function changeProgramPartnetEnrollChoiceColor(){
	if($("#programPartner option[isenrollmentexist!=0]").length){
		$("#programPartner option[isenrollmentexist!=0]").each(function(){
			$(".select2-selection__choice:contains('" + $(this).text() + "')").css("background-color", "gray");
		});
	}
}
function manageProgramSyndicationWithoutPartner(){
	if($('input[name="allowSyndication"]:checked').length){
		$('input[name="allowSyndication"]').prop("checked",false).attr("disabled","disabled");
		$('#allowSyndicationHolder').addClass('d-none');
		$('#noSyndicatePrograms').removeClass('d-none');
		if (typeof saveSWODRateSyndication !== 'undefined' && typeof saveSWODRateSyndication === 'function') {
			saveSWODRateSyndication();
		}
		if (typeof saveSWLRateSyndication !== 'undefined' && typeof saveSWLRateSyndication === 'function') {
			saveSWLRateSyndication();
		}
	}else if(!$('input[name="allowSyndication"]:checked').length){
		$('input[name="allowSyndication"]').attr("disabled","disabled");
		$('#noSyndicatePrograms').removeClass('d-none');
	}
}
function selectAllSWProgramPartners(mode){
	if (isSWProgramLocked()) return false;

	var programPartnerSelect = $('#programPartner');

	programPartnerSelect.find('option').prop('selected', true);
	programPartnerSelect.trigger('change');
	var orgCodeList = $('#programPartner').val() || '';
	if(mode == 'swbsyndtab')
		saveSWBOptIn(orgCodeList.join(','),mode);
	else
		addSWProgramPartner(orgCodeList.join(','),mode);
	changeProgramPartnetEnrollChoiceColor();
}
function addSWProgramPartner(data,mode){
	if (isSWProgramLocked()) return false;

	var addResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			changeProgramPartnetEnrollChoiceColor();
		} else {
			alert('An error occured while adding this Program Partner. Try again.');
		}

		toggleSWProgramPartnerSaveProgress(false,mode);
	};

	toggleSWProgramPartnerSaveProgress(true,mode);

	var objParams = { orgCodeList:data, seminarID:getSWProgramID(), programType:sw_itemtype };
	TS_AJX('ADMINSWCOMMON','optInSWSeminar',objParams,addResult,addResult,60000,addResult);
}
function deleteSWProgramPartner(data,mode){
	if (isSWProgramLocked()) return false;

	var removeResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			if (typeof window['onOptOutOfSyndicatedProgram'] != 'undefined' && typeof window['onOptOutOfSyndicatedProgram'] == 'function') 
				window['onOptOutOfSyndicatedProgram']();
			else {
				var orgArray = data.split(",");
				if(orgArray.length > 1){
					getSWProgramPartners();
				}else{
					changeProgramPartnetEnrollChoiceColor();
				}
			}
		} else {
			alert('An error occured while deleting this Program Partner. Try again.');
		}
		toggleSWProgramPartnerSaveProgress(false,mode);
	};

	toggleSWProgramPartnerSaveProgress(true,mode);

	var objParams = { orgCodeList:data, seminarID:getSWProgramID(), programType:sw_itemtype };
	TS_AJX('ADMINSWCOMMON','optOutSWSeminar',objParams,removeResult,removeResult,20000,removeResult);
}
function toggleSWProgramPartnerSaveProgress(flag,mode){
	if (isSWProgramLocked()) return false;

	if (mode && $.inArray(mode, ['swlsyndtab','swodsyndtab','swbsyndtab']) != -1){
		$(programAdded ? '#nextButton, #prevButton' : 'div#program-syndication .card-footer button.save-button').prop('disabled',flag);
		$('#divProgramPartnerSaveProgress').toggleClass('d-none',!flag);
	}
}
function removeSWLRegistrant(eid) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Cancel Registration',
		iframe: true,
		contenturl: link_removeenrollment + '&eid=' + eid + '&ft=SWL',
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			closebuttonlabel: 'Cancel',
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.doCallRemoveReg',
			extrabuttonlabel: 'Remove Registrant',
		}
	});
}
function doRemoveSWLRegistrant(obj) {
	var removeSWLRegistrantResult = function(r) {
		top.MCModalUtils.hideModal();
		if (r.success && r.success.toLowerCase() == 'true') {
			SWLRegistrantsListTable.draw(false);  
		} 
		else if (r.success && r.success.toLowerCase() == 'false' && r.errmsg) { alert(r.errmsg); } 
		else { alert('Unable to remove registrant.'); }
	};

	var objParams = { enrollmentID:obj.enrollmentID, AROption:obj.AROption, emailRegistrant:obj.emailRegistrant };
	TS_AJX('ADMINSWL','removeEnrollment',objParams,removeSWLRegistrantResult,removeSWLRegistrantResult,20000,removeSWLRegistrantResult);
}
function manageSWLRegAttendance() {
	if(getSWRegistrantsRowCount('SWL') == 0) {
		alert('There are no registrants to act upon.');
		return false;
	}
	var manageAttendanceLink = link_manageattendance + '&' + $('#frmRegistrantFilter').serialize();
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Manage Attendance For Filtered Registrants',
		iframe: true,
		contenturl: manageAttendanceLink,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto ',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnSaveAttendance").click',
			extrabuttonlabel: 'Save Attendance',
		}
	});
}
function uploadSWLRegAttendance() {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Upload Stream Provider Attendance Report',
		iframe: true,
		contenturl: link_uploadattendance,
		strmodalfooter: {
			classlist: 'd-none'
		}
	});
}

function massEmailSWLInstructions() {
	if(getSWRegistrantsRowCount('SWL') == 0) {
		alert('There are no registrants to act upon.');
		return false;
	}
	var massEmailInstrLink = link_massemailinstr + '&' + $('#frmRegistrantFilter').serialize();
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Email Program Components to Filtered Registrants',
		iframe: true,
		contenturl: massEmailInstrLink,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmInstructions :submit").click',
			extrabuttonlabel: 'Send'
		}
	});
}
function massEmailSWLRegMaterials() {
	if(getSWRegistrantsRowCount('SWL') == 0) {
		alert('There are no registrants to act upon.');
		return false;
	}
	
	var massEmailRegLink = link_massemailmaterials + '&' + $('#frmRegistrantFilter').serialize();
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Email Materials to Filtered Registrants',
		iframe: true,
		contenturl: massEmailRegLink,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmMaterials :submit").click',
			extrabuttonlabel: 'Send'
		}
	});
}
function massEmailSWLRegReplayLinks() {
	if(getSWRegistrantsRowCount('SWL') == 0) {
		alert('There are no registrants to act upon.');
		return false;
	}
	
	var massEmailReplayLink = link_massemailreplaylinks + '&' + $('#frmRegistrantFilter').serialize();
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Send Replay Link to Filtered Registrants',
		iframe: true,
		contenturl: massEmailReplayLink,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmReplay :submit").click',
			extrabuttonlabel: 'Send'
		}
	});
}
function showSWLProviderDetails(host, isSuperUser) {
	$('div.swlprovider').addClass('d-none');
	if (host == 'Assoc') {
		$('#divWebinarHostAssocDetails').removeClass('d-none');
		$('#infoAlert').addClass('d-none');
	}
	else if (host == 'SemWeb') {
		$('#divWebinarHostSemWebDetails').removeClass('d-none');

		if (isSuperUser && $('#btnDisconnectZoomWebinar').length) {
			// Hide info alert when a host is added by a Superuser
			$('#infoAlert').addClass('d-none');
		} else {
			// Show the info alert by default if no host is added
			$('#infoAlert').removeClass('d-none');
		}
	}
}
function refreshSWLZoomWebinarLicenses() {
	$('#span_swl_zoomwebinarhostrefresh').html('<i class="fa-light fa-circle-notch fa-spin"></i> Refreshing...');
	$('#zoomHostID').empty();
	$.getJSON('/?event=proxy.ts_json&c=ADMINSWL&m=refreshSWLZoomWebinarLicenses')
		.done(populateSWLZoomLicenses)
		.fail(populateSWLZoomLicenses);
}
function populateSWLZoomLicenses(r) {
	$('#span_swl_zoomwebinarhostrefresh').html('<a href="javascript:refreshSWLZoomWebinarLicenses();" style="color:#aeaeae;"><i class="fa-regular fa-sync"></i></a>');
	if (r.success) {
		$.each(r.arrzoomwebinarlicenses, function (i, item) {
			$('#zoomHostID').append( $('<option>', { value:item.id, text:item.firstname }) );
		});
	} else {
		alert('We were unable to refresh hosts. Try again.');
	}
}
function disconnectSWLSeminarFromZoomWebinar(wid) {
	var disconnectResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			self.location.href = link_editswlprogram;
		} else { 
			$('#btnDisconnectZoomWebinar').prop('disabled',false);
			alert('We were unable to disconnect this seminar from Zoom.');
		}
	};
	MCModalUtils.showModal({
		verticallycentered: true,
		size: 'md',
		title: 'Confirmation Needed',
		strmodalbody: {
			content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>Are you sure you want to disconnect this seminar from Zoom?</span></div>'
		},
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-outline-danger ml-auto',
			extrabuttonlabel: 'Confirm'
		}
	});
	$('#btnMCModalSave').on('click', function(){
		MCModalUtils.hideModal();
		$('#btnDisconnectZoomWebinar').prop('disabled',true);
		var objParams = { seminarID:swl_seminarid, webinarID:wid };
		TS_AJX('ADMINSWL','disconnectSWLSeminarFromZoomWebinar',objParams,disconnectResult,disconnectResult,10000,disconnectResult);
	});
	$('#MCModal').on('hidden.bs.modal', function() {
		$('#btnMCModalSave').off('click');
	});
}
function registerSWLRegForSWODPrompt() {
	if(getSWRegistrantsRowCount('SWL') == 0) {
		alert('There are no registrants to act upon.');
		return false;
	}
	var regLink = link_swlregforswod + '&' + $('#frmRegistrantFilter').serialize();
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Register for the Associated On Demand Program',
		iframe: true,
		contenturl: regLink,
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnRegisterSWLRegistrants").click',
			extrabuttonlabel: 'Register for OnDemand',
			extrabuttoniconclass: 'fa-light fa-right'
		}
	});
}
function viewSWLFeeStructure(title){
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		title: title,
		strmodalbody: { content: $('#mc_SWLFeeStructureHTML').html() }
	});
}
function markSWLBillingAsClosed(){	
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Close Billing',
		strmodalbody: {
			content: $('#mc_SWLCloseBillingTemplate').html()
		},
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			closebuttonlabel: 'Cancel',
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: 'doSaveSWLBillingInfo',
			extrabuttonlabel: 'Save',
		}
	});

	mca_setupDateTimePickerRangeFields('actualSWLStartDate','actualSWLEndDate',15);
	mca_setupCalendarIcons('MCModalBody');
	roundSWLBillingDates();
}
function doSaveSWLBillingInfo() {
	let saveSWLBillingInfoResult = function(r) {
		MCModalUtils.hideModal();
		if (r.success && r.success.toLowerCase() == 'true') {
			self.location.href = link_editswlprogram + '&tab=billing';
		} else {
			alert('An error occured while saving the seminar billing information. Try again.');
		}
	};

	let arrReq = [];
	let objParams = { seminarID: swl_seminarid, actualSWLStartDate: $('#actualSWLStartDate').val(), actualSWLEndDate: $('#actualSWLEndDate').val(),
						numOtherSWLRegistrants: $('#numOtherSWLRegistrants').val() };
	if (!objParams.actualSWLStartDate.length || !objParams.actualSWLEndDate.length) arrReq.push('Enter both the Start and End Dates.');
	else {
		let parsedStartDate = mca_getParsedDateTime(objParams.actualSWLStartDate);
		let parsedEndDate = mca_getParsedDateTime(objParams.actualSWLEndDate);
		let SWBillingMinimumMins = Number($('#SWBillingMinimumMins').val());

		if (((parsedEndDate - parsedStartDate) /  (1000 * 60)) < SWBillingMinimumMins) arrReq.push('Recorded time does not meet the minimum required minutes for billing. Actual Start and End Dates must be at least '+SWBillingMinimumMins+' minutes apart.');
		else if (roundSWLBillingDates()) arrReq.push('Start/End Dates are rounded to follow 15-minute increments. Please confirm and save the dates again.');
	}
	if (!objParams.numOtherSWLRegistrants.length) arrReq.push('How many other registrants should be accounted for?');

	if (arrReq.length) {
		mca_showAlert('err_swlbillinginfo',arrReq.join('<br />'),true);
		return false;
	} else {
		mca_hideAlert('err_swlbillinginfo');
		$('#btnMCModalSave').prop('disabled',true).html('<i class="fa-light fa-circle-notch fa-spin"></i> Saving...');
		TS_AJX('ADMINSWL','updateSWLBillingInfo',objParams,saveSWLBillingInfoResult,saveSWLBillingInfoResult,10000,saveSWLBillingInfoResult);
	}
}
function roundSWLBillingDates() {
	let objDates = { actualSWLStartDate: $('#actualSWLStartDate').val(), actualSWLEndDate: $('#actualSWLEndDate').val() };
	let datesRounded = false;
	
	if (objDates.actualSWLStartDate.length) {
		let parsedStartDate = mca_getParsedDateTime(objDates.actualSWLStartDate);
		let parsedStartDateMins = new Date(parsedStartDate).getMinutes();
		if ($.inArray(parsedStartDateMins,[0,15,30,45]) == -1) {
			let decrementMins = parsedStartDateMins - (Math.floor(parsedStartDateMins/15) * 15);
			mca_setValueForDatePickerField((parsedStartDate - (decrementMins * 60 * 1000)),$('#actualSWLStartDate'),'M/D/YYYY - h:mm A');
			datesRounded = true;
		}
	}
	if (objDates.actualSWLEndDate.length) {
		let parsedEndDate = mca_getParsedDateTime(objDates.actualSWLEndDate);
		let parsedEndDateMins = new Date(parsedEndDate).getMinutes();
		if ($.inArray(parsedEndDateMins,[0,15,30,45]) == -1) {
			let incrementMins = (Math.ceil(parsedEndDateMins/15) * 15) - parsedEndDateMins;
			mca_setValueForDatePickerField((parsedEndDate + (incrementMins * 60 * 1000)),$('#actualSWLEndDate'),'M/D/YYYY - h:mm A');
			datesRounded = true;
		}
	}

	return datesRounded;
}
function cancelSWLProgram(){
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Cancel Program',
		iframe: true,
		contenturl: link_swcancelprogramlink,
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-outline-danger ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.cancelSWLProgram',
			extrabuttonlabel: 'Cancel Program',
		}
	});
}
function doCancelSWLProgram(obj) {
	let cancelSWLProgramResult = function(r) {
		top.MCModalUtils.hideModal();
		if (r.success && r.success.toLowerCase() == 'true') {
			self.location.href = link_editswlprogram + '&tab=billing';
		} else {
			alert(r.errmsg && r.errmsg.length ? r.errmsg : 'We were unable to cancel this Program. Try again.');
		}
	};

	$('#btnMCModalSave').prop('disabled',true).html('<i class="fa-light fa-circle-notch fa-spin"></i> Cancelling Program...');

	let objParams = { seminarID:swl_seminarid, cancellationFee:obj.cancellationFee,
		emailRegistrants:obj.emailRegistrants, customText:obj.customText };
	TS_AJX('ADMINSWL','cancelSeminar',objParams,cancelSWLProgramResult,cancelSWLProgramResult,600000,cancelSWLProgramResult);
}

/* SWOD functions */
function initializeSWODProgramsTable(){				
	let arrColumns = [];

	arrColumns.push(
		{
			"data": null,
			"render": function (data, type) {
				let renderData = '';
				if (type === 'display') {
					if (!data.isPublished)
						renderData +=  '<div class="badge badge-warning mb-1">Inactive</div>';
					renderData += '<div class="d-flex"><span>'+data.seminarName+'</span>';
					if (data.lockSettings)
						renderData +=  '<span class="fa-solid fa-lock ml-1" style="font-size:17px;color:grey;" title="Changes to this program are restricted by a Client Administrator."></span>';
					if(data.seminarID){
						renderData +=  '<span id="featuredBadgeContainer_'+data.seminarID+'" class="ml-auto">'+(data.isFeatured ? '<span class="badge badge-info">Featured</span>' : '')+'</span>';
					}
					renderData += '</div>';
					if(data.seminarSubTitle.length) renderData += '<div class="text-dim small">'+data.seminarSubTitle+'</div>';
				}
				return type === 'display' ? renderData : data;
			},
			"className": "align-top",
			"width": "50%"
		},
		{ "data": "dateActivated", "width": "10%", "className": "align-top text-center"},
		{ "data": "dateOrigPublished", "width": "10%", "className": "align-top text-center"}
	);

	if (sw_hop == 0) {
		arrColumns.push(
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						if (data.gridMode.toLowerCase() == 'swsearchgrid') renderData += data.publisherOrgCode;
						else {
							if (data.publisherOrgCode == sw_sitecode) {
								renderData += 'Publisher';
								if (data.isSyndicated)
									renderData += '<div class="text-dim small">SYNDICATED</div>';
							} else
								renderData += '<div>Opt-In</div><div class="text-dim small">'+data.publisherOrgCode+'</div>';
							renderData += '</div>';
						}
					}
					return type === 'display' ? renderData : data;
				},
				"width": "10%",
				"className": "align-top text-center"
			}
		);
	}

	arrColumns.push(
		{ "data": null,
			"render": function ( data, type, row, meta ) {
				let renderData = '';
				if (type === 'display') {
					if(data.gridMode.toLowerCase() == 'swsearchgrid') {
						if($.trim(data.siteHostName).length) {
							renderData += '<a href="'+mca_getSiteToolLink(data.publisherOrgCode,'SeminarWebAdmin','listSWOD','editSWODProgram','pid='+data.seminarID)+'" target="_swodPrg'+data.seminarID+'" title="Edit This Program" class="btn btn-xs btn-outline-primary p-1 m-1"><i class="fa-solid fa-pencil"></i></a>';
						} else {
							renderData += '<a class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-pencil"></i></a>';
						}
					} else {
						renderData += '<a href="javascript:editSWODProgram('+data.seminarID+');" title="Edit This Program" class="btn btn-xs btn-outline-primary p-1 m-1"><i class="fa-solid fa-pencil"></i></a>';															
					}
					if(data.copyProgramRights){
						if (data.gridMode.toLowerCase() == 'swsearchgrid' || data.publisherOrgCode == sw_sitecode) {
							renderData += '<a href="javascript:copyProgram('+data.seminarID+');" class="btn btn-xs btn-outline-primary p-1 m-1" title="Copy Program"><i class="fa-solid fa-copy"></i></a>';
						} else {
							renderData += '<a class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-copy"></i></a>';
						}
					}
					if(data.isFeatured) {
						renderData += '<a href="javascript:removeFromFeaturedPrograms('+data.seminarID+');" class="btn btn-xs btn-outline-first p-1 m-1" title="Remove from Featured Programs"><i class="fa-solid fa-check-circle swfeatured_icon_'+data.seminarID+'"></i></a>';
					} else {
						renderData += '<a href="javascript:addToFeaturedPrograms('+data.seminarID+');" class="btn btn-xs btn-outline-dark p-1 m-1" title="Add to Featured Programs"><i class="fa-solid fa-check-circle swfeatured_icon_'+data.seminarID+'"></i></a>';
					}
					if (data.deleteProgramRights) {
						if(data.gridMode.toLowerCase() == 'swsearchgrid' || data.publisherOrgCode == sw_sitecode) {
							renderData += '<a href="javascript:deleteProgram('+data.seminarID+','+data.enrolledCount+');" class="btn btn-xs btn-outline-danger p-1 m-1" title="Delete Program"><i class="fa-solid fa-trash-alt"></i></a>';
						} else {
							renderData += '<a class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-trash-alt"></i></a>';
						}
					}
					if(data.gridMode.toLowerCase() == 'swsearchgrid') {
						if($.trim(data.siteHostName).length) {
							renderData += '<a href="'+mca_getSiteToolLink(data.publisherOrgCode,'SeminarWebAdmin','listSWOD','editSWODProgram','pid='+data.seminarID+'&tab=registrants')+'" target="_swodPrgReg'+data.seminarID+'" title="View Registrants" class="btn btn-xs btn-outline-primary p-1 m-1"><i class="fa-solid fa-users"></i></a>';
						} else {
							renderData += '<a class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-users"></i></a>';
						}
					} else if(data.manageSWODRegistrantsSignUp == 1 || data.manageSWODRegistrantsAll == 1) {
						renderData += '<a href="javascript:editSWODProgramRegistrants('+data.seminarID+');" class="btn btn-xs btn-outline-primary p-1 m-1" title="View Registrants"><i class="fa-solid fa-users"></i></a>';
					}
					renderData += '<span class="d-inline-flex ml-2 small text-first w-5">'+data.enrolledCount+'</span>';
				}
				return type === 'display' ? renderData : data;
			},
			"width": "20%",
			"className": "align-top text-center",
			"orderable": false
		}		
	);

	swodProgramsTable = $('#swodProgramsTable').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 10,
		"dom": "<'row'<'col-sm-3 col-md-3'l><'col-sm-9 col-md-9'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
		"lengthMenu": [ 10, 25, 50 ],
		"ajax": { 
			"url": link_listswodprograms,
			"type": "post",
			"data": function(d) {
				$.each($('#frmFilter').serializeArray(),function() {
					d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
				});
			}
		},
		"autoWidth": false,
		"columns": arrColumns,
		"order": [[0, 'asc']],
		"searching": false
	});
}
function addSWODProgram() {
	mca_hideAlert('err_swodaddprogram');
	if ($('#divAddProgramForm').length && $('#divAddProgramForm').hasClass('d-none')) {
		$('#swSeminarName').val('');
		$('div.divSWODTool').addClass('d-none');
		$('#divAddProgramForm').removeClass('d-none');
	}
}
function doAddSWODProgram() {
	var addSWODProgramResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true' && r.seminarid){
			self.location.href = link_editswodprogram + '&pid=' + r.seminarid + '&programAdded=true';
		} else {
			mca_showAlert('err_swodaddprogram','We were unable to create this SWOD Program. Try again.');
			$('#btnAddSWODProgram').prop('disabled',false);
		}
	};

	mca_hideAlert('err_swodaddprogram');
	var arrReq = [];
	var semname = $('#swSeminarName').val().trim();
	var dateOrigPublished = $('#dateOrigPublished').val().trim();

	if (semname == '') arrReq[arrReq.length] = 'Enter the Seminar Name.';
	if (dateOrigPublished == '') arrReq[arrReq.length] = 'Enter the date originally published.';

	if (arrReq.length) {
		mca_showAlert('err_swodaddprogram',arrReq.join('<br/>'));
		return false;
	}

	$('#btnAddSWODProgram').prop('disabled',true);
	
	var objParams = { seminarName:semname, dateOrigPublished:dateOrigPublished };
	TS_AJX('ADMINSWOD','createSWODProgram',objParams,addSWODProgramResult,addSWODProgramResult,10000,addSWODProgramResult);
}
function editSWODProgram(pid) {
	window.open(link_editswodprogram + '&pid=' + pid,'_blank'); 
}
function editSWODProgramRegistrants(pid) {
	window.open(link_editswodprogram + '&pid=' + pid + '&tab=registrants','_blank');
}
function viewSWODCertificate(eid) {
	viewCertificate(eid,'SWOD');
}
function viewSWODProgress(pid,eid,gridmode) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: 'Seminar Progress',
		iframe: true,
		contenturl: link_viewswodprogress + '&pid=' + pid + '&eid=' + eid + '&from=SWOD&gridmode=' + gridmode,
		strmodalfooter : {
			showclose: true,	
		}
	});
}
function viewSWODCommunication(pid,did,eid) {
	viewCommunication(pid,did,eid,'SWOD');
}
function viewSWODInstructions(pid,did,eid) {
	resendInstructions(pid,did,eid,'SWOD');
}
function filterSWODPrograms() {
	if ($('#divFilterForm').hasClass('d-none')) {
		$('div.divSWODTool').addClass('d-none');
		$('#divFilterForm').removeClass('d-none');
	}
}
function filterSWODRegistrants() {
	if ($('#divFilterRegistrantsForm').hasClass('d-none')) {
		$('#divItemForm').addClass('d-none');
		$('#divFilterRegistrantsForm').removeClass('d-none');
	}
}
function clearSWODProgramFilters() {
	$('#frmFilter').find('input[type="text"]').val('');
	$('#fPubType').val('PO');
	$('#fStatus').val(1);
	$('#fFeaturedOnly').prop('checked',false);
	$('#fSyndicatedOnly').prop('checked',false);
	$('#frmFilter [data-toggle="custom-select2"]').trigger('change.select2');
	dofilterSWODPrograms();
}
function dofilterSWODPrograms() {
	swodProgramsTable.draw();
}
function clearSWODBundleFilters() {
	$('#fActivatedDateFrom').val('');
	$('#fActivatedDateTo').val('');
	$('#fOrigPublishDateFrom').val('');
	$('#fOrigPublishDateTo').val('');
	$('#fKeyword').val('');
	$('#fProgramCode').val('');
	$('#fPubType').val('PO');
	$('#fStatus').val(1);
	$('#fFeaturedOnly').val(0);
	$('#fFeaturedOnly').removeAttr('checked');
	$('#fSyndicatedOnly').val(0);
	$('#fSyndicatedOnly').removeAttr('checked');
	dofilterSWODBundlePrograms();
}
function dofilterSWODBundlePrograms(){
	bundlesTable.draw();
}

function clearSWLPgmFilters() {
	$('#fDateFrom').val('');
	$('#fDateTo').val('');
	$('#fKeyword').val('');
	$('#fProgramCode').val('');
	$('#fPubType').val('PO');
	$('#fStatus').val(1);
	$('#fFeaturedOnly').val(0);
	$('#fFeaturedOnly').removeAttr('checked');
	$('#fSyndicatedOnly').val(0);
	$('#fSyndicatedOnly').removeAttr('checked');
	dofilterSWLPgm();
}
function dofilterSWLPgm(){
	swlProgramsTable.draw();
}
	
function clearSWODRegSearchFilters() {
	$('#frmRegistrantFilter')[0].reset();
	dofilterSWODRegistrants();
}
function dofilterSWODRegistrants() {	
	$('#SWODRegistrantGriddates').html($('#frrDateFrom').val() + ' to ' + $('#frrDateTo').val());
	SWODRegistrantsListTable.draw();
}
function quickFilterSWODRegRegistrants(d) {
	var now = new Date();
	var thisDate = new Date();
	var todayDate = new Date(thisDate.getFullYear(), thisDate.getMonth(), thisDate.getDate());

	var n = d * 1;
	if(n >= 0){
		$('#frrDateFrom').val(moment(todayDate).format('M/D/YYYY'));
		todayDate.setDate(now.getDate()+n);
		$('#frrDateTo').val(moment(todayDate).format('M/D/YYYY')); 
	}else{
		n = d * -1;
		$('#frrDateTo').val(moment(todayDate).format('M/D/YYYY'));
		todayDate.setDate(now.getDate()-n);
		$('#frrDateFrom').val(moment(todayDate).format('M/D/YYYY'));
	} 
	dofilterSWODRegistrants();
}
function quickFilterSWODRegComRegistrants(d) {
	var now = new Date();
	var thisDate = new Date();
	var todayDate = new Date(thisDate.getFullYear(), thisDate.getMonth(), thisDate.getDate());

	var n = d * 1;
	if(n >= 0){
		$('#frrDateCompletedFrom').val(moment(todayDate).format('M/D/YYYY'));
		todayDate.setDate(now.getDate()+n);
		$('#frrDateCompletedTo').val(moment(todayDate).format('M/D/YYYY')); 
	}else{
		n = d * -1;
		$('#frrDateCompletedTo').val(moment(todayDate).format('M/D/YYYY'));
		todayDate.setDate(now.getDate()-n);
		$('#frrDateCompletedFrom').val(moment(todayDate).format('M/D/YYYY'));
	} 
	dofilterSWODRegistrants();
}
function quickFilterSWODCreatedPrograms(d) {
	var now = new Date();
	var thisDate = new Date();
	var todayDate = new Date(thisDate.getFullYear(), thisDate.getMonth(), thisDate.getDate());

	var n = d * 1;
	if(n >= 0){
		$('#fCreatedDateFrom').val(moment(todayDate).format('M/D/YYYY'));
		todayDate.setDate(now.getDate()+n);
		$('#fCreatedDateTo').val(moment(todayDate).format('M/D/YYYY')); 
	}else{
		n = d * -1;
		$('#fCreatedDateTo').val(moment(todayDate).format('M/D/YYYY'));
		todayDate.setDate(now.getDate()-n);
		$('#fCreatedDateFrom').val(moment(todayDate).format('M/D/YYYY'));
	} 
	dofilterSWODPrograms();
}
function quickFilterSWODActPrograms(d) {
	var now = new Date();
	var thisDate = new Date();
	var todayDate = new Date(thisDate.getFullYear(), thisDate.getMonth(), thisDate.getDate());

	var n = d * 1;
	if(n >= 0){
		$('#fActivatedDateFrom').val(moment(todayDate).format('M/D/YYYY'));
		todayDate.setDate(now.getDate()+n);
		$('#fActivatedDateTo').val(moment(todayDate).format('M/D/YYYY')); 
	}else{
		n = d * -1;
		$('#fActivatedDateTo').val(moment(todayDate).format('M/D/YYYY'));
		todayDate.setDate(now.getDate()-n);
		$('#fActivatedDateFrom').val(moment(todayDate).format('M/D/YYYY'));
	} 
	dofilterSWODPrograms();
}
function quickFilterSWODPubPrograms(d) {
	var now = new Date();
	var thisDate = new Date();
	var todayDate = new Date(thisDate.getFullYear(), thisDate.getMonth(), thisDate.getDate());

	var n = d * 1;
	if(n >= 0){
		$('#fOrigPublishDateFrom').val(moment(todayDate).format('M/D/YYYY'));
		todayDate.setDate(now.getDate()+n);
		$('#fOrigPublishDateTo').val(moment(todayDate).format('M/D/YYYY')); 
	}else{
		n = d * -1;
		$('#fOrigPublishDateTo').val(moment(todayDate).format('M/D/YYYY'));
		todayDate.setDate(now.getDate()-n);
		$('#fOrigPublishDateFrom').val(moment(todayDate).format('M/D/YYYY'));
	} 
	dofilterSWODPrograms();
}

function exportSWODPrograms() {
	if ($('#divExportForm').hasClass('d-none')) {
		$('div.divSWODTool').addClass('d-none');
		$('#divExportFormArea').html('<h5>Download Programs</h5>' + mca_getLoadingHTML());
		$('#divExportForm').removeClass('d-none');
		$('#divExportFormArea').load(link_exportswodprogram + '&' + $('#frmFilter').serialize());
	}
}
function exportSWODRegistrants() {
	$('#divFilterRegistrantsForm').addClass('d-none');
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Download Filtered Registrants',
		iframe: true,
		contenturl: link_exportswregprompt,
		strmodalfooter: {
			classlist: 'text-right',
			showclose: false,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.doExportSWProgramReg',
			extrabuttonlabel: 'Download CSV',
			extrabuttoniconclass: 'fa-light fa-file-csv'
		}
	});
}
function doExportSWODPrograms(u) {
	self.location.href = '/tsdd/' + u;
	$('div.divSWODTool').addClass('d-none');
}
function filterSWODProgramRegistrants() {
	if ($('#divFilterForm').hasClass('d-none')) {
		$('div.divSWRegistrantsTool').addClass('d-none');
		$('#divFilterForm').removeClass('d-none');
	}
}
function clearSWODRegFilters() {
	$('#frmRegistrantFilter')[0].reset();
	doFilterSWODProgramRegistrants();
}
function doFilterSWODProgramRegistrants() {
	SWODRegistrantsListTable.draw();
}
function addSWReg(mid) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'xl',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: seminarName,
		iframe: true,
		contenturl: link_addSWReg + '&mid=' + mid
	});
}
function returnToRegistration(mid){
	MCModalUtils.hideModal();
	$('.modal-backdrop').remove();	
	addSWReg(mid);			
}
function closeUpdateRegistration(ft,gridMode) { 
	reloadSWProgramRegGrid(ft,gridMode);
	MCModalUtils.hideModal();
	
}
function loadSWODRegistrantSearchTab() {
	mca_setupDatePickerRangeFields('rDateFrom','rDateTo');
	mca_setupDatePickerRangeFields('cDateFrom','cDateTo');
	mca_setupCalendarIcons('frmRegistrantFilter');
	initSWODRegistrants();
}
function initSWODRegistrants() {
	let filterID = sw_reggridmode == "reggrid" ? "frmSWODRegFilter" : "frmRegistrantFilter";	
	let domString = "<'row'<'col-sm-3 col-md-3'l><'col-sm-9 col-md-9'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";

	SWODRegistrantsListTable = $('#SWODRegistrantsListTable').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 25,
		"lengthMenu": [ 10, 25, 50, 100 ],
		"dom": domString,
		"language": {
			"lengthMenu": "_MENU_",
			"emptyTable": "No Registrations found"
		},
		"ajax": { 
			"url": link_swodregistrantslist,
			"type": "post",
			"data": function(d) {
				$.each($('#'+filterID).serializeArray(),function() {
					d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
				});
			}
		},
		"autoWidth": false,
		"columns": [
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						if(sw_reggridmode == "reggrid"){
							renderData += '<a href="javascript:editSWODProgram('+data.seminarid+');">'+data.seminarname+'</a>';
							if(data.seminarsubtitle.length){
								renderData += '<div class="text-dim small">'+data.seminarsubtitle+'</div>';
							}
						} else {
							if (!data.isActive) renderData += '<div><span class="badge badge-danger">Deleted</div>';
							if(data.memberid)
								renderData += '<a href="javascript:editMember('+data.memberid+');">'+data.lastname+', '+data.firstname+' ('+data.membernumber+')</a>';
							else
								renderData += data.lastname+', '+data.firstname;
							if (data.company.length)				
								renderData += '<div class="text-dim small">'+data.company+'</div>';
							if (sw_reggridmode == "regsearchgrid")	{
								renderData += '<div class="text-dim small">'+data.seminarname+'</div>';
								if(data.seminarsubtitle.length){
									renderData += '<div class="text-dim small">'+data.seminarsubtitle+'</div>';
								}
							}
							if (data.signuporgcode != sw_sitecode)				
								renderData += '<div class="text-dim small">'+data.signuporgcode+'</div>';
						}						
					}
					return type === 'display' ? renderData : data;
				},
				"className": "align-top"
			},
			{ "data": "dateenrolled", 'className': 'align-top', "width": "10%" },	
			{ "data": "creditcount", 'className': 'text-center align-top', "width": "7%", "orderable": false },
			{ "data": "amountbilled", 'className': 'text-right align-top', "width": "7%", "orderable": false },
			{ "data": "amountdue", 'className': 'text-right align-top', "width": "7%", "orderable": false },
			{ 
				"data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						if (data.hasmanageswodregcreditrights) {
							renderData += '<a href="javascript:manageCredit('+data.seminarid+','+data.depomemberdataid+','+data.enrollmentid+',\'SWOD\',\''+sw_reggridmode+'\');" class="btn btn-xs btn-outline-warning px-1 m-1" title="Manage Credit"><i class="fa-solid fa-file-certificate"></i></a>';
						} else {
							renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 text-muted disabled"><i class="fa-solid fa-file-certificate"></i></a>';
						}

						if (data.hasviewswodcertificate) {
							renderData += '<a href="javascript:viewSWODCertificate('+data.enrollmentid+');" class="btn btn-xs btn-outline-primary px-1 m-1" title="View Certificate"><i class="fa-solid fa-certificate"></i></a>';
						} else {
							renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 text-muted disabled"><i class="fa-solid fa-certificate"></i></a>';
						}

						if (data.hasviewswodprogress) {
							renderData += '<a href="javascript:viewSWODProgress('+data.seminarid+','+data.enrollmentid+',\''+sw_reggridmode+'\');" class="btn btn-xs btn-outline-primary px-1 m-1" title="Manage Progress"><i class="fa-solid fa-eye"></i></a>';
						} else {
							renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 text-muted disabled"><i class="fa-solid fa-eye"></i></a>';
						}

						if (data.hasviewswodcommunication) {
							renderData += '<a href="javascript:viewSWODCommunication('+data.seminarid+','+data.depomemberdataid+','+data.enrollmentid+');" class="btn btn-xs btn-outline-primary px-1 m-1" title="View Communications"><i class="fa-solid fa-table"></i></a>';
						} else {
							renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 text-muted disabled"><i class="fa-solid fa-table"></i></a>';
						}

						if (data.hasviewswodinstructions) {
							renderData += '<a href="javascript:viewSWODInstructions('+data.seminarid+','+data.depomemberdataid+','+data.enrollmentid+');" class="btn btn-xs btn-outline-primary px-1 m-1" title="Resend Connection Instructions"><i class="fa-solid fa-envelope"></i></a>';
						} else {
							renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 text-muted disabled"><i class="fa-solid fa-envelope"></i></a>';
						}
		
						if (!data.assochandlesownpayment) {
							if(data.haschangeswregistrantprice) {
								renderData += '<a href="javascript:changeSWRegistrantPrice('+data.enrollmentid+',\'SWOD\');" class="btn btn-xs btn-outline-primary p-1 m-1" title="Change Price"><i class="fa-solid fa-money-bill-alt text-green"></i></a>';
							} else {
								renderData += '<a href="javascript:void(0)" class="btn btn-xs btn-outline-secondary p-1 m-1 text-muted disabled"><i class="fa-solid fa-money-bill-alt"></i></a>';
							}
						} else if(data.addpaymentencstring.length){
							renderData += '<a href="javascript:addSWPayment(\''+data.addpaymentencstring+'\',\'SWOD\');" class="btn btn-xs btn-outline-primary p-1 m-1" title="Pay for Registration"><i class="fa-solid fa-money-bill-alt text-green"></i></a>';
						} else {
							renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 text-muted disabled"><i class="fa-solid fa-money-bill-alt"></i></a>';
						}
						
						if(data.hasisfeeexemptrights) {
							if(data.showfeeexemptcol) {
								if(data.isfeeexempt){
									renderData += '<a href="javascript:unMarkAsFeeExempt('+data.enrollmentid+',\'SWOD\');" class="btn btn-xs btn-outline-dark px-1 m-1" title="Unmark the registrant as fee exempt"><i class="fa-solid fa-check-circle"></i></a>';
								}else{
									renderData += '<a href="javascript:markAsFeeExempt('+data.enrollmentid+',\'SWOD\');" class="btn btn-xs btn-outline-dark text-muted p-1 m-1" title="Mark the registrant as fee exempt"><i class="fa-solid fa-check-circle"></i></a>';
								}							
							} else {
								renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 text-muted disabled"><i class="fa-solid fa-check-circle"></i></a>';
							}
						}

						if(data.candelete) {
							renderData += '<a href="javascript:removeSWODRegistrant('+data.enrollmentid+');" class="btn btn-xs btn-outline-danger p-1 m-1" title="Cancel Registration"><i class="fa-solid fa-circle-minus"></i></a>';
						} else {
							renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 text-muted disabled"><i class="fa-solid fa-circle-minus"></i></a>';
						}
					}
					return type === 'display' ? renderData : data;
				},
				"className": "text-center align-top",
				"width": "25%",
				"orderable": false
			}
		],
		"order": [[1, 'desc']],
		"searching": false
	});
}
function checkMediaFiles(arrReq=[],layoutID) {
	var mediaResult = function(objResult){
		if(objResult.success && objResult.success.toLowerCase() == 'true') {
			if(!objResult.alltitleshavemediafiles && objResult.titleswithnomediafiles.length) {
				arrReq[arrReq.length] = 'You cannot activate this program because there are Titles with no uploaded media files. <a href="#" onclick="expandProgramTabSection(\'program-titles\');">Go to Titles</a>.';
				$('#isPublished').val(0);
			} else if (objResult.qrysetupissues && objResult.qrysetupissues.rowcount > 0) {
				var check7Added = false;
				objResult.qrysetupissues.data.checkid.forEach((checkID, index) => {
					if (checkID === 7 && !check7Added) {
						arrReq[arrReq.length] = "You cannot activate this program because there are Titles with media files uploaded (MP3 or MP4) that have invalid streaming formats. <a href='#' onclick=\"expandProgramTabSection('program-titles');\">Go to Titles</a>.";
						check7Added = true;
					} else if (checkID === 16) {
						arrReq[arrReq.length] = 'This program includes an MP3 file but the player mode is not set to Audio Only. Switch the player mode to Audio Only to correct the issue. <a href="#" onclick="scrollToField(\'layoutID\');">Go to Player Mode</a>.';
					} else if (checkID === 17) {
						arrReq[arrReq.length] = 'This program includes an MP4 file but the player mode is not set to Large Video. Switch the player mode to Large Video to correct the issue. <a href="#" onclick="scrollToField(\'layoutID\');">Go to Player Mode</a>.';
					} else if (checkID === 5) {
						arrReq[arrReq.length] = "You cannot activate this program because there are Titles with downloadable files that have invalid formats. <a href='#' onclick=\"expandProgramTabSection('program-titles');\">Go to Titles</a>.";
					}
				});

				$('#isPublished').val(0);
			}
		}
		else if(objResult.alltitleshavemediafiles) top.mca_hideAlert('err_swoddetails');	
	}
	var objParams = { participantID:sw_participantid, seminarID:sw_seminarid };
	if (layoutID !== undefined) {
        objParams.ovLayoutID = layoutID;
    }
	TS_AJX_SYNC('ADMINSWFILE','getFilesFromSeminarTitles',objParams,mediaResult,mediaResult,20000,mediaResult);
}
function validateAndSaveSWODSeminar(callback) {
	mca_hideAlert('err_swoddetails');	
	var arrReq = [];
	if($('#seminarName').val().trim().length == 0)
		arrReq[arrReq.length] = 'Enter the seminar name.';

	var programCode = $('#programCode').val().trim();
	if(programCode.length == 0)
		arrReq[arrReq.length] = 'Enter a program code for this seminar.';
	
	var seminarDesc = "";
	if(CKEDITOR.instances['seminarDesc'] != null)
		seminarDesc = CKEDITOR.instances['seminarDesc'].getData().trim();
	else 
		seminarDesc = $('textarea[name="seminarDesc"]').val().trim();

	if(seminarDesc.length == 0)
		arrReq[arrReq.length] = 'Enter the seminar description.';

	var layoutID = $('#layoutID').val();

	if(!layoutID || layoutID.trim().length == 0)
		arrReq[arrReq.length] = 'Select a player mode.';
	else if($('#isPublishedHidden').val() == 1 && $('#isPublished').val() == 1 && layoutID !== $('#layoutIDHidden').val()){
		let confirmed = false;
		MCModalUtils.showModal({
			verticallycentered: true,
			size: 'md',
			title: 'Confirmation Needed',
			strmodalbody: {
				content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>You cannot change the Player Mode on an Active program. Click Confirm to mark the program Inactive and continue editing your program.</span></div>'
			},
			strmodalfooter: {
				classlist: 'd-flex',
				showclose: true,
				showextrabutton: true,
				extrabuttonclass: 'btn-outline-danger ml-auto',
				extrabuttonlabel: 'OK'
			}
		});

		$('#btnMCModalSave').on('click', function(){
			confirmed = true;
			$('#isPublished').val(0);
			$('#layoutID').val($('#layoutIDHidden').val())
			layoutID = $('#layoutIDHidden').val();
			scrollToLayout = true;
			MCModalUtils.hideModal();
			continueValidationAfterModal();
		});

		$('#MCModal').on('hidden.bs.modal', function() {
			$('#btnMCModalSave').off('click');
			$('#MCModal').off('hidden.bs.modal');
			if (!confirmed) {
				$('#layoutID').val($('#layoutIDHidden').val());
				if(programAdded)
					$('#nextButton, #prevButton').prop('disabled',false);
				else
					$('#program-basics .card-footer .save-button').prop('disabled',false);
			}
		});
		return;
	}

	continueValidationAfterModal();

	function continueValidationAfterModal() {
		if($('#dateOrigPublished').val().trim().length == 0)
			arrReq[arrReq.length] = 'Enter the originally published date.';
		var seminarLength = $('#seminarLength').val();
		if(seminarLength == '' || !mca_validateInteger(seminarLength))
			arrReq[arrReq.length] = 'Enter a valid estimated time to complete this seminar';
		if($('#isPublished').val() == 1){
			checkMediaFiles(arrReq,layoutID);
			if(totalTitlesPresent == 0){
				$('#isPublished').val(0);
				arrReq[arrReq.length] = 'You cannot activate a program with no Titles. <a href="#" onclick="expandProgramTabSection(\'program-titles\');">Go to Titles</a>.';
			}
		}	
		if(arrReq.length) {
			showValidationAlertForSWODSeminar(arrReq.join('<br/>'));
		} else {
			var checkProgramCodeResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					var saveResult = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							let reloadSWODProgram = false;
							// program locked / unlocked
							if($("#lockSWODProgramSettings").is(':checked') && !sw_lockprogramsettings) {
								sw_lockprogramsettings = true;
								reloadSWODProgram = true;
							} else if (!$("#lockSWODProgramSettings").is(':checked') && sw_lockprogramsettings) {
								reloadSWODProgram = true;
							}

							if (reloadSWODProgram) self.location.href = getSWEditProgramLink();

							$('#frmSWODProgramDetails,#divSWProgramDetailsSaveLoading').toggle();
							if($('#isPublished').val() == 1) {
								removeStatement(publishedIssues);
								addStatementIfNotExists(publishedSuccessStatement,'seminarSetupStatements');
							}
							else {
								addStatementIfNotExists(publishedIssues);
								removeStatement(publishedSuccessStatement,'seminarSetupStatements');
							}
							// If no more issues, hide the entire alert
							if ($('.seminarSetupIssues ul li').length === 0) {
								$('.seminarSetupIssues').addClass('d-none');
								$('.seminarSetupStatements').removeClass('d-none');
							}
							else if($('.seminarSetupIssues').hasClass('d-none')) {
								$('.seminarSetupIssues').removeClass('d-none');
								$('.seminarSetupStatements').addClass('d-none');
							}

							if(!$("#program-basics .card-header:first #saveResponse").length)
								$("#program-basics .card-header:first .card-header--title").after('<span id="saveResponse"></span>');
							$('#program-basics .card-header:first #saveResponse').html('<span class="badge badge-success">SAVED SUCCESSFULLY</span>').addClass('text-success').show().fadeOut(5000);
							if(programAdded)
								$('#nextButton, #prevButton').prop('disabled',false);
							else
								$('#program-basics .card-footer .save-button').prop('disabled',false);
							if(scrollToLayout) {
								scrollToField('layoutID');
							}
							refreshSWODSeminarTitleHeader();
							if (callback && !scrollToLayout) {
								callback();
							}
							if(scrollToLayout) scrollToLayout = false;

							$('#isPublishedHidden').val($('#isPublished').val());
						} else {
							var arrReq = [];
							arrReq.push(r.err && r.err.length ? r.err : 'We were unable to save sw program details.');
							$('#err_swoddetails').html(arrReq.join('<br/>')).removeClass('d-none');
							$('html,body').animate({scrollTop: $('#err_swoddetails').offset().top-120},500);
							if(programAdded)
								$('#nextButton').prop('disabled',false);
							else
								$('#program-basics .card-footer .save-button').prop('disabled',false);
						}
					}
					$('#frmSWODProgramDetails,#divSWProgramDetailsSaveLoading').toggle();
					var objParams = { 
						seminarID:$('#seminarID').val(),
						seminarName:$('#seminarName').val().trim(),
						seminarSubTitle:$('#seminarSubTitle').val(),
						programCode:programCode,
						seminarDesc:seminarDesc,
						isPublished:$("#isPublished").val(),
						dateActivated:$("#dateActivated").val(),
						dateorigPublished:$("#dateOrigPublished").val(),
						layoutID:layoutID,
						seminarLength:$("#seminarLength").val(),
						lockSWODProgramSettings:$("#lockSWODProgramSettings:checked").length > 0
					};

					TS_AJX('ADMINSWOD','updateSWODProgram',objParams,saveResult,saveResult,20000,saveResult);
				}
				else showValidationAlertForSWODSeminar('Program Code must be unique.');
			};
			var objParams = { programType:'SWOD', programID:$('#seminarID').val(), programCode:programCode };
			TS_AJX('ADMINSWCOMMON','checkProgramCode',objParams,checkProgramCodeResult,checkProgramCodeResult,10000,checkProgramCodeResult);
		}
	} // End of continueValidationAfterModal function
} // End of validateAndSaveSWODSeminar function
function refreshSWODSeminarTitleHeader(){
	var seminarName = $('#seminarName').val().trim();
	var seminarSubTitle = $('#seminarSubTitle').val();
	var hasSubTitle = seminarSubTitle.length > 0;
	$('h4#dispSeminarTitle').text(seminarName).removeClass('mb-1 mb-2').addClass(hasSubTitle ? 'mb-1' : 'mb-2');
	$('h5#dispSeminarSubTitle').text(seminarSubTitle).toggleClass('d-none',!hasSubTitle);
}
function refreshSWBSeminarTitleHeader(){
	var bundleName = $('#bundleName').val().trim();
	var bundleSubTitle = $('#bundleSubTitle').val();
	var hasSubTitle = bundleSubTitle.length > 0;
	$('h4#dispBundleTitle').text(bundleName).removeClass('mb-1 mb-2').addClass(hasSubTitle ? 'mb-1' : 'mb-2');
	$('h5#dispBundleSubTitle').text(bundleSubTitle).toggleClass('d-none',!hasSubTitle);
}
function showValidationAlertForSWODSeminar(msg){
	$('#err_swoddetails').html(msg).removeClass('d-none');
	$('html,body').animate({scrollTop: $('#err_swoddetails').offset().top-120},500);
	if(programAdded)
		$('#nextButton').prop('disabled',false);
	else
		$('#program-basics .card-footer .save-button').prop('disabled',false);
}
function checkSWShowPriceOptionGroups(v) {
	$('#divPriceOptionGroups,#divRatesGridContainer,#manageRatesActionBtn,#copyRatesActionBtn').toggleClass('d-none',!v);
}
function initSWODTitles() {
	titlesListTable = $('#titlesListTable').DataTable({
		"processing": true,
		"serverSide": true,
		"paginate": false,
		"language": {
			"lengthMenu": "_MENU_"
		},
		"ajax": { 
			"url": titlesListLink,
			"type": "post",
			"data": function(d) { 
				if (window.reorderData && window.reorderData.length > 0) { 
					d.reorderData = JSON.stringify(window.reorderData); 
					window.reorderData = [];
				} 
				return d; 
			}
		},
		"autoWidth": false,
		"columns": [
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<i class="fa-light fa-bars"></i>';
						
					}
					return type === 'display' ? renderData : data;
				},
				"width": "5%",
				"orderable": false
			}, 
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<div>' + data.titleName;

						// Check if the title has media files
						renderData += '<span id="iconMediaFiles_' + data.titleID + '" class="ml-2 ' + 
							(data.hasMediaFiles && !data.setupIssues ? 'd-none' : '') + '" title="' + 
							(data.setupIssues ? 'This Title has media files (MP3 or MP4) with invalid streaming formats' 
											: 'This Title has no media files (MP3 or MP4) uploaded.') + '">';
						renderData += '<i class="fa-regular fa-exclamation-triangle text-warning fa-lg"></i>';
						renderData += '</span>';

						// Check if the title has downloadable issues
						renderData += '<span id="iconDownloadIssues_' + data.titleID + '" class="ml-2 ' + 
							(data.hasDownloadFiles ? (!data.downloadSetupIssues ? 'd-none' : '') : 'd-none') + '" title="This Title has uploaded downloadable files with invalid formats.">'
						renderData += '<i class="fa-regular fa-exclamation-triangle text-warning fa-lg"></i>';
						renderData += '</span>';

						renderData += '</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "85%",
				"className": "text-left"
			},
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display' && !data.isSeminarLocked) {
						isPublishedSeminar = $('#isPublishedHidden').val();
						renderData += '<a href="#" id="btnEditSWODTitleProgram'+data.titleID+'" class="btn btn-xs btn-outline-primary p-1 m-1" onclick="editSWODTitle('+data.titleID+',\'' + data.titleNameEncoded + '\');return false;" title="Edit Title Program"><i class="fa-solid fa-pencil"></i></a>';
						if(data.numOfTitles == 1 && parseInt(isPublishedSeminar) == 1){
							renderData += '<a href="#" id="btnDelSWODTitleProgram'+data.titleID+'" class="btn btn-xs btn-outline-danger p-1 m-1" onclick="removeLastSWODTitle('+data.titleID+');return false;" title="Delete Title"><i class="fa-solid fa-trash-can"></i></a>';
							
						}else{
							renderData += '<a href="#" id="btnDelSWODTitleProgram'+data.titleID+'" class="btn btn-xs btn-outline-danger p-1 m-1" onclick="removeSWODTitle('+data.titleID+');return false;" title="Delete Title"><i class="fa-solid fa-trash-can"></i></a>';
						}
					}
					return type === 'display' ? renderData : data;
				},
				"width": "10%",
				"className": "text-center"
			}
		],
		"ordering": false,
		"searching": false,
		"rowReorder": {
			dataSrc: "columnid" 
		},
		"drawCallback": function(table) {
			drawCallbackExecuted = typeof drawCallbackExecuted !== 'undefined' ? drawCallbackExecuted : true;
			totalTitlesPresent = table._iRecordsTotal;
			if (table._iRecordsTotal == 0 && programAdded && !drawCallbackExecuted){
				addSWODTitle();
			}
			else if (table._iRecordsTotal == 1 && programAdded && !drawCallbackExecuted) {
				// If there is only one row, trigger the click event on the button with ID containing 'btnEditSWODTitleProgram'
				var button = $('#titlesListTable').find('a[id*="btnEditSWODTitleProgram"]').first();
				if (button.length) {
					var copyLink = link_editswodtitle;
					link_editswodtitle = link_editswodtitle + '&tab=files'
					button.trigger('click');
					link_editswodtitle = copyLink;
				}
				drawCallbackExecuted = true;
			} else if(openTitleID !== 0)  
				$('#btnEditSWODTitleProgram' + openTitleID).click();
				openTitleID = 0;
		}
	});
	titlesListTable.on('row-reorder', function (e, diff, edit) {
		let orderData = [];
		diff.forEach(function(item){
			orderData.push({
				id: titlesListTable.row(item.node).data().titleID,
				newOrder: item.newPosition
			});
		});
		window.reorderData = orderData;
	});
}
function doAddSWODTitle(){
	var addResult = function(r) {
		$('#btnAddTitle').prop('disabled',false);
		$('#divAddTitleForm').addClass('d-none');
		if (r.success && r.success.toLowerCase() == 'true') {
			titlesListTable.draw();
			if (typeof window['onSaveSWProgramTitle'] != 'undefined' && typeof window['onSaveSWProgramTitle'] == 'function') 
				window['onSaveSWProgramTitle']();
			var saveResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					reloadSWEditProgram('titles');
				} 
			}
			var seminarDesc = "";
			if(CKEDITOR.instances['seminarDesc'] != null)
				seminarDesc = CKEDITOR.instances['seminarDesc'].getData().trim();
			else 
				seminarDesc = $('textarea[name="seminarDesc"]').val().trim();
			var objParams = { 
				seminarID:$('#seminarID').val(),
				seminarName:$('#seminarName').val().trim(),
				seminarSubTitle:$('#seminarSubTitle').val(),
				programCode:$('#programCode').val().trim(),
				seminarDesc:seminarDesc,
				isPublished:0,
				dateActivated:$("#dateActivated").val(),
				dateorigPublished:$("#dateOrigPublished").val(),
				layoutID:$("#layoutID").val(),
				seminarLength:$("#seminarLength").val(),
				lockSWODProgramSettings:$("#lockSWODProgramSettings:checked").length > 0,
				sendConfirmationEmail:0
			};
		
			TS_AJX('ADMINSWOD','updateSWODProgram',objParams,saveResult,saveResult,20000,saveResult);
		} else {
			alert('An error occured while adding a title to this seminar.');
		}
	};
	let titleName = $('#divAddTitleForm').data('seminarname');
	$('#divAddTitleForm').removeClass('d-none');
	$('#btnAddTitle').prop('disabled',true);
	var objParams = { seminarID:sw_seminarid, titleName:titleName };
	TS_AJX('ADMINSWTL','insertTitle',objParams,addResult,addResult,10000,addResult);
}
function addSWODTitle() {
	if (isSWProgramLocked()) return false;
	if($('#isPublished').val() == '1') {
		MCModalUtils.showModal({
			verticallycentered: true,
			size: 'md',
			title: 'Confirmation Needed',
			strmodalbody: {
				content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>An active program must include at least one media file in a title. Click \'OK\' to add a new title and set the program as inactive.</span></div>'
			},
			strmodalfooter: {
				classlist: 'd-flex',
				showclose: true,
				showextrabutton: true,
				extrabuttonclass: 'btn-outline-danger ml-auto',
				extrabuttonlabel: 'OK'
			}
		});
		$('#btnMCModalSave').on('click', function(){
			MCModalUtils.hideModal();
			doAddSWODTitle();
		});
		$('#MCModal').on('hidden.bs.modal', function() {
			$('#btnMCModalSave').off('click');
		});
	} else {
		var addResult = function(r) {
			$('#btnAddTitle').prop('disabled',false);
			$('#divAddTitleForm').addClass('d-none');
			if (r.success && r.success.toLowerCase() == 'true') {
				titlesListTable.draw();
				mca_hideAlert('err_swoddetails');	
				if (typeof window['onSaveSWProgramTitle'] != 'undefined' && typeof window['onSaveSWProgramTitle'] == 'function') 
					window['onSaveSWProgramTitle']();
			} else {
				alert('An error occured while adding a title to this seminar.');
			}
		};
	
		let titleName = $('#divAddTitleForm').data('seminarname');
		$('#divAddTitleForm').removeClass('d-none');
		$('#btnAddTitle').prop('disabled',true);
		var objParams = { seminarID:sw_seminarid, titleName:titleName };
		TS_AJX('ADMINSWTL','insertTitle',objParams,addResult,addResult,10000,addResult);
	}
}
function removeSWODTitle(tid) {
	if (isSWProgramLocked()) return false;
	isPublishedSeminar = $('#isPublishedHidden').val();

	if(totalTitlesPresent == 1 && parseInt(isPublishedSeminar) == 1){
		removeLastSWODTitle(tid);
	}else{
		var removeResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				titlesListTable.draw();
				if (typeof window['onSaveSWProgramTitle'] != 'undefined' && typeof window['onSaveSWProgramTitle'] == 'function') 
					window['onSaveSWProgramTitle']();
			} else {
				alert('An error occured while deleting this title.');
				delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
			}
		};
	
		let delBtn = $('#btnDelSWODTitleProgram'+tid);
		mca_initConfirmButton(delBtn, function() {
			var objParams = { titleID:tid };
			TS_AJX('ADMINSWTL','deleteTitle',objParams,removeResult,removeResult,10000,removeResult);
		});
	}
}
function doRemoveLastSWODTitle(tid) {
	var removeTitleResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			localStorage.setItem("gotoTitle", 1);
			top.location.reload();
		} else {
			alert('An error occured while deleting this title.');
			delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
		}
	};
	var objParams = { titleID:tid };
	TS_AJX('ADMINSWTL','deleteTitle',objParams,removeTitleResult,removeTitleResult,10000,removeTitleResult);
}
function removeLastSWODTitle(tid) {
	if (isSWProgramLocked()) return false;
	MCModalUtils.showModal({
		verticallycentered: true,
		size: 'md',
		title: 'Confirmation Needed',
		strmodalbody: {
			content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>You cannot have an Active program with no Titles. Click OK to delete the last Title and change the programs status to Inactive.</span></div>'
		},
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-outline-danger ml-auto',
			extrabuttonlabel: 'OK'
		}
	});
	$('#btnMCModalSave').on('click', function(){
		MCModalUtils.hideModal();
		doRemoveLastSWODTitle(tid);
	});
	$('#MCModal').on('hidden.bs.modal', function() {
		$('#btnMCModalSave').off('click');
	});
}
function loadSWODOptInsTab() {
	var result = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') { populateSWODOptInAndOuts(r); }
		else { alert('Some error occurred while loading opt-ins tab. Please try again.'); }
	};
	var objParams = { seminarID:swod_seminarid };
	TS_AJX('ADMINSWOD','getSWODProgramOptInAndOuts',objParams,result,result,10000,result);
}
function populateSWODOptInAndOuts(r) {
	var newOpts = '';

	if (r.arroptins.length) {
		newOpts = '';
		for (var i=0; i<r.arroptins.length; i++) {
			newOpts += '<option value="' + r.arroptins[i]["orgcode"] + '">' + r.arroptins[i]["description"] + '</option>';
		}
		$('#swodOptIns').find('option').remove().end().append(newOpts);
	} else {
		$('#swodOptIns').find('option').remove();
	}

	if (r.arroptouts.length) {
		newOpts = '';
		for (var i=0; i<r.arroptouts.length; i++) {
			newOpts += '<option value="' + r.arroptouts[i]["orgcode"] + '">' + r.arroptouts[i]["description"] + '</option>';
		}
		$('#swodOptOuts').find('option').remove().end().append(newOpts);
	} else {
		$('#swodOptOuts').find('option').remove();
	}

	$('#frmSWODOptIns select').prop('disabled',false);
}
function doAddSWODNatOptInProgram() {
	if (isSWProgramLocked()) return false;

	var result = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') { loadSWODOptInsTab(); }
		else { alert('Some error occurred while opt-in national program. Please try again.'); }
	};

	mca_hideAlert('err_optIns');
	var programID = Number($('#natProgramID').val());
	if (programID == 0) {
		mca_showAlert('err_optIns', 'Select a program');
		return false;
	}

	var objParams = { seminarID:getSWProgramID(), programType:sw_itemtype, natProgramID:programID };
	TS_AJX('ADMINSWCOMMON','optInSWNationalProgram',objParams,result,result,10000,result);
}
function saveSWODOptIn() {
	if (isSWProgramLocked()) return false;

	var saveOptInResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') { loadSWODOptInsTab(); }
		else { alert('Unable to save Opt-In change.'); }
	};

	mca_hideAlert('err_optIns');
	var orgCodeList = $('#swodOptOuts').val() || '';
	if (orgCodeList == '') {
		mca_showAlert('err_optIns', 'Select an association');
		return false;
	}

	orgCodeList = orgCodeList.join(',');

	var objParams = { orgCodeList:orgCodeList, seminarID:swod_seminarid, programType:sw_itemtype };
	TS_AJX('ADMINSWCOMMON','optInSWSeminar',objParams,saveOptInResult,saveOptInResult,10000,saveOptInResult);
}
function saveSWODOptOut() {
	if (isSWProgramLocked()) return false;

	var saveOptOutResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') { loadSWODOptInsTab(); }
		else { alert('Unable to save Opt-Out change.'); }
	};

	mca_hideAlert('err_optIns');
	var orgCodeList = $('#swodOptIns').val() || '';
	if (orgCodeList == '') {
		mca_showAlert('err_optIns', 'Select an association');
		return false;
	}

	orgCodeList = orgCodeList.join(',');
	
	var objParams = { orgCodeList:orgCodeList, seminarID:swod_seminarid, programType:sw_itemtype };
	TS_AJX('ADMINSWCOMMON','optOutSWSeminar',objParams,saveOptOutResult,saveOptOutResult,10000,saveOptOutResult);
}
function removeSWODRegistrant(eid,gnum) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Cancel Registration',
		iframe: true,
		contenturl: link_removeenrollment + '&eid=' + eid + '&gnum=' + gnum + '&ft=SWOD',
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			closebuttonlabel: 'Cancel',
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.doCallRemoveReg',
			extrabuttonlabel: 'Remove Registrant',
		}
	});
}
function showBulkDeactivatePrograms() {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: '<div>Deactivate Programs</div><div id="deactivatePrgSubTitle" class="text-grey font-size-md mt-2"><div>Shown below are active programs in your catalog that have catalog sale and credit offered dates that are expired.</div><div class="mt-1">Deactivation will prevent program access to current registrants.</div></div>',
		iframe: true,
		contenturl: link_deactivateSWODPrograms
	});
	$('#MCModalHeader').css({ 'display':'flex', 'height':'90px', 'padding-bottom':'0.25rem' });
	$('#MCModalLabel').css({ 'line-height':1 });
}
function doRemoveSWODRegistrant(obj) {
	var removeSWODRegistrantResult = function(r) {
		top.MCModalUtils.hideModal();
		if (r.success && r.success.toLowerCase() == 'true') {
			SWODRegistrantsListTable.draw(false);  
		}
		else { alert(r.errmsg && r.errmsg.length ? r.errmsg : 'Unable to remove registrant.'); }
	};

	var objParams = { enrollmentID:obj.enrollmentID, AROption:obj.AROption, emailRegistrant:obj.emailRegistrant };
	TS_AJX('ADMINSWOD','removeEnrollment',objParams,removeSWODRegistrantResult,removeSWODRegistrantResult,20000,removeSWODRegistrantResult);
}
function viewSWODFeeStructure(title){
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		title: title,
		strmodalbody: { content: $('#mc_SWODFeeStructureHTML').html() }
	});
}
function inactivateSWODProgram() {
	return new Promise((resolve,reject) => {
		let saveResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				top.$('#isPublished').val(0);
				top.$('#isPublishedHidden').val($('#isPublished').val());
				top.$('.seminarSetupIssues ul').append('<li>' + top.publishedIssues + '</li>');
				top.$('.seminarSetupStatements ul li').filter(function() {
					return $(this).text().includes(top.publishedSuccessStatement);
				}).remove();
				
				// If no more issues, hide the entire alert
				if (top.$('.seminarSetupIssues ul li').length === 0) {
					top.$('.seminarSetupIssues').addClass('d-none');
					top.$('.seminarSetupStatements').removeClass('d-none');
				}
				else if(top.$('.seminarSetupIssues').hasClass('d-none')) {
					top.$('.seminarSetupIssues').removeClass('d-none');
					top.$('.seminarSetupStatements').addClass('d-none');
				}
				resolve({success:true});
			} else {
				reject({success:false, errmsg:'We were unable to inactivate this program.'});
			}
		}

		let objParams = { seminarID: top.$('#seminarID').val(), seminarName: top.$('#seminarName').val().trim(), seminarSubTitle: top.$('#seminarSubTitle').val(),
						programCode: top.$('#programCode').val().trim(), seminarDesc: window.parent.seminarDescription, isPublished: 0,
						dateActivated: top.$("#dateActivated").val(), dateorigPublished: top.$("#dateOrigPublished").val(), layoutID: top.$("#layoutID").val(),
						seminarLength: top.$("#seminarLength").val(), lockSWODProgramSettings: top.$("#lockSWODProgramSettings:checked").length > 0, sendConfirmationEmail: 0 };
		TS_AJX('ADMINSWOD', 'updateSWODProgram', objParams, saveResult, saveResult, 20000, saveResult);
	});
}
/* SWOD Submissions */
function showSubmitSWODProgramForm() {
	$('div#SWODTabsContainer').addClass('d-none');
	var loadingHTML = '<h5>Submit a Program</h5>' + mca_getLoadingHTML();
	$('#divSubmitSWODProgramForm').html(loadingHTML).removeClass('d-none').load(link_submitswodprogram, function (responseText, textStatus) {
		if (textStatus == "error") {
			$('#divSubmitSWODProgramForm').html('<div class="alert alert-danger">Some error occured while trying to load the form.</div>');
		}
	});
}
function cancelSubmitSWODProgramForm(id) {
	if(id > 0){
		$('#divSWODSubmissionForm').html('').addClass('d-none');
		$('div#divSWODSubmissionsListing').removeClass('d-none');
	}
	else {
		$('#divSubmitSWODProgramForm').html('').addClass('d-none');
		$('div#SWODTabsContainer').removeClass('d-none');
	}
	return false;
}
function initializeSWODSubmissionsTable(){				
	swodSubmissionsTable = $('#swodSubmissionsTable').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 10,
		"dom": "<'row'<'col-sm-3 col-md-3'l><'col-sm-9 col-md-9'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
		"lengthMenu": [ 10, 25, 50 ],
		"ajax": { 
			"url": link_listswodsubmissions,
			"type": "post",
			"data": function(d) {
				$.each($('#frmFilter').serializeArray(),function() {
					d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
				});
			}
		},
		"autoWidth": false,
		"columns": [
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<div class="d-flex"><span>'+data.seminarName+'</span>'
							+(data.status == "R" ? '<span class="ml-auto"><span class="badge badge-info">Converted</span><a href="'+mca_getSiteToolLink(data.publisherOrgCode,'SeminarWebAdmin','listSWOD','editSWODProgram','pid='+data.convertedSeminarID)+'" target="_swodPrg'+data.convertedSeminarID+'" title="Go to Seminar" class="btn btn-xs p-0 mx-1"><i class="fa-solid fa-external-link-alt"></i></a></span>' : '')
							+'</div>';
						if(data.seminarSubTitle.length) renderData += '<div class="text-dim small">'+data.seminarSubTitle+'</div>';
						if(data.dateCatalogStart.length) renderData += '<div class="text-dim small">'+data.dateCatalogStart+' - '+data.dateCatalogEnd+'</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"className": "align-top",
				"width": "45%"
			},
			{ "data": "publisherOrgCode", "width": "10%", "className": "align-top"},
			{ "data": "dateSubmitted", "width": "12%", "className": "align-top"},
			{ "data": "submittedBy", "width": "18%", "className": "align-top"},
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<a href="javascript:viewSWODSubmission('+data.submissionID+');" title="'+ (data.status == 'R' ? 'View' : 'Review') +'" class="btn btn-xs btn-outline-primary p-1 m-1"><i class="fa-solid '+ (data.status == 'R' ? 'fa-eye' : 'fa-pencil') +'"></i></a>';
						renderData += '<a href="javascript:deleteSWODSubmission('+data.submissionID+','+data.participantID+',\''+data.publisherOrgCode+'\');" id="btnDelSubmission'+data.submissionID+'" class="btn btn-xs btn-outline-danger p-1 m-1" title="Delete Submission" data-confirm="0"><i class="fa-solid fa-trash-can"></i></a>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "15%",
				"className": "align-top text-center",
				"orderable": false
			}
		],
		"order": [[0, 'asc']],
		"searching": false,
		"createdRow": function( row, data, index ) {
			if (data.status.toLowerCase() == 'r') $(row).addClass('table-success');
		}
	});
}
function filterSWODSubmissions() {
	if ($('#divFilterForm').hasClass('d-none')) {
		$('#divFilterForm').removeClass('d-none');
	}
}
function clearSWODSubmissionFilters() {
	$('#frmFilter')[0].reset();
	doFilterSWODSubmissions();
}
function doFilterSWODSubmissions() {	
	swodSubmissionsTable.draw();
}
function deleteSWODSubmission(id,pid,orgcode) {
	var removeResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			swodSubmissionsTable.draw();
		} else {
			delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
			alert('We were unable to remove this submission.');
		}
	};

	let delBtn = $('#btnDelSubmission'+id);
	mca_initConfirmButton(delBtn, function(){
		var objParams = { submissionID:id, participantID:pid, participantOrgCode:orgcode };
		TS_AJX('ADMINSWOD','removeProgramSubmission',objParams,removeResult,removeResult,20000,removeResult);
	});
}
function viewSWODSubmission(id) {
	$('div#divSWODSubmissionsListing').addClass('d-none');
	var loadingHTML = '<h5>Review Program Submission</h5>' + mca_getLoadingHTML();
	$('#divSWODSubmissionForm').html(loadingHTML).removeClass('d-none').load(link_viewswodsubmission + '&submissionID=' + id, function (responseText, textStatus) {
		if (textStatus == "error") {
			$('#divSWODSubmissionForm').html('<div class="alert alert-danger">Some error occured while trying to load the form.</div>');
		}
	});
}
function filterRegistrantsList() {
	if ($('#divFilterListRegistrantsForm').hasClass('d-none')) {
		$('#divFilterListRegistrantsForm').removeClass('d-none');
	}
}
function clearRegListFilters() {
	$('#frmRegistrantsListFilter')[0].reset();
	dofilterRegistrantsList();
}
function dofilterRegistrantsList() {	
	$('#RegistrantListGriddates').html($('#frrDateFrom').val() + ' to ' + $('#frrDateTo').val());
	registrantsListTable.draw();
}
function exportRegistrantsList() {
	if ($('#divExportRegistrantsListForm').hasClass('d-none')) {
		$('#divFilterListRegistrantsForm').addClass('d-none');
		$('#divExportRegistrantsListFormArea').html('<h5>Download Registrants</h5>' + mca_getLoadingHTML());
		$('#divExportRegistrantsListForm').removeClass('d-none');
		$('#divExportRegistrantsListFormArea').load(link_exportregistrantslist + '&' + $('#frmRegistrantsListFilter').serialize());
	}
}
function doExportRegistrantsList(u) {
	self.location.href = '/tsdd/' + u;
	$('#divFilterListRegistrantsForm, #divFilterListRegistrantsForm, #divExportRegistrantsListForm').addClass('d-none');
}
function quickFilterRegRegistrantsList(d) {
	var now = new Date();
	var thisDate = new Date();
	var todayDate = new Date(thisDate.getFullYear(), thisDate.getMonth(), thisDate.getDate());

	var n = d * 1;
	if(n >= 0){
		$('#frrDateFrom').val(moment(todayDate).format('M/D/YYYY'));
		todayDate.setDate(now.getDate()+n);
		$('#frrDateTo').val(moment(todayDate).format('M/D/YYYY')); 
	}else{
		n = d * -1;
		$('#frrDateTo').val(moment(todayDate).format('M/D/YYYY'));
		todayDate.setDate(now.getDate()-n);
		$('#frrDateFrom').val(moment(todayDate).format('M/D/YYYY'));
	} 
	dofilterRegistrantsList();
}

function initializeRegistrantsListTable() {
	let domString = "<'row'<'col-sm-3 col-md-3'l><'col-sm-9 col-md-9'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";

	registrantsListTable = $('#registrantsListTable').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 25,
		"lengthMenu": [ 10, 25, 50, 100 ],
		"dom": domString,
		"language": {
			"lengthMenu": "_MENU_",
			"emptyTable": "No Registrations found"
		},
		"ajax": { 
			"url": link_registrantslist,
			"type": "post",
			"data": function(d) {
				
				$.each($('#frmRegistrantsListFilter').serializeArray(),function() {	
					d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
				});
			}
		},
		"autoWidth": false,
		"columns": [
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						renderData += data.dateenrolled;
						renderData += '<div class="text-dim small">'+data.signuporgcode+'</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"className": "align-top",
				"width": "10%"
			},
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						if (!data.isactive) renderData += '<div><span class="badge badge-danger">Deleted</div>';
						if(data.memberid && $.trim(data.sitehostname).length)
							renderData += '<a href="https://'+data.sitehostname+'/?pg=admin&jumpToTool=MemberAdmin%7Csearch%7Cedit&memberID='+data.memberid+'" target="_blank">'+data.lastname+', '+data.firstname+' ('+data.membernumber+')</a>';
						else
							renderData += data.lastname+', '+data.firstname+' ('+data.membernumber+')';
						if (data.company.length)				
							renderData += '<div class="text-dim small">'+data.company+'</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"className": "align-top",
				"width": "35%"
			},
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						if (data.isnatle)
							renderData += '<span class="float-right pl-3"><img src="/assets/common/images/seminarWeb/natle.png" width="37" height="12"></span>';
						renderData += data.programformat+'-'+data.seminarid+': '+data.seminarname;
						renderData += '<div class="text-dim small">'+data.publisherorgcode+'</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"className": "align-top",
				"width": "55%"
			}
		],
		"order": [[0, 'desc']],
		"searching": false
	});
}
/* SWOD Title functions */
function editSWODTitle(pid,programTitle) {
	if (isSWProgramLocked()) return false;
	if(CKEDITOR.instances['seminarDesc'] != null)
		window.seminarDescription = CKEDITOR.instances['seminarDesc'].getData().trim();
	else 
		window.seminarDescription = $('textarea[name="seminarDesc"]').val().trim();

	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: truncateFilename(programTitle,80),
		iframe: true,
		contenturl: link_editswodtitle + '&pid=' + pid
	});
}
function closeModal(){
	MCModalUtils.hideModal();
}
function viewSWCPProgress(pid,did) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Certificate Program Progress',
		iframe: true,
		contenturl: link_viewswcpprogress + '&pid=' + pid + '&did=' + did
	});
}
function viewSWCPProgressDetail(pid,eid,cpid,did) {
	
	MCModalUtils.hideModal();
	$('.modal-backdrop').remove();
	MCModalUtils.showModal({
		isslideout: true,
		size: 'xl',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Certificate Program Progress Details',
		iframe: true,
		contenturl: link_viewswcpdetailprogress + '&pid=' + pid + '&eid=' + eid + '&cpid=' + cpid + '&did=' + did + '&from=SWCP'
	});
}
function validateAndSaveSWTLProgram(callback) {
	saveErrMsg = '';
	mca_hideAlert('err_swtldetails');
	top.$('#btnMCModalSave').prop('disabled',true);
	var arrReq = [];
	if($('#titleName').val().trim().length == 0) arrReq[arrReq.length] = 'Enter the title name.'; 
	
	if(arrReq.length){
		$('#err_swtldetails').html(arrReq.join('<br/>')).removeClass('d-none');
		top.$('#btnMCModalSave').prop('disabled',false);
	}
	else {
		var fd = $('#frmSWTLProgramDetails').serializeArray();
		$("#divSWTLProgramDetailsFormSubmitArea").load(link_updateswtlprogramdetails, fd, function() { onSWTLProgramSaveComplete(callback); });
	}

	return false;
}
function onSWTLProgramSaveComplete(callback) {
	if(saveErrMsg == ''){
		$("#divSWTLProgramDetailsFormSubmitArea").html('');
		if(typeof top.titlesListTable != "undefined") top.titlesListTable.draw();
		else if(typeof top.titlesTable != "undefined") top.titlesTable.draw();
		if (top.$('#MCModalFooter #saveSWTLInfoMsg').length === 0) 
			top.$('#MCModalFooter #btnMCModalSave').before('<span id="saveSWTLInfoMsg" style="padding-left:50rem;"></span>');
		top.$('#MCModalFooter').find('#saveSWTLInfoMsg').html('<span class="badge badge-success">SAVED SUCCESSFULLY</span>').addClass('text-success').show().fadeOut(10000);
		top.$('#MCModalHeader').find('#MCModalLabel').text($('#titleName').val());
		if(callback)
			callback();
	}
	else {
		mca_showAlert('err_swtldetails', saveErrMsg);
		saveErrMsg='';
	}
	top.$('#btnMCModalSave').prop('disabled',false);
}
function editSWTLFile(fid) {
	if (isSWProgramLocked()) return false;
	top.$("#btnMCModalSave").addClass('d-none');

	var loadHTML = '<h5>'+ (Number(fid) == 0 ? 'Add File' : 'Edit File') +'</h5><div><img src="/assets/common/images/progress.gif" width="16" height="16" alt="Please wait..."> Please Wait...</div><br/><br/>';
	var editFileLink = link_editswtlfile + '&fid=' + fid;
	$('#divTitleFilesContainer').addClass('d-none');
	$('#divFileDefinitionFormArea').html(loadHTML).load(editFileLink, function() {});
	$('#divFileDefinitionContainer').removeClass('d-none');
}
function cancelFileDefinitionForm() {
	$('#divFileDefinitionFormArea').html('');
	$('#divFileDefinitionContainer').addClass('d-none');
	$('#divTitleFilesContainer').removeClass('d-none');
	return false;
}
function deleteSWTLFile(fid) {
	if (isSWProgramLocked()) return false;
	let fileRemoveElement = $('#tfileRemove_'+fid);
	if(fileRemoveElement.attr('data-filemode') == 'stream' && steamingFileCount == 1 && top.$('#isPublished').val() == '1') {
		let msg = 'You cannot have an \'Active\' program with no media files uploaded to a Title. Click OK to delete the last media file and change the program\'s status to \'Inactive\'';
		let content = '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>' + msg + '</span></div>';
		
		MCModalUtils.showConfirmationPopup('Confirmation Needed', content,
			{ classList: 'btn-sm btn-secondary', label:'Cancel', btnID: 'btnMCModalCancelBtn' },
			{ classList: 'btn-sm btn-danger', label:'Confirm', btnID: 'btnMCModalConfirmBtn', clickFnName: 'doDeleteFile', clickFnArgs: {fid:fid}, isClickFnPromise:1 },
			'md'
		);
	} else {
		mca_initConfirmButton(fileRemoveElement, function(){
			fileRemoveElement.attr('data-confirm', 0).removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
			var deleteFileResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				getSWTLFileDetails();
			} else { 
				alert('We were unable to delete this file.');
			}
		};
		let objParams = { titleID:sw_titleid, fileID:fid };
		TS_AJX('ADMINSWFILE','deleteTitleFile',objParams,deleteFileResult,deleteFileResult,200000,deleteFileResult);
		});
	}
}
function doDeleteFile(fid) {
	return new Promise(function(resolve, reject) {
		var deleteLastStreamFileResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				var saveResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true'){
						openTitleID = sw_titleid;
						top.self.location.href = getSWEditProgramLink() + '&tab=titles&openTitleID='+openTitleID;
					} 
				}
				var objParams = { 
					seminarID:top.$('#seminarID').val(),
					seminarName:top.$('#seminarName').val().trim(),
					seminarSubTitle:top.$('#seminarSubTitle').val(),
					programCode:top.$('#programCode').val().trim(),
					seminarDesc:window.parent.seminarDescription,
					isPublished:0,
					dateActivated:top.$("#dateActivated").val(),
					dateorigPublished:top.$("#dateOrigPublished").val(),
					layoutID:top.$("#layoutID").val(),
					seminarLength:top.$("#seminarLength").val(),
					lockSWODProgramSettings:top.$("#lockSWODProgramSettings:checked").length > 0,
					sendConfirmationEmail:0
				};
			
				TS_AJX('ADMINSWOD','updateSWODProgram',objParams,saveResult,saveResult,20000,saveResult);
			} else { 
				top.$('#btnMCModalConfirmBtn').html('Confirm').prop('disabled',false);
				alert('We were unable to delete this file.');
			}
		};
		top.$('#btnMCModalConfirmBtn').html('Please wait...').prop('disabled',true);
		let objParams = { titleID:sw_titleid, fileID:fid };
		TS_AJX('ADMINSWFILE','deleteTitleFile',objParams,deleteLastStreamFileResult,deleteLastStreamFileResult,200000,deleteLastStreamFileResult);
	});
}
function deleteAllFiles() {
	let msg = top.$('#isPublished').val() == '1'
				? 'You cannot delete all files for an Active program. Click OK to proceed with deleting all files and changing the program\'s status to "Inactive".'
				: 'Are you sure you want to delete all files for this Title? This cannot be undone. Proceed carefully if registrants have started or completed the seminar.';
	let content = '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>' + msg + '</span></div>';
	
	MCModalUtils.showConfirmationPopup('Confirmation Needed', content,
		{ classList: 'btn-sm btn-secondary', label:'Cancel', btnID: 'btnMCModalCancelBtn' },
		{ classList: 'btn-sm btn-danger', label:'Yes, Delete All Files', btnID: 'btnMCModalConfirmBtn', clickFnName: 'doDeleteAllFiles', isClickFnPromise:1 },
		'md'
	);
}
function doDeleteAllFiles() {
	return new Promise(function(resolve, reject) {
		let deleteAllFilesResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				if(top.$('#isPublished').val() == '1') {
					var saveResult = function(r) {
						if (r.success && r.success.toLowerCase() == 'true'){
							openTitleID = sw_titleid;
							top.self.location.href = getSWEditProgramLink() + '&tab=titles&openTitleID='+openTitleID;
						} else {
							top.MCModalUtils.hideModal();
						}
					}
					var objParams = { 
						seminarID:top.$('#seminarID').val(),
						seminarName:top.$('#seminarName').val().trim(),
						seminarSubTitle:top.$('#seminarSubTitle').val(),
						programCode:top.$('#programCode').val().trim(),
						seminarDesc:window.parent.seminarDescription,
						isPublished:0,
						dateActivated:top.$("#dateActivated").val(),
						dateorigPublished:top.$("#dateOrigPublished").val(),
						layoutID:top.$("#layoutID").val(),
						seminarLength:top.$("#seminarLength").val(),
						lockSWODProgramSettings:top.$("#lockSWODProgramSettings:checked").length > 0,
						sendConfirmationEmail:0
					};
				
					TS_AJX('ADMINSWOD','updateSWODProgram',objParams,saveResult,saveResult,20000,saveResult);
				} else getSWTLFileDetails();
				
				resolve();
			} else {
				top.$('#btnMCModalConfirmBtn').html('Yes, Delete All Files').prop('disabled',false);
				alert('We were unable to remove these files.');
				reject();
			}
		};

		top.$('#btnMCModalConfirmBtn').html('Please wait...').prop('disabled',true);

		let objParams = { titleID:sw_titleid };
		TS_AJX('ADMINSWFILE','deleteAllFiles',objParams,deleteAllFilesResult,deleteAllFilesResult,10000,deleteAllFilesResult);
	});
}

function moveSWTLFile(titleid,orderData) {
	if (isSWProgramLocked()) return false;

	var moveItemUp = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			getSWTLFileDetails(); 
		} else {
			alert('We were unable to move this title file. Try again.');
		}
	};
	var objParams = { titleID:titleid, orderData : orderData};
	TS_AJX('ADMINSWFILE','moveTitleFile',objParams,moveItemUp,moveItemUp,10000,moveItemUp);
}
function hideFromLearnTab(chk,cbox) {
	if (chk) $('#'+cbox).prop('checked',true);
}
function refreshSWTLFile(fid,pid) {
	if (isSWProgramLocked()) return false;

	var refreshFileResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			getSWTLFileDetails(); 
		} else {
			alert('We were unable to refresh files. Try again.');
		}
		$('#divTitleFilesContainer').removeClass('d-none');
		$('#divRefreshFormatsLoading').html('').addClass('d-none');
	};

	var msg = mca_getLoadingHTML('Please wait...<div class="font-weight-normal mt-2">We\'re updating the list of file formats.</div>');
	$('#divTitleFilesContainer').addClass('d-none');
	$('#divRefreshFormatsLoading').html(msg).removeClass('d-none');

	var pathToFile = sw_sitecode.toLowerCase() + '.' + pid;

	var objParams = { titleID:sw_titleid, fileID:fid, pathToFile:pathToFile };
	TS_AJX('ADMINSWFILE','refreshFiles',objParams,refreshFileResult,refreshFileResult,20000,refreshFileResult);
}
function saveSWTLFilesInfo(reload=1) {
	if (isSWProgramLocked()) return false;
	top.$("#btnMCModalSave").prop('disabled',true);	
	var fd = $('#frmSWTLFileInfo').serializeArray();
	$("#divTitleFilesListLoading").load(link_saveswtlfilesinfo,fd, function() {
		top.$('#err_titles').addClass('d-none');
		checkMediaFiles();
		if (typeof top.window['autoScrollToSection'] === 'function') 
			top.autoScrollToSection(top.$('#program-titles'));
		if(reload === 1) 
			getSWTLFileDetails();
		else {
			top.$("#btnMCModalSave").prop('disabled',false);
			if (top.$('#MCModalFooter #saveSWTLInfoMsg').length === 0) 	
				top.$('#MCModalFooter #btnMCModalSave').before('<span id="saveSWTLInfoMsg" style="padding-left:50rem;"></span>');
				top.$('#MCModalFooter').find('#saveSWTLInfoMsg').html('<span class="badge badge-success">SAVED SUCCESSFULLY</span>').addClass('text-success').show().fadeOut(10000);
			top.$('#MCModalHeader').find('#MCModalLabel').text($('#titleName').val());
		}
	});
	return false;
}
function truncateFilename(filename, maxLength) {
	if (filename.toString().length <= maxLength) return filename;
	var truncatedBaseName = filename.substring(0, maxLength) + '...';
	return truncatedBaseName;
}
function getSWTLFileDetails() {
	if ($.fn.DataTable.isDataTable('#TitleStreamingFilesListTable') || $.fn.DataTable.isDataTable('#TitleDownloadableFilesListTable') || $.fn.DataTable.isDataTable('#TitlePaperFilesListTable')) {
		if($.fn.DataTable.isDataTable('#TitleStreamingFilesListTable'))
			TitleStreamingFilesListTable.draw();
		if ($.fn.DataTable.isDataTable('#TitleDownloadableFilesListTable')) 
			TitleDownloadableFilesListTable.draw();
		if ($.fn.DataTable.isDataTable('#TitlePaperFilesListTable')) 
			TitlePaperFilesListTable.draw();
	}
	else {
		var ajaxUrl = link_getTitleFilesInfoLink + '&titleID=' + sw_titleid;

		var streamingColumns = [
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<i class="fa-light fa-bars"></i>';

					}
					return type === 'display' ? renderData : data;
				},
				"width": "5%",
				"orderable": false
			}, 
			{ "data": null,
				"render": function (data, type, row, meta) {
					let renderData = '';
					if (type === 'display') {
						var showInvalidWarning  = (data.fileType === 'video' && data.streamingExt !== 'mp4' && (!data.duration.length || data.duration == 'N/A')) || (data.fileType === 'audio' && data.streamingExt !== 'mp3');
						renderData += '<div class="form-label-group mb-0' + (showInvalidWarning ? ' d-flex align-items-center' : '') + '">';
						renderData += '<input type="text" data-fileID="'+data.fileID+'" id="fieldLabel_'+data.fileID+'" name="fieldLabel_'+data.fileID+'" class="form-control inputFieldLabel" onblur="saveSWTLFilesInfo(0);" value="'+data.fileTitle+'">';
						if (showInvalidWarning) {
							renderData += '<i class="fa-regular fa-exclamation-triangle text-warning fa-lg ml-2" title="This media file has an invalid streaming format. Delete the file and try uploading it again by clicking \'Add More Files\'"></i>';
						}
						renderData += '<label for="fieldLabel_'+data.fileID+'">' + data.fileName + (data.streamingExt==''?'':'.'+data.streamingExt) + '</label>'
						renderData += '</div>';
					}										
					return type === 'display' ? renderData : data;
				},
				"width": "70%",
				"orderable": false 
			},
			{ "data": "duration","width": "10%", "className": "text-right", "orderable": false },
			{ "data": null,
				"render": function (data, type, row, meta) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<a href="##" id="swtlReplaceFile'+data.fileID+'" class="btn btn-sm text-primary px-1 m-1" title="Replace Media File" onclick="replaceSWTLFile('+data.fileID+',\''+data.filemode+'\');return false;" data-titleid="'+data.titleID+'"><i class="fa-solid fa-arrow-right-arrow-left"></i></a>';
						renderData += '<a href="##" data-fileid="'+data.fileID+'" data-filemode="'+data.filemode+'" data-ext="'+data.streamingExt+'" data-filetitle="'+data.fileTitle+'" ' + 
						(data.filemode === "paper" ? 'data-paperpgdesc="'+data.pgDesc+'"' : '') + 
						' class="btn btn-sm text-primary px-1 m-1 ' + 
						(data.streamingExt ? '' : 'invisible') + 
						'" title="View File Details" ' + 
						(data.streamingExt ? 'onclick="top.downloadSWTLFile(this,'+data.titleID+');return false;"' : '') + 
						'><i class="fa-solid fa-eye"></i></a>';
						renderData += '<a href="##" id="tfileRemove_'+data.fileID+'" data-filemode="'+data.filemode+'" class="btn btn-sm text-danger px-1 m-1" title="Delete File" onclick="deleteSWTLFile('+data.fileID+');return false;" data-confirm="0"><i class="fa-solid fa-trash-can"></i></a>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "20%",
				"className": "text-center",
				"orderable": false 
			}
		];

		var downloadableColumns = [
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<i class="fa-light fa-bars"></i>';

					}
					return type === 'display' ? renderData : data;
				},
				"width": "5%",
				"orderable": false
			}, 
			{ "data": null,
				"render": function (data, type, row, meta) {
					let renderData = '';
					if (type === 'display') {
						var showInvalidWarning  = ((data.fileType === 'paper' && (!data.filemode.length || data.filemode !== 'download' || !data.downloadableExt.length)) || (data.fileType === 'paper' && data.filemode === 'download' && !['pdf','doc','docx','ppt','pptx','xls','xlsx'].includes(data.downloadableExt)));
						renderData += '<div class="form-label-group mb-0' + (showInvalidWarning ? ' d-flex align-items-center' : '') + '">';
						renderData += '<input type="text" data-fileID="'+data.fileID+'" id="fieldLabel_'+data.fileID+'" name="fieldLabel_'+data.fileID+'" class="form-control inputFieldLabel" onblur="saveSWTLFilesInfo(0);" value="'+data.fileTitle+'">';
						if (showInvalidWarning) {
							renderData += '<i class="fa-regular fa-exclamation-triangle text-warning fa-lg ml-2" title="This downloadable file has an invalid format. Delete the file and try your upload again."></i>';
						}
						renderData += '<label for="fieldLabel_'+data.fileID+'">' + data.fileName + (data.downloadableExt==''?'':'.'+data.downloadableExt) + '</label>'
						renderData += '</div>';
					}										
					return type === 'display' ? renderData : data;
				},
				"width": "80%",
				"orderable": false 
			},
			{ "data": null,
				"render": function (data, type, row, meta) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<a href="##" id="swtlReplaceFile'+data.fileID+'" class="btn btn-sm text-primary px-1 m-1 " title="Replace Downloadable File" onclick="replaceSWTLFile('+data.fileID+',\''+data.filemode+'\');return false;" data-titleid="'+data.titleID+'"><i class="fa-solid fa-arrow-right-arrow-left"></i></a>';
						renderData += '<a href="##" data-fileid="'+data.fileID+'" data-filemode="'+data.filemode+'" data-ext="'+data.downloadableExt+'" data-filetitle="'+data.fileTitle+'" ' + 
						(data.filemode === "paper" ? 'data-paperpgdesc="'+data.pgDesc+'"' : '') + 
						' class="btn btn-sm text-primary px-1 m-1 ' + 
						(data.downloadableExt ? '' : 'invisible') + 
						'" title="View File Details" ' + 
						(data.downloadableExt ? 'onclick="top.downloadSWTLFile(this,'+data.titleID+');return false;"' : '') + 
						'><i class="fa-solid fa-eye"></i></a>';
						renderData += '<a href="##" id="tfileRemove_'+data.fileID+'" data-filemode="'+data.filemode+'" class="btn btn-sm text-danger px-1 m-1" title="Delete File" onclick="deleteSWTLFile('+data.fileID+');return false;" data-confirm="0"><i class="fa-solid fa-trash-can"></i></a>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "20%",
				"className": "text-center",
				"orderable": false 
			}
		];

		var paperColumns = [
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<i class="fa-light fa-bars"></i>';

					}
					return type === 'display' ? renderData : data;
				},
				"width": "5%",
				"orderable": false
			}, 
			{ "data": null,
				"render": function (data, type, row, meta) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<td><div style="display: flex; align-items: center;">';
						renderData += '<input type="text" data-fileID="'+data.fileID+'" id="fieldLabel_'+data.fileID+'" name="fieldLabel_'+data.fileID+'" class="form-control form-control-sm inputFieldLabel" onblur="saveSWTLFilesInfo(0);" value="'+data.fileTitle+'">';
						if (data.hasVideoPreview) {
							renderData += '<span class="badge badge-pill badge-primary ml-2">Preview</span>';
						}
						renderData += '</div></td>';
					}										
					return type === 'display' ? renderData : data;
				},
				"width": "85%",
				"orderable": false 
			},
			{ "data": null,
				"render": function (data, type, row, meta) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<a href="##" data-fileid="'+data.fileID+'" data-filemode="'+data.filemode+'" data-ext="'+(data.streamingExt || data.downloadableExt)+'" data-filetitle="'+data.fileTitle+'" ' + 
						(data.filemode === "paper" ? 'data-paperpgdesc="'+data.pgDesc+'"' : '') + 
						' class="btn btn-sm text-primary px-1 m-1 ' + 
						((data.streamingExt || data.downloadableExt) ? '' : 'invisible') + 
						'" title="View File Details" ' + 
						((data.streamingExt || data.downloadableExt) ? 'onclick="top.downloadSWTLFile(this,'+data.titleID+');return false;"' : '') + 
						'><i class="fa-solid fa-eye"></i></a>';
						renderData += '<a href="##" id="tfileRemove_'+data.fileID+'" data-filemode="'+data.filemode+'" class="btn btn-sm text-danger px-1 m-1" title="Delete File" onclick="deleteSWTLFile('+data.fileID+');return false;" data-confirm="0"><i class="fa-solid fa-trash-can"></i></a>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "15%",
				"className": "text-right",
				"orderable": false 
			}
		];

		TitleStreamingFilesListTable = $('#TitleStreamingFilesListTable').DataTable({
			"processing": true,
			"serverSide": true,
			"paging": false,
			"info": false,
			"ajax": {
				"url": ajaxUrl,
				"type": "post",
				"dataSrc": function (json) {
					var streamingData = [];
					var downloadableData = [];
					var paperData = [];
					if (json.recordsTotal === 0) {
						toggleTitleRowWarning('This Title has no media files (MP3 or MP4) uploaded.');
						initializePlupload(0);
					} else {
						var hasInvalidMediaFile = false;
						$('#fileIDs').val(json.fileIDList);
						json.data.forEach(function (item) {
							if (item.fileType === 'audio' || item.fileType === 'video') {
								if((item.fileType === 'audio' && item.streamingExt != 'mp3') || (item.fileType === 'video' && item.streamingExt != 'mp4')) hasInvalidMediaFile = true;
								if (item.fileType === 'audio') alreadyHasMp3 = true;
								if (item.fileType === 'video') alreadyHasMp4 = true;
								streamingData.push(item);
							} else if(item.downloadableExt === 'pvr'){
								paperData.push(item);
							} else downloadableData.push(item);
						});
						if (streamingData.length > 0) {
							steamingFileCount = streamingData.length;
							streamingData[0].canmoveup = false;  // First element cannot move up
							streamingData[streamingData.length - 1].canmovedown = false;  // Last element cannot move down
							$('#TitleStreamingFilesListTable').show();
							$('#streamingHeading').show();
							toggleTitleRowWarning(hasInvalidMediaFile ? 'This Title has media files (MP3 or MP4) with invalid streaming formats.' : '');
						}
						else {
							steamingFileCount = 0;
							toggleTitleRowWarning('This Title has no media files (MP3 or MP4) uploaded.');
							alreadyHasMp3 = false;
							alreadyHasMp4 = false;
							$('#TitleStreamingFilesListTable').hide();
							$('#streamingHeading').hide();
						}

						// Directly update canmoveup and canmovedown for downloadableData
						if (downloadableData.length > 0) {
							downloadableData[0].canmoveup = false;  // First element cannot move up
							downloadableData[downloadableData.length - 1].canmovedown = false;  // Last element cannot move down
							$('#TitleDownloadableFilesListTable').show();
							$('#downloadableHeading').show();
						} else {
							$('#TitleDownloadableFilesListTable').hide();
							$('#downloadableHeading').hide();
						}

						if (paperData.length > 0) {
							paperData[0].canmoveup = false;  // First element cannot move up
							paperData[paperData.length - 1].canmovedown = false;  // Last element cannot move down
							$('#TitlePaperFilesListTable').show();
							$('#paperHeading').show();
						} else {
							$('#TitlePaperFilesListTable').hide();
							$('#paperHeading').hide();
						}
						populateDownloadableFiles(downloadableData);
						populatePaperFiles(paperData);
					}
					return streamingData;
				}
			},
			"autoWidth": false,
			"columns": streamingColumns,
			"searching": false,
			"ordering": false,
			"rowReorder": {
				dataSrc: "columnid" 
			}
		});
		TitleStreamingFilesListTable.on('row-reorder', function (e, diff, edit) {
			let orderData = [];
			diff.forEach(function(item){
				orderData.push({
					id: TitleStreamingFilesListTable.row(item.node).data().fileID,
					newOrder: item.newPosition
				});
			});
			window.reorderData = JSON.stringify(orderData);
			moveSWTLFile(sw_titleid,window.reorderData);
		});
		function toggleTitleRowWarning(msg){
			top.$('#iconMediaFiles_'+sw_titleid).prop('title', msg).toggleClass('d-none', msg.length == 0);
		}
		function populateDownloadableFiles(data) {
			if (typeof TitleDownloadableFilesListTable !== 'undefined') {
				TitleDownloadableFilesListTable.clear().rows.add(data).draw();
			} else {
				TitleDownloadableFilesListTable = $('#TitleDownloadableFilesListTable').DataTable({
					"data": data,
					"autoWidth": false,
					"paging": false,
					"info": false,
					"columns": downloadableColumns,
					"searching": false,
					"ordering": false,
					"rowReorder": {
						dataSrc: "columnid" 
					}
				});
				TitleDownloadableFilesListTable.on('row-reorder', function (e, diff, edit) {
					let orderData = [];
					diff.forEach(function(item){
						orderData.push({
							id: TitleDownloadableFilesListTable.row(item.node).data().fileID,
							newOrder: item.newPosition
						});
					});
					window.reorderData = JSON.stringify(orderData);
					moveSWTLFile(sw_titleid,window.reorderData);
				});
			}
		}
		function populatePaperFiles(data) {
			if (typeof TitlePaperFilesListTable !== 'undefined' && $.fn.DataTable.isDataTable('#TitlePaperFilesListTable')) {
				TitlePaperFilesListTable.clear().rows.add(data).draw();
			} else {
				TitlePaperFilesListTable = $('#TitlePaperFilesListTable').DataTable({
					"data": data,
					"autoWidth": false,
					"paging": false,
					"info": false,
					"columns": paperColumns,
					"searching": false,
					"ordering": false,
					"rowReorder": {
						dataSrc: "columnid" 
					}
				});
				TitlePaperFilesListTable.on('row-reorder', function (e, diff, edit) {
					let orderData = [];
					diff.forEach(function(item){
						orderData.push({
							id: TitlePaperFilesListTable.row(item.node).data().fileID,
							newOrder: item.newPosition
						});
					});
					window.reorderData = JSON.stringify(orderData);
					moveSWTLFile(sw_titleid,window.reorderData);
				});
			}
		}
		if (isSWProgramLocked()) {
			$('#divTitleFilesList').find('input:not([type=hidden]),select,textarea,button').prop('disabled', true);
		}
	}
}
function replaceSWTLFile(fileID,fileMode) {
	var msg = '';
	if(fileMode == 'stream') {
		var msg = 'To replace media file, your program must be set to Inactive. Click OK to inactivate the program and proceed with the replace media file process.';
	} else {
		var msg = 'To replace downloadable file, your program must be set to Inactive. Click OK to inactivate the program and proceed with the replace downloadable file process.';
	}
	if (top.$('#isPublishedHidden').val() == 1) {
		let content = '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>' + msg + '</span></div>';
		
		MCModalUtils.showConfirmationPopup('Confirmation Needed', content,
			{ classList: 'btn-sm btn-secondary', label:'Cancel', btnID: 'btnMCModalCancelBtn' },
			{ classList: 'btn-sm btn-danger', label:'Confirm', btnID: 'btnMCModalConfirmBtn', clickFnName: 'doInactivateWhenReplaceFile', clickFnArgs: {fileID:fileID, fileMode:fileMode}, isClickFnPromise:0 },
			'md'
		);
	} else {
		let title = fileMode === 'stream' ? 'Replace Media File' : 'Replace Downloadable File';
		let replaceSWTLFileLink = link_replaceswtlfile + '&fid=' + fileID;
		top.MCModalUtils.showLoading(title);
		self.location.href = replaceSWTLFileLink;
	}

	
}
async function doInactivateWhenReplaceFile(fileID,fileMode) {
	$('#swtlReplaceFile'+fileID).html('<i class="fa-solid fa-spinner fa-spin"></i>');
	let inactivateResult = await inactivateSWODProgram();
	if (!inactivateResult.success) {
		alert(inactivateResult.errmsg);
		return false;
	}
	let title = fileMode === 'stream' ? 'Replace Media File' : 'Replace Downloadable File';
	let replaceSWTLFileLink = link_replaceswtlfile + '&fid=' + fileID;
	top.MCModalUtils.showLoading(title);
	self.location.href = replaceSWTLFileLink;
}
function initializePlupload(hasFiles=1,addPVR=0) {
	if (isSWProgramLocked()) return false;
	var fileExtension='';
	var isFirstUpload = true;
	var fileid;
	var formatsAvailable = '';
	isPublishedSeminar = parseInt(top.$('#isPublishedHidden').val());
	$('#divTitleFilesContainer').addClass('d-none');
	$('#addMoreTitleFilesUploadContainer').empty().html($('#divContentToOverwrite').html());
	if(!hasFiles) {
		$('#addMoreTitleFilesUploadContainer #noFiles').removeClass('d-none');
		$('#addMoreTitleFilesUploadContainer #returnToFiles').addClass('d-none');
	}
	if(addPVR)
		var mime_types = [ {title:"Paper Images", extensions:"jpg,jpeg,gif,png"} ];
	else
		var mime_types = [ {title:"Streaming files", extensions:"mp3,mp4,pdf,doc,docx,ppt,pptx,xls,xlsx"} ];
		$("#addMoreTitleFilesUploadContainer #SWTLFileUploader").pluploadQueue({
			runtimes : 'html5',
			url : '',
			multipart : true,
			file_data_name : 'file', 
			multiple_queues : true,
			init : {
			FilesAdded: function(up, addedFiles) {
				if(!addPVR) {
					let hasMediaFiles = false;
					let hasDownloadableFiles = false;
					let conflictingFiles = false;
					let hasMp3 = false;
					let hasMp4 = false;
					var msg='';

					// Check for media files being added when program is active
					if (isPublishedSeminar == 1) {
						for (let file of addedFiles) {
							let fileExt = file.name.split('.').pop().toLowerCase();
							if (fileExt === 'mp3' || fileExt === 'mp4') {
								hasMediaFiles = true;
								msg="You cannot upload new media files (MP4 or MP3) to an Active program. Click 'OK' to mark the program as Inactive and continue with uploading new media.";
								break;
							}
							if (['pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx'].includes(fileExt)) {
								hasDownloadableFiles = true;
								msg="You cannot upload new downloadable files to an Active program. Click 'OK' to mark the program as Inactive and continue with uploading new downloadable files.";
								break;
							}
						}
			
						if (hasMediaFiles || hasDownloadableFiles) {
							let content = '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>' + msg + '</span></div>';
		
							MCModalUtils.showConfirmationPopup('Confirmation Needed', content,
								{ classList: 'btn-sm btn-secondary', label:'Cancel', btnID: 'btnMCModalCancelBtn', clickFnName: 'onCancel', clickFnArgs: {up:up}},
								{ classList: 'btn-sm btn-danger', label:'Confirm', btnID: 'btnMCModalConfirmBtn', clickFnName: 'doInactivateWhenUploadNewFile', isClickFnPromise:0 },
								'md'
							);
						}
					}

					up.files.forEach(function (file) {
						fileExtension = file.name.split('.').pop().toLowerCase();
						if (fileExtension === 'mp3' || alreadyHasMp3) hasMp3 = true;
						if (fileExtension === 'mp4' || alreadyHasMp4) hasMp4 = true;
					});

					addedFiles.forEach(function (file) {
						fileExtension = file.name.split('.').pop().toLowerCase();
						if (fileExtension === 'mp3' && hasMp4) {
							conflictingFiles = true;
						}
						if (fileExtension === 'mp4' && hasMp3) {
							conflictingFiles = true;
						}
						if (conflictingFiles) {
							up.removeFile(file);
							$('#' + file.id).remove();
						}
					});

					if (conflictingFiles) {
						alert("Error: conflicting streaming formats. Only upload MP4 (video) or MP3 (audio).");
						up.refresh();
					}
				}
				if (addedFiles.length > 0) {
						// add original name field
					up.files.forEach(function(file) {
						fileExtension = file.name.split('.').pop().toLowerCase();
						if(['jpg', 'jpeg', 'gif', 'png'].includes(fileExtension)) {
							if (typeof file.orginalName == 'undefined')
								file.orginalName = file.name;
						}
						
					});

					// remove duplicates 
					var i = 0;
					while (i < addedFiles.length) {
						fileExtension = addedFiles[i].name.split('.').pop().toLowerCase();
						if(['jpg', 'jpeg', 'gif', 'png'].includes(fileExtension)) {
							var dupe = false;
							var j = 0;
							while (j < (up.files.length - addedFiles.length)) {
								if (addedFiles[i].name == up.files[j].orginalName) {
									dupe = true;
									up.removeFile(up.getFile(addedFiles[i].id));
									$('#' + addedFiles[i].id).remove();
									addedFiles.splice(i, 1);
									break;
								}
								j++;
							}
						}
						if (!dupe) i++;
					}
					// sort the files alphabetically
					if(addPVR) {
						up.files.sort(sortSWTLFilesByFilename);
		
						var padLength = up.files.length.toString().length;
						up.files.forEach(function(file,index) {
							var ext = file.name.substr(file.name.lastIndexOf('.')).toLowerCase();
							file.name = ('0'.repeat(padLength) + (index+1)).slice(padLength * -1) + ext;

							$('#' + file.id + ' div.plupload_file_name span').html(file.name + ' (formally ' + file.orginalName + ')');
							$('#' + file.id).data('position',index);
						});
						
						$('#addMoreTitleFilesUploadContainer #SWTLFileUploader_filelist li').sort(sortSWTLFilesList).appendTo('#addMoreTitleFilesUploadContainer #SWTLFileUploader_filelist');
					}
					if(up.files.length)
						$('.plupload_start').addClass('plupload_start_button');
				}
			},
			BeforeUpload: function(up, file) {
				var fileExtension = file.name.split('.').pop().toLowerCase();
				var fileName = file.name.replace(/\.[^/.]+$/, "").replace(/[^a-zA-Z0-9]/g, " ").replace(/\s+/g, " ");
				var m='';
				var fileTypeID;
				//up.stop();
				if(addPVR){
					m = 'paper';
					fileTypeID = 2;
						
					if(isFirstUpload) {
						var saveFileResult = function(r) {
							if (r.success && r.success.toLowerCase() == 'true') {
								if (mc_environment === 'production') {
									function uploadSWTLFileResult(obj) {
										if (obj.success && obj.success.toLowerCase() == 'true') {
											up.setOption('url', obj.uploadurl);
											var contentType = "application/octet-stream";
											var key = obj.objectkey + file.name;
											up.fileid = obj.fileid;
											file.fileid = obj.fileid;
											up.pid = obj.participantid;
											file.pid = obj.participantid;
											
											if (m != 'paper') {
												var ext = file.name.substr(file.name.lastIndexOf('.')).toLowerCase();
												var key = obj.objectkey + up.fileid + ext;
											}
											
											up.settings.multipart_params = {
												'Filename': key,
												'key': key,
												'AWSAccessKeyId': obj.accesskey,
												'acl': 'public-read',
												'content-type': contentType,
												'policy': obj.policy,
												'signature': obj.policysignature
											};
											up.setOption('params', up.settings.multipart_params);
											
										} else {
											alert('We could not display the upload form. Try again.');
											cancelSWTLFilesForm();
										}
									}
									fileid = r.fileid;
									var objParams = { titleID:sw_titleid, fileID:r.fileid, filemode:m };
									TS_AJX_SYNC('ADMINSWFILE','getUploadFileSetUp',objParams,uploadSWTLFileResult,uploadSWTLFileResult,20000,uploadSWTLFileResult);
								}
							} else { 
								alert('We were unable to save this file.');
							}
						};
						
						var formatsAvailable = '<formats><format ext="pvr" accesstype="D">';   
						up.files.forEach(function(uploadFile) {
							formatsAvailable += '<page pg="' + uploadFile.name + '" />';
						});
						formatsAvailable += '</format></formats>';

						var objParams = { titleID:sw_titleid, fileID:0, fileTypeID:fileTypeID, fileTitle:'Slides',
							fileName:'Slides', fileDesc:'', addPVR:1, formatsAvailable:formatsAvailable };
						TS_AJX_SYNC('ADMINSWFILE','saveFile',objParams,saveFileResult,saveFileResult,10000,saveFileResult);

						isFirstUpload = false;
					} else {
						if(mc_environment == 'production') {
							function uploadSWTLFileResult(obj) {
								if (obj.success && obj.success.toLowerCase() == 'true') {
									up.setOption('url', obj.uploadurl);
									var contentType = "application/octet-stream";
									var key = obj.objectkey + file.name;
									up.fileid = obj.fileid;
									file.fileid = obj.fileid;
									up.pid = obj.participantid;
									file.pid = obj.participantid;
									
									if (m != 'paper') {
										var ext = file.name.substr(file.name.lastIndexOf('.')).toLowerCase();
										var key = obj.objectkey + up.fileid + ext;
									}
									
									up.settings.multipart_params = {
										'Filename': key,
										'key': key,
										'AWSAccessKeyId': obj.accesskey,
										'acl': 'public-read',
										'content-type': contentType,
										'policy': obj.policy,
										'signature': obj.policysignature
									};
									up.setOption('params', up.settings.multipart_params);
									
								} else {
									alert('We could not display the upload form. Try again.');
									cancelSWTLFilesForm();
								}
							}
							var objParams = { titleID:sw_titleid, fileID:fileid, filemode:m };
							TS_AJX_SYNC('ADMINSWFILE','getUploadFileSetUp',objParams,uploadSWTLFileResult,uploadSWTLFileResult,20000,uploadSWTLFileResult);
						}
					}
				}
				if(!addPVR) {
					if (['mp3'].includes(fileExtension)) {
						m = 'stream';
						fileTypeID = 3; 
					} else if (['mp4'].includes(fileExtension)) {
						m = 'stream';
						fileTypeID = 4; 
					} else {
						m = 'download';
						fileTypeID = 2; 
					}
					var saveFileResult = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							if (mc_environment == 'production') {
								function uploadSWTLFileResult(obj) {
									if (obj.success && obj.success.toLowerCase() == 'true') {
										up.setOption('url', obj.uploadurl);
										var contentType = "application/octet-stream";
										var key = obj.objectkey + file.name;
										up.fileid = obj.fileid;
										file.fileid = obj.fileid;
										up.pid = obj.participantid;
										file.pid = obj.participantid;
										
										if (m != 'paper') {
											var ext = file.name.substr(file.name.lastIndexOf('.')).toLowerCase();
											var key = obj.objectkey + up.fileid + ext;
										}
										
										up.settings.multipart_params = {
											'Filename': key,
											'key': key,
											'AWSAccessKeyId': obj.accesskey,
											'acl': 'public-read',
											'content-type': contentType,
											'policy': obj.policy,
											'signature': obj.policysignature
										};
										up.setOption('params', up.settings.multipart_params);
										
									} else {
										alert('We could not display the upload form. Try again.');
										cancelSWTLFilesForm();
									}
								}
									
								var objParams = { titleID:sw_titleid, fileID:r.fileid, filemode:m };
								TS_AJX_SYNC('ADMINSWFILE','getUploadFileSetUp',objParams,uploadSWTLFileResult,uploadSWTLFileResult,20000,uploadSWTLFileResult);
							}
						} else { 
							alert('We were unable to save this file.');
						}
					};

					if(fileExtension === 'mp3')
						formatsAvailable = '<formats><format ext="mp3" accesstype="S" /></formats>';
					else if(fileExtension === 'mp4')
						formatsAvailable = '<formats><format ext="mp4" accesstype="S" /></formats>';
					else formatsAvailable = '<formats><format ext="'+ fileExtension +'" accesstype="D" /></formats>';

					var objParams = { titleID:sw_titleid, fileID:0, fileTypeID:fileTypeID, fileTitle:fileName,
						fileName:fileName, fileDesc:'',formatsAvailable:formatsAvailable };
					TS_AJX_SYNC('ADMINSWFILE','saveFile',objParams,saveFileResult,saveFileResult,10000,saveFileResult);
				}

			},
			UploadComplete: function(up, files) {
				$('#addMoreTitleFilesUploadContainer').html(mca_getLoadingHTML());
				if (mc_environment === 'production') {
					if(!addPVR) {
						up.files.forEach(function(file) {
							refreshFileResults(file.fileid, file.pid);
						});
					} else {
						setTimeout(onSWTLFileUploadComplete,1000,up.fileid,up.pid);
					}
				}
				setTimeout(getSWTLFileDetails,2000);
				$('#addMoreTitleFilesUploadContainer').html('');
				$('#divTitleFilesContainer').removeClass('d-none');
				$('#divRefreshFormatsLoading').html('').addClass('d-none');
				top.mca_hideAlert('err_swoddetails');	
			},
			Error: mc_environment === 'production' ? function(up, error) {
				if (error.message !== 'File extension error.' && error.message !== 'File size error.') {
					$('#addMoreTitleFilesUploadContainer').html('<p>Error: ' + error.message + '</p>');
					// Handle other errors appropriately here
				}
			} : undefined
		},
		filters : {
			max_file_size : '2048mb',
			mime_types: mime_types
		}
	});
	var sortSWTLFilesByFilename = function(a, b) {
		var aName = a.orginalName;
		var bName = b.orginalName;
		return ((aName < bName) ? -1 : ((aName > bName) ? 1 : 0));
	};

	var sortSWTLFilesList = function(a, b) {
		return ($(b).data('position')) < ($(a).data('position')) ? 1 : -1;
	}
}
function doInactivateWhenUploadNewFile(){
	inactivateSWODProgram();
	isPublishedSeminar = 0;
}
function onCancel(up) {
	up.splice(0, up.files.length); // Clear queue
	up.refresh();
}
function initSWTLReplaceFile(obj) {
	let uploader = new plupload.Uploader({
		runtimes : 'html5',
		browse_button : 'btnReplaceSWFile',
		container: $('replaceSWFileBtnContainer')[0],
		drop_element: $('#SWFileContainer')[0],
		url : "",
		multi_selection: false,
		filters : {
			max_file_size : '2048mb',
			mime_types: [ {title:"Replace File", extensions:sw_allowedmimetypes} ]
		},
		init: {
			PostInit: function() {
				$('#newSWFile').html('');
				$('#btnUploadSWFile').off('click').on('click',function() {
					if (uploader.files.length) {
						top.$('#btnUploadSWFile').html('<i class="fa-light fa-circle-notch fa-spin"></i> Uploading... ').prop('disabled',true);
						uploader.start();
					}
					return false;
				});
			},
			FilesAdded: function(up, files) {
				$('#newSWFile').html('');
				plupload.each(files, function(file) {
					$('#newSWFile').append('<div id="' + file.id + '" class="text-primary mb-2">' + file.name + ' (' + plupload.formatSize(file.size) + ') <b></b></div>');
				});
				$('#errReplaceSWFile').html('').addClass('d-none');
			},
			BeforeUpload: function(up, file) {
				let saveFileResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						sw_newfileid = r.fileid;
						if (mc_environment == 'production' && sw_newfileid) {
							up.setOption('url', obj.uploadurl);
							up.fileid = sw_newfileid;
							file.fileid = sw_newfileid;
							up.pid = obj.participantid;
							file.pid = obj.participantid;
							let ext = file.name.substr(file.name.lastIndexOf('.')).toLowerCase();
							let key = obj.objectkey + sw_newfileid + ext;
						
							up.settings.multipart_params = {
								'Filename': key,
								'key': key,
								'AWSAccessKeyId': obj.accesskey,
								'acl': 'public-read',
								'content-type': 'application/octet-stream',
								'policy': obj.policy,
								'signature': obj.policysignature
							};
							up.setOption('params', up.settings.multipart_params);
						}
					}
				};

				let fileExtension = file.name.split('.').pop().toLowerCase();
				let fileName = file.name.replace(/\.[^/.]+$/, "").replace(/[^a-zA-Z0-9]/g, " ").replace(/\s+/g, " ");
				let newFileMode = '', fileTypeID, formatsAvailable;

				if (['mp3'].includes(fileExtension)) {
					newFileMode = 'stream';
					fileTypeID = 3;
					formatsAvailable = '<formats><format ext="mp3" accesstype="S" /></formats>';
				} else if (['mp4'].includes(fileExtension)) {
					newFileMode = 'stream';
					fileTypeID = 4;
					formatsAvailable = '<formats><format ext="mp4" accesstype="S" /></formats>';
				} else {
					newFileMode = 'download';
					fileTypeID = 2; 
					formatsAvailable = '<formats><format ext="'+ fileExtension +'" accesstype="D" /></formats>';
				}

				if (sw_filemode != newFileMode) return false;

				let objParams = { titleID:sw_titleid, fileID:0, fileTypeID:fileTypeID, fileTitle:fileName,
					fileName:fileName, fileDesc:'',formatsAvailable:formatsAvailable };
				TS_AJX_SYNC('ADMINSWFILE','saveFile',objParams,saveFileResult,saveFileResult,10000,saveFileResult);
			},
			UploadProgress: function(up, file) {
				$('#'+file.id).find('b').html('<span>' + file.percent + '%</span>');
			},
			UploadComplete: function(up, files) {
				let replaceFileResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
					} else {
						alert('We were unable to replace the file. Try again.');
					}
					returnToSWTLFiles();
				};
				
				$('#replaceFileLoading').html(mca_getLoadingHTML()).removeClass('d-none');
				$('#frmReplaceSWFile').addClass('d-none');

				let objParams = { seminarID:top.getSWProgramID(), titleID:sw_titleid, newFileID:sw_newfileid, oldFileID:sw_oldfileid, fileMode:sw_filemode };
				if (sw_filemode == 'stream') objParams.carryOverProgress = $('#carryOverProgress').is(':checked') ? 1 : 0;
				TS_AJX_SYNC('ADMINSWFILE','replaceFile',objParams,replaceFileResult,replaceFileResult,30000,replaceFileResult);
			},
			Error: function(up, err) {
				top.$('#btnUploadSWFile').html('Upload').prop('disabled',false);
				if (mc_environment == 'production' && err.message != 'File extension error.')
					$('#errReplaceSWFile').html('We ran into an issue. ' + err.message).removeClass('d-none');
				else {
					if (err.message == 'File extension error.') {
						alert('Sorry, we can\'t upload ' + err.file.name + '.\n\nThe file extension isn\'t supported. Please upload a different file type.');
					} else if (err.message == 'File size error.') {
						alert('Sorry, we can\'t upload ' + err.file.name + '.\n\nThe file is too large. Please upload a smaller file.');
					}
				}
			}
		}
	});
	uploader.init();
}
function returnToSWTLFiles() {
	top.MCModalUtils.buildFooter({});
	top.MCModalUtils.showLoading(truncateFilename(sw_titlename,80));
	self.location.href = top.link_editswodtitle + '&pid=' + sw_titleid;
}
function refreshFileResults(fid, pid) {
	var refreshFileResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			// Add any success handling logic here
		} else {
			alert('We were unable to refresh files. Try again.');
		}
	};
	var pathToFile = sw_sitecode.toLowerCase() + '.' + pid;
	var objParams = { titleID: sw_titleid, fileID: fid, pathToFile: pathToFile };
	TS_AJX('ADMINSWFILE', 'refreshFiles', objParams, refreshFileResult, refreshFileResult, 20000, refreshFileResult);
}
function onSWTLFileUploadComplete(fid,pid) {
	cancelSWTLFilesForm();
	refreshSWTLFile(fid,pid);
}
function cancelSWTLFilesForm() {
	$('#divTitleFilesUploadContainer').html('').addClass('d-none');
	$('#addMoreTitleFilesUploadContainer').html('');
	$('#divTitleFilesContainer').removeClass('d-none');
	return false;
}
function downloadSWTLFile(f,titleID) {
	var pgmTitle = $('#MCModalHeader #MCModalLabel').html();
	MCModalUtils.hideModal();
	$('#MCModal').on('hidden.bs.modal', function() {
		showSWTLFileDownloadModal(f,titleID);
		$('#MCModal').on('hidden.bs.modal', function() {
			editSWODTitle(titleID,pgmTitle);
		});
	});
}
function showSWTLFileDownloadModal(f,titleID) {
	let fileID = $(f).data('fileid');
	let fileMode = $(f).data('filemode');
	let fileExt = $(f).data('ext');
	let fileBody = "";
	var titleName = truncateFilename($(f).data('filetitle'),30);

	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'md',
		title: titleName,
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: false,
			buttons: [
				{ class: "btn-danger btn-sm mr-auto d-none", clickhandler: 'deleteSWTLFileFormat', label: 'Delete Format', iconclass: '', name: 'btnDeleteFormat', id: 'btnDeleteFormat' },
				{ class: "btn-primary btn-sm ml-auto", clickhandler: 'doDownloadSWTLFile', label: 'Download', iconclass: '', name: 'btnDownloadFormat', id: 'btnDownloadFormat' }
			]
		}
	});

	switch(fileMode) {
		case 'download':
			fileBody = '<b>File Format:</b> Download<br/>';
			fileBody += '<b>File Extension:</b> ' + fileExt + '<br/>';
			break;
		case 'paper':
			fileBody = '<b>File Format:</b> Paper<br/>';
			fileBody += '<b>File Extension:</b> ' + fileExt + '<br/><br/>';
			fileBody += $(f).data('paperpgdesc');
			$('#btnDownloadFormat, #btnDeleteFormat').addClass('d-none');
			break;
		case 'stream':
			fileBody = '<b>File Format:</b> Streaming<br/>';
			fileBody += '<b>File Extension:</b> ' + fileExt + '<br/>';
			break;
	};
	$('#MCModalBody').html(fileBody);
	$('#btnDeleteFormat').attr('data-fileid',fileID);
	$('#btnDeleteFormat').attr('data-filemode',fileMode);
	$('#btnDeleteFormat').attr('data-fileext',fileExt);
	$('#btnDeleteFormat').attr('data-titleid',titleID);
}
function doDownloadSWTLFile() {
	var getDownloadLinkResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			self.location.href = r.s3filelink;
			window.setTimeout(function(){ MCModalUtils.hideModal(); },4000);
		} else {
			alert('We were unable to download this title file.');
			$('#btnDownloadFormat').prop('disabled',false).html('Download');
		}
	};

	$('#btnDownloadFormat').prop('disabled',true).html('Downloading...');
	let objParams = { fileID:$('#btnDeleteFormat').data('fileid'), titleID:$('#btnDeleteFormat').data('titleid'),
		fileMode:$('#btnDeleteFormat').data('filemode'), fileExt:$('#btnDeleteFormat').data('fileext') };
	TS_AJX('ADMINSWFILE','getDownloadLinkFromFileID',objParams,getDownloadLinkResult,getDownloadLinkResult,10000,getDownloadLinkResult);
}
function deleteSWTLFileFormat(){
	titleID = $('#btnDeleteFormat').data('titleid');
	fileID = $('#btnDeleteFormat').data('fileid');
	fileMode = $('#btnDeleteFormat').data('filemode');
	fileExt = $('#btnDeleteFormat').data('fileext');
	
	if (fileID > 0) {
		var deleteResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true'){
				MCModalUtils.hideModal();
			} else {
				alert('We were unable to delete this title file.');
				$('#btnDeleteFormat').prop('disabled',false).html('Download');
			}
		};
		$('#btnDeleteFormat').prop('disabled',true).html('Downloading...');
		var objParams = { titleID:titleID,fileID:fileID,fileExt:fileExt,fileMode:fileMode };
		TS_AJX('ADMINSWFILE','deleteTitleFileFormat',objParams,deleteResult,deleteResult,20000,deleteResult);
	} 
}
/* SWB functions */
function addSWBProgram() {
	mca_hideAlert('err_swbaddprogram');
	if ($('#divAddProgramForm').length && $('#divAddProgramForm').hasClass('d-none')) {
		$('#swBundleName').val('');
		$('div.divSWBTool').addClass('d-none');
		$('#divAddProgramForm').removeClass('d-none');
	}
}
function doAddSWBProgram() {
	var addSWBProgramResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true' && r.bundleid){
			self.location.href = link_editswbprogram + '&pid=' + r.bundleid + '&programAdded=true';
		} else {
			alert('We were unable to create this Bundle. Try again.');
			$('#btnAddSWBProgram').prop('disabled',false);
		}
	};

	mca_hideAlert('err_swbaddprogram');
	var arrReq = [];
	var bundleName = $('#swBundleName').val().trim();
	var swFormat = $('#swFormat').val();

	if (bundleName == '') arrReq[arrReq.length] = 'Enter the Bundle Name.';
	if (swFormat == '') arrReq[arrReq.length] = 'Select the Program Type.';

	if (arrReq.length) {
		mca_showAlert('err_swbaddprogram', arrReq.join('<br/>'));
		return false;
	}

	$('#btnAddSWBProgram').prop('disabled',true);
	
	var objParams = { bundleName:bundleName, swFormat:swFormat };
	TS_AJX('ADMINSWB','createSWBProgram',objParams,addSWBProgramResult,addSWBProgramResult,10000,addSWBProgramResult);
	return false;
}
function initializeBundlesTable(){
	let arrColumns = [];

	arrColumns.push(
		{
			"data": null,
			"render": function (data, type) {
				let renderData = '';
				if (type === 'display') {
					if (!data.isActive)
						renderData +=  '<div class="badge badge-warning mb-1">Inactive</div>';
					renderData += '<div class="d-flex"><span>'+data.bundleName+'</span>' + (data.lockSettings ? '<span class="fa-solid fa-lock ml-1" style="font-size: 17px; color: grey;" title="Changes to this program are restricted by a Client Administrator."></span>' : '') + '<span id="featuredBadgeContainer_'+data.bundleID+'" class="ml-auto">'+(data.isFeatured ? '<span class="badge badge-info">Featured</span>' : '')+'</span></div>';
					if(data.bundleSubTitle.length) renderData += '<div class="small text-dim">'+data.bundleSubTitle+'</div>';
				}
				return type === 'display' ? renderData : data;
			},
			"width": "55%",
			"className": "align-top"
		},
		{ "data": "dateActivated","width": "10%", "className": "align-top" }
	);

	if (sw_hop == 0) {
		arrColumns.push(
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						if (data.gridMode.toLowerCase() == 'swsearchgrid') renderData += data.publisherOrgCode;
						else {
							if (data.publisherOrgCode == sw_sitecode) {
								renderData += 'Publisher';
								if (data.isSyndicated)
									renderData += '<div class="text-dim small">SYNDICATED</div>';
							} else
								renderData += '<div> Opt-In</div><div class="text-dim small">'+data.publisherOrgCode+'</div>';
							renderData += '</div>';
						}
					}
					return type === 'display' ? renderData : data;
				},
				"width": "10%",
				"className": "align-top"
			}
		);
	}

	arrColumns.push(
		{ "data": "bundleType","width": "10%", "className": "align-top" },
		{ "data": null,
			"render": function ( data, type, row, meta ) {
				let renderData = '';
				if (type === 'display') {									
					if (data.gridMode.toLowerCase() == 'swsearchgrid') {
						if ($.trim(data.siteHostName).length) {
							renderData += '<a href="'+mca_getSiteToolLink(data.publisherOrgCode,'SeminarWebAdmin','listSWB','editSWBProgram','pid='+data.bundleID)+'" target="_swbPrg'+data.bundleID+'" title="Edit This Bundle" class="btn btn-xs btn-outline-primary p-1 m-1"><i class="fa-solid fa-pencil"></i></a>';
						} else {
							renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-pencil"></i></a>';
						}
					} else {
						renderData += '<a href="javascript:editSWBProgram('+data.bundleID+');" title="Edit Bundle" class="btn btn-xs btn-outline-primary p-1 m-1"><i class="fa-solid fa-pencil"></i></a>';
					}
					if (data.copyBundleRights) {
						if (data.publisherOrgCode == sw_sitecode) {
							renderData += '<a href="javascript:copyProgram('+data.bundleID+');" class="btn btn-xs btn-outline-primary p-1 m-1" title="Copy Bundle"><i class="fa-solid fa-copy"></i></a>';
						} else {
							renderData += '<a class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-copy"></i></a>';
						}
					}
					if(data.isFeatured){
						renderData += '<a href="javascript:removeFromFeaturedPrograms('+data.bundleID+');" class="btn btn-xs btn-outline-first p-1 m-1" title="Remove from Featured Programs"><i class="fa-solid fa-check-circle swfeatured_icon_'+data.bundleID+'"></i></a>';
					}else{
						renderData += '<a href="javascript:addToFeaturedPrograms('+data.bundleID+');" class="btn btn-xs btn-outline-dark p-1 m-1" title="Add to Featured Programs"><i class="fa-solid fa-check-circle swfeatured_icon_'+data.bundleID+'"></i></a>';
					}
					if (data.hasManageSWBRegistrantsRights) {
						if (data.gridMode.toLowerCase() == 'swsearchgrid') {
							if ($.trim(data.siteHostName).length) {
								renderData += '<a href="'+mca_getSiteToolLink(data.publisherOrgCode,'SeminarWebAdmin','listSWB','editSWBProgram','pid='+data.bundleID+'&tab=registrants')+'" target="_swbPrg'+data.bundleID+'" title="View Registrants" class="btn btn-xs btn-outline-primary p-1 m-1"><i class="fa-solid fa-users"></i></a>';
							
							} else {
								renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-pencil"></i></a>';
							}
						} else {
							renderData += '<a href="javascript:editSWBProgram('+data.bundleID+',\'registrants\');" title="View Registrants" class="btn btn-xs btn-outline-primary p-1 m-1"><i class="fa-solid fa-users"></i></a>';
						}
					} else {
						renderData += '<a class="btn btn-xs p-1 m-1 text-muted"><i class="fa-solid fa-users"></i></a>';
					}
					renderData += '<span class="d-inline-flex ml-2 small text-first w-5">'+data.enrolledCount+'</span>';
				}
				return type === 'display' ? renderData : data;
			},
			"width": "15%",
			"className": "text-center",
			"orderable": false
		}
	);

	bundlesTable = $('#bundlesTable').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 10,
		"dom": "<'row'<'col-sm-3 col-md-3'l><'col-sm-9 col-md-9'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
		"lengthMenu": [ 10, 25, 50 ],
		"ajax": { 
			"url": link_listswbprograms,
			"type": "post",
			"data": function(d) {
				$.each($('#frmFilter').serializeArray(),function() {
					d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
				});
			}
		},
		"autoWidth": false,
		"columns": arrColumns,
		"order": [[0, 'asc']],
		"searching": false
	});
}
function filterSWBPrograms() {
	if ($('#divFilterForm').hasClass('d-none')) {
		$('div.divSWBTool').addClass('d-none');
		$('#divFilterForm').removeClass('d-none');
	}
}
function quickFilterSWBPrograms(d) {
	var now = new Date();
	var thisDate = new Date();
	var todayDate = new Date(thisDate.getFullYear(), thisDate.getMonth(), thisDate.getDate());

	var n = d * 1;
	if (n >= 0) {
		$('#fActivatedDateFrom').val(moment(todayDate).format('M/D/YYYY'));
		todayDate.setDate(now.getDate()+n);
		$('#fActivatedDateTo').val(moment(todayDate).format('M/D/YYYY'));
	} else {
		n = d * -1;
		$('#fActivatedDateTo').val(moment(todayDate).format('M/D/YYYY'));
		todayDate.setDate(now.getDate()-n);
		$('#fActivatedDateFrom').val(moment(todayDate).format('M/D/YYYY'));
	} 
	dofilterSWBPrograms();
}
function dofilterSWBPrograms() {
	bundlesTable.draw();
}
function editSWBProgram(pid,t){
	let swbProgramLink = link_editswbprogram + '&pid=' + pid + (typeof t != "undefined" ? "&tab=" + t : '');
	window.open(swbProgramLink,'_blank');
}
function validateAndSaveSWBProgram(callback) {
	mca_hideAlert('err_swbdetails');
	var arrReq = [];
	if($('#bundleName').val().trim().length == 0)
		arrReq[arrReq.length] = 'Enter the bundle name.';
	var programCode = $('#programCode').val().trim();
	if(programCode.length == 0)
		arrReq[arrReq.length] = 'Enter a program code for this bundle.';
	var bundleDesc = "";
	if(CKEDITOR.instances['bundleDesc'] != null)
		bundleDesc = CKEDITOR.instances['bundleDesc'].getData().trim();
	else 
		bundleDesc = $('textarea[name="bundleDesc"]').val().trim();

	if(bundleDesc.length == 0)
		arrReq[arrReq.length] = 'Enter the bundle description.';
	if(hasIncludedSeminarsCount == 1 && $('#bundleStatus').val() === 'A' && !programAdded) 
		arrReq[arrReq.length] = 'You cannot activate a bundle with only one (1) included program. <a href="#" onclick="expandProgramTabSection(\'program-seminars\');">Go to Included Programs.</a>';
	if(hasInactiveSeminarsIncluded && $('#bundleStatus').val() === 'A' && !programAdded) 
		arrReq[arrReq.length] = 'You cannot activate a bundle with included programs marked as "Inactive". <a href="#" onclick="expandProgramTabSection(\'program-seminars\');">Go to Included Programs.</a>'; 
	

	if((hasIncludedSeminarsCount == 1 || hasInactiveSeminarsIncluded) && $('#bundleStatus').val() === 'A' && !programAdded) 
		$('#bundleStatus').val($('#bundleStatusHidden').val());
	if(arrReq.length) {
		showValidationAlertForSWBProgram(arrReq.join('<br/>'));
	}
	else {
		var checkProgramCodeResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				var saveResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						let reloadSWBProgram = false;
						// program locked / unlocked
						if($("#lockSWBProgramSettings").is(':checked') && !sw_lockprogramsettings) {
							sw_lockprogramsettings = true;
							reloadSWBProgram = true;
						} else if (!$("#lockSWBProgramSettings").is(':checked') && sw_lockprogramsettings) {
							reloadSWBProgram = true;
						}

						if (reloadSWBProgram) self.location.href = getSWEditProgramLink();
						
						$('#frmSWBProgramDetails,#divSWProgramDetailsSaveLoading').toggle();
						if($('#bundleStatus').val() == 'A') {
							removeStatement(publishedIssues);
							addStatementIfNotExists(publishedSuccessStatement,'seminarSetupStatements');
						}
						else {
							addStatementIfNotExists(publishedIssues);
							removeStatement(publishedSuccessStatement,'seminarSetupStatements');
						}
						// If no more issues, hide the entire alert
						if ($('.seminarSetupIssues ul li').length === 0) {
							$('.seminarSetupIssues').addClass('d-none');
							$('.seminarSetupStatements').removeClass('d-none');
						}
						else if($('.seminarSetupIssues').hasClass('d-none')) {
							$('.seminarSetupIssues').removeClass('d-none');
							$('.seminarSetupStatements').addClass('d-none');
						}
						if(!$("#program-basics .card-header:first #saveResponse").length)
							$("#program-basics .card-header:first .card-header--title").after('<span id="saveResponse"></span>');
						$('#program-basics .card-header:first #saveResponse').html('<span class="badge badge-success">SAVED SUCCESSFULLY</span>').addClass('text-success').show().fadeOut(5000);
						if(programAdded)
							$('#nextButton, #prevButton').prop('disabled',false);
						else
							$('#program-basics .card-footer .save-button').prop('disabled',false);
						
						if (callback) {
							callback();
						}
						refreshSWBSeminarTitleHeader();

						$('#bundleStatusHidden').val($('#bundleStatus').val());
					} else {
						var arrReq = [];
						arrReq.push(r.err && r.err.length ? r.err : 'We were unable to save sw program details.');
						$('#err_swbdetails').html(arrReq.join('<br/>')).removeClass('d-none');
						$('html,body').animate({scrollTop: $('#err_swbdetails').offset().top-120},500);
						if(programAdded)
							$('#nextButton').prop('disabled',false);
						else
							$('#program-basics .card-footer .save-button').prop('disabled',false);
					}
				}
				
				$('#frmSWBProgramDetails,#divSWProgramDetailsSaveLoading').toggle();
				var bundleDesc = "";
				if(CKEDITOR.instances['bundleDesc'] != null)
					bundleDesc = CKEDITOR.instances['bundleDesc'].getData().trim();
				else 
					bundleDesc = $('textarea[name="bundleDesc"]').val().trim();
				var objParams = { 
					bundleID:$('#bundleID').val(),
					bundleStatus:$('#bundleStatus').val(),
					bundleName:$('#bundleName').val().trim(),
					bundleSubTitle:$('#bundleSubTitle').val(),
					programCode:programCode,
					bundleDesc:bundleDesc,
					dateActivated:$("#dateActivated").val(),
					lockSWBProgramSettings:$("#lockSWBProgramSettings:checked").length > 0
				};

				TS_AJX('ADMINSWB','updateSWBProgram',objParams,saveResult,saveResult,20000,saveResult);
			}
			else showValidationAlertForSWBProgram('Program Code must be unique.');
		};
		var objParams = { programType:'SWB', programID:$('#bundleID').val(), programCode:programCode };
		TS_AJX('ADMINSWCOMMON','checkProgramCode',objParams,checkProgramCodeResult,checkProgramCodeResult,10000,checkProgramCodeResult);
	}

	return false;
}
function getSWBBasicsFormData(){
	var bundleDesc = "";
	if(CKEDITOR.instances['bundleDesc'] != null)
		bundleDesc = CKEDITOR.instances['bundleDesc'].getData().trim();
	else 
		bundleDesc = $('textarea[name="bundleDesc"]').val().trim();

	return { 
		bundleID:$('#bundleID').val(),
		bundleStatus:$('#bundleStatus').val(),
		bundleName:$('#bundleName').val().trim(),
		bundleSubTitle:$('#bundleSubTitle').val(),
		programCode:$('#programCode').val().trim(),
		bundleDesc:bundleDesc,
		dateActivated:$("#dateActivated").val(),
		lockSWBProgramSettings:$("#lockSWBProgramSettings:checked").length > 0
	}
}
function showValidationAlertForSWBProgram(msg){
	$('#err_swbdetails').html(msg).removeClass('d-none');
	$('html,body').animate({scrollTop: $('#err_swbdetails').offset().top-120},500);
	if(programAdded)
		$('#nextButton').prop('disabled',false);
	else
		$('#program-basics .card-footer .save-button').prop('disabled',false);
}
function manageSWBRates() {
	hideSWProgramPricingAlert();
	$('div.divSWBRateTool').addClass('d-none');
	$('#divRatesGridContainer').removeClass('d-none');
}
function manageSWBRateOptions(){
	manageSWBRates();
	$('#divRateSelection').removeClass('d-none');
}
function saveSWBRateOptions() {
	var saveRateOptionsResult = function(r) {
		$("#btnSaveSWProgramOption").prop('disabled',false).html('Save Changes');
		if (r.success && r.success.toLowerCase() == 'true') {
			if ($('#GLSaveWarning').length) {
				$('#GLSaveWarning').remove();
			}
			if(sw_itemtype == 'SWOD' && $('input[name="allowCatalog"]:checked').val() == 0){
				reloadSWRatesGrid();
			}
			$('#saveSWProgramOptionInfo').html('<span class="badge badge-success">Saved Successfully</span>').show().fadeOut(5000);
		} else {
			showSWProgramPricingAlert(r.err);
		}
	};

	$("#btnSaveSWProgramOption").attr('disabled',true).html('Saving...');	
	var revGL = 0;
	if ($.trim($('#revenueGLAccountID').val()).length)
		revGL = $('#revenueGLAccountID').val();

	var objParams = { bundleID:sw_bundleid, 
		isPriceBasedOnActual:$('input[name=isPriceBasedOnActual]:checked').val(), 
		revenueGLAccountID:revGL, freeRateDisplay:$('input[name=freeRateDisplay]:checked').val() };
	TS_AJX('ADMINSWB','saveBundleRateOptions',objParams,saveRateOptionsResult,saveRateOptionsResult,10000,saveRateOptionsResult);
}
function manageSWBSyndication(){
	manageSWBRates();
	$('#divRateSelection,#divRatesGridContainer').addClass('d-none');
	$('#divSyndication').removeClass('d-none');
}
function editSWBRateGrouping(rgid) {
	if (isSWProgramLocked()) return false;

	manageSWBRates();

	var editRateGroupResult	= function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			let swRateGroupingTemplate = Handlebars.compile($('#mc_swRateGroupingTemplate').html());
			$('#MCModalBody').html(swRateGroupingTemplate({rateGroupingID:r.rategroupingid,rateGrouping:r.rategrouping}));
		} else {
			alert('We were unable to load rate grouping form. Try again.');
		}
	};

	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Edit Rate Grouping',
		strmodalfooter : {
			classlist: 'd-flex',
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: 'saveSWBRateGrouping',
			extrabuttonlabel: 'Save',
		}
	});

	var objParams = { participantID:sw_participantid, rateGroupingID:rgid };
	TS_AJX('ADMINSWB','getBundleRateGrouping',objParams,editRateGroupResult,editRateGroupResult,10000,editRateGroupResult);
}
function saveSWBRateGrouping() {
	if (isSWProgramLocked()) return false;

	var saveRateGroupResult	= function(r) {
		MCModalUtils.hideModal();
		if (r.success && r.success.toLowerCase() == 'true'){
			manageSWBRates();
			reloadSWRatesGrid();
		} else {
			alert('We were unable to save this rate grouping. Try again.');
		}
	};

	mca_hideAlert('err_rategrouping');
	if($('#rateGrouping').val().trim()!='') {
		$('#btnMCModalSave').prop('disabled',true).html('Saving...');
		var objParams = { participantID:sw_participantid, rateGroupingID:$('#rateGroupingID').val(), rateGrouping:$('#rateGrouping').val(), bundleID:sw_bundleid };
		TS_AJX('ADMINSWB','saveBundleRateGrouping',objParams,saveRateGroupResult,saveRateGroupResult,10000,saveRateGroupResult);
		return false;
	} else {
		mca_showAlert('err_rategrouping', 'Enter a name for this Rate Grouping.');
		return false;
	}
}
function removeSWBRateGrouping(pid,rgid) {
	if (isSWProgramLocked()) return false;

	MCModalUtils.showModal({
		verticallycentered: true,
		size: 'md',
		title: 'Remove Rate Grouping',
		strmodalbody: {
			content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-question-circle"></i></span><span>Are you sure you want to remove this rate grouping?<br/>Any rates in this rate grouping will be ungrouped.</span></div>',
		},
		strmodalfooter : {
			classlist: 'text-right',
			showclose: false,
			showextrabutton: true,
			extrabuttonclass: 'btn-outline-danger',
			extrabuttonlabel: 'Remove Rate Grouping',
		}
	});

	$('#btnMCModalSave').on('click', function(){
		doRemoveSWBRateGrouping(pid,rgid);
	});
}
function doRemoveSWBRateGrouping(pid,rgid) {
	if (isSWProgramLocked()) return false;

	manageSWBRates();
	var removeRateGroupingResult	= function(r) {
		MCModalUtils.hideModal();
		if (r.success && r.success.toLowerCase() == 'true') {
			reloadSWRatesGrid();
		} else {
			alert('We were unable to remove this rate grouping.');
		}
	};
	var objParams = { participantID:sw_participantid, bundleID:pid, rateGroupingID:rgid };
	TS_AJX('ADMINSWB','deleteBundleRateGrouping',objParams,removeRateGroupingResult,removeRateGroupingResult,10000,removeRateGroupingResult);
}
function editSWBRate(rid){
	if (isSWProgramLocked()) return false;
	manageSWBRates();
	
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: rid > 0 ? 'Edit Bundle Rate' : 'Add New Bundle Rate',
		iframe: true,
		contenturl: link_editSWBRate + '&rateID=' + rid,
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmRate :submit").click',
			extrabuttonlabel: 'Save Rate',
		}
	});
}
function removeSWBRate(rid) {
	if (isSWProgramLocked()) return false;

	MCModalUtils.showModal({
		verticallycentered: true,
		size: 'md',
		title: 'Remove Rate',
		strmodalbody: {
			content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning alert-dismissible fade show" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-question-circle"></i></span><span>Are you sure you want to remove this rate?</span></div>',
		},
		strmodalfooter : {
			classlist: 'text-right',
			showclose: false,
			showextrabutton: true,
			extrabuttonclass: 'btn-outline-danger',
			extrabuttonlabel: 'Remove Rate',
		}
	});

	$('#btnMCModalSave').on('click', function(){
		doRemoveSWBRate(rid);
	});
}
function doRemoveSWBRate(rid) {
	if (isSWProgramLocked()) return false;

	manageSWBRates();
	var removeRateResult = function(r) {
		MCModalUtils.hideModal();
		if (r.success && r.success.toLowerCase() == 'true'){
			reloadSWRatesGrid();
		} else {
			alert('We were unable to remove this rate.');
		}
	};
	$('#btnMCModalSave').prop('disabled',true).html('Removing...');
	var objParams = { participantID:sw_participantid, bundleID:sw_bundleid, rateID:rid };
	TS_AJX('ADMINSWB','deleteBundleRate',objParams,removeRateResult,removeRateResult,10000,removeRateResult);
}
function moveSWBRateGrouping(rgID,rowID,pRowID,dir) {
	if (isSWProgramLocked()) return false;

	manageSWBRates();
	var moveResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			moveSWRatesGridRow(rowID,pRowID,dir);
		}
	};
	var objParams = { participantID:sw_participantid, bundleID:sw_bundleid, rateGroupingID:rgID, dir:dir };
	TS_AJX('ADMINSWB','doBundleRateGroupingMove',objParams,moveResult,moveResult,10000,moveResult);
}
function moveSWBRate(rateID,rowID,pRowID,dir) {
	if (isSWProgramLocked()) return false;

	manageSWBRates();
	var moveResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			moveSWRatesGridRow(rowID,pRowID,dir);
		}
	};
	var objParams = { participantID:sw_participantid, bundleID:sw_bundleid, rateID:rateID, dir:dir };
	TS_AJX('ADMINSWB','doBundleRateMove',objParams,moveResult,moveResult,10000,moveResult);
}
function removeSWBMemberGroup(rateid,grpid,inc) {
	if (isSWProgramLocked()) return false;

	manageSWBRates();
	var removeMGData = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			reloadSWRatesGrid();
		} else {
			alert('We were unable to remove this group from this rate.');
		}
	};

	var objParams = { participantID:sw_participantid, bundleID:sw_bundleid, rateid:rateid, groupid:grpid, include:inc };
	TS_AJX('ADMINSWB','deleteMemberGroup',objParams,removeMGData,removeMGData,10000,removeMGData);
}
function hideAddSWProgramForms(){
	$('.addSWProgramForm').addClass('d-none');
}

function addSWLProgramToBundle() {
	if (isSWProgramLocked()) return false;

	mca_hideAlert('err_addswlprogram');
	var result = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			var html = '<option value="">-- Choose Webinar --</option>';
			if (r.arrswlprograms.length) {
				for (var i=0; i<r.arrswlprograms.length; i++) {
					html += '<option value="'+r.arrswlprograms[i].seminarid+'" '+ (r.arrswlprograms[i].ispublished == 0 ? 'class="text-danger"' : '') +'>'+r.arrswlprograms[i].seminarname+'</option>';
				}
			}
			$('select#fSWLProgram').html(html);
			hideAddSWProgramForms();
			$('#divAddSWLProgramForm').removeClass('d-none');
		} else {
			alert('An error occured while loading seminars.');
		}
	};
	var objParams = { bundleID:sw_bundleid };
	TS_AJX('ADMINSWB','getAvailableSWLSeminarsToAdd',objParams,result,result,10000,result);
}
function doAddSWLProgramToBundle() {
	if (isSWProgramLocked()) return false;

	var addResult = function(r) {
		$('#btnAddSWLProgram').prop('disabled',false);
		MCModalUtils.hideModal();
		if (r.success && r.success.toLowerCase() == 'true') {
			SWBListItemsTable.draw();
			hideAddSWProgramForms();
			$('#fSWLProgram').val('');
			$('#divAddSWLProgramForm').removeClass('d-none');
			addSWLProgramToBundle();
			displaySavedResponse($('#program-seminars'));
			autoScrollToSection($('#program-seminars'));
		} else {
			alert('An error occured while adding this seminar to the bundle.');
		}
	};

	mca_hideAlert('err_addswlprogram');
	var seminarID = $('select#fSWLProgram').val();
	if (seminarID == "") {
		mca_showAlert('err_addswlprogram', 'Select a seminar.');
		return false;
	} else {
		hasInactiveSeminarsIncluded = $('select#fSWLProgram option[value="' + seminarID + '"]').hasClass('text-danger');

		if (hasInactiveSeminarsIncluded && $('#bundleStatusHidden').val() === 'A' && !programAdded) {
			let message = "You cannot have an Active bundle with included programs marked as 'Inactive'. Click OK to mark the bundle as Inactive & to continue editing your Included Programs.";
			// Show confirm pop-up
			MCModalUtils.showModal({
				verticallycentered: true,
				size: 'md',
				title: 'Confirmation Needed',
				strmodalbody: {
					content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>' + message + '</span></div>' 
				},
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-outline-danger ml-auto',
					extrabuttonlabel: 'OK'
				}
			});
			$('#btnMCModalSave').on('click', function(){
				$(this).prop('disabled', true).html('Inactivating...');
				$('#bundleStatus').val('I'); // Update UI display
				validateAndSaveSWBProgram(); // Save the updated status
				$('#btnAddSWLProgram').prop('disabled',true);
				var objParams = { bundleID:sw_bundleid, seminarID:seminarID };
				TS_AJX('ADMINSWB','addSeminarToBundle',objParams,addResult,addResult,10000,addResult);
			});
			$('#MCModal').on('hidden.bs.modal', function() {
				$('#btnMCModalSave').off('click');
			});
		} else {
			$('#btnAddSWLProgram').prop('disabled',true);
			var objParams = { bundleID:sw_bundleid, seminarID:seminarID };
			TS_AJX('ADMINSWB','addSeminarToBundle',objParams,addResult,addResult,10000,addResult);
		}
	}
}
function addSWODProgramToBundle() {
	if (isSWProgramLocked()) return false;

	mca_hideAlert('err_addswodprogram');
	var result = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			var html = '<option value="">-- Choose Seminar --</option>';
			if (r.arrswodprograms.length) {
				var activeOptions = "";
				var InactiveOptions = "";
				for (var i=0; i<r.arrswodprograms.length; i++) {
					if(r.arrswodprograms[i].ispublished)
						activeOptions += '<option value="'+r.arrswodprograms[i].seminarid+'">'+r.arrswodprograms[i].seminarname+'</option>';
					else
						InactiveOptions += '<option value="'+r.arrswodprograms[i].seminarid+'">'+r.arrswodprograms[i].seminarname+'</option>';
				}
				if(activeOptions.length > 0)
					html += '<optgroup label="Active Seminars">' + activeOptions + '</optgroup>';
				if(InactiveOptions.length > 0)
					html += '<optgroup label="Inactive Seminars">' + InactiveOptions + '</optgroup>';
			}
			$('select#fSWODProgram').html(html);
			hideAddSWProgramForms();
			$('#divAddSWODProgramForm').removeClass('d-none');
		} else {
			alert('An error occured while adding this seminar to the bundle.');
		}
	};
	var objParams = { bundleID:sw_bundleid };
	TS_AJX('ADMINSWB','getAvailableSWODSeminarsToAdd',objParams,result,result,10000,result);
}
function doAddSWODProgramToBundle() {
	if (isSWProgramLocked()) return false;
	mca_hideAlert('err_addswodprogram');
	var seminarID = $('select#fSWODProgram').val();
	var addResult = function(r) {
		$('#btnAddSWODProgram').prop('disabled',false);
		if (r.success && r.success.toLowerCase() == 'true') {
			SWBListItemsTable.draw();
			hideAddSWProgramForms();
			$('#divAddSWODProgramForm').removeClass('d-none');
			$('#fSWODProgram').val('');
			addSWODProgramToBundle();
			displaySavedResponse($('#program-seminars'));
			autoScrollToSection($('#program-seminars'));
		} else {
			alert('An error occured while adding this seminar to the bundle.');
		}
	};

	if (seminarID == "") {
		mca_showAlert('err_addswodprogram', 'Select a seminar.');
		return false;
	} else {
		hasInactiveSeminarsIncluded = $('select#fSWODProgram optgroup[label="Inactive Seminars"] option[value="' + seminarID + '"]').length > 0;

		if (hasInactiveSeminarsIncluded && $('#bundleStatusHidden').val() === 'A' && !programAdded) {
			let message = "You cannot have an Active bundle with included programs marked as 'Inactive'. Click OK to mark the bundle as Inactive & to continue editing your Included Programs.";
	
			// Show confirm pop-up
			MCModalUtils.showModal({
				verticallycentered: true,
				size: 'md',
				title: 'Confirmation Needed',
				strmodalbody: {
					content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>' + message + '</span></div>' 
				},
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-outline-danger ml-auto',
					extrabuttonlabel: 'OK'
				}
			});
			$('#btnMCModalSave').on('click', function(){
				MCModalUtils.hideModal();
				$('#bundleStatus').val('I'); // Update UI display
				validateAndSaveSWBProgram(); // Save the updated status
				$('#btnAddSWODProgram').prop('disabled',true);
				var objParams = { bundleID:sw_bundleid, seminarID:seminarID };
				TS_AJX('ADMINSWB','addSeminarToBundle',objParams,addResult,addResult,10000,addResult);
			});
			$('#MCModal').on('hidden.bs.modal', function() {
				$('#btnMCModalSave').off('click');
			});
		} else {
			$('#btnAddSWODProgram').prop('disabled',true);
			var objParams = { bundleID:sw_bundleid, seminarID:seminarID };
			TS_AJX('ADMINSWB','addSeminarToBundle',objParams,addResult,addResult,10000,addResult);
		}
	}
}
function removeSeminarFromBundle(pid) {
	if (isSWProgramLocked()) return false;

	var removeResult = function(r) {
		MCModalUtils.hideModal();
		if (r.success && r.success.toLowerCase() == 'true') {
			SWBListItemsTable.draw();
			displaySavedResponse($('#program-seminars'));
			autoScrollToSection($('#program-seminars'));
			if(bundleType =='OnDemand') {
				$('#divAddSWODProgramForm').removeClass('d-none');
				addSWODProgramToBundle();
			}
			else {
				$('#divAddSWLProgramForm').removeClass('d-none');
				addSWLProgramToBundle();
			}
		} else {
			delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
			alert('An error occured while removing this seminar from this bundle.');
		}
	};

	let delBtn = $('#btnDelSWBSeminar'+pid);
	mca_initConfirmButton(delBtn, function(){
		if (hasIncludedSeminarsCount <= 2 && $('#bundleStatusHidden').val() === 'A' && !programAdded) {
			let message = "You cannot have a bundle with only one program. Click OK to delete this program and mark the bundle as Inactive.";
	
			MCModalUtils.showModal({
				verticallycentered: true,
				size: 'md',
				title: 'Confirmation Needed',
				strmodalbody: {
					content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>' + message + '</span></div>' 
				},
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-outline-danger ml-auto',
					extrabuttonlabel: 'OK'
				}
			});
			$('#btnMCModalSave').on('click', function(){
				$(this).prop('disabled', true).html('Inactivating...');
				top.$('#bundleStatus').val('I'); // UI display update
				validateAndSaveSWBProgram();
				hideAddSWProgramForms();
				var objParams = {  bundleID:sw_bundleid, seminarID:pid };
				TS_AJX('ADMINSWB','removeSeminarFromBundle',objParams,removeResult,removeResult,10000,removeResult);
			});
			$('#MCModal').on('hidden.bs.modal', function() {
				$('#btnMCModalSave').off('click');
				delBtn.attr('data-confirm',0).removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
			});
			
		} else {
			hideAddSWProgramForms();
			var objParams = {  bundleID:sw_bundleid, seminarID:pid };
			TS_AJX('ADMINSWB','removeSeminarFromBundle',objParams,removeResult,removeResult,10000,removeResult);
		}
	});
}

function overrideSWBItemDescription(id) {
	if (isSWProgramLocked()) return false;

	hideAddSWProgramForms();
	var editSWBItemDescription	= function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			$('#divProgramDescriptionForm').removeClass('d-none');
			$('#swbItemID').val(r.itemid);
			$('#swbItemDescription').val(r.itemdescription);
		}
	};
	var objParams = { itemID:id };
	TS_AJX('ADMINSWB','getSWBItemOverrideDescription',objParams,editSWBItemDescription,editSWBItemDescription,10000,editSWBItemDescription);
}
function saveSWBItemDescription() {
	if (isSWProgramLocked()) return false;

	var saveRateGroupResult	= function(r) {
		$('#btnSaveItemDescription').prop('disabled',false);
		if (r.success && r.success.toLowerCase() == 'true'){
			$('#swbItemDescription').val('');
			$('#swbItemID').val(0);
			hideAddSWProgramForms();
			if(bundleType === 'OnDemand')
				$('#divAddSWODProgramForm').removeClass('d-none');
			else $('#divAddSWLProgramForm').removeClass('d-none');
			displaySavedResponse($('#program-seminars'));
			autoScrollToSection($('#program-seminars'));
		} else {
			alert('We were unable to save this description. Try again.');
		}
	};
	$('#btnSaveItemDescription').prop('disabled',true);
	var objParams = { bundleID:sw_bundleid, itemID:$('#swbItemID').val(), itemDescription:$('#swbItemDescription').val() };
	TS_AJX('ADMINSWB','saveSWBItemOverrideDescription',objParams,saveRateGroupResult,saveRateGroupResult,10000,saveRateGroupResult);
}
function loadSWBOptInsTab() {
	if (isSWProgramLocked()) return false;
	
	var partnerResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			var html = '';
			if (r.arroptinsoptouts.length) {
				for (var i=0; i<r.arroptinsoptouts.length; i++) {
					var isSelected = r.arroptinsoptouts[i].isadded === 1 ? 'selected' : '';
					html += '<option value="'+r.arroptinsoptouts[i].orgcode+'" ' + isSelected + ' isEnrollmentExist='+r.arroptinsoptouts[i].isenrollmentexist+'>'+r.arroptinsoptouts[i].description+'</option>';
				}
			}else{
				manageProgramSyndicationWithoutPartner();
			}
			$('select#programPartner').html(html);
			mca_setupSelect2ByID('programPartner');
			changeProgramPartnetEnrollChoiceColor();
		} else {
			alert('An error occured while loading Program Partner. Try again.');
		}
	};
	var objParams = { bundleID:sw_bundleid, programType: sw_itemtype};
	TS_AJX('ADMINSWB','getSWBProgramOptInAndOuts',objParams,partnerResult,partnerResult,10000,partnerResult);
}
function filterSWBProgramRegistrants() {
	if ($('#divFilterForm').hasClass('d-none')) {
		$('div.divSWRegistrantsTool').addClass('d-none');
		$('#divFilterForm').removeClass('d-none');
	}
}
function clearSWBRegFilters() {
	$('#frmRegistrantFilter')[0].reset();
	doFilterSWBProgramRegistrants();
}
function doFilterSWBProgramRegistrants() {
	SWBRegistrantsListTable.draw();
}
function initSWBRegistrants(){
	mca_setupDatePickerRangeFields('rDateFrom','rDateTo');
	mca_setupCalendarIcons('frmRegistrantFilter');
	
	let domString = "<'row'<'col-sm-3 col-md-3'l><'col-sm-9 col-md-9'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";

	SWBRegistrantsListTable = $('#SWBRegistrantsListTable').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 10,
		"lengthMenu": [ 10, 25, 50 ],
		"dom": domString,
		"language": {
			"lengthMenu": "_MENU_",
			"zeroRecords": "No Registrations Found."
		},
		"ajax": { 
			"url": link_swbregistrantslist,
			"type": "post",
			"data": function(d) {
				$.each($('#frmRegistrantFilter').serializeArray(),function() {
					d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
				});
			}
		},
		"autoWidth": false,
		"columns": [
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						if(data.memberid)
							renderData += '<a href="javascript:editMember('+data.memberid+');">'+data.lastname+', '+data.firstname+' ('+data.membernumber+')</a>';
						else
							renderData += data.lastname+', '+data.firstname;
						if (data.company.length)				
							renderData += '<div class="text-dim small">'+data.company+'</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"className": "align-top",
				"width": "35%"
			},
			{ "data": "dateoforder", 'className': 'align-top', "width": "15%"},
			{ "data": "seminarcount", 'className': 'text-center align-top', "width": "20%", "orderable": false },
			{ "data": "amountbilled", 'className': 'text-right align-top', "width": "10%", "orderable": false },
			{ "data": "amountdue", 'className': 'text-right align-top', "width": "10%", "orderable": false },
			{ 
				"data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						if (!data.assocHandlesOwnPayment) {
							if(data.canchangeprice) {
								renderData += '<a href="javascript:changeSWRegistrantPrice('+data.orderid+',\'SWB\');" class="btn btn-xs btn-outline-primary p-1 m-1" title="Change Price"><i class="fa-solid fa-money-bill-alt text-green"></i></a>';
							} else {
								renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-money-bill-alt"></i></a>';
							}
						}
						if(data.candelete) {
							renderData += '<a href="javascript:removeSWBRegistrant('+data.orderid+');" class="btn btn-xs btn-outline-danger p-1 m-1" title="Cancel Registrations"><i class="fa-solid fa-circle-minus"></i></a>';
						} else {
							renderData += '<a href="javascript:void(0)" class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-circle-minus"></i></a>';
						}
					}
					return type === 'display' ? renderData : data;
				},
				"className": "text-center align-top",
				"width": "10%",
				"orderable": false
			}
		],
		"order": [[1, 'desc']],
		"searching": false
	});
}
function doAddSWBNatOptInProgram() {
	if (isSWProgramLocked()) return false;

	var result = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') { loadSWBOptInsTab(); }
		else { alert('Some error occurred while opt-in national program. Please try again.'); }
	};

	mca_hideAlert('err_optIns');
	var programID = Number($('#natProgramID').val());
	if (programID == 0) {
		mca_showAlert('err_optIns', 'Select a program');
		return false;
	}

	var objParams = { bundleID:sw_bundleid, programID:programID };
	TS_AJX('ADMINSWB','optInSWBNationalProgram',objParams,result,result,10000,result);
}
function saveSWBOptIn(data,mode){
	if (isSWProgramLocked()) return false;

	var addResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			changeProgramPartnetEnrollChoiceColor();
		} else {
			alert('An error occured while adding this Program Partner. Try again.');
		}

		//toggleSWProgramPartnerSaveProgress(false,mode);
	};

	//toggleSWProgramPartnerSaveProgress(true,mode);

	var objParams = { orgCodeList:data, bundleID:sw_bundleid};
	TS_AJX('ADMINSWB','optInSWBundle',objParams,addResult,addResult,60000,addResult);
}
function saveSWBOptOut(data,mode){
	if (isSWProgramLocked()) return false;

	var removeResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			if (typeof window['onOptOutOfSyndicatedProgram'] != 'undefined' && typeof window['onOptOutOfSyndicatedProgram'] == 'function') 
				window['onOptOutOfSyndicatedProgram']();
			else {
				var orgArray = data.split(",");
				if(orgArray.length > 1){
					getSWProgramPartners();
				}else{
					changeProgramPartnetEnrollChoiceColor();
				}
			}
		} else {
			alert('An error occured while deleting this Program Partner. Try again.');
		}
		//toggleSWProgramPartnerSaveProgress(false,mode);
	};

	//toggleSWProgramPartnerSaveProgress(true,mode);

	var objParams = { orgCodeList:data, bundleID:sw_bundleid };
	TS_AJX('ADMINSWB','optOutSWBundle',objParams,removeResult,removeResult,20000,removeResult);
}
function viewSWBFeeStructure(title){
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		title: title,
		strmodalbody: { content: $('#mc_SWBFeeStructureHTML').html() }
	});
}
function removeSWBRegistrant(oid) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Cancel Registrations',
		iframe: true,
		contenturl: link_removeswbenrollment + '&oid=' + oid,
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			closebuttonlabel: 'Cancel',
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.doCallRemoveReg',
			extrabuttonlabel: 'Remove Registrations',
		}
	});
}
function doRemoveSWBRegistrant(obj) {
	var removeSWBRegistrantResult = function(r) {
		top.MCModalUtils.hideModal();
		if (r.success && r.success.toLowerCase() == 'true') {
			SWBRegistrantsListTable.draw();
		}
		else {
			alert(r.errmsg && r.errmsg.length ? r.errmsg : 'Unable to remove registrations.');
		}
	};

	var objParams = { orderID:obj.orderID, AROption:obj.AROption, emailRegistrant:obj.emailRegistrant };
	TS_AJX('ADMINSWB','removeEnrollment',objParams,removeSWBRegistrantResult,removeSWBRegistrantResult,20000,removeSWBRegistrantResult);
}

/* SWCP Functions */
function addSWCPProgram() {
	mca_hideAlert('err_swcpaddprogram');
	if ($('#divAddProgramForm').length && $('#divAddProgramForm').hasClass('d-none')) {
		$('#programName,#certificateMessage').val('');
		$('div.divSWCPTool').addClass('d-none');
		$('#divAddProgramForm').removeClass('d-none');
	}
}
function doAddSWCPProgram() {
	var addSWCPProgramResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true' && r.programid){
			self.location.href = link_editswcpprogram + '&pid=' + r.programid;
		} else {
			alert('We were unable to create this Program. Try again.');
			$('#btnAddSWCPProgram').prop('disabled',false);
		}
	};

	mca_hideAlert('err_swcpaddprogram');
	strError = '';
	var programName = $('#programName').val().trim();
	var certificateMessage = $('#certificateMessage').val().trim();
	if (programName == '') {
		strError += '<li>Enter the Program Name.</li>';
	}
	if (certificateMessage == '') {
		strError += '<li>Enter the certificate message.</li>';
	}
	if (strError != '') {
		mca_showAlert('err_swcpaddprogram', '<ul>'+strError+'</ul>');
		return false;
	}

	$('#btnAddSWCPProgram').prop('disabled',true);
	
	var objParams = { programName:programName, certificateMessage:$('#certificateMessage').val().trim() };
	TS_AJX('ADMINSWCP','createSWCPProgram',objParams,addSWCPProgramResult,addSWCPProgramResult,10000,addSWCPProgramResult);
}
function initSWCPProgramsTable(){
	certProgramsTable = $('#certProgramsTable').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 10,
		"lengthMenu": [ 10, 25, 50 ],
		"dom": "<'row'<'col-sm-3 col-md-3'l><'col-sm-9 col-md-9'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
		"ajax": { 
			"url": link_listswcpprograms,
			"type": "post",
			"data": function(d) {
				$.each($('#frmFilter').serializeArray(),function() {
					d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
				});
			}
		},
		"autoWidth": false,
		"columns": [
			{ "data": "programName", "width": "70%", "className": "align-top" },
			{ "data": "publisherOrgCode", "width": "15%", "className": "align-top" },
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						if (data.gridMode.toLowerCase() == 'swsearchgrid') {
							if ($.trim(data.siteHostName).length) {
								renderData += '<a href="http://'+data.siteHostName+'/?pg=admin&jumpToTool=SeminarWebAdmin%7ClistSWCP%7CeditSWCPProgram&pID='+data.programID+'" target="_blank" title="Edit This Program" class="btn btn-xs btn-outline-primary p-1 m-1"><i class="fa-solid fa-pencil"></i></a>';
							} else {
								renderData += '<a class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-pencil"></i></a>';
							}
						} else {
							renderData += '<a href="javascript:editSWCPProgram('+data.programID+');" title="Edit This Program" class="btn btn-xs btn-outline-primary p-1 m-1"><i class="fa-solid fa-pencil"></i></a>';
						}
						if (data.copyProgramRights) {
							if (data.gridMode.toLowerCase() == 'swsearchgrid' || data.publisherOrgCode == sw_sitecode) {
								renderData += '<a href="javascript:copySWCPProgram('+data.programID+');" class="btn btn-xs btn-outline-primary p-1 m-1" title="Copy This Program"><i class="fa-solid fa-copy"></i></a>';
							} else {
								renderData += '<a class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-copy"></i></a>';
							}
						}
						if (data.gridMode.toLowerCase() != 'swsearchgrid') {
							if (data.manageSWCPRegistrantsPublish == 1 || data.manageSWCPRegistrantsAll == 1) {
								renderData += '<a href="javascript:editSWCPRegistrants('+data.programID+');" class="btn btn-xs btn-outline-primary p-1 m-1" title="View Registrants"><i class="fa-solid fa-users"></i></a>';
							} else {
								renderData += '<a href="javascript:void(0);" class="btn btn-xs btn-outline-primary p-1 m-1" title="'+ data.enrolledCount + ' Registrant' + (data.enrolledCount > 1 || data.enrolledCount == 0 ? 's' : '')+'"><i class="fa-solid fa-users"></i></a>';
							}
							renderData += '<span class="badge ml-2 badge-neutral-first text-first" title="'+ data.enrolledCount + ' Registrant' + (data.enrolledCount > 1 || data.enrolledCount == 0 ? 's' : '')+'" style="width:45px;">'+data.enrolledCount+'</span>';
						}
					}
					return type === 'display' ? renderData : data;
				},
				"width": "15%",
				"className": "text-center",
				"orderable": false
			}
		],
		"order": [[0, 'asc']],
		"searching": false
	});
}
function editSWCPProgram(pid) { 
	window.open(link_editswcpprogram + '&pid=' + pid,'_blank');
}
function editSWCPRegistrants(pid){ 
	window.open(link_editswcpprogram + '&pid=' + pid + '&tab=registrants','_blank');
}		
function filterSWCP() {
	if ($('#divFilterForm').hasClass('d-none')) {
		$('div.divSWCPTool').addClass('d-none');
		$('#divFilterForm').removeClass('d-none');
	}
}
function dofilterSWCP() {
	certProgramsTable.draw();
}
function exportSWCP() {
	if ($('#divExportForm').hasClass('d-none')) {
		$('div.divSWCPTool').addClass('d-none');
		$('#divExportFormArea').html('<h5>Download Programs</h5>' + mca_getLoadingHTML());
		$('#divExportForm').removeClass('d-none');
		$('#divExportFormArea').load(link_exportswcpprogram + '&' + $('#frmFilter').serialize());
	}
}
function exportSWCPRegistrants() {
	if ($('#divExportForm').hasClass('d-none')) {
		$('div.divExportForm').addClass('d-none');
		$('#divExportFormArea').html('<h5>Download Registrants</h5>' + mca_getLoadingHTML());
		$('#divExportForm').removeClass('d-none');
		$('#divExportFormArea').load(link_exportswcpregistrants + '&fSearch=' + SWCPRegistrantsListTable.search());
	}
}
function doExportSWCP(u) {
	self.location.href = '/tsdd/' + u;
	$('div.divSWCPTool').addClass('d-none');
}
function doExportSWCPRegistrants(u) {
	self.location.href = '/tsdd/' + u;
	$('#divExportForm').addClass('d-none');
}
function copySWCPProgram(pid) {
	var copySWCPProgramResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			mcg_reloadGrid();
		} else {
			alert('We had a problem copying the program. Try again.');
		}
	};
	var objParams = { programID:pid };
	TS_AJX('ADMINSWCP','copyProgram',objParams,copySWCPProgramResult,copySWCPProgramResult,10000,copySWCPProgramResult);
}
function validateAndSaveSWCPProgram() {
	mca_hideAlert('err_swcpdetails');
	
	if($('#programName').val().trim().length == 0) {
		mca_showAlert('err_swcpdetails', 'Enter the program name.');
		return false;
	}
	
	programName = $('#programName').val().trim();
	pid = $('#programID').val().trim();
	certificateMessage = $('#certificateMessage').val().trim();
	
	var saveSWCPProgramResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			$('#saveResponse').html('<span class="badge badge-success">SAVED SUCCESSFULLY</span>').addClass('text-success').show().fadeOut(10000);
		} else {
			alert('We had a problem saving the program. Try again.');
		}
		$('#btnUpdateSWCPProgram').removeAttr('disabled');
	};
	var objParams = { programID:pid,programName:programName,certificateMessage:certificateMessage };
	TS_AJX('ADMINSWCP','updateSWCPProgramDetails',objParams,saveSWCPProgramResult,saveSWCPProgramResult,10000,saveSWCPProgramResult);
	
	$('#btnUpdateSWCPProgram').prop('disabled',true);
	return false;
}
function hideAddSWCPProgramForms(){
	$('.addSWCPProgramForm').addClass('d-none');
}
function addSWLProgramToCertProgram() {
	var result = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			var html = '<option value="">-- Choose Seminar --</option>';
			if (r.arrswlprograms.length) {
				for (var i=0; i<r.arrswlprograms.length; i++) {
					html += '<option value="'+r.arrswlprograms[i].seminarid+'" '+ (r.arrswlprograms[i].ispublished == 0 ? 'class="text-danger"' : '') +'>'+r.arrswlprograms[i].seminarname+'</option>';
				}
			}
			$('select#fSWLProgram').html(html);
			hideAddSWCPProgramForms();
			$('#divAddSWLProgramForm').removeClass('d-none');
		} else {
			alert('An error occured while loading seminars.');
		}
	};
	var objParams = { programID:sw_programid };
	TS_AJX('ADMINSWCP','getAvailableSWLSeminarsToAdd',objParams,result,result,10000,result);
}
function doAddSWLProgramToCertProgram() {
	var addResult = function(r) {
		$('#btnAddSWLProgram').prop('disabled',false);
		if (r.success && r.success.toLowerCase() == 'true') {
			mcg2_reloadGrid();
			hideAddSWCPProgramForms();
		} else {
			alert('An error occured while adding this seminar to the ' + swcp_brandname + '.');
		}
	};
	mca_hideAlert('err_addswlprogram');
	var seminarID = $('select#fSWLProgram').val();
	if (seminarID == "") {
		mca_showAlert('err_addswlprogram', 'Select a seminar.');
		return false;
	} else {
		$('#btnAddSWLProgram').prop('disabled',true);
		var objParams = { programID:sw_programid, seminarID:seminarID };
		TS_AJX('ADMINSWCP','addSeminarToCertProgram',objParams,addResult,addResult,10000,addResult);
	}
}
function addSWODProgramToCertProgram() {
	var result = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			var html = '<option value="">-- Choose Seminar --</option>';
			if (r.arrswodprograms.length) {
				var activeOptions = "";
				var InactiveOptions = "";
				for (var i=0; i<r.arrswodprograms.length; i++) {
					if(r.arrswodprograms[i].ispublished)
						activeOptions += '<option value="'+r.arrswodprograms[i].seminarid+'">'+r.arrswodprograms[i].seminarname+'</option>';
					else
						InactiveOptions += '<option value="'+r.arrswodprograms[i].seminarid+'">'+r.arrswodprograms[i].seminarname+'</option>';
				}
				if(activeOptions.length > 0)
					html += '<optgroup label="Active Seminars">' + activeOptions + '</optgroup>';
				if(InactiveOptions.length > 0)
					html += '<optgroup label="Inactive Seminars">' + InactiveOptions + '</optgroup>';
			}
			$('select#fSWODProgram').html(html);
			hideAddSWCPProgramForms();
			$('#divAddSWODProgramForm').removeClass('d-none');
		} else {
			alert('An error occured while loading seminars.');
		}
	};
	var objParams = { programID:sw_programid };
	TS_AJX('ADMINSWCP','getAvailableSWODSeminarsToAdd',objParams,result,result,10000,result);
}
function doAddSWODProgramToCertProgram() {
	var addResult = function(r) {
		$('#btnAddSWODProgram').prop('disabled',false);
		if (r.success && r.success.toLowerCase() == 'true') {
			SWCPIncSeminarsListTable.draw(false);
			hideAddSWCPProgramForms();
		} else {
			alert('An error occured while adding this seminar to the ' + swcp_brandname + '.');
		}
	};
	mca_hideAlert('err_addswodprogram');
	var seminarID = $('select#fSWODProgram').val();
	if (seminarID == "") {
		mca_showAlert('err_addswodprogram', 'Select a seminar.');
		return false;
	} else {
		$('#btnAddSWODProgram').prop('disabled',true);
		var objParams = { programID:sw_programid, seminarID:seminarID };
		TS_AJX('ADMINSWCP','addSeminarToCertProgram',objParams,addResult,addResult,10000,addResult);
	}
}
function addSWBProgramToCertProgram() {
	var result = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			var html = '<option value="">-- Choose Bundle --</option>';
			if (r.arrbundles.length) {
				for (var i=0; i<r.arrbundles.length; i++) {
					html += '<option value="'+r.arrbundles[i].bundleid+'">'+r.arrbundles[i].bundlename+'</option>';
				}
			}
			$('select#fSWBProgram').html(html);
			hideAddSWCPProgramForms();
			$('#divAddSWBProgramForm').removeClass('d-none');
		} else {
			alert('An error occured while loading bundles.');
		}
	};
	var objParams = {};
	TS_AJX('ADMINSWCP','getAvailableBundlesToAdd',objParams,result,result,10000,result);
}
function doAddSWBProgramToCertProgram() {
	var addResult = function(r) {
		$('#btnAddSWBProgram').prop('disabled',false);
		if (r.success && r.success.toLowerCase() == 'true') {
			SWCPIncSeminarsListTable.draw(false);
			hideAddSWCPProgramForms();
		} else {
			alert('An error occured while adding this bundle to the ' + swcp_brandname + '.');
		}
	};

	mca_hideAlert('err_addswbprogram');
	var bundleID = $('select#fSWBProgram').val();
	if (bundleID == "") {
		mca_showAlert('err_addswbprogram', 'Select a Bundle.');
		return false;
	} else {
		$('#btnAddSWBProgram').prop('disabled',true);
		var objParams = { programID:sw_programid, bundleID:bundleID };
		TS_AJX('ADMINSWCP','addBundleToCertProgram',objParams,addResult,addResult,10000,addResult);
	}
}
function removeSeminarFromCertProgram(pid) {
	var removeResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			SWCPIncSeminarsListTable.draw();
		} else {
			delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
			alert('Some error occured while removing this seminar. Try again.');
		}
	};

	let delBtn = $('#btnDelIncSeminar'+pid);
	mca_initConfirmButton(delBtn, function(){
		var objParams = { programID:sw_programid, seminarID:pid };
		TS_AJX('ADMINSWCP','removeSeminarFromCertProgram',objParams,removeResult,removeResult,10000,removeResult);
	});
}
function moveSWCPItem(itemID,dir) {
	var moveResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			SWCPIncSeminarsListTable.draw(false);
		} else {
			alert('An error occured while moving '+ dir +' this item.');
		}
	};
	var objParams = { programID:sw_programid, itemID:itemID, dir:dir };
	TS_AJX('ADMINSWCP','doMoveSWCPItem',objParams,moveResult,moveResult,10000,moveResult);
}

function viewSWCPCertificate(pid,did) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Registrant Certificate',
		iframe: true,
		contenturl: link_viewcertificate + '&swtype=SWCP&pID=' + pid + '&dID=' + did,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmSendCert1 :submit").click',
			extrabuttonlabel: 'Send',
			extrabuttoniconclass: 'fa-light fa-share'
		}
	});
}
function initSWCPRegistrants() {
	SWCPRegistrantsListTable = $('#SWCPRegistrantsListTable').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 10,
		"lengthMenu": [ 10, 25, 50 ],
		"language": {
			"lengthMenu": "_MENU_",
			"zeroRecords": "No Registrations Found."
		},
		"ajax": { 
			"url": link_swcpregistrantslist,
			"type": "post"
		},
		"autoWidth": false,
		"columns": [
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						if (data.gridmode == 'reggrid') {
							renderData += '<a title="'+data.programName+'" href="javascript:editSWCPProgram('+data.programID+')">'+data.programName+'</a>';
						} else {
							if(data.membernumber.length > 0) {
								renderData += '<a href="javascript:editMember('+data.memberID+')">'+data.memberName+'</a>';
							} else {
								renderData += '<b>'+data.memberName+'</b>';
							}
							renderData += '<div class="small text-dim">'+data.company+'</div>';
						}
					}
					return type === 'display' ? renderData : data;
				},
				"width": "60%",
				"className": "align-top"
			},
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						if(data.statusText == 'In Progress')
							renderData += '<span class="text-warning mx-1">In Progress</span>';
						else  if(data.statusText == 'Failure')
							renderData += '<span class="text-danger mx-1">Failure</span>';
						else
							renderData += '<span class="text-success mx-1">Success</span>';

						if (data.progressVal.length)
							renderData += ' <span>' + data.progressVal + '</span>';
						if (data.gridmode == 'grid')
							renderData = '<div class="d-flex align-items-center">' + data.progressBar + ' ' + renderData + '</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "30%",
				"className": "align-top",
				"orderable": false 
			},
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<a href="#" class="btn btn-xs btn-outline-primary p-1 m-1" title="View Progress" onclick="viewSWCPProgress('+data.programID+','+data.depomemberdataid+');return false;"><i class="fa-solid fa-eye"></i></a>';
						if(data.canView){
							renderData += '<a href="#" class="btn btn-xs btn-outline-warning p-1 m-1" title="View Certificate" onclick="viewSWCPCertificate('+data.programID+','+data.depomemberdataid+');return false;"><i class="fa-solid fa-certificate"></i></a>';
						} else {
							renderData += '<a href="#" class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-certificate"></i></a>';
						}						
					}
					return type === 'display' ? renderData : data;
				},
				"width": "10%",
				"className": "text-center align-top",
				"orderable": false 
			}
		],
		"order": [[0, 'asc']]
	});
}
function initSWCPIncSeminars() {
	SWCPIncSeminarsListTable = $('#SWCPIncSeminarsListTable').DataTable({
		"processing": true,
		"serverSide": true,
		"paging": false,
		"language": {
			"lengthMenu": "_MENU_",
			"zeroRecords": "No Seminars Found."
		},
		"ajax": { 
			"url": link_swcpincseminarlist,
			"type": "post"
		},
		"autoWidth": false,
		"columns": [
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						renderData += data.contentName;
						if (!data.contentPublished) renderData += '<span class="badge badge-warning ml-2">Inactive</span>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "50%",
				"orderable": false
			},
			{ "data": "contentType", "width": "10%", "orderable": false },
			{ "data": "reqAwardCredit", "width": "20%", "orderable": false },
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<a href="#" class="btn btn-xs btn-outline-primary p-1 m-1" title="Edit Program" onclick="javascript:edit'+ data.contentType + 'Program('+data.contentID+');return false;"><i class="fa-solid fa-pencil"></i></a>';
						renderData += '<a href="#" class="btn btn-xs btn-outline-primary p-1 m-1'+(!data.canmoveup ? ' invisible' : '')+'" title="Move Up" onclick="moveSWCPItem(' +  data.itemID+',\'up\');return false;"><i class="fa-solid fa-arrow-up"></i></a>';
						renderData += '<a href="#" class="btn btn-xs btn-outline-primary p-1 m-1'+(!data.canmovedown ? ' invisible' : '')+'" title="Move Down" onclick="moveSWCPItem(' + data.itemID+',\'down\');return false;"><i class="fa-solid fa-arrow-down"></i></a>';
						renderData += '<a href="#" class="btn btn-xs btn-outline-danger p-1 m-1" id="btnDelIncSeminar'+data.contentID+'" onclick="javascript:removeSeminarFromCertProgram('+data.contentID+');return false;" title="Remove Program"><i class="fa-solid fa-trash-can"></i></a>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "20%",
				"className": "text-center",
				"orderable": false
			}
		],
		"ordering" : false,
		"searching" : false
	});
}

// SW Participants
function filterSWParticipants() {
	if (!$('#divFilterForm').is(':visible')) {
		$('#divFilterForm').show();
	}
}
function doFilterSWParticipants() {	
	swParticipatingAssocListTable.draw();
}
function resetFilterSWParticipants() {
	$('#frmFilter')[0].reset();
	$('#frmFilter [data-toggle="custom-select2"]').trigger('change.select2');
	doFilterSWParticipants();
}

// SW Credit Sponsors
function toggleCreditSponsorGrid(f) {
	$('#divCreditSponsorsGridContainer').toggleClass('d-none', !f);
}
function clearCreditSponsorFormContent() {
	$('#divCreditSponsorsFormContainer').html('').addClass('d-none');
}
function editCreditSponsor(id,tab) {
	toggleCreditSponsorGrid(false);
	var loadHTML = '<h5>'+ (Number(id) == 0 ? 'Add' : 'Edit') +' Credit Sponsor</h5>' + mca_getLoadingHTML();
	var editCreditSponsorLink = sw_editcreditsponsorlink + '&csid='+id;
	if (tab) editCreditSponsorLink += '&tab='+tab;
	$('#divCreditSponsorsFormContainer').html(loadHTML).load(editCreditSponsorLink).removeClass('d-none');
}
function cancelCreditSponsorForm() {
	var gridBox = $('#creditSponsorsTable');
	clearCreditSponsorFormContent();
	toggleCreditSponsorGrid(true);

	if(gridBox.length && gridBox.is(':visible')) {
		$('html, body').animate({scrollTop: gridBox.offset().top - 450}, 750);
	}
	return false;
}
function setCreditSponsorProgramCreditRowBackground(scID, f, locked) {
	if (!locked)
		$('div#credittxt' + scID).toggleClass('bg-neutral-first',f).toggleClass('bg-transparent',!f);
}
function editCreditSponsorProgramCreditRow(scID,locked) {
	if (locked) return false;

	if (lastEditedCreditRow != null) {
		cancelCreditSponsorProgramCreditRow(lastEditedCreditRow);
	}
	lastEditedCreditRow = scID;
	$('div#credittxt' + scID).addClass('d-none');
	$('div#creditfrm' + scID).removeClass('d-none');
}
function cancelCreditSponsorProgramCreditRow(scID) {
	$('div#credittxt' + scID).removeClass('d-none');
	$('div#creditfrm' + scID).addClass('d-none');
}
function saveCreditSponsorProgramCredit(scId) {
	var saveCreditResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			reloadCreditSponsorCreditTab();
		} else {
			alert('We were unable to save this program credit. Try again.');
		}
		$('#btnSave' + scId).attr('disabled', true);
	};

	var objParams = { seminarCreditID:scId };
	var scIdStr = '';
	var inputType = '';
	$('div#creditfrm'+scId+' .frmCredInput'+scId).each(function() {
		scIdStr = '_' + scId + '_';
		inputType = $(this).attr('type');
		if(inputType != 'checkbox' || (inputType == 'checkbox' && $(this).is(':checked'))){
			objParams[$(this).attr('name').replace(scIdStr,'')] = $(this).val();
		}
	});
	$('#btnSave' + scId).attr('disabled', true);
	TS_AJX('ADMINSWCREDIT','saveCreditForSWSeminar',objParams,saveCreditResult,saveCreditResult,10000,saveCreditResult);
}
function gotoNextCreditPage(ft) {
	strCreditTab[ft+'CurrStart'] += maxRows;
	loadCreditsTab(ft);
}
function gotoPreviousCreditPage(ft) {
	strCreditTab[ft+'CurrStart'] -= maxRows;
	loadCreditsTab(ft);
}
function viewCreditSponsorProgramCreditForm(scId) {
	window.open(link_viewSWSeminarCreditForm + '&scId=' + scId);
}
function deleteCreditSponsorProgramCreditForm(scId) {
	var deleteFormResult = function(r) {
		MCModalUtils.hideModal();
		if (r.success && r.success.toLowerCase() == 'true') { reloadCreditSponsorCreditTab(); }
		else { alert('Some error occurred while deleting this seminar credit form. Try again.'); }
	};
	MCModalUtils.showModal({
		verticallycentered: true,
		size: 'md',
		title: 'Confirmation Needed',
		strmodalbody: {
			content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>Are you sure you want to delete this form?</span></div>'
		},
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-outline-danger ml-auto',
			extrabuttonlabel: 'Confirm'
		}
	});
	$('#btnMCModalSave').on('click', function(){
		$(this).prop('disabled', true).html('Deleting...');
		var objParams = { seminarCreditID:scId };
		TS_AJX('ADMINSWCREDIT','deleteSeminarCreditForm',objParams,deleteFormResult,deleteFormResult,10000,deleteFormResult);
	});
	$('#MCModal').on('hidden.bs.modal', function() {
		$('#btnMCModalSave').off('click');
	});
}
function uploadCreditSponsorProgramCreditForm(scId) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'md',
		title: 'Upload Form 1 PDF file',
		iframe: true,
		contenturl: link_getSWSeminarCreditFormUpload + '&scId=' + scId,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#uploadFileBtn").click',
			extrabuttonlabel: 'Upload PDF',
			extrabuttoniconclass: 'fa-light fa-file-arrow-up'
		}
	});

}
function forceCreditSponsorProgramCreditPreselect(reqCr,preCrId) {
	if (reqCr.checked) $('#'+preCrId).prop("checked",true);
}

/* SW Featured Programs common functions*/
function addToFeaturedPrograms(programID){ toggleFeaturedProgram(programID, 1) }
function removeFromFeaturedPrograms(programID){ toggleFeaturedProgram(programID, 0) }
function toggleFeaturedProgram(programID, isFeatured){
	var toggleResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			$('span#featuredBadgeContainer_' + programID).html(isFeatured ? '<span class="badge badge-info">Featured</span>' : '');
			var thisLink = $('i.swfeatured_icon_' + programID).closest('a');
			thisLink.attr("onclick", (isFeatured ? 'removeFromFeaturedPrograms' : 'addToFeaturedPrograms') + '('+programID+');return false;')
				.attr("title", (isFeatured ? 'Remove from Featured Programs' : 'Add to Featured Programs'))
				.toggleClass('btn-outline-dark', !isFeatured)
				.toggleClass('btn-outline-first', isFeatured);
		} else {
			alert('An error occured while ' + (isFeatured ? 'adding this to' : 'removing this from') + ' featured programs.');
		}
	};

	var objParams = { programType:sw_itemtype, programID:programID, isFeatured:isFeatured };
	TS_AJX('ADMINSWCOMMON','toggleFeaturedProgram',objParams,toggleResult,toggleResult,10000,toggleResult);
}

/* SW Mark As Fee Exempt common functions*/
function markAsFeeExempt(enrollmentID,ft){ toggleFeeExempt(enrollmentID, 1,ft); }
function unMarkAsFeeExempt(enrollmentID,ft){ toggleFeeExempt(enrollmentID, 0,ft); }
function toggleFeeExempt(enrollmentID, isFeeExempt,ft){
	MCModalUtils.showModal({
		isslideout: true,
		size: 'md',
		title: (isFeeExempt ? 'Mark' : 'Unmark')+' Registrant as Fee Exempt',
		strmodalbody: {
			content: '<div class="alert alert-info"><b>Confirmation Needed</b><br/>Are you sure you want to '+(isFeeExempt ? 'mark' : 'unmark')+' this registrant as fee exempt?<br/><br/><button type="button" id="btnToggleFeeExempt" class="btn btn-sm btn-outline-primary" onClick="doToggleFeeExempt('+enrollmentID+','+isFeeExempt+',\''+ft+'\');"><i class="fa-light fa-arrow-alt-right"></i> Continue</button></div>'
		},
		strmodalfooter : {
			classlist: 'd-none'
		}
	});
}
function doToggleFeeExempt(enrollmentID, isFeeExempt, ft){
	var toggleResult = function(r) {
		MCModalUtils.hideModal();
		if (r.success && r.success.toLowerCase() == 'true') {
			if (ft == 'SWL') {
				SWLRegistrantsListTable.draw(false);
			} else if (ft == 'SWOD') {
				SWODRegistrantsListTable.draw(false);
			}
		} else {
			alert('An error occured while marking/unmarking the registrant as fee exempt.');
		}
	};
	var objParams = { enrollmentID:enrollmentID, isFeeExempt:isFeeExempt, programType:sw_itemtype };
	TS_AJX('ADMINSWCOMMON','toggleFeeExempt',objParams,toggleResult,toggleResult,10000,toggleResult);
}

/* SW Learning Objectives common functions */
function editSWObjective(objID){
	
	if (isSWProgramLocked()) return false;

	var objStr = objID > 0 ? learningObjectivesTable.row('#row_'+objID).data().objective : '';
	$('#btnSaveObjective, #btnCancelObjective').removeClass('d-none');
	$('#btnAddObjective').addClass('d-none');
	
	$('#learnObjective').val(objStr).removeClass('d-none');
	$('#objectiveID').val(objID);
}
function resetSWObjectiveButtons(){
	$('#btnSaveObjective, #btnCancelObjective, #learnObjective').addClass('d-none');
	$('#btnAddObjective').removeClass('d-none');
}
function removeSWObjectiveFromProgram(objID) {
	if (isSWProgramLocked()) return false;

	var removeResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			reloadLearningObjectivesTable();
		} else {
			delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
			alert('We were unable to remove this Learning Objective.');
		}
	};

	let delBtn = $('#btnDelLo'+objID);
	mca_initConfirmButton(delBtn, function(){
		var objParams = { programType:sw_itemtype, programID:getSWProgramID(), objectiveID:objID };
		TS_AJX('ADMINSWCOMMON','removeLearningObjective',objParams,removeResult,removeResult,10000,removeResult);
	});
}
function saveSWObjective(){
	if (isSWProgramLocked()) return false;

	var objID = $('#objectiveID').val();
	var objectiveStr = $('#learnObjective').val();
	var saveResult	= function(r) {
		$('#btnSaveObjective').attr('disabled',false);
		if (r.success && r.success.toLowerCase() == 'true'){
			reloadLearningObjectivesTable();
			resetSWObjectiveButtons();
		} else {
			alert('We were unable to save this Objective. Try again.');
		}
	};
	
	if(objectiveStr == ''){
		alert('Enter the objective');
		return false;
	}

	$('#btnSaveObjective').attr('disabled',true);
	var objParams = { objectiveID:objID, objective:objectiveStr, programType:sw_itemtype, programID:getSWProgramID() };
	TS_AJX('ADMINSWCOMMON','saveLearningObjective',objParams,saveResult,saveResult,10000,saveResult);
}
function reloadLearningObjectivesTable(){
	learningObjectivesTable.draw();
}
function initLearningObjectivesTable(){
	learningObjectivesTable = $('#learningObjectivesTable').DataTable({
		"processing": true,
		"serverSide": true,
		"paging": false,
		"info": false,
		"language": {
			"lengthMenu": "_MENU_"
		},
		"ajax": { 
			"url": link_learningObjectives,
			"type": "post",
			"data": function(d) { 
				if (window.reorderData && window.reorderData.length > 0) { 
					d.reorderData = JSON.stringify(window.reorderData); 
					window.reorderData = [];
				} 
				return d; 
			}
		},
		"autoWidth": false,
		"columns": [
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<i class="fa-light fa-bars"></i>';
						
					}
					return type === 'display' ? renderData : data;
				},
				"width": "5%",
				"orderable": false
			},
			{ "data": "objective", className: "align-top",  "orderable": false},
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display' && !data.isSWProgramLocked) {
						renderData += '<a href="" class="btn btn-xs text-primary p-1 m-1 '+(!data.isreadonly ? ' invisible' : '')+'" title="Edit Objective" onclick="editSWObjective('+data.objectiveid+');return false;"><i class="fa-solid fa-pencil"></i></a>';
						renderData += '<a href="" id="btnDelLo'+data.objectiveid+'" class="btn btn-xs text-danger p-1 m-1 '+(!data.isreadonly ? ' invisible' : '')+'" title="Delete Objective" onclick="removeSWObjectiveFromProgram('+data.objectiveid+');return false;" data-confirm="0"><i class="fa-solid fa-trash-can"></i></a>';
					}
					return type === 'display' ? renderData : data;
				},
				"width": "18%",
				"className": "text-center",
				"orderable": false
			}
		],
		"ordering": false,
		"rowReorder": {
				dataSrc: "columnid" 
			},
		"searching": false
	});
	learningObjectivesTable.on('row-reorder', function (e, diff, edit) {
		let orderData = [];
		diff.forEach(function(item){
			orderData.push({
				id: learningObjectivesTable.row(item.node).data().objectiveid,
				newOrder: item.newPosition
			});
		});
		window.reorderData = orderData;
	});
}

/* SW Video Previews */
function getSWProgramVideoPreviews() {
	$('#divManageVideoPreviews').addClass('d-none');
	$('#divVideoPreviewsLoading').removeClass('d-none');
	var objParams = { programType:sw_itemtype, programID:getSWProgramID() };
	$.getJSON('/?event=proxy.ts_json&c=ADMINSWCOMMON&m=getProgramVideoPreviews', objParams)
		.done(populateSWProgramVideoPreviews)
		.fail(populateSWProgramVideoPreviews);
}
function populateSWProgramVideoPreviews(objResult) {
	var source = $('#mc_swprogramvideopreview_template').html();
	var template = Handlebars.compile(source);
	$('#divProgramVideoPreviewList').html(template(objResult));

	if (objResult.success) {
		if (objResult.arrprogramvideopreviews.length)
			$('#btnAddVideoPreview').addClass('d-none');
		else 
			$('#btnAddVideoPreview').removeClass('d-none');
	}

	$('#divVideoPreviewsLoading').addClass('d-none');
	$('#divManageVideoPreviews').removeClass('d-none');
}
function createVideoPreview() {
	if (isSWProgramLocked()) return false;

	$('#prevVideoFileID').val(0);
	$('#prevVideoStartTime,#prevVideoEndTime').val('');
	if(!$('#btnAddVideoPreview').prop('checked'))
		$('#divVideoPreviewForm').addClass('d-none');
	else
		$('#divVideoPreviewForm').removeClass('d-none');
}
function returnToVideoPreviews(t) {
	$('#btnAddVideoPreview').prop('checked',true);
	$('#divVideoPreviewForm').addClass('d-none');
	hideAndPauseSWVideoPlayer();
	if (typeof t !== 'undefined') 
		$('#btnAddVideoPreview').prop('checked', !$('#btnAddVideoPreview').prop('checked'));
	else
		$('#btnAddVideoPreview').prop('disabled',true);
}
function saveVideoPreview(){
	if (isSWProgramLocked()) return false;

	var saveVideoPreviewResult	= function(r) {
		$('#btnSaveVideoPreview').prop('disabled',false);
		if (r.success && r.success.toLowerCase() == 'true'){
			returnToVideoPreviews();
			getSWProgramVideoPreviews();
		} else {
			alert('We were unable to add this video preview. Try again.');
		}
	};

	var arrReq = [];
	var fileID = $('#prevVideoFileID').val();
	var startTimeCode = $('#prevVideoStartTime').val().trim();
	var endTimeCode = $('#prevVideoEndTime').val().trim();

	if (fileID == 0) arrReq.push('Select a Video File.');
	if (startTimeCode == '') arrReq.push('Enter the start time.');
	if (endTimeCode == '') arrReq.push('Enter the end time.');
	
	if(arrReq.length){
		alert(arrReq.join('\n'));
		return false;
	}

	$('#btnSaveVideoPreview').prop('disabled',true);

	var objParams = { programType:sw_itemtype, programID:getSWProgramID(), fileID:fileID, startTimeCode:startTimeCode, endTimeCode:endTimeCode };
	TS_AJX('ADMINSWCOMMON','saveVideoPreview',objParams,saveVideoPreviewResult,saveVideoPreviewResult,10000,saveVideoPreviewResult);
}
function deleteVideoPreview(id) {
	if (isSWProgramLocked()) return false;

	var deleteResult = function(r) {
		MCModalUtils.hideModal();
		if (r.success && r.success.toLowerCase() == 'true') {
			$('#swVideoPreviewList').remove();
			$('#btnAddVideoPreview').removeClass('d-none');
			$('#btnAddVideoPreview').prop('checked', !$('#btnAddVideoPreview').prop('checked'));
			$('#btnAddVideoPreview').prop('disabled', false);
		} else {
			alert('An error occured while removing this video preview.');
		}
	};
	
	MCModalUtils.showModal({
		verticallycentered: true,
		size: 'md',
		title: 'Confirmation Needed',
		strmodalbody: {
			content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>Are you sure you want to remove this video preview?</span></div>'
		},
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-outline-danger ml-auto',
			extrabuttonlabel: 'Confirm'
		}
	});
	$('#btnMCModalSave').on('click', function(){
		$(this).prop('disabled', true).html('Deleting...');
		var objParams = { programType:sw_itemtype, programID:getSWProgramID(), previewID:id };
		TS_AJX('ADMINSWCOMMON','deleteVideoPreview',objParams,deleteResult,deleteResult,10000,deleteResult);
	});
	$('#MCModal').on('hidden.bs.modal', function() {
		$('#btnMCModalSave').off('click');
	});
}
function getSWVideoLink() {
	var getSWVideoLinkResult	= function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			SWVideoPlayer.src(r.videopreviewlink);
			$('#divSWOriginalVideo').removeClass('d-none');
		} else {
			alert('We were unable to get this video link. Try again.');
			hideAndPauseSWVideoPlayer();
		}
	};

	var fileID = $('#prevVideoFileID').val();
	if (fileID > 0) {
		var objParams = { fileID:fileID };
		TS_AJX('ADMINSWFILE','getVideoPreviewLinkFromFileID',objParams,getSWVideoLinkResult,getSWVideoLinkResult,20000,getSWVideoLinkResult);
	} else {
		hideAndPauseSWVideoPlayer();
	}
}
function hideAndPauseSWVideoPlayer() {
	$('#divSWOriginalVideo').addClass('d-none');
	try { SWVideoPlayer.pause(); } catch(e) {};
}