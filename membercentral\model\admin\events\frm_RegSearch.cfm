<cfsavecontent variable="local.js">
	<cfoutput>
	<script language="javascript">
		var mcma_hasrights_refundpmt = "1"; /* Bypassing the JS method; a read-only form will be shown in this case */
		var #ToScript(local.refundPaymentURL,"mcma_link_refpayment")#
		let evRegistrantsTable;

		function initializeEventRegistrantsTable(){
			let domString = "<'row'<'col-sm-12 col-md-5'<'row'<'col-auto'l><'col-sm-12 col-md pl-md-0'i>>><'col-sm-12 col-md-7'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";

			evRegistrantsTable = $('##evRegistrantsTable').DataTable({
				"processing": true,
				"serverSide": true,
				"pageLength": 50,
				"lengthMenu": [ 10, 25, 50, 100 ],
				"dom": domString,
				"language": {
					"lengthMenu": "_MENU_"
				},
				"ajax": { 
					"url": "#local.eventRegistrantsLink#",
					"type": "post",
					"data": function(d) {
						$.each($('##frmFilter').serializeArray(),function() {
							d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
						});
					}
				},
				"autoWidth": false,
				"columns": [
					{
						"data": null,
						"render": function (data, type) {
							let renderData = '';
							if (type === 'display') {
								if (data.status == "D") renderData += '<div><span class="badge badge-danger">Deleted</span></div>';
								renderData += '<div><a href="javascript:editMember('+data.memberid+')">'+data.lastname+', '+data.firstname+' ('+data.membernumber+')</a>';
								if(data.isflagged) renderData += '<i class="fa-solid fa-flag fa-sm text-danger ml-2"></i>';
								renderData += '</div>';
								if (data.company.length) renderData += '<div class="small text-dim">'+data.company+'</div>';
								renderData += '<div class="small text-dim">Event: '+data.eventtitle+'</div>';
								if(data.regrate.length) renderData += '<div class="small text-dim">Rate: '+data.regrate+'</div>';
							}
							return type === 'display' ? renderData : data;
						},
						"width": "40%",
						'className': 'align-top'
					},
					{
						"data": null, 
						"render": function (data, type) {
							return type === 'display' ? '<span title="'+data.registereddatetime+'">'+data.registereddate+'</span>' : data;
						},
						"width": "10%",
						'className': 'align-top'
					},
					{ "data": "attendedinfo", "width": "10%", 'className': 'align-top', "orderable": false },
					{ "data": "totalregfee", "width": "10%", 'className': 'align-top', "orderable": false },
					{ "data": "amountduedisplay", "width": "10%", 'className': 'align-top', "orderable": false },
					{ 
						"data": null,
						"render": function (data, type, row, meta) {
							let renderData = '';
							if (type === 'display') {
								var arrGridAction = [
									{ title:"Pay for Registration", btnClass:"btn-outline-green", iconClass: "fa-money-bill", isVisible:data.amountdue > 0, onClickFnCall:'addPayment(\''+data.addpaymentencstring+'\')' },
									{ title:"Edit This Registrant's Credit", btnClass:"btn-outline-warning", iconClass: "fa-file-certificate", isVisible:data.eventstatus == 'A', onClickFnCall:'manageSubEventAC('+data.registrantid+','+data.eventid+')' },
									{ title:"Print This Registration", btnClass:"btn-outline-dark", iconClass: "fa-print", isVisible:true, onClickFnCall:'printRegistrant('+data.registrantid+','+data.memberid+','+data.eventid+','+data.calendarid+')' },
									{ title:"Resend Email Confirmation", btnClass:"btn-outline-primary", iconClass: "fa-envelope", isVisible:true, onClickFnCall:'sendConfirmation('+data.registrantid+','+data.memberid+','+data.eventid+','+data.calendarid+')' },
									{ title:"Edit This Registration", btnClass:"btn-outline-primary", iconClass: "fa-pencil", isVisible:true, onClickFnCall:'editRegistrant('+data.registrantid+','+data.memberid+','+data.eventid+')' },
									<cfif local.badgePrinterEnabled AND local.hasBadgeDevices AND local.qryBadgeTemplates.recordcount>
										{ title:"Print This Registrant's Badge", btnClass:"btn-outline-dark", iconClass: "fa-badge", isVisible:true, onClickFnCall:'printBadgeRegistrant('+data.registrantid+','+data.eventid+')' },
									</cfif>
									{ title:"Remove This Registration", btnClass:"btn-outline-danger", iconClass: "fa-circle-minus", isVisible:true, onClickFnCall:'removeRegistrant('+data.registrantid+')' }
								];
								
								if(data.canedit) {
									$.each(arrGridAction, function(index, item) {
										renderData += '<a href="##" class="btn btn-xs '+(item.isVisible ? item.btnClass : '')+' p-1 m-1'+ (!item.isVisible ? ' text-muted disabled' : '') +'" '+ (item.isVisible ? 'onclick="'+item.onClickFnCall+';return false;" title="'+item.title+'"' : '') +'><i class="fa-solid '+item.iconClass+'"></i></a>';
									});
								} else {
									$.each(arrGridAction, function(index, item) {
										renderData += '<a href="##" class="btn btn-xs px-1 m-1 invisible"><i class="fa-solid '+item.iconClass+'"></i></a>';
									});
								}
							}
							return type === 'display' ? renderData : data;
						},
						"width": "20%",
						"className": "text-center align-top",
						"orderable": false
					}
				],
				"order": [[1, 'desc']],
				"searching": false
			});
		}

		function filterRegistrants() {
			if (!$('##divFilterForm').is(':visible')) {
				$('##divFilterForm').show();
			}
		}
		function dofilterRegistrants() {
			if (validateFields()) {
				$('##evgriddates').html($('##rDateFrom').val() + ' to ' + $('##rDateTo').val());
				evRegistrantsTable.draw();
			}
		}
		function quickEV(d) {
			var now = new Date();
			var thisDate = new Date();
			var todayDate = new Date(thisDate.getFullYear(), thisDate.getMonth(), thisDate.getDate());

			var n = d * 1;
			if(n >= 0){
				$('##evDateFrom').val($.datepicker.formatDate('m/d/yy', todayDate));
				todayDate.setDate(now.getDate()+n);
				$('##evDateTo').val($.datepicker.formatDate('m/d/yy', todayDate));
			}else{
				n = d * -1;
				$('##evDateTo').val($.datepicker.formatDate('m/d/yy', todayDate));
				todayDate.setDate(now.getDate()-n);
				$('##evDateFrom').val($.datepicker.formatDate('m/d/yy', todayDate));
			} 
			dofilterRegistrants();
		}	
		function quickEVR(d) {
			var now = new Date();
			var thisDate = new Date();
			var todayDate = new Date(thisDate.getFullYear(), thisDate.getMonth(), thisDate.getDate());

			var n = d * 1;
			if(n >= 0){
				$('##rDateFrom').val($.datepicker.formatDate('m/d/yy', todayDate));
				todayDate.setDate(now.getDate()+n);
				$('##rDateTo').val($.datepicker.formatDate('m/d/yy', todayDate));
			}else{
				n = d * -1;
				$('##rDateTo').val($.datepicker.formatDate('m/d/yy', todayDate));
				todayDate.setDate(now.getDate()-n);
				$('##rDateFrom').val($.datepicker.formatDate('m/d/yy', todayDate));
			} 
			$('##evgriddates').html($('##rDateFrom').val() + ' to ' + $('##rDateTo').val());
			dofilterRegistrants();
		}	
		function chainedSelect(elemIdName,elemIdDefault,elemValueDefault,selected,format) {
			var strURL = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mode=stream&pg=admin&mca_jsonlib=events&mca_jsonfunc=getAllCategoriesForCalendar&calid="+ selected;
			$.ajax({
				url: strURL,
				dataType: 'json',
				success: function(response){
					$('##' + elemIdName).empty();
					for (var i = 0; i < response.DATA.length; i++) {
						var o = new Option(response.DATA[i][1], response.DATA[i][0]);
						$(o).html(response.DATA[i][1]);
						$('##' + elemIdName).append(o);
					}
				},
				error: function(ErrorMsg){ }
			});
		}	
		function callChainedSelect(evCalendar,evCategory) {
			var strSelected = $("##" + evCalendar).val();
			chainedSelect(
				evCategory,			/* select box id  */
				0,					/* select box default value */
				'Select Options',	/* select box default text */
				strSelected,		/* value of the select */
				'json'				/* return format */
			);
		}
		function getExportRegistrantsLink(fsid,swcfid,qid,rqid) {
			var exportRegLink = '#local.exportRegSearchLink#&fsid=' + fsid + (qid ? '&qid=' + qid : '') + (swcfid ? '&swcfid=' + swcfid : '') + (rqid ? '&rqid=' + rqid : '') + '&' + $('##frmFilter').serialize();
			return exportRegLink;
		}		
		function exportRegistrants() {
			if (validateFields()) {
				$('div.divEVTool').hide();
				MCModalUtils.showModal({
					isslideout: true,
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					size: 'lg',
					title: 'Download Filtered Registrants',
					iframe: true,
					contenturl: '#local.exportRegPromptLink#',
					strmodalfooter: {
						classlist: 'text-right',
						showclose: false,
						showextrabutton: true,
						extrabuttonclass: 'btn-primary',
						extrabuttononclickhandler: 'exportRegistrantsButtonHandler',
						extrabuttonlabel: 'Download CSV',
						extrabuttoniconclass: 'fa-light fa-file-csv'
					}
				});
			}
		}
		function exportRegistrantsButtonHandler(){
			$('##MCModalBodyIframe')[0].contentWindow.fnDnReg();
		}
		function selectMemberInvFilter() {
			var selhref = '#local.memSelectGotoLink#&mode=direct&fldName=rAssociatedMemberID&dispTitle=';
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'Filter Registrants by Member',
				iframe: true,
				contenturl: selhref,
				strmodalfooter : {
					classlist: 'd-none',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '',
					extrabuttonlabel: 'Submit',
				}
			});
		}
		function selectGroupInvFilter() {
			var selhref = '#local.grpSelectGotoLink#&mode=direct&fldName=rAssociatedGroupID&retFunction=top.updateGroupField&dispTitle=' + escape('Filter Registrants by Group');

			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'Filter Registrants by Group',
				iframe: true,
				contenturl: selhref,
				strmodalfooter: {
					classlist: 'd-none'
				}
			});
		}
		function updateField(fldID, mID, mNum, mName) {
			var fld = $('##'+fldID);
			var fldName = $('##rAssociatedVal');
			fld.val(mID);
			if ((mName.length > 0) && (mNum.length > 0)) {
				fldName.html('<b>' + mName + ' (' + mNum + ')</b>&nbsp;');
				$('##rAssociatedGroupID').val("");
				$('##divAssociatedVal').show();
			} else {
				fldName.html('');
				$('##divAssociatedVal').hide();
			}
		}
		function updateGroupField(fldID,gID,gPath) {
			var fld = $('##'+fldID);
			var fldName = $('##rAssociatedVal');	
			fld.val(gID);
			if (gPath.length > 0) {
				var newgPath = gPath.split("\\");
					newgPath.shift();
					newgPath = newgPath.join(" \\ ");
				fldName.html('<b>' + newgPath + '</b>&nbsp;');
				$('##associatedMemberID').val("");
				$('##divAssociatedVal').show();
			} else {
				fldName.html('');
				$('##divAssociatedVal').hide();
			}
		}
		function changeAssocType() {
			var assocType = $('input:radio[name=rAssocType]:checked').val();
			if ( assocType != undefined) {
				if (assocType == "group") selectGroupInvFilter();
				else selectMemberInvFilter();
			}
		}
		function clearAssocType() {
			$(".rAssocType").each(function(){
				$(this).attr("checked",false);
			});
			$('##rAssociatedVal').html("");
			$('##rAssociatedMemberID').val("");
			$('##rAssociatedGroupID').val("");
			$('##divAssociatedVal').hide();
		}
		function rFormatCurrency(el) {
			if (el.val() != '') el.val(formatCurrency(el.val()));
		}
		#local.strFieldFilters.fieldSelectJS#

		function closeBox() { MCModalUtils.hideModal(); }

		function editMember(mid) {
			window.open('#this.link.editMember#&memberID=' + mid);
		}
		function addPayment(po) {
			mca_addPayment(po,'#this.link.addPayment#');
		}
		function closeAddPayment(po) { 
			reloadPage(); 
			<cfif local.myRightsTransactionsAdmin.transAllocatePayment is not 1>
				MCModalUtils.hideModal();
			<cfelse>
				allocIndivPayment(po);
			</cfif>
		}
		function allocIndivPayment(po) {
			mca_allocIndivPayment(po,'#this.link.allocatePayment#');
		}
		function closeAllocPayment() { reloadPage(); MCModalUtils.hideModal(); }
		function manageSubEventAC(rid,eid) {
			MCModalUtils.showModal({
				isslideout: true,
				size: 'lg',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				title: 'Manage Attendance and Credits',
				iframe: true,
				contenturl: '#this.link.manageSubEventAttendanceCredit#&eid=' + eid + (rid ? '&_rid=' + rid : '')
			});
		}
		function printRegistrant(rid,mid,eid,cid) {
			MCModalUtils.showModal({
				isslideout: true,
				size: 'lg',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				title: 'Print Registrant',
				iframe: true,
				contenturl: '#this.link.printReg#&cID='+ cid + '&eID='+ eid +'&mid=' + mid + '&registrantID=' + rid
			});
		}
		<cfif local.badgePrinterEnabled>
			function printBadgeRegistrant(rid,eid){
				MCModalUtils.showModal({
						isslideout: true,
						size: 'lg',
						modaloptions: {
							backdrop: 'static',
							keyboard: false
						},
						title: 'Print Registrant Badge',
						iframe: true,
						contenturl: '#this.link.printBadgeRegistrant#&eid=' + eid + '&registrantID=' + rid,
						strmodalfooter: {
							classlist: 'd-none'
						}
					});

			}
		</cfif>
		function sendConfirmation(rid,mid,eid,cid) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Resend Registration Confirmation',
				iframe: true,
				contenturl: '#this.link.sendConfirmation#&cID='+ cid +'&eID='+ eid +'&mid=' + mid + '&registrantID=' + rid,
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmSendConf :submit").click',
					extrabuttonlabel: 'Resend Confirmation',
					extrabuttoniconclass: 'fa-light fa-share'
				}
			});
		}
		function editRegistrant(rid,mid,eid) {
			MCModalUtils.showModal({
				isslideout: true,
				size: 'xl',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				title: 'Edit Registrant',
				iframe: true,
				contenturl: '#replace(replace(this.link.addReg,"bc=e","bc=rs"),"bc=c","bc=rs")#&mode=direct&eID=' + eid + '&mid=' + mid + '&registrantID=' + rid,
				strmodalfooter : {
					classlist: 'd-none'
				}
			});
		}
		function returnToRegistration(cid,eid,rid,mid){
			MCModalUtils.hideModal();
			$('.modal-backdrop').remove();
				editRegistrant(rid,mid,eid);	
		}
		function closeUpdateRegistration() { 
			evRegistrantsTable.draw(true);
			MCModalUtils.hideModal();
			
		}
		function removeRegistrant(rid) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Remove Registrant Confirmation',
				iframe: true,
				contenturl: '#this.link.removeRegistrant#&mode=direct&registrantID=' + rid,
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.doCallRemoveReg',
					extrabuttonlabel: 'Remove Registrant'
				}
			});
		}
		function doRemoveReg(objParams) {
			var removeData = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){ 
					evRegistrantsTable.draw(false); 
					if(r.showrefund && r.showrefund.toString() == 'true'){
						showRefundPaymentOnRegRemoveSuccess(objParams.registrantMemberID);
					} else{
						MCModalUtils.hideModal();
					}
				} else { 
					alert('Unable to Remove Registrant - We were unable to remove this registrant. Contact MemberCentral for assistance.');
				}
			};
			
			TS_AJX('ADMINEVENT','removeRegistrant',objParams,removeData,removeData,10000,removeData);
		}
		function showRefundPaymentOnRegRemoveSuccess(memberID){
			$('##MCModal').on('hidden.bs.modal', function() { refundPayment(memberID); });
			MCModalUtils.hideModal();
		}
		function refundPayment(mid,ptid) {
			if (Number(mcma_hasrights_refundpmt) == 1) {
				MCModalUtils.showModal({
					isslideout: true,
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					size: 'lg',
					title: 'Issue Refund',
					iframe: true,
					contenturl: mcma_link_refpayment+'&mid=' + mid + (typeof ptid == 'undefined' ? '' : '&ptid=' + ptid)
				});
			}
		}
		function reloadPage() { evRegistrantsTable.draw(); }
		function reloadEventGrid() { reloadPage(); }
		
		function emailRegistrants() {
			if(evRegistrantsTable.page.info().recordsTotal == 0) {
				alert('There are no registrants to act upon.');
				return false;
			}
			var emailLink = '#local.massEmailRegistrants#&' + $('##frmFilter').serialize();
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'E-mail Filtered Registrants',
				iframe: true,
				contenturl: emailLink,
				strmodalfooter: {
					classlist: 'd-none'
				}
			});
		}

		$(function() {
			mca_setupDatePickerRangeFields('evDateFrom','evDateTo');
			mca_setupDatePickerRangeFields('rDateFrom','rDateTo');
			mca_setupCalendarIcons('frmFilter');

			if($('select##rEvRole').length) {
				mca_setupSelect2();
			}
			$('body').on('change', '##evCalendar', function(e) {
				callChainedSelect("evCalendar","evCategory");
			});

			initializeEventRegistrantsTable();
		});
	</script>
	<style type="text/css">
		div.bn { padding:5px 0px; } 
		div.bn2 { padding-top: 2px; }
		div##evRegistrantsTable_wrapper div##evRegistrantsTable_info { padding-top: 0.25em; }
		span.rfinvalid { color:##f00; font-weight:bold; padding-left:10px; }
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.js)#">

<cfoutput>
<h4>Registrant Search</h4>

<!--- button bar --->
<div class="toolButtonBar">
	<div><a href="javascript:filterRegistrants();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter registrants."><i class="fa-regular fa-filter"></i> Filter Registrants</a></div>
	<div><a href="javascript:exportRegistrants();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to download registrants."><i class="fa-regular fa-download"></i> Download Registrants</a></div>
	<div><a href="##" onclick="emailRegistrants();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to email filtered registrants."><i class="fa-regular fa-envelope"></i> Email Registrants</a></div>
</div>
<div id="divFilterForm" style="display:none;">
	<form name="frmFilter" id="frmFilter" onsubmit="dofilterRegistrants();return false;">
	<div class="row">
		<div class="col">
			<div class="card card-box">
				<div class="card-header py-1 bg-light">
					<div class="card-header--title font-weight-bold font-size-lg">Registrant Search</div>
				</div>
				<div class="card-body">
					<div class="row">
						<div class="col-sm-12 font-weight-bold">Event Filters</div>
					</div>
					<div class="row mt-2">
						<div class="col-md-6">
							<div class="card card-box h-100">
								<div class="card card-body p-3">
									<div class="form-row">
										<div class="col-xl-6 col-md-12">
											<div class="form-group">
												<div class="form-label-group mb-2">
													<div class="input-group dateFieldHolder">
														<input type="text" name="evDateFrom" id="evDateFrom" value="#local.evDateFrom#" class="form-control dateControl">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="evDateFrom"><i class="fa-solid fa-calendar"></i></span>
															<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('evDateFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
														</div>
														<label for="evDateFrom">Start Date From</label>
													</div>
												</div>
											</div>
										</div>
										<div class="col-xl-6 col-md-12">
											<div class="form-group">
												<div class="form-label-group mb-2">
													<div class="input-group dateFieldHolder">
														<input type="text" name="evDateTo" id="evDateTo" value="#local.evDateTo#" class="form-control dateControl">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="evDateTo"><i class="fa-solid fa-calendar"></i></span>
															<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('evDateTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
														</div>
														<label for="evDateTo">Start Date To</label>
													</div>
												</div>
											</div>
										</div>
									</div>

									<div class="row mt-3">
										<div class="col-md-3 pr-md-0 font-weight-bold">
											Next
										</div>
										<div class="col-md-9 pl-md-0">
											<a href="javascript:quickEV(7);" class="badge badge-neutral-second text-second mr-1">1w</a>
											<a href="javascript:quickEV(14);" class="badge badge-neutral-second text-second mr-1">2w</a>
											<a href="javascript:quickEV(21);" class="badge badge-neutral-second text-second mr-1">3w</a>
											<a href="javascript:quickEV(30);" class="badge badge-neutral-second text-second mr-1">1m</a>
											<a href="javascript:quickEV(60);" class="badge badge-neutral-second text-second mr-1">2m</a>
											<a href="javascript:quickEV(90);" class="badge badge-neutral-second text-second mr-1">3m</a>
											<a href="javascript:quickEV(180);" class="badge badge-neutral-second text-second mr-1">6m</a>
											<a href="javascript:quickEV(365);" class="badge badge-neutral-second text-second">1y</a>
										</div>
									</div>
									<div class="row mt-1">
										<div class="col-md-3 pr-md-0 font-weight-bold">
											Last
										</div>
										<div class="col-md-9 pl-md-0">
											<a href="javascript:quickEV(-7);" class="badge badge-neutral-second text-second mr-1">1w</a>
											<a href="javascript:quickEV(-14);" class="badge badge-neutral-second text-second mr-1">2w</a>
											<a href="javascript:quickEV(-21);" class="badge badge-neutral-second text-second mr-1">3w</a>
											<a href="javascript:quickEV(-30);" class="badge badge-neutral-second text-second mr-1">1m</a>
											<a href="javascript:quickEV(-60);" class="badge badge-neutral-second text-second mr-1">2m</a>
											<a href="javascript:quickEV(-90);" class="badge badge-neutral-second text-second mr-1">3m</a>
											<a href="javascript:quickEV(-180);" class="badge badge-neutral-second text-second mr-1">6m</a>
											<a href="javascript:quickEV(-365);" class="badge badge-neutral-second text-second">1y</a>
										</div>
									</div>
								</div>
							</div>
						</div>

						<div class="col-md-6 pt-2 pl-sm-1">
							<div class="form-row">
								<div class="col-xl-6 col-md-12 pl-md-0">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<select name="evCalendar" id="evCalendar" class="form-control">
												<option value="0">All Calendars</option>
												<cfloop query="local.qryCalendars">
													<option value="#local.qryCalendars.calendarID#">#local.qryCalendars.calendarName#</option>
												</cfloop>
											</select>
											<label for="evCalendar">Calendar</label>
										</div>
									</div>
								</div>

								<div class="col-xl-6 col-md-12 pl-md-0">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<select name="evCategory" id="evCategory" class="form-control">
												<option value="0">All Categories</option>
											</select>
											<label for="evCategory">Category</label>
										</div>
									</div>
								</div>
							</div>

							<div class="form-row">
								<div class="col-xl-6 col-md-12 pl-md-0">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<input type="text" name="evKeyword" id="evKeyword" value="" class="form-control">
											<label for="evKeyword">Event Name Contains...</label>
										</div>
									</div>
								</div>
								<div class="col-xl-6 col-md-12 pl-md-0">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<input type="text" name="evReportCode" id="evReportCode" value="" class="form-control">
											<label for="evReportCode">Report Code</label>
										</div>
									</div>
								</div>
							</div>
							<div class="form-row">
								<div class="col-sm-12 pl-md-0">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<select name="evEventType" id="evEventType" class="form-control">
												<option value="all">Show All Events</option>
												<option value="main" selected="selected">Main Events Only</option>
												<option value="sub">Sub-Events Only</option>
											</select>
											<label for="evEventType">Event Type</label>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="row mt-4">
						<div class="col-sm-12 font-weight-bold">Registrant Filters</div>
					</div>
					<div class="row mt-2">
						<div class="col-md-6">
							<div class="card card-box h-100">
								<div class="card card-body p-3">
									<div class="form-row">
										<div class="col-xl-6 col-md-12">
											<div class="form-group">
												<div class="form-label-group mb-2">
													<div class="input-group dateFieldHolder">
														<input type="text" name="rDateFrom" id="rDateFrom" value="#local.rDateFrom#" class="form-control dateControl">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="rDateFrom"><i class="fa-solid fa-calendar"></i></span>
															<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('rDateFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
														</div>
														<label for="rDateFrom">Registered From</label>
													</div>
												</div>
											</div>
										</div>
										<div class="col-xl-6 col-md-12">
											<div class="form-group">
												<div class="form-label-group mb-2">
													<div class="input-group dateFieldHolder">
														<input type="text" name="rDateTo" id="rDateTo" value="#local.rDateTo#" class="form-control dateControl">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="rDateTo"><i class="fa-solid fa-calendar"></i></span>
															<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('rDateTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
														</div>
														<label for="rDateTo">Registered To</label>
													</div>
												</div>
											</div>
										</div>
									</div>

									<div class="row mt-3">
										<div class="col-md-3 pr-md-0 font-weight-bold">
											Last
										</div>
										<div class="col-md-9 pl-md-0">
											<a href="javascript:quickEVR(-7);" class="badge badge-neutral-second text-second mr-1">1w</a>
											<a href="javascript:quickEVR(-14);" class="badge badge-neutral-second text-second mr-1">2w</a>
											<a href="javascript:quickEVR(-21);" class="badge badge-neutral-second text-second mr-1">3w</a>
											<a href="javascript:quickEVR(-30);" class="badge badge-neutral-second text-second mr-1">1m</a>
											<a href="javascript:quickEVR(-60);" class="badge badge-neutral-second text-second mr-1">2m</a>
											<a href="javascript:quickEVR(-90);" class="badge badge-neutral-second text-second mr-1">3m</a>
											<a href="javascript:quickEVR(-180);" class="badge badge-neutral-second text-second mr-1">6m</a>
											<a href="javascript:quickEVR(-365);" class="badge badge-neutral-second text-second">1y</a>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="col-md-6 pt-2 pl-sm-1">
							<div class="form-row">
								<div class="col-xl-6 col-md-12 pl-md-0">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<select name="rAttended" id="rAttended" class="form-control">
												<option value="">Attended or Not Attended</option>
												<option value="1">Attended</option>
												<option value="0">Not Attended</option>
											</select>
											<label for="rAttended">Attended?</label>
										</div>
									</div>
								</div>
								<div class="col-xl-6 col-md-12 pl-md-0">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<input type="text" name="rCompany" id="rCompany" value="" class="form-control" >
											<label for="rCompany">Company Name Contains...</label>
										</div>
									</div>
								</div>
							</div>

							<div class="form-row">
								<div class="col-xl-6 col-md-12 pl-md-0">
									<div class="form-group mb-2">
										<div class="input-group flex-nowrap">
											<div class="input-group-prepend">
												<span class="input-group-text px-3">$</span>
											</div>
											<div class="form-label-group flex-grow-1 mb-0">
												<input type="text" name="rBillFrom" id="rBillFrom" value="" class="form-control amtBox" onBlur="rFormatCurrency($(this));">
												<label for="rBillFrom">Billed Amt To</label>
											</div>
										</div>
									</div>
								</div>
								<div class="col-xl-6 col-md-12 pl-md-0">
									<div class="form-group mb-2">
										<div class="input-group flex-nowrap">
											<div class="input-group-prepend">
												<span class="input-group-text px-3">$</span>
											</div>
											<div class="form-label-group flex-grow-1 mb-0">
												<input type="text" name="rBillTo" id="rBillTo" value="" onBlur="rFormatCurrency($(this));" class="form-control amtBox">
												<label for="rBillTo">Billed Amt To</label>
											</div>
										</div>
									</div>
								</div>
							</div>

							<div class="form-row">
								<div class="col-xl-6 col-md-12 pl-md-0">
									<div class="form-group mb-2">
										<div class="input-group flex-nowrap">
											<div class="input-group-prepend">
												<span class="input-group-text  px-3">$</span>
											</div>
											<div class="form-label-group flex-grow-1 mb-0">
												<input type="text" name="rDuesFrom" id="rDuesFrom" value="" onBlur="rFormatCurrency($(this));" class="form-control amtBox">
												<label for="rDuesFrom">Due Amt From</label>
											</div>
										</div>
									</div>
								</div>
								<div class="col-xl-6 col-md-12 pl-md-0">
									<div class="form-group mb-2">
										<div class="input-group flex-nowrap">
											<div class="input-group-prepend">
												<span class="input-group-text px-3">$</span>
											</div>
											<div class="form-label-group flex-grow-1 mb-0">
												<input type="text" name="rDuesTo" id="rDuesTo" value="" onBlur="rFormatCurrency($(this));" class="form-control amtBox">
												<label for="rDuesTo">Due Amt To</label>
											</div>
										</div>
									</div>
								</div>
							</div>
							<cfif local.qryEventRoles.recordcount>
								<div class="form-row">
									<div class="col-12 pl-md-0">
										<div class="form-group">
											<div class="form-label-group">
												<select name="rEvRole" id="rEvRole" class="form-control form-control-sm" multiple="yes" data-toggle="custom-select2" >
													<option value="0">No Role</option>
													<cfloop query="local.qryEventRoles">
														<option value="#local.qryEventRoles.categoryID#">#local.qryEventRoles.categoryName#</option>
													</cfloop>
												</select>
												<label for="rEvRole">Role</label>
											</div>
										</div>
									</div>
								</div>
							<cfelse>
								<input type="hidden" name="rEvRole" id="rEvRole" value="">
							</cfif>

							<div class="form-group row">
								<div class="col-sm-auto ml-3 pl-md-0">Associated With:</div>
								<div class="col-sm pl-md-0">
									<div class="d-inline-block pr-1">
										<input class="rAssocType" type="radio" name="rAssocType" id="rAssocTypeMember" value="member" onclick="changeAssocType();">
										<label for="rAssocTypeMember">A Specific Member</label>
									</div>
									<div class="d-inline-block">
										<input class="rAssocType" type="radio" name="rAssocType" id="rAssocTypeGroup" value="group" onclick="changeAssocType();">
										<label for="rAssocTypeGroup">A Specific Group</label>
									</div>
									<div id="divAssociatedVal" style="display:none;">
										<span id="rAssociatedVal"></span> &nbsp; <a href="javascript:clearAssocType();" id="aClearAssocType" class="text-danger"><i class="fa-solid fa-circle-xmark"></i></a>
									</div>
									<input type="hidden" name="rAssociatedMemberID" id="rAssociatedMemberID" value="">
									<input type="hidden" name="rAssociatedGroupID" id="rAssociatedGroupID" value="">
								</div>
							</div>
						</div>
					</div>
					<cfif local.strFieldFilters.qryFields.recordcount>
						<div class="row">
							<div class="col-sm-12 font-weight-bold mt-4 mb-2">Custom Field Filters</div>
							<div class="col-sm-12">
								#local.strFieldFilters.fieldSelectArea#
							</div>
						</div>
					</cfif>
					<div class="mt-4">
						<button type="submit" class="btn btn-sm btn-primary"><i class="fa-light fa-filter"></i> Show Registrants</button>
					</div>
				</div>
			</div>
		</div>
	</div>
	</form>
</div>

<div class="d-flex mt-3">
	<h5>Registrants registered from <span id="evgriddates">#local.rDateFrom# to #local.rDateTo#</span></h5>
</div>

<table id="evRegistrantsTable" class="table table-sm table-striped table-bordered" style="width:100%">
	<thead>
		<tr>
			<th>Registrant</th>
			<th>Registered</th>
			<th>Attendance</th>
			<th>Billed</th>
			<th>Due</th>
			<th>Actions</th>
		</tr>
	</thead>
</table>

<div id="divCustomFilterFieldsHolder" class="d-none">
	#local.strFieldFilters.fieldSelectControls#
</div>
</cfoutput>