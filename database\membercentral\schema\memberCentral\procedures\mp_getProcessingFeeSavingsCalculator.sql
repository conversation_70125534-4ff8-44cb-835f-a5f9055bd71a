CREATE PROC dbo.mp_getProcessingFeeSavingsCalculator
@profileID int, -- leave null for all Auth pay profiles
@startDate datetime,
@endDate datetime,
@estCardfeePct decimal(18,2)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	IF OBJECT_ID('tempdb..#tmpMP') IS NOT NULL
		DROP TABLE #tmpMP;
	IF OBJECT_ID('tempdb..#tmpMPStats') IS NOT NULL
		DROP TABLE #tmpMPStats;
	IF OBJECT_ID('tempdb..#tmpMPPFD') IS NOT NULL
		DROP TABLE #tmpMPPFD;
	IF OBJECT_ID('tempdb..#tmpMPSur') IS NOT NULL
		DROP TABLE #tmpMPSur;
	CREATE TABLE #tmpMP (profileID int, siteCode varchar(10), profileName varchar(100), orgID int, 
		processFeeDonationFeePercent decimal(18,5), surchargePercent decimal(18,5), enableProcessingFeeDonation bit, enableSurcharge bit);
	CREATE TABLE #tmpMPStats (profileID int, grossPaymentCount int, grossPaymentAmount decimal(18,2), 
		numPaymentsOfferedFeeCount int, estimatedCardFees decimal(18,2) DEFAULT 0, numPaymentsOfferedFeePct int DEFAULT 0);
	CREATE TABLE #tmpMPPFD (profileID int, numDonationsAccepted int, feeRevenueAmount decimal(18,2),
		numDonationsAcceptedPct int DEFAULT 0, estimatedShareOfFees int DEFAULT 0);
	CREATE TABLE #tmpMPSur (profileID int, numSurcharges int, feeRevenueAmount decimal(18,2),
		estimatedShareOfFees int DEFAULT 0);

	INSERT INTO #tmpMP (profileID, siteCode, profileName, orgID, processFeeDonationFeePercent, surchargePercent,
		enableProcessingFeeDonation, enableSurcharge)
	SELECT mp.profileID, s.siteCode, mp.profileName, s.orgID, mp.processFeeDonationFeePercent, mp.surchargePercent,
		mp.enableProcessingFeeDonation, mp.enableSurcharge
	FROM dbo.mp_profiles as mp
	INNER JOIN dbo.sites as s on s.siteID = mp.siteID
	WHERE mp.gatewayID = 10
	AND mp.enableMCPay = 1
	AND (mp.enableProcessingFeeDonation = 1 OR mp.enableSurcharge = 1);

	IF @profileID IS NOT NULL
		DELETE FROM #tmpMP WHERE profileID <> @profileID;

	INSERT INTO #tmpMPStats (profileID, grossPaymentCount, grossPaymentAmount, numPaymentsOfferedFeeCount)
	select tmp.profileID, 
		grossPaymentCount = count(tp.paymentID), 
		grossPaymentAmount = sum(t.amount),
		numPaymentsOfferedFeeCount = sum(case when tp.offeredPaymentFee = 1 then 1 else 0 end)
	from #tmpMP as tmp
	INNER JOIN dbo.tr_transactionPayments as tp on tp.orgID = tmp.orgID
		and tp.profileID = tmp.profileID
	inner join dbo.tr_transactions as t on t.ownedByOrgID = tmp.orgID 
		and t.transactionID = tp.transactionID
		and t.transactionDate between @startDate and @endDate
	GROUP BY tmp.profileID;

	IF @estCardfeePct > 0
		UPDATE #tmpMPStats
		SET estimatedCardFees = cast(grossPaymentAmount*(@estCardfeePct/100) as decimal(18,2));

	UPDATE #tmpMPStats
	SET numPaymentsOfferedFeePct = cast(numPaymentsOfferedFeeCount/cast(grossPaymentCount as decimal(18,2))*100 as int)
	WHERE grossPaymentCount > 0
	AND numPaymentsOfferedFeeCount > 0;


	INSERT INTO #tmpMPPFD (profileID, numDonationsAccepted, feeRevenueAmount)
	select tmp.profileID, 
		numDonationsAccepted = count(r.transactionID), 
		feeRevenueAmount = sum(t.amount)
	from #tmpMP as tmp
	inner join dbo.tr_transactionPayments as tp on tp.orgID = tmp.orgID
		and tp.profileID = tmp.profileID
		and tp.offeredPaymentFee = 1
	inner join dbo.tr_relationships as r on r.orgID = tmp.orgID 
		and r.typeID = 19 
		and r.appliedToTransactionID = tp.transactionID
	inner join dbo.tr_transactions as t on t.ownedByOrgID = tmp.orgID 
		and t.transactionID = r.transactionID
		and t.transactionDate between @startDate and @endDate
	inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
		and ts.paymentFeeTypeID = 1
	GROUP BY tmp.profileID;

	UPDATE tmpPFD
	SET tmpPFD.numDonationsAcceptedPct = cast(tmpPFD.numDonationsAccepted/cast(tmpS.numPaymentsOfferedFeeCount as decimal(18,2))*100 as int)
	FROM #tmpMPPFD as tmpPFD
	INNER JOIN #tmpMPStats as tmpS on tmpS.profileID = tmpPFD.profileID
	WHERE tmpS.numPaymentsOfferedFeeCount > 0;

	UPDATE tmpPFD
	SET tmpPFD.estimatedShareOfFees = cast(tmpPFD.feeRevenueAmount/tmpS.estimatedCardFees*100 as int)
	FROM #tmpMPPFD as tmpPFD
	INNER JOIN #tmpMPStats as tmpS on tmpS.profileID = tmpPFD.profileID
	WHERE tmpS.estimatedCardFees > 0;


	INSERT INTO #tmpMPSur (profileID, numSurcharges, feeRevenueAmount)
	select tmp.profileID, 
		numDonationsAccepted = count(r.transactionID), 
		feeRevenueAmount = sum(t.amount)
	from #tmpMP as tmp
	inner join dbo.tr_transactionPayments as tp on tp.orgID = tmp.orgID
		and tp.profileID = tmp.profileID
	inner join dbo.tr_relationships as r on r.orgID = tmp.orgID 
		and r.typeID = 19 
		and r.appliedToTransactionID = tp.transactionID
	inner join dbo.tr_transactions as t on t.ownedByOrgID = tmp.orgID 
		and t.transactionID = r.transactionID
		and t.transactionDate between @startDate and @endDate
	inner join dbo.tr_transactionSales as ts on ts.transactionID = t.transactionID
		and ts.paymentFeeTypeID = 2
	GROUP BY tmp.profileID;

	UPDATE tmpSur
	SET tmpSur.estimatedShareOfFees = cast(tmpSur.feeRevenueAmount/tmpS.estimatedCardFees*100 as int)
	FROM #tmpMPSur as tmpSur
	INNER JOIN #tmpMPStats as tmpS on tmpS.profileID = tmpSur.profileID
	WHERE tmpS.estimatedCardFees > 0;



	SELECT tmp.profileID, tmp.siteCode, tmp.profileName, tmp.enableProcessingFeeDonation, tmp.enableSurcharge, 
		tmpS.GrossPaymentCount, tmpS.GrossPaymentAmount, tmpS.EstimatedCardFees,
		tmp.processFeeDonationFeePercent, 
		tmpS.numPaymentsOfferedFeeCount as CountPFDOffered, tmpS.numPaymentsOfferedFeePct as PctPFDOffered, 
		isnull(tmpPFD.numDonationsAccepted,0) as CountPFDAccepted, isnull(tmpPFD.numDonationsAcceptedPct,0) as PctPFDAccepted,
		isnull(tmpPFD.feeRevenueAmount,0) as SumPFDRevenue, isnull(tmpPFD.estimatedShareOfFees,0) as PctPFDShareOfCardFeesCoveredEstimate,
		tmp.surchargePercent,
		isnull(tmpSur.numSurcharges,0) as CountSurcharges, isnull(tmpSur.feeRevenueAmount,0) as SumSurchargeRevenue,
		isnull(tmpSur.estimatedShareOfFees,0) as PctSurchargeShareOfCardFeesCoveredEstimate
	FROM #tmpMP as tmp
	LEFT OUTER JOIN #tmpMPStats as tmpS on tmpS.profileID = tmp.profileID
	LEFT OUTER JOIN #tmpMPPFD as tmpPFD on tmpPFD.profileID = tmp.profileID
	LEFT OUTER JOIN #tmpMPSur as tmpSur on tmpSur.profileID = tmp.profileID
	WHERE tmpS.GrossPaymentCount > 0
	ORDER BY tmp.siteCode, tmp.profileName;

	IF OBJECT_ID('tempdb..#tmpMP') IS NOT NULL
		DROP TABLE #tmpMP;
	IF OBJECT_ID('tempdb..#tmpMPStats') IS NOT NULL
		DROP TABLE #tmpMPStats;
	IF OBJECT_ID('tempdb..#tmpMPPFD') IS NOT NULL
		DROP TABLE #tmpMPPFD;
	IF OBJECT_ID('tempdb..#tmpMPSur') IS NOT NULL
		DROP TABLE #tmpMPSur;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
