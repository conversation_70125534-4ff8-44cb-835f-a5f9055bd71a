ALTER PROCEDURE dbo.ev_updateUpcomingRecurringEvents 
@siteID int,
@copyFromEventID int,
@recordedByMemberID int,
@recurringEventsImportResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#mc_EvImport') IS NOT NULL
		DROP TABLE #mc_EvImport;
	IF OBJECT_ID('tempdb..#tmpExistingRecurringEvents') IS NOT NULL
		DROP TABLE #tmpExistingRecurringEvents;
	IF OBJECT_ID('tempdb..#tmp_CF_ItemIDs') IS NOT NULL
		DROP TABLE #tmp_CF_ItemIDs;
	IF OBJECT_ID('tempdb..#tmp_CF_FieldData') IS NOT NULL
		DROP TABLE #tmp_CF_FieldData;
	IF OBJECT_ID('tempdb..#tmpSWCF') IS NOT NULL
		DROP TABLE #tmpSWCF;
	
	-- bit cols defined as varchar for import validation
	CREATE TABLE #mc_EvImport (rowID int, Calendar varchar(100), EventTitle varchar(200), EventSubTitle varchar(200), InternalNotes varchar(max), EventCode varchar(15) NOT NULL, EventCategory varchar(max), 
		EventStart datetime, EventEnd datetime, EventHidden varchar(10), EventAllDay varchar(10), EventDescription varchar(max), ContactTitle varchar(200), 
		Contact varchar(max), ContactInclude varchar(max), LocationTitle varchar(200), Location varchar(max), LocationInclude varchar(max), 
		CancellationTitle varchar(200), Cancellation varchar(max), CancellationInclude varchar(max), TravelTitle varchar(200), Travel varchar(max), TravelInclude varchar(max), 
		InformationTitle varchar(200), Information varchar(max), RegistrationReplyEmail varchar(400));
	CREATE TABLE #tmpExistingRecurringEvents (eventID int PRIMARY KEY, siteResourceID int, eventCode varchar(15), startDate datetime, endDate datetime);
	CREATE TABLE #tmp_CF_ItemIDs (itemID int, itemType varchar(20));
	CREATE TABLE #tmp_CF_FieldData (fieldID int, fieldValue varchar(max), amount decimal(14,2), itemID int);

	DECLARE @recurringEvents bit, @recurringSeriesID int, @swcfList varchar(max), @fullsql varchar(max), @tmpSuffix varchar(36), 
		@eventStartDate datetime, @eventEndDate datetime, @calendarPageName varchar(50), @categoryIDList varchar(200), 
		@categoryList varchar(max), @defaultTimeZoneID int, @eventSiteResourceID int;

	SET @tmpSuffix = REPLACE(CAST(NEWID() AS varchar(36)),'-','');

	SELECT @defaultTimeZoneID = defaultTimeZoneID 
	FROM dbo.sites 
	WHERE siteID = @siteID;

	SELECT @recurringEvents = recurringEvents
	FROM dbo.siteFeatures 
	WHERE siteID = @siteID;

	SELECT @recurringSeriesID = e.recurringSeriesID, @eventStartDate = et.startTime, @eventEndDate = et.endTime, 
		@calendarPageName = aip.pageName, @categoryIDList = cat.categoryIDList, @eventSiteResourceID = e.siteResourceID
	FROM dbo.ev_events AS e
	INNER JOIN dbo.ev_times AS et ON et.eventid = e.eventID
		AND et.timeID = e.defaultTimeID
	INNER JOIN dbo.ev_calendarEvents AS ce ON ce.sourceEventID = e.eventID 
		AND ce.calendarID = ce.sourceCalendarID
	INNER JOIN dbo.ev_calendars AS c ON c.siteID = @siteID
		AND c.calendarID = ce.calendarID
	INNER JOIN dbo.cms_applicationInstances AS ai ON ai.siteID = @siteID
		AND ai.applicationInstanceID = c.applicationInstanceID
	CROSS APPLY dbo.fn_cms_getApplicationInstancePagePath(@siteID,c.applicationInstanceID) as aip
	LEFT OUTER JOIN dbo.cache_calendarEventsCategoryIDList AS cat ON cat.eventID = e.eventID 
		AND cat.calendarID = ce.calendarID
	WHERE e.eventID = @copyFromEventID
	AND e.siteID = @siteID
	AND aip.applicationSiteResourceID = ai.siteResourceID
	AND aip.applicationSiteResourceType = 'Events';

	-- site doesn't support recurring events or not an recurring event
	IF @recurringEvents = 0 OR @recurringSeriesID IS NULL
		GOTO on_done;

	SELECT @categoryList = STRING_AGG(evCat.category,'|')
	FROM dbo.fn_IntListToTable(@categoryIDList,',') as tmpCat
	INNER JOIN dbo.ev_categories as evCat on evCat.categoryID = tmpCat.listItem;

	SET @categoryList = ISNULL(@categoryList,'');

	-- upcoming recurring events
	INSERT INTO #tmpExistingRecurringEvents (eventID, siteResourceID, eventCode, startDate, endDate)
	SELECT e.eventID, e.siteResourceID, e.reportCode, et.startTime, et.endTime
	FROM dbo.ev_events AS e
	INNER JOIN dbo.ev_times AS et ON et.eventid = e.eventID
		AND et.timeID = e.defaultTimeID
	WHERE e.recurringSeriesID = @recurringSeriesID
	AND e.siteID = @siteID
	AND e.[status] IN ('A','I')
	AND et.startTime > @eventStartDate;

	IF @@ROWCOUNT = 0
		GOTO on_done;
	
	-- event custom fields	
	IF OBJECT_ID('tempdb..##tmpSWCF'+@tmpSuffix) IS NOT NULL
		EXEC ('DROP TABLE ##tmpSWCF'+@tmpSuffix);
	
	INSERT INTO #tmp_CF_ItemIDs (itemID, itemType)
	SELECT siteResourceID, 'CrossEvent'
	FROM dbo.ev_events
	WHERE eventID = @copyFromEventID
	AND siteID = @siteID;

	EXEC dbo.cf_getFieldData;

	SELECT e.eventID, replace(f.fieldReference,',','') as fieldReference, 
		CASE WHEN ft.displayTypeCode IN ('SELECT','RADIO','CHECKBOX') THEN REPLACE(fd.fieldValue,', ', '|') ELSE fd.fieldValue END AS answer
	INTO #tmpSWCF
	FROM #tmp_CF_FieldData AS fd
	INNER JOIN dbo.cf_fields AS f ON f.fieldID = fd.fieldID
	INNER JOIN dbo.cf_fieldTypes AS ft ON ft.fieldTypeID = f.fieldTypeID
	INNER JOIN dbo.ev_events as e ON e.siteID = @siteID and e.siteResourceID = fd.itemID;

	-- event custom fields pivoted
	set @swcfList = '';
	select @swcfList = COALESCE(@swcfList + ',', '') + quoteName(fieldReference) from #tmpSWCF group by fieldReference;
	IF left(@swcfList,1) = ','
		set @swcfList = right(@swcfList,len(@swcfList)-1);
	IF len(@swcfList) > 0 BEGIN
		-- add swcf cols to import table
		select @fullsql = COALESCE(@fullsql, '') + 'ALTER TABLE #mc_EvImport ADD ' + quoteName(fieldReference)  + ' varchar(max);'
		from #tmpSWCF 
		group by fieldReference;

		EXEC(@fullsql);

		set @fullsql = '
			select * 
			into ##tmpSWCF'+@tmpSuffix+'
			from (
				select eventID, fieldReference, answer
				from #tmpSWCF
			) as cf
			PIVOT (min(answer) for fieldReference in (' + @swcfList + ')) as p ';
		EXEC(@fullsql);
	END
	ELSE
		EXEC('SELECT eventID INTO ##tmpSWCF'+@tmpSuffix+' FROM #tmpSWCF WHERE 0=1');

	-- prep final data for import
	SET @fullsql = 'SELECT DISTINCT ROW_NUMBER() OVER (ORDER BY tmp.startDate) AS rowID, '''+@calendarPageName+''', eventcontent.contentTitle, ev.eventSubTitle, ev.internalNotes, tmp.eventCode, '''+@categoryList+''', 
		tmp.startDate, tmp.endDate, ISNULL(ev.hiddenFromCalendar,0), ev.isAllDayEvent, eventcontent.rawContent, contactcontent.contentTitle, contactcontent.rawContent, 
		ISNULL(ev.emailContactContent,0), locationcontent.contentTitle, locationcontent.rawContent, ISNULL(ev.emailLocationContent,0), cancelcontent.contentTitle, cancelcontent.rawContent, 
		ISNULL(ev.emailCancelContent,0), travelcontent.contentTitle, travelcontent.rawContent, ISNULL(ev.emailTravelContent,0), 
		informationcontent.contentTitle, informationcontent.rawContent, '''' AS RegistrationReplyEmail';
	IF len(@swcfList) > 0
		SET @fullsql = @fullsql + ', swcf.' + replace(@swcfList,',',',swcf.');
	SET @fullsql = @fullsql + '
		FROM #tmpExistingRecurringEvents AS tmp
		INNER JOIN dbo.ev_events AS ev ON ev.siteID = '+CAST(@siteID AS varchar(10))+' AND ev.eventID = '+CAST(@copyFromEventID AS varchar(10))+'
		CROSS APPLY dbo.fn_getContent(ev.eventcontentID,1) AS eventcontent
		CROSS APPLY dbo.fn_getContent(ev.locationcontentID,1) AS locationcontent
		CROSS APPLY dbo.fn_getContent(ev.travelcontentID,1) AS travelcontent
		CROSS APPLY dbo.fn_getContent(ev.contactcontentID,1) AS contactcontent
		CROSS APPLY dbo.fn_getContent(ev.cancellationPolicycontentID,1) AS cancelcontent
		CROSS APPLY dbo.fn_getContent(ev.informationContentID,1) AS informationcontent';
	IF len(@swcfList) > 0
		SET @fullsql = @fullsql + '
			LEFT OUTER JOIN ##tmpSWCF'+@tmpSuffix+' AS swcf ON swcf.eventID = ev.eventID';

	INSERT INTO #mc_EvImport
	EXEC(@fullsql);

	-- drop global table
	IF OBJECT_ID('tempdb..##tmpSWCF'+@tmpSuffix) IS NOT NULL
		EXEC ('DROP TABLE ##tmpSWCF'+@tmpSuffix);

	-- queue recurring events import
	EXEC dbo.ev_importEvents @siteID=@siteID, @recordedByMemberID=@recordedByMemberID, @ovAction='o', @importResult=@recurringEventsImportResult OUTPUT;

	IF @recurringEventsImportResult.value('count(/import/errors/error)','int') = 0 BEGIN
		-- update asset categories
		DELETE csr
		FROM dbo.cms_categorySiteResources AS csr
		INNER JOIN #tmpExistingRecurringEvents AS tmp ON tmp.siteResourceID = csr.siteResourceID;

		INSERT INTO dbo.cms_categorySiteResources (categoryID, siteResourceID)
		SELECT DISTINCT csr.categoryID, tmp.siteResourceID
		FROM #tmpExistingRecurringEvents AS tmp
		INNER JOIN dbo.cms_categorySiteResources AS csr ON csr.siteResourceID = @eventSiteResourceID;
	END

	on_done:

	IF OBJECT_ID('tempdb..#mc_EvImport') IS NOT NULL
		DROP TABLE #mc_EvImport;
	IF OBJECT_ID('tempdb..#tmpExistingRecurringEvents') IS NOT NULL
		DROP TABLE #tmpExistingRecurringEvents;
	IF OBJECT_ID('tempdb..#tmp_CF_ItemIDs') IS NOT NULL
		DROP TABLE #tmp_CF_ItemIDs;
	IF OBJECT_ID('tempdb..#tmp_CF_FieldData') IS NOT NULL
		DROP TABLE #tmp_CF_FieldData;
	IF OBJECT_ID('tempdb..#tmpSWCF') IS NOT NULL
		DROP TABLE #tmpSWCF;
	IF OBJECT_ID('tempdb..##tmpSWCF'+@tmpSuffix) IS NOT NULL
		EXEC ('DROP TABLE ##tmpSWCF'+@tmpSuffix);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
