CREATE PROC dbo.queue_addMCPayECheckTrans_download
@status varchar(60),
@csvfilename varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @selectsql varchar(max) = '';

	SELECT @selectsql = 'SELECT ''addMCPayECheckTrans'' as queueType, qs.queueStatus, qid.itemID, qid.MPProfileID, qid.dateFrom, qid.dateTo, qid.filterOptions, 
			qid.dateAdded, qid.dateUpdated, ROW_NUMBER() OVER(order by qid.dateAdded) as mcCSVorder
		*FROM* platformQueue.dbo.queue_addMCPayECheckTrans as qid
		INNER JOIN platformQueue.dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qid.statusID and qs.queueStatus = ''' + @status + '''';

	EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@csvfilename, @returnColumns=0;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
