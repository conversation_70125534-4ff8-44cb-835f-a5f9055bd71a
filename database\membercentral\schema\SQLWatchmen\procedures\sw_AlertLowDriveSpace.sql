----------------------------------------------------------------------------------------------------
/* Query #36: PASSED */
ALTER PROCEDURE sw_AlertLowDriveSpace
(
    @LowDriveSpaceGBFreeThresholdPage DECIMAL(9) = NULL,
    @LowDriveSpacePercentFullThresholdEmail tinyint = NULL,
    @LowDriveSpaceMinimumDurationFull smallint = NULL, 
	@LowDriveSpaceAlertInterval int = NULL,
	@LowDriveSpaceExcludedDrives varchar(128) = NULL,
	@MailProfileName varchar(128) = NULL,
	@AlertEmail varchar(128) = NULL,
	@PageEmail varchar(128) = NULL,
	@Company varchar(128) = NULL,
	@Configuration varchar(128) = NULL
)
AS
BEGIN
	/* Checking alert interval to determine if any alert should be sent */
	IF @LowDriveSpaceAlertInterval IS NULL
	BEGIN
		SELECT @LowDriveSpaceAlertInterval = CAST([Value] AS int) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'LowDriveSpaceAlertInterval'
	END

	DECLARE @DateTimeCutoff datetime = DATEADD(minute, (-1 * @LowDriveSpaceAlertInterval), GETDATE())

	/* If alert has already been sent in the past @LowDriveSpaceAlertInterval minutes then the procedure exits */
	IF (SELECT TOP 1 [DateTime] FROM [SQLWatchmen].[dbo].[AlertLog] WHERE [Alert] = 'Low Drive Space' AND [DateTime] > @DateTimeCutoff) IS NOT NULL
	BEGIN
		RETURN
	END

	/* Get additional parameters from the Parameters table */
    IF @LowDriveSpacePercentFullThresholdEmail IS NULL
	BEGIN
		SELECT @LowDriveSpacePercentFullThresholdEmail = CAST([Value] AS tinyint) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'LowDriveSpacePercentFullThresholdEmail'
	END

    IF @LowDriveSpaceMinimumDurationFull IS NULL
    BEGIN
        SELECT @LowDriveSpaceMinimumDurationFull = CAST([Value] AS smallint) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'LowDriveSpaceMinimumDurationFull'
    END

	IF @LowDriveSpaceExcludedDrives IS NULL
	BEGIN
		SELECT @LowDriveSpaceExcludedDrives = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'LowDriveSpaceExcludedDrives'
	END

	/* Checks the last log entry to see if any drives are over the alerting threshold and exits the stored procedure if there aren't any */
	DECLARE @LastLogDate DATETIME2 = (SELECT TOP 1 [DateTime] FROM [SQLWatchmen].[dbo].[DriveSpaceLog] ORDER BY [DateTime] DESC)

    IF (SELECT TOP 1 [ID] FROM [SQLWatchmen].[dbo].[DriveSpaceLog] WHERE [PercentFull] > @LowDriveSpacePercentFullThresholdEmail AND [DateTime] = @LastLogDate AND (',' + @LowDriveSpaceExcludedDrives + ',' NOT LIKE '%,' + [Drive] + ',%')) IS NULL
    BEGIN
		RETURN
    END 

	/* Get additional parameters */
	IF @LowDriveSpaceGBFreeThresholdPage IS NULL
	BEGIN
		SELECT @LowDriveSpaceGBFreeThresholdPage = CAST([Value] AS DECIMAL(9)) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'LowDriveSpaceGBFreeThresholdPage'
	END

	IF @MailProfileName IS NULL
	BEGIN
		SELECT @MailProfileName = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'MailProfileName'
	END

	IF @AlertEmail IS NULL
	BEGIN
		SELECT @AlertEmail = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'AlertEmail'
	END

	IF @PageEmail IS NULL
	BEGIN
		SELECT @PageEmail = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'PageEmail'
	END

	IF @Company IS NULL
	BEGIN
		SELECT @Company = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Company'
	END

	IF @Configuration IS NULL
	BEGIN
		SELECT @Configuration = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Configuration'
	END

	/* Loads the last @LowDriveSpaceMinimumDurationFull + 10 minutes into a temp table to determine if there should be an email alert or a page or no alert because the time duration threshold hasn't been met yet */
	IF OBJECT_ID('tempdb..#DriveSpaceLog') IS NOT NULL
	BEGIN
		DROP TABLE #DriveSpaceLog
	END

	CREATE TABLE #DriveSpaceLog 
	(
		[DateTime] DATETIME2 DEFAULT SYSDATETIME() NOT NULL,
		[Drive] CHAR(1) NOT NULL, 
		[PercentFull] DECIMAL(9, 4) NOT NULL,
		[Size_GB] DECIMAL(19,4) NOT NULL, 
		[Used_GB] DECIMAL(19,4) NOT NULL, 
		[Free_GB] DECIMAL(19,4) NOT NULL
	)

	SET @DateTimeCutoff = DATEADD(minute, (-1 * (@LowDriveSpaceMinimumDurationFull + 10)), GETDATE())    
	
	INSERT INTO #DriveSpaceLog
	SELECT [DateTime], [Drive], [PercentFull], [Size_GB], [Used_GB], [Free_GB] 
	FROM [SQLWatchmen].[dbo].[DriveSpaceLog] 
	WHERE [DateTime] >  @DateTimeCutoff
	AND (',' + @LowDriveSpaceExcludedDrives + ',' NOT LIKE '%,' + [Drive] + ',%')

	SET @DateTimeCutoff = DATEADD(minute, (-1 * (@LowDriveSpaceMinimumDurationFull)), GETDATE())
	DECLARE @Subject NVARCHAR(255)
	DECLARE @Body NVARCHAR(MAX)

	IF (SELECT TOP 1 [DateTime] FROM #DriveSpaceLog WHERE [Free_GB] < @LowDriveSpaceGBFreeThresholdPage AND [DateTime] < @DateTimeCutoff) IS NOT NULL
	BEGIN
		SET @Subject = 'CRITICAL: Low Drive Space Detected | ' + @Configuration + ' | ' + @Company

		SET @Body = 'At least one drive has less than ' + CAST(@LowDriveSpaceGBFreeThresholdPage AS VARCHAR) + ' GB of free space for at least ' + CAST(@LowDriveSpaceMinimumDurationFull AS VARCHAR) + ' minutes' + CHAR(13)
			+ 'Last Log Record: ' + CHAR(13) + CHAR(10)

		SELECT @Body += CAST(x.[DateTime] AS VARCHAR) + ' | ' + [Drive] + ' | ' + CAST([PercentFull] AS VARCHAR) + '% Full | ' +  CAST([Free_GB] AS VARCHAR) + 'GB (Free) | ' + CAST([Size_GB] AS VARCHAR) + 'GB (Total)' + CHAR(13) + CHAR(10)
			FROM (SELECT [DateTime], [Drive], [PercentFull], [Free_GB], [Size_GB] FROM #DriveSpaceLog WHERE [Free_GB] < @LowDriveSpaceGBFreeThresholdPage) AS x

		EXEC msdb.dbo.sp_send_dbmail
			@profile_name = @MailProfileName,
			@recipients = @AlertEmail,
			@blind_copy_recipients = @PageEmail,
			@subject = @Subject,
			@body = @Body

		INSERT INTO [SQLWatchmen].[dbo].[AlertLog] ([Alert]) VALUES ('Low Drive Space')
	END
	ELSE IF (SELECT TOP 1 [DateTime] FROM #DriveSpaceLog WHERE [PercentFull] > @LowDriveSpacePercentFullThresholdEmail AND [DateTime] < @DateTimeCutoff) IS NOT NULL
	BEGIN
		SET @Subject = 'Low Drive Space Detected | ' + @Configuration + ' | ' + @Company

		SET @Body = 'At least one drive has been more than ' + CAST(@LowDriveSpacePercentFullThresholdEmail AS VARCHAR) + '% full for at least ' + CAST(@LowDriveSpaceMinimumDurationFull AS VARCHAR) + ' minutes' + CHAR(13) + CHAR(10)
			+ 'Last Log Record: ' + CHAR(13) + CHAR(10)

		SELECT @Body += CAST(x.[DateTime] AS VARCHAR) + ' | ' + [Drive] + ' | ' + CAST([PercentFull] AS VARCHAR) + '% Full | ' +  CAST([Free_GB] AS VARCHAR) + 'GB (Free) | ' + CAST([Size_GB] AS VARCHAR) + 'GB (Total)' + CHAR(13) + CHAR(10)
			FROM (SELECT [DateTime], [Drive], [PercentFull], [Free_GB], [Size_GB] FROM #DriveSpaceLog) AS x

		EXEC msdb.dbo.sp_send_dbmail
			@profile_name = @MailProfileName,
			@recipients = @AlertEmail,
			@subject = @Subject,
			@body = @Body

		INSERT INTO [SQLWatchmen].[dbo].[AlertLog] ([Alert]) VALUES ('Low Drive Space')
	END
	ELSE
	BEGIN
		RETURN
	END

	IF OBJECT_ID('tempdb..#DriveSpaceLog') IS NOT NULL
	BEGIN
		DROP TABLE #DriveSpaceLog
	END

END
GO
