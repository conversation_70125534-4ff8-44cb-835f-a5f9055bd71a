----------------------------------------------------------------------------------------------------
/* Query #27: PASSED */
ALTER PROCEDURE [dbo].[sw_AddParameter]
(
    @Parameter VARCHAR(128) = NULL,
    @Value VARCHAR(128) = NULL,
    @Description VARCHAR(128) = NULL,
    @Help BIT = 0
)
AS
BEGIN

    IF @Parameter IS NULL OR @Help = 1
    BEGIN
        PRINT 
            'Stored Procedure sw_AddParameter' + CHAR(13) +
            '-------------------------------------------------------------------------------' + CHAR(13) +
            'Purpose: ' + CHAR(13) +
            CHAR(9) + 'To add a parameter to the Parameters table.' + CHAR(13) +
            'Parameters: ' + CHAR(13) +
            CHAR(9) + '@Parameter VARCHAR(128) - The name of the parameter to add.' + CHAR(13) +
            CHAR(9) + '@Value VARCHAR(128) - The value of the parameter to add.' + CHAR(13) +
            CHAR(9) + '@Description VARCHAR(128) - The description of the parameter to add.' + CHAR(13) +
            CHAR(9) + '@Help BIT - Set to 1 to display this help message.' + CHAR(13) + 
            'Note: ' + CHAR(13) +
            CHAR(9) + 'Default values for Value and Description are NULL. @Parameter is required.'
        RETURN
    END

    INSERT INTO [SQLWatchmen].[dbo].[Parameters] ([Parameter], [Value], [Description]) VALUES (@Parameter, @Value, @Description) 

    SELECT * FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = @Parameter

END
GO
