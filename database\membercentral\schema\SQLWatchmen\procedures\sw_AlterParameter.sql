----------------------------------------------------------------------------------------------------
/* Query #40: PASSED */
ALTER PROCEDURE [dbo].[sw_AlterParameter]
(
    @Parameter VARCHAR(128) = NULL,
    @Value VARCHAR(128) = '{VALUE}',
    @Description VARCHAR(128) = '{DESCRIPTION}',
    @Help BIT = 0
)
AS
BEGIN

    IF @Parameter IS NULL OR @Help = 1
    BEGIN
        SELECT 'See Messages Tab for Help Info'
        SELECT 'Current Values'
        SELECT * FROM [SQLWatchmen].[dbo].[Parameters]
        PRINT 
            'Stored Procedure sw_AlterParameter' + CHAR(13) +
            '-------------------------------------------------------------------------------' + CHAR(13) +
            'Purpose: ' + CHAR(13) +
            CHAR(9) + 'To alter a parameter''s values in the Parameters table.' + CHAR(13) +
            'Parameters: ' + CHAR(13) +
            CHAR(9) + '@Parameter VARCHAR(128) - The name of the parameter to alter.' + CHAR(13) +
            CHAR(9) + '@Value VARCHAR(128) - The new value of the parameter.' + CHAR(13) +
            CHAR(9) + '@Description VARCHAR(128) - The new description of the parameter.' + CHAR(13) +
            CHAR(9) + '@Help BIT - Set to 1 to display this help message.' + CHAR(13) +
            'Note: ' + CHAR(13) +
            CHAR(9) + '@Parameter, @Value, and @Description must be specified. NULL is a valid value for @Value and @Description.' + CHAR(13) +
            CHAR(9) + 'The current value of of Value and Description will not be kept if they are not specified or if NULL is specified.'
        RETURN
    END

    IF @Value = '{VALUE}' OR @Description = '{DESCRIPTION}'
    BEGIN
        SELECT 'Current Value', * FROM [dbo].[Parameters] WHERE [Parameter] = @Parameter
        SELECT 'sw_AlterParameter @Help = 1 for more information'
        RETURN
    END

    SELECT 'Previous Values', * FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = @Parameter
    UPDATE [SQLWatchmen].[dbo].[Parameters] SET [Value] = @Value, [Description] = @Description WHERE [Parameter] = @Parameter
    SELECT 'New Values', * FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = @Parameter

END
GO
