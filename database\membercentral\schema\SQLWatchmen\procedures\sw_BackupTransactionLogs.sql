----------------------------------------------------------------------------------------------------
/* Query #43: PASSED */
ALTER PROCEDURE [dbo].[sw_BackupTransactionLogs]
(
    @BackupTransactionLogDatabases nvarchar(max) = NULL,
    @BackupTransactionLogDirectory nvarchar(max) = NULL,
    @BackupTransactionLogVerify nvarchar(max) = NULL,
    @BackupTransactionLogCleanupTime int = NULL,
    @BackupTransactionLogCleanupMode nvarchar(max) = NULL,
    @BackupTransactionLogCompress nvarchar(max) = NULL,
    @BackupTransactionLogCheckSum nvarchar(max) = NULL,
    @BackupTransactionLogBlockSize int = NULL,
    @BackupTransactionLogBufferCount int = NULL,
    @BackupTransactionLogMaxTransferSize int = NULL,
    @BackupTransactionLogNumberOfFiles int = NULL,
    @BackupTransactionLogMinBackupSizeForMultipleFiles int = NULL,
    @BackupTransactionLogMaxFileSize int = NULL,
    @BackupTransactionLogURL nvarchar(max) = NULL,
    @BackupTransactionLogCredential nvarchar(max) = NULL
)
AS 
BEGIN
    SET NOCOUNT ON

    IF @BackupTransactionLogDatabases IS NULL
    BEGIN
        SET @BackupTransactionLogDatabases = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupTransactionLogDatabases')
    END

    IF (@BackupTransactionLogDirectory IS NOT NULL) AND ((SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = @BackupTransactionLogDirectory) IS NOT NULL)
    BEGIN
        SET @BackupTransactionLogDirectory = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = @BackupTransactionLogDirectory)
    END

    IF @BackupTransactionLogDirectory IS NULL
    BEGIN
        SET @BackupTransactionLogDirectory = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'PrimaryBackupLocation')
    END

    IF @BackupTransactionLogVerify IS NULL
    BEGIN
        SET @BackupTransactionLogVerify = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupTransactionLogVerify')
    END

    IF @BackupTransactionLogCleanupTime IS NULL
    BEGIN
        SET @BackupTransactionLogCleanupTime = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupTransactionLogCleanupTime')
    END

    IF @BackupTransactionLogCleanupMode IS NULL
    BEGIN
        SET @BackupTransactionLogCleanupMode = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupTransactionLogCleanupMode')
    END

    IF @BackupTransactionLogCompress IS NULL
    BEGIN
        SET @BackupTransactionLogCompress = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupTransactionLogCompress')
    END

    IF @BackupTransactionLogCheckSum IS NULL
    BEGIN
        SET @BackupTransactionLogCheckSum = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupTransactionLogCheckSum')
    END

    IF @BackupTransactionLogBlockSize IS NULL
    BEGIN
        SET @BackupTransactionLogBlockSize = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupTransactionLogBlockSize')
    END

    IF @BackupTransactionLogBufferCount IS NULL
    BEGIN
        SET @BackupTransactionLogBufferCount = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupTransactionLogBufferCount')
    END

    IF @BackupTransactionLogMaxTransferSize IS NULL
    BEGIN
        SET @BackupTransactionLogMaxTransferSize = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupTransactionLogMaxTransferSize')
    END

    IF @BackupTransactionLogNumberOfFiles IS NULL
    BEGIN
        SET @BackupTransactionLogNumberOfFiles = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupTransactionLogNumberOfFiles')
    END

    IF @BackupTransactionLogMinBackupSizeForMultipleFiles IS NULL
    BEGIN
        SET @BackupTransactionLogMinBackupSizeForMultipleFiles = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupTransactionLogMinBackupSizeForMultipleFiles')
    END

    IF @BackupTransactionLogMaxFileSize IS NULL
    BEGIN
        SET @BackupTransactionLogMaxFileSize = (SELECT CAST([Value] AS INT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupTransactionLogMaxFileSize')
    END

    IF @BackupTransactionLogURL IS NULL
    BEGIN
        SET @BackupTransactionLogURL = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupTransactionLogURL')
    END

    IF @BackupTransactionLogCredential IS NULL
    BEGIN
        SET @BackupTransactionLogCredential = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupTransactionLogCredential')
    END

    EXEC DatabaseBackup
        @Databases = @BackupTransactionLogDatabases,
        @Directory = @BackupTransactionLogDirectory,
        @BackupType = 'LOG',
        @Verify = @BackupTransactionLogVerify,
        @CleanupTime = @BackupTransactionLogCleanupTime,
        @CleanupMode = @BackupTransactionLogCleanupMode,
        @Compress = @BackupTransactionLogCompress,
        @CheckSum = @BackupTransactionLogCheckSum,
        @BlockSize = @BackupTransactionLogBlockSize,
        @BufferCount = @BackupTransactionLogBufferCount,
        @MaxTransferSize = @BackupTransactionLogMaxTransferSize,
        @NumberOfFiles = @BackupTransactionLogNumberOfFiles,
        @MinBackupSizeForMultipleFiles = @BackupTransactionLogMinBackupSizeForMultipleFiles,
        @MaxFileSize = @BackupTransactionLogMaxFileSize,
        @URL = @BackupTransactionLogURL,
        @Credential = @BackupTransactionLogCredential,
        @LogToTable = 'Y'

END
GO
