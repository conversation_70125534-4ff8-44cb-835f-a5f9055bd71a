ALTER PROC dbo.queue_conditionCacheCheck_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	
	DECLARE @issueCount int, @timeToUse datetime, @queueTypeID int, @processingStatusID int, @readyStatusID int, 
		@errorSubject varchar(400), @errorTitle varchar(400), @itemAsStr varchar(60), @xmlMessage xml;
	EXEC dbo.queue_getQueueTypeID @queueType='conditionCacheCheck', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@processingStatusID OUTPUT;

	-- conditionCacheCheck / processingItem autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_conditionCacheCheck WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.queue_conditionCacheCheck
		SET statusID = @readyStatusID,
			dateUpdated = GETDATE()
			OUTPUT inserted.itemID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @processingStatusID
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs;
		WHILE @itemAsStr IS NOT NULL BEGIN
			SELECT @xmlMessage = ISNULL((
				SELECT cast(orgID as varchar(10)) as o
				FROM dbo.queue_conditionCacheCheck
				WHERE itemID = CAST(@itemAsStr AS int)
				FOR XML RAW('mc'), TYPE
			),'<mc/>');

			EXEC dbo.queue_conditionCacheCheck_sendMessage @xmlMessage=@xmlMessage;

			SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'conditionCacheCheck Queue Issue';
		SET @errorSubject = 'conditionCacheCheck queue moved items from processingItem to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- conditionCacheCheck / ReadyToProcess resend message
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -220, GETDATE());
	SELECT @issueCount = count(itemID) FROM dbo.queue_conditionCacheCheck WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		update dbo.queue_conditionCacheCheck
		SET dateUpdated = getdate()
			OUTPUT inserted.itemID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @readyStatusID 
		AND dateUpdated < @timeToUse;

		SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs;
		WHILE @itemAsStr IS NOT NULL BEGIN
			SELECT @xmlMessage = ISNULL((
				SELECT cast(orgID as varchar(10)) as o
				FROM dbo.queue_conditionCacheCheck
				WHERE itemID = CAST(@itemAsStr AS int)
				FOR XML RAW('mc'), TYPE
			),'<mc/>');

			EXEC dbo.queue_conditionCacheCheck_sendMessage @xmlMessage=@xmlMessage;

			SELECT @itemAsStr = min(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'conditionCacheCheck Queue Issue';
		SET @errorSubject = 'conditionCacheCheck queue resent items in ReadyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- conditionCacheCheck catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_conditionCacheCheck WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'conditionCacheCheck Queue Issue';
		SET @errorSubject = 'conditionCacheCheck queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO