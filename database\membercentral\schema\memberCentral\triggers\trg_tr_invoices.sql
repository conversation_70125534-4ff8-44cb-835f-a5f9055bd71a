ALTER TRIGGER trg_tr_invoices ON dbo.tr_invoices
AFTER UPDATE 
AS 

SET NOCOUNT ON
BEGIN TRY

	IF NOT EXISTS (SELECT * FROM inserted) RETURN

	IF UPDATE(statusID) or UPDATE(dateDue) or UPDATE(assignedToMemberID) or UPDATE(payprofileID) BEGIN
		DECLARE @anyChange int, @invoiceIDList varchar(max);

		-- did any of my tracking fields change for any of the rows updated?
		SELECT @anyChange = COUNT(*) 
		FROM Inserted as i 
		INNER JOIN Deleted as d ON i.invoiceID = d.invoiceID 
		WHERE i.statusID != d.statusID
		OR i.dateDue != d.dateDue
		OR i.assignedToMemberID != d.assignedToMemberID
		OR isnull(i.payprofileID,0) != isnull(d.payprofileID,0);

		IF @anyChange > 0 BEGIN
			
			-- handle any conditions that need updating
			BEGIN TRY
				SET XACT_ABORT OFF;	

				IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
					DROP TABLE #tblMCQRun;
				CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

				IF OBJECT_ID('tempdb..#tblCCProfile') IS NOT NULL 
					DROP TABLE #tblCCProfile;
				CREATE TABLE #tblCCProfile (orgID int, profileID int, memberID int);

				declare @conditionKeyID int;
				select @conditionKeyID = conditionKeyID from dbo.ams_virtualGroupConditionKeys where conditionKey = 'acctInvProf';
				
				INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
				SELECT distinct m.orgID, m.activeMemberID, c.conditionID 
				from dbo.ams_virtualGroupConditions as c WITH(NOLOCK)
				inner join dbo.ams_virtualGroupConditionValues as cv on cv.conditionKeyID = @conditionKeyID and cv.conditionID = c.conditionID
				inner join Inserted as i on cast(i.invoiceProfileID as varchar(20)) = cv.conditionValue
				inner join dbo.ams_members as m on m.orgID = i.orgID and m.memberID = i.assignedToMemberID
				where c.orgID = i.orgID 
				and c.fieldcode = 'acct_inv'
				and c.conditionTypeID = 1;

				-- trigger reprocessing of credit card expiration conditions (if limiting to invoices) 
				IF UPDATE(payprofileID) BEGIN
					SET @invoiceIDList = NULL;

					-- entries where payprofileID was changed
					SELECT @invoiceIDList = COALESCE(@invoiceIDList + ',', '') + cast(i.invoiceID as varchar(10))
					FROM Inserted as i 
					INNER JOIN Deleted as d ON i.invoiceID = d.invoiceID 
					WHERE ISNULL(i.payprofileID,0) != ISNULL(d.payprofileID,0);

					IF LEN(ISNULL(@invoiceIDList,'')) > 0 BEGIN
						INSERT INTO #tblCCProfile (orgID, profileID, memberID)
						SELECT i.orgID, mpp.profileID, mpp.memberID
						FROM Inserted as i
						INNER JOIN dbo.fn_varcharListToTable(@invoiceIDList,',') AS tmp ON tmp.listitem = i.invoiceID
						INNER JOIN dbo.ams_memberPaymentProfiles AS mpp on mpp.payProfileID = i.payProfileID
						WHERE i.payProfileID IS NOT NULL
							UNION
						SELECT d.orgID, mpp.profileID, mpp.memberID
						FROM Deleted as d
						INNER JOIN dbo.fn_varcharListToTable(@invoiceIDList,',') AS tmp ON tmp.listitem = d.invoiceID
						INNER JOIN dbo.ams_memberPaymentProfiles AS mpp on mpp.payProfileID = d.payProfileID
						WHERE d.payProfileID IS NOT NULL;

						INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
						SELECT DISTINCT c.orgID, tmp.memberID, c.conditionID
						FROM dbo.ams_virtualGroupConditions AS c
						INNER JOIN dbo.ams_virtualGroupConditionValues AS cv ON cv.conditionID = c.conditionID
						INNER JOIN #tblCCProfile AS tmp on cast(tmp.profileID as varchar(20)) = cv.conditionValue
						INNER JOIN dbo.ams_virtualGroupConditionKeys AS k ON k.conditionKeyID = cv.conditionKeyID
							AND k.conditionKey = 'acctCCProf'
						INNER JOIN dbo.ams_virtualGroupConditionValues AS cv2
						INNER JOIN dbo.ams_virtualGroupConditionKeys AS k2 ON k2.conditionKeyID = cv2.conditionKeyID
							AND k2.conditionKey = 'acctCCLimitToInvDue'
							ON cv2.conditionID = c.conditionID
							AND cv2.conditionValue = '1'
						WHERE c.orgID = tmp.orgID
						AND c.fieldCode = 'acct_cc';
					END
				END

				IF UPDATE(dateDue) BEGIN
					SET @invoiceIDList = NULL;

					-- only due date was updated when a card is associated to it
					SELECT @invoiceIDList = COALESCE(@invoiceIDList + ',', '') + cast(i.invoiceID as varchar(10))
					FROM Inserted as i 
					INNER JOIN Deleted as d ON i.invoiceID = d.invoiceID 
					WHERE i.payprofileID IS NOT NULL
					AND d.payprofileID IS NOT NULL
					AND i.payprofileID = d.payprofileID
					AND i.dateDue != d.dateDue;

					IF LEN(ISNULL(@invoiceIDList,'')) > 0 BEGIN
						DELETE FROM #tblCCProfile;

						INSERT INTO #tblCCProfile (orgID, profileID, memberID)
						SELECT DISTINCT i.orgID, mpp.profileID, mpp.memberID
						FROM Inserted as i
						INNER JOIN dbo.fn_varcharListToTable(@invoiceIDList,',') AS tmp ON tmp.listitem = i.invoiceID
						INNER JOIN dbo.ams_memberPaymentProfiles AS mpp on mpp.payProfileID = i.payProfileID;

						INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
						SELECT DISTINCT c.orgID, tmp.memberID, c.conditionID
						FROM dbo.ams_virtualGroupConditions AS c
						INNER JOIN dbo.ams_virtualGroupConditionValues AS cv ON cv.conditionID = c.conditionID
						INNER JOIN #tblCCProfile AS tmp on cast(tmp.profileID as varchar(20)) = cv.conditionValue
						INNER JOIN dbo.ams_virtualGroupConditionKeys AS k ON k.conditionKeyID = cv.conditionKeyID
							AND k.conditionKey = 'acctCCProf'
						INNER JOIN dbo.ams_virtualGroupConditionValues AS cv2
							INNER JOIN dbo.ams_virtualGroupConditionKeys AS k2 ON k2.conditionKeyID = cv2.conditionKeyID
								AND k2.conditionKey IN ('acctCCInvoiceDueLower','acctCCInvoiceDueUpper')
							ON cv2.conditionID = c.conditionID
							AND LEN(cv2.conditionValue) > 0
						WHERE c.orgID = tmp.orgID
						AND c.fieldCode = 'acct_cc';
					END
				END

				EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

				IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
					DROP TABLE #tblMCQRun;
				IF OBJECT_ID('tempdb..#tblCCProfile') IS NOT NULL 
					DROP TABLE #tblCCProfile;

				SET XACT_ABORT ON;
			END TRY
			BEGIN CATCH
				SET XACT_ABORT ON;
				EXEC dbo.up_MCErrorHandler @raise=0, @email=1;
			END CATCH

		END
	END

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO
