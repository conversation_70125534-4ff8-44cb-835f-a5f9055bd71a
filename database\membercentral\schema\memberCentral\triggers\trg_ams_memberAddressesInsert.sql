CREATE TRIGGER [dbo].[trg_ams_memberAddressesInsert] ON [dbo].[ams_memberAddresses]
AFTER INSERT
AS 

SET NOCOUNT ON
BEGIN TRY

	IF NOT EXISTS (SELECT * FROM inserted) RETURN

    UPDATE ma
    SET ma.postalCodeForSearch = CASE 
		WHEN ma.countryID = 1 then left(replace(ma.postalCode,' ',''),5)
		ELSE replace(ma.postalCode,' ','')
		END
	FROM dbo.ams_memberAddresses as ma 
	INNER JOIN Inserted as I ON ma.addressID = I.addressID

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO
