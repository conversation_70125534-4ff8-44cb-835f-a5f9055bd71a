ALTER PROC dbo.general_exportCPECredit
@sponsorID int,
@publishingOrg varchar(15),
@startdate datetime,
@enddate datetime,
@filename varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @orgCode varchar(10), @fieldsList varchar(max);

	-- set startdate to 00:00:00 of startdate, 00:00:00 of enddate
	SELECT @startdate = DATEADD(dd, DATEDIFF(dd,0,@startdate), 0);
	SELECT @enddate = DATEADD(dd, DATEDIFF(dd,0,dateadd(dd,1,@enddate)), 0);

	IF OBJECT_ID('tempdb..#tmpEnrollments') IS NOT NULL 
		DROP TABLE #tmpEnrollments;
	IF OBJECT_ID('tempdb..#tmpEnrollments2') IS NOT NULL 
		DROP TABLE #tmpEnrollments2;
	IF OBJECT_ID('tempdb..#tmpCPE') IS NOT NULL 
		DROP TABLE #tmpCPE;
	IF OBJECT_ID('tempdb..##tmpCPE') IS NOT NULL 
		DROP TABLE ##tmpCPE;
	IF OBJECT_ID('tempdb..#tmpMAMemberIDs') IS NOT NULL
		DROP TABLE #tmpMAMemberIDs;

	CREATE TABLE #tmpCPE ([memberNumber] [varchar](50) NOT NULL, [firstName] [varchar](75) NOT NULL, [lastName] [varchar](75) NOT NULL,
		[email] [varchar](255) NULL, [Address_Phone] [varchar](40) NULL, [NABP_ePID] [varchar](255) NULL, [DOB] [varchar](4) NULL,
		[orgCode] [varchar](10) NOT NULL, [participantID] [int]  NULL, [pubOrg] [varchar](10) NOT NULL, 
		[dateEnrolled] [smalldatetime] NOT NULL, [dateCompleted] [smalldatetime] NULL, [joinTime] [varchar](20) NULL,
		[exitTime] [varchar](20) NULL, [finaltimespent] [int] NULL, [seminarID] [int] NOT NULL, [seminarName] [varchar](250) NOT NULL,
		[SeminarType] [varchar](9) NULL, [ACPE_UAN] [varchar](80) NOT NULL, [Date_Of_Participation] [varchar](10) NULL,
		[Participant_Type] [varchar](1) NULL);
	CREATE TABLE #tmpEnrollments2 (enrollmentID int, memberNumber varchar(50), firstName varchar(75), lastName varchar(75),
		email varchar(200), Address_Phone varchar(40), NABP_ePID varchar(200), DOB varchar(10));
	CREATE TABLE #tmpMAMemberIDs (memberID int PRIMARY KEY);

	-- get enrollments
	SELECT e.enrollmentID, m.activeMemberID as MCMemberID, u.depomemberdataid, pEnrolled.orgCode, pEnrolled.participantID, 
		pPublisher.orgCode as pubOrg, e.dateEnrolled, e.dateCompleted, eswl.joinTime, eswl.exitTime, 
		eac.finaltimespent, eac.idNumber, sem.seminarID, sem.seminarName, 
		CASE WHEN sswod.onDemandID IS NOT NULL THEN 'On-Demand' WHEN swl.liveID IS NOT NULL THEN 'Webinar' ELSE NULL END AS SeminarType, 
		sac.courseApproval AS ACPE_UAN, 
		CASE WHEN swl.dateEnd IS NOT NULL THEN convert(varchar(10),swl.dateEnd,101) ELSE convert(varchar(10),e.datecompleted,101) END AS Date_Of_Participation, 
		CASE WHEN CSA.CSALinkID = 149 THEN 'P'
			WHEN CSA.CSALinkID = 204 THEN 'P'
			WHEN CSA.CSALinkID = 183 THEN 'P' 
			WHEN CSA.CSALinkID = 208 THEN 'T'
			WHEN CSA.CSALinkID = 206 THEN 'T'
			ELSE NULL
			END AS Participant_Type	
	INTO #tmpEnrollments
	FROM dbo.tblenrollments as e
	INNER JOIN dbo.tblParticipants pEnrolled on e.participantID = pEnrolled.participantID
	INNER JOIN dbo.tblSeminars as sem on sem.seminarID = e.seminarID
	INNER JOIN membercentral.dbo.ams_members as m ON m.memberID = e.MCMemberID
	LEFT OUTER JOIN dbo.tblSeminarsSWOD as sswod on sswod.seminarID = sem.seminarID
	LEFT OUTER JOIN dbo.tblSeminarsSWLive as swl on swl.seminarID = sem.seminarID
	INNER JOIN dbo.tblParticipants pPublisher on pPublisher.participantID = sem.participantID
	LEFT OUTER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
	INNER JOIN dbo.tblenrollmentsandcredit as eac on eac.enrollmentid = e.enrollmentid
	INNER JOIN dbo.tblSeminarsAndCredit as sac on eac.seminarCreditID = sac.seminarCreditID
	INNER JOIN dbo.tblCreditSponsorsAndAuthorities as csa on csa.csalinkid = sac.csalinkid
	INNER JOIN dbo.tblCreditSponsors as cs on cs.sponsorid = csa.sponsorid and cs.sponsorID = @sponsorID
	INNER JOIN dbo.tblCreditAuthorities as ca on ca.authorityid = csa.authorityid
	INNER JOIN dbo.tblUsers as u on u.userid = e.userid
	INNER JOIN trialsmith.dbo.depomemberdata as d on d.depomemberdataid = u.depomemberdataid 
	WHERE e.passed = 1
	AND e.datecompleted between @startdate and @enddate
	AND e.isActive = 1
	AND pPublisher.orgCode = @publishingOrg
	AND eac.earnedCertificate = 1
	AND (d.adminflag2 is null or d.adminflag2 <> 'Y');

	INSERT INTO #tmpMAMemberIDs (memberID)
	SELECT DISTINCT MCMemberID
	FROM #tmpEnrollments;

	SET @orgCode = 'TXRX';
	IF EXISTS (select top 1 enrollmentID from #tmpEnrollments where orgCode = @orgCode) BEGIN
		IF OBJECT_ID('tempdb..#tmpMAResults3') IS NOT NULL
			DROP TABLE #tmpMAResults3;
		CREATE TABLE #tmpMAResults3 (MCAutoID int IDENTITY(1,1) NOT NULL);

		SET @fieldsList = 'memberNumber,firstname,lastname,Email,Address_Home Phone,nabpNumber,dateOfBirth';
		SELECT @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode(@orgCode);

		EXEC membercentral.dbo.ams_getMemberDataByFields @orgID=@orgID, @fieldsList=@fieldsList,
			@membersTableName='#tmpMAMemberIDs', @membersResultTableName='#tmpMAResults3';

		insert into #tmpEnrollments2 (enrollmentID, membernumber, firstname, lastName, email, address_Phone, NABP_ePID, DOB)
		select e.enrollmentID, tmp.memberNumber, tmp.firstName, tmp.lastName, tmp.Email, tmp.[Address_Home Phone], 
			cast(tmp.nabpNumber as varchar(200)) as NABP_ePID,
			right('0' + rtrim(month(tmp.dateOfBirth)),2) + right('0' + rtrim(day(tmp.dateOfBirth)),2) as DOB
		from #tmpEnrollments as e
		inner join #tmpMAResults3 as tmp on tmp.memberID = e.MCMemberID
		where e.orgcode = @orgCode;

		IF OBJECT_ID('tempdb..#tmpMAResults3') IS NOT NULL
			DROP TABLE #tmpMAResults3;
	END

	insert into #tmpCPE (memberNumber, firstName, lastName, orgCode, participantID, pubOrg, dateEnrolled, dateCompleted,
		joinTime, exitTime, finaltimespent, seminarID, seminarName, SeminarType, ACPE_UAN, Date_Of_Participation, Participant_Type,
		email, Address_Phone, NABP_ePID, DOB)
	select e3.memberNumber, e3.firstName, e3.lastName, e.orgCode, e.participantID, e.pubOrg, e.dateEnrolled, e.dateCompleted, 
		e.joinTime, e.exitTime, e.finaltimespent, e.seminarID, e.seminarName, e.SeminarType, e.ACPE_UAN, e.Date_Of_Participation, 
		e.Participant_Type, e3.email, e3.Address_Phone, isnull(e3.NABP_ePID,e.idNumber) as NABP_ePID, e3.DOB
	from #tmpEnrollments as e
	inner join #tmpEnrollments2 as e3 on e3.enrollmentID = e.enrollmentID;

	-- final results
	SELECT DISTINCT
		dbo.fn_csvSafeString(memberNumber) as [MemberNumber]
		, dbo.fn_csvSafeString(firstName) AS [First Name]
		, dbo.fn_csvSafeString(lastName) AS [Last Name]
		, dbo.fn_csvSafeString(seminarName) AS [SeminarName]
		, dbo.fn_csvSafeString(seminarType) AS [Seminar Type]
		, orgCode AS [Signed Up On]
		, dbo.fn_csvSafeString(email) AS [Email]
		, dbo.fn_csvSafeString(Address_Phone) AS [Phone Number]
		, convert(varchar(10),dateEnrolled,101) as [Date Enrolled]
		, convert(varchar(10),dateCompleted,101) as [Date Completed]
		, dbo.fn_csvSafeString(joinTime) AS [Join Time]
		, dbo.fn_csvSafeString(exitTime) AS [Exit Time]
		, finaltimespent AS [Minutes Spent In Course]
		, dbo.fn_csvSafeString('I') as [Action] 
		, dbo.fn_csvSafeString(NABP_ePID) as NABP_ePID
		, dbo.fn_csvSafeString(DOB) as DOB
		, dbo.fn_csvSafeString(ACPE_UAN) as ACPE_UAN
		, dbo.fn_csvSafeString(Date_Of_Participation) as Date_Of_Participation
		, Participant_Type as Participant_Type
		, '' AS Participant_Count
	INTO ##tmpCPE
	FROM #tmpCPE
	ORDER BY [Last Name], OrgCode;

	-- export data
	DECLARE @cmd varchar(6000);
	declare @tmpBCP TABLE (theoutput varchar(max));
	set @cmd = 'bcp ##tmpCPE out ' + @filename + ' -c -t, -T -S' + CAST(serverproperty('servername') as varchar(40));
	insert into @tmpBCP (theoutput)
	exec master..xp_cmdshell @cmd;

	-- return count of records
	SELECT count(*) AS returnCount
	FROM ##tmpCPE;

	-- get fields returned
	EXEC tempdb.dbo.SP_COLUMNS ##tmpCPE;

	IF OBJECT_ID('tempdb..#tmpEnrollments') IS NOT NULL 
		DROP TABLE #tmpEnrollments;
	IF OBJECT_ID('tempdb..#tmpEnrollments2') IS NOT NULL 
		DROP TABLE #tmpEnrollments2;
	IF OBJECT_ID('tempdb..#tmpCPE') IS NOT NULL 
		DROP TABLE #tmpCPE;
	IF OBJECT_ID('tempdb..##tmpCPE') IS NOT NULL 
		DROP TABLE ##tmpCPE;
	IF OBJECT_ID('tempdb..#tmpMAMemberIDs') IS NOT NULL
		DROP TABLE #tmpMAMemberIDs;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
