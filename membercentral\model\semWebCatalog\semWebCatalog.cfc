<cfcomponent extends="model.AppLoader" output="no">

	<cfset defaultEvent = "controller">
	<cfset variables.applicationReservedURLParams = "item,panel,skair,regstp,rk,perr">

	<!--- check for bots --->
	<cfif isDefined("session.mcstruct.deviceProfile.is_bot") and session.mcstruct.deviceProfile.is_bot is 1>
		<cfset variables.isBot = 1>
	<cfelse>
		<cfset variables.isBot = 0>
	</cfif>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
	
		<cfset var local = structNew()>
		<cfset local.rc = arguments.event.getCollection()>
		<cfset local.semWeb = structNew()>
		<cfset local.parameterSet = structNew()>
		
		<!--- url validations --->
		<cfif NOT isSimpleValue(arguments.event.getValue('item',''))>
			<cflocation url="/?pg=semwebCatalog" addtoken="false">
		<cfelseif len(trim(arguments.event.getValue('item','')))>
			<cfset local.item = trim(arguments.event.getValue('item',''))>
			<cfif listLen(local.item,'-') is not 2 OR NOT IsNumeric(ListGetAt(local.item,2,'-'))>
				<cflocation url="/?pg=semwebCatalog" addtoken="false">
			</cfif>
		</cfif>

		<cfif NOT isSimpleValue(arguments.event.getValue('seminarID','')) OR (len(trim(arguments.event.getValue('seminarID',''))) AND NOT IsNumeric(trim(arguments.event.getValue('seminarID',''))))>
			<cflocation url="/?pg=semwebCatalog" addtoken="false">
		</cfif>

		<!--- get app instance settings --->
		<cfset variables.instanceSettings = getInstanceSettings(this.appInstanceID)>

		<!--- bootstrap detection --->
		<cfif (application.objCMS.getTemplateSetting(arguments.event,"supportsBootstrap") eq "true") or (isdefined("session.enableMobile") and session.enableMobile)>
			<cfset arguments.event.setValue('viewDirectory', 'responsive')>
		<cfelse>
			<cfset arguments.event.setValue('viewDirectory', 'default')>
		</cfif>

		<!--- set variables --->
		<cfset arguments.event.setValue("mainurl","/?pg=semwebCatalog")>
		<cfset arguments.event.setValue('locatorurl','/?event=cms.showResource&resID=#this.siteResourceID#&mode=stream')>
		<cfset local.semWeb.catalogPanel = arguments.event.getValue("panel","gateway")>
		<cfset local.semWeb.emailSuccessMsg = arguments.event.getValue("msg",0)>
		<cfset local.semWeb.mainurl = arguments.event.getValue('mainurl')>
		<cfset local.semWeb.baseurl = "#arguments.event.getValue('mc_siteInfo.scheme','http')#://#arguments.event.getValue('mc_siteInfo.mainhostname')#">
		<cfset local.semWeb.siteCode = local.rc.mc_siteInfo.siteCode>
		<cfset local.semWeb.orgname = local.rc.mc_siteInfo.orgname>
		<cfset local.semWeb.orgcode = local.rc.mc_siteinfo.orgcode>
		<cfset local.viewDirectory = arguments.event.getValue('viewDirectory')>

		<!--- verify we have a valid panel --->
		<cfif NOT isSimpleValue(local.semWeb.catalogPanel) OR NOT listFindNoCase("downloadFile,browse,addCredit,viewSpeakerInfo,insertSelfCLE,deleteSelfCLE,reg,addSWReg,showReceipt,downloadFile,My,showLive,showFAQ,showBundle,showCart,showSWOD,showSWODQA,showSWODQAQuestions,showSWODQAQuestionDetails,saveQ,viewCert,viewCertPDF,viewCPCert,viewCPCertPDF,gateway,joinLive,downloadICal,downloadGCal,downloadMaterialsDoc,emailMaterialsDoc",local.semWeb.catalogPanel)>
			<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="no">
		</cfif>

		<cfif variables.isBot eq 1 and local.semWeb.catalogPanel eq "reg">
			<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="no">
		</cfif>

		<!--- create objects --->
		<cfset local.objSWCommon = CreateObject("component","model.seminarweb.SWCommon")>
		<cfset local.objSWP = CreateObject("component","model.seminarweb.SWParticipants")>
		<cfset local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>
		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>
		<cfset local.objSWTL = CreateObject("component","model.seminarweb.SWTLTitles")>
		<cfset local.objCredit = CreateObject("component","model.seminarweb.SWCredits")>
		<cfset local.objAuthor = CreateObject("component","model.seminarweb.SWAuthors")>
		<cfset local.objCategory = CreateObject("component","model.seminarweb.SWCategories")>
		<cfset local.objSWFiles = CreateObject("component","model.seminarweb.SWFiles")>
		<cfset local.objSWBundle = CreateObject("component","model.seminarweb.SWBundles")>
		<cfset local.objSWODQA = CreateObject("component","model.seminarweb.SWODQA")>
		<cfset local.objFeaturedImages= CreateObject("component","model.admin.common.modules.featuredImages.featuredImages")>
		<cfset local.objSWBrowse = CreateObject("component","browse")>
		<cfset local.objSWReg = CreateObject("component","semwebReg")>
		
		<cfset local.dataFormat.objSWCommon = local.objSWCommon>
		<cfset local.dataFormat.objSWP = local.objSWP>
		<cfset local.dataFormat.objSWL = local.objSWL>
		<cfset local.dataFormat.objSWOD = local.objSWOD>
		<cfset local.dataFormat.objSWTL = local.objSWTL>
		<cfset local.dataFormat.objCredit = local.objCredit>
		<cfset local.dataFormat.objAuthor = local.objAuthor>
		<cfset local.dataFormat.objCategory = local.objCategory>
		<cfset local.dataFormat.objSWFiles = local.objSWFiles>
		<cfset local.dataFormat.objSWBundle = local.objSWBundle>
		
		<!--- check for participation in each format and get branding --->
		<cfset local.strAssociation = local.objSWP.getAssociationDetails(local.rc.mc_siteInfo.siteCode)>
		<cfif NOT structKeyExists(local.strAssociation, "qryAssociation")>
			<cflocation url="/" addtoken="No">
		<cfelse>
			<cfset local.qrySWP = local.strAssociation.qryAssociation>
			<cfset local.semWeb.qrySWP = local.qrySWP>
		</cfif>
		
		<!--- supports memberkey --->
		<cfset local.memberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=local.rc.mc_siteInfo.orgID)>
		<cfif local.memberID GT 0 and NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_getTLASITESDepoMemberDataIDByMemberID">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.memberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.rc.mc_siteInfo.siteID#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.depoMemberDataID">
			</cfstoredproc>	
		<cfelse>
			<cfset local.depoMemberDataID = session.cfcuser.memberdata.depomemberdataid>
		</cfif>

		<cfset local.viewToUse = 'echo'>
		
		<cfswitch expression="#local.semWeb.catalogPanel#">
		<cfcase value="browse">
			<!--- handle browse redirects from old browsing links --->
			<cfif len(arguments.event.getTrimValue('ft',''))>
				<cfif NOT len(arguments.event.getTrimValue('_swft',''))>
					<cfset arguments.event.setValue('_swft',arguments.event.getTrimValue('ft'))>
				</cfif>
				<cfset arguments.event.removeValue('ft')>
			</cfif>
			<cfif len(arguments.event.getTrimValue('aut',''))>
				<cfif NOT len(arguments.event.getTrimValue('_swa',''))>
					<cfset arguments.event.setValue('_swa',arguments.event.getTrimValue('aut'))>
				</cfif>
				<cfset arguments.event.removeValue('aut')>
			</cfif>
			<cfif len(arguments.event.getTrimValue('cle',''))>
				<cfif NOT len(arguments.event.getTrimValue('_swca','')) and int(val(arguments.event.getTrimValue('cle')))>
					<cfset arguments.event.setValue('_swca',int(val(arguments.event.getTrimValue('cle'))))>
				</cfif>
				<cfset arguments.event.removeValue('cle')>
			</cfif>
			<cfif len(arguments.event.getTrimValue('cat',''))>
				<cfif NOT len(arguments.event.getTrimValue('_sws',''))>
					<cfset arguments.event.setValue('_sws',arguments.event.getTrimValue('cat'))>
				</cfif>
				<cfset arguments.event.removeValue('cat')>
			</cfif>
			<cfif len(arguments.event.getTrimValue('pub',''))>
				<cfif NOT len(arguments.event.getTrimValue('_swp',''))>
					<cfset arguments.event.setValue('_swp',arguments.event.getTrimValue('pub'))>
				</cfif>
				<cfset arguments.event.removeValue('pub')>
			</cfif>

			<cfset local.strBrowseProgramArgs = { 
				siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
				catalogOrgCode=local.rc.mc_siteinfo.sitecode, 
				memberID=local.memberID, 
				depoMemberDataID=local.depomemberdataid, 
				qrySWP=local.qrySWP, 
				strAssociation=local.strAssociation, 
				swRegCart=local.objSWReg.swRegCartToQuery(), 
				strFilters=getSWProgramFilters(event=arguments.event, qrySWP=local.qrySWP),
				mode="browse"
				}>
			
			<cfset local.data = {
				"qrySWP":local.qrySWP,
				"semWeb":local.semWeb,
				"swRegCart":local.strBrowseProgramArgs.swRegCart,
				"strFilters":local.strBrowseProgramArgs.strFilters,
				"strBrowse":local.objSWBrowse.getSWPrograms(argumentCollection=local.strBrowseProgramArgs),
				"savedProgramsCount":local.objSWBrowse.getSWProgramsSavedForLater().recordCount,
				"hasActiveBundles": local.objSWBrowse.hasActiveBundles(catalogOrgCode=local.rc.mc_siteinfo.sitecode),
				"catalogOrgCode": local.rc.mc_siteinfo.sitecode
			}>
			<cfset local.viewToUse = 'semWebCatalog/#local.viewDirectory#/browseContent'>
		</cfcase>

		<!--- form to add CLE Credit --->
		<cfcase value="addCredit">
			<cfset local.data.qryGetCreditAuthorities = local.objCredit.getCreditAuthoritiesByOrgCode(event.getCollection())>
			<cfset local.data.pageName = 'Add Self-Reported Credit'>
			<cfset local.data.formlink = '/?pg=semwebCatalog&panel=insertSelfCLE&mode=stream'>
			<cfset local.viewToUse = 'semWebCatalog/#local.viewDirectory#/frm_addCredit'>
		</cfcase>

		<!--- view speaker details from program details page --->
		<cfcase value="viewSpeakerInfo">
			<cfif NOT len(arguments.event.getTrimValue('authorID','')) OR NOT IsNumeric(arguments.event.getTrimValue('authorID',''))>
				<cflocation url="/?pg=semwebCatalog" addtoken="false">
			</cfif>
			<cfset local.data.qryAuthor = createObject("component","model.seminarWeb.SWAuthors").getAuthor(authorID=arguments.event.getTrimValue('authorID'))>
			<cfset local.data.semWeb = local.semWeb>
			<cfset local.viewToUse = 'semWebCatalog/#local.viewDirectory#/showSpeakerInfo'>
		</cfcase>
		
		<!--- Insert CLE Credit to DB --->
		<cfcase value="insertSelfCLE">
			<cfsavecontent variable="local.data">
				<cfoutput>
					<cfscript>
						local.insert = local.objCredit.addSelfReportedCredits(event,local.depoMemberDataID);
					</cfscript>
					<script language="javascript">
			         top.location.reload();
					 top.closeBox();
					</script>
				</cfoutput>
			</cfsavecontent>
		</cfcase>
		
		<!--- Delete Self-Reported Event --->
		<cfcase value="deleteSelfCLE">
			<cfset local.objCredit.deleteSelfReportedCredits(ID=arguments.event.getValue("ID"), depoMemberDataID=local.depoMemberDataID)>
			<cflocation url="/?pg=semwebCatalog&panel=My" addtoken="false">
		</cfcase>

		<cfcase value="reg">
			<cfset local.data = local.objSWReg.doEventReg(event=arguments.event, settings=variables.instanceSettings)>
		</cfcase>
		<cfcase value="addSWReg">
			<cfset local.strResponse = local.objSWReg.addSWReg(event=arguments.event, settings=variables.instanceSettings, qrySWP=local.qrySWP)>
			<cfif local.strResponse.success>
				<cfset local.receiptKeyEnc = local.objSWReg.prepareRegReceipt(event=arguments.event, strAddReg=local.strResponse, qrySWP=local.qrySWP)>
				<cflocation url="#arguments.event.getValue('mainurl')#&panel=showReceipt&rk=#local.receiptKeyEnc#" addtoken="false">
			<cfelse>
				<cflocation url="/?pg=semwebCatalog&panel=showCart&perr=#urlEncodedFormat(local.strResponse.response)#" addtoken="false">
			</cfif>
		</cfcase>
		<cfcase value="showReceipt">
			<cfset local.data = local.objSWReg.showRegReceipt(event=arguments.event, qrySWP=local.qrySWP)>
		</cfcase>

		<!--- download title file --->
		<cfcase value="downloadFile">
			<cfset local.objSWFiles.downloadFile(extension=arguments.event.getTrimValue('extension',''), fileID=arguments.event.getTrimValue('fileID',''),
												 titleID=arguments.event.getTrimValue('titleID',''), enrollmentID=arguments.event.getTrimValue('enrollmentID',''),
												 depomemberDataID=local.depoMemberDataID, pvrFile=arguments.event.getTrimValue('pvrFile',''))>
			<cflocation url="/?pg=semWebCatalog" addtoken="no">
		</cfcase>

		<!--- downloading ICAL --->
		<cfcase value="downloadICal">
			<cfif NOT len(arguments.event.getTrimValue('seminarid','')) OR NOT IsNumeric(arguments.event.getTrimValue('seminarid',''))>
				<cflocation url="/?pg=semwebCatalog" addtoken="false">
			</cfif>

			<!--- Get the seminar --->
			<cfset local.qrySWLSeminar = local.objSWL.getSeminarBySeminarID(seminarID=arguments.event.getTrimValue('seminarid'))>
			<!--- no seminar found --->
			<cfif local.qrySWLSeminar.recordcount is 0>
				<cfsavecontent variable="local.data">
					<cfoutput>That seminar does not exist.</cfoutput>
				</cfsavecontent>
			<cfelse>
				<cfset local.qrySeminar = local.objSWL.addParsedTimeZoneToSeminars(qrySeminars=local.qrySWLSeminar, catalogOrgCode=local.rc.mc_siteinfo.sitecode)>
				<cfset local.qryProgramObjectives = local.objSWCommon.getProgramObjectives(programType="SWL", programID=arguments.event.getTrimValue('seminarid'))>		
				<cfset local.accessCode = arguments.event.getTrimValue('accesscode','')>
				<cfset local.frmView = arguments.event.getTrimValue('frmView',0)>
				<cfset local.title = local.qrySeminar.seminarName>
				<cfif len(local.accessCode) is 10>
					<cfset local.verifyReg = local.objSWL.verifySWLCode(local.accesscode)>
					<cfif val(local.verifyReg.isCodeValid)>
						<cfif local.verifyReg.SWLUserType eq "speaker">
							<cfset local.title = 'Webinar Speaker: '& local.title>
							<cfsavecontent variable="local.description"><cfoutput>Thank you for choosing to present: #EncodeForHTML(local.qrySeminar.seminarName)#\n\nProvided by: #local.strAssociation.qryAssociation.description#\n\nClick #local.strAssociation.qryAssociation.catalogURL#/?k=#local.accessCode# to enter the program.\n\nNEED CUSTOMER SUPPORT?\n Call (737) 201-2059 <NAME_EMAIL></cfoutput></cfsavecontent>
						<cfelse>
							<cfsavecontent variable="local.description"><cfoutput>You're registered for #EncodeForHTML(local.qrySeminar.seminarName)#\n\nProvided by: #local.strAssociation.qryAssociation.description#\n\nClick #local.strAssociation.qryAssociation.catalogURL#/?k=#local.accessCode# to enter the program.<cfif local.qryProgramObjectives.recordCount>\n\nLEARNING OBJECTIVES:\n<cfloop query="local.qryProgramObjectives">-#trim(local.qryProgramObjectives.objective)#\n</cfloop></cfif>\n\nNEED CUSTOMER SUPPORT?\n Call (737) 201-2059 <NAME_EMAIL></cfoutput></cfsavecontent>
						</cfif>
					<cfelse>
						<cfset local.description = local.qrySeminar.SeminarDesc>	
					</cfif>						
				<cfelse>
					<cfif local.frmView EQ 1>
						<cfset local.title = '[REMINDER] Register for '& local.title>
						<cfsavecontent variable="local.description"><cfoutput>This invite was added from a program registration page provided by SeminarWeb. It is not confirmation of registration.\n\nTo register for #EncodeForHTML(local.qrySeminar.seminarName)#, click: #local.strAssociation.qryAssociation.catalogURL#?pg=semwebcatalog&panel=reg&item=SWL-#arguments.event.getTrimValue('seminarid')#<cfif local.qryProgramObjectives.recordCount>\n\nLEARNING OBJECTIVES:\n<cfloop query="local.qryProgramObjectives">-#trim(local.qryProgramObjectives.objective)#\n</cfloop></cfif>\n\nNEED CUSTOMER SUPPORT?\n Call (737) 201-2059 <NAME_EMAIL></cfoutput></cfsavecontent>
						<cfelse>
						<cfset local.description = local.qrySeminar.SeminarDesc>
					</cfif>
				</cfif>

				<!--- generate ics content --->
				<cfset local.objTSTZ = CreateObject("component","model.system.platform.tsTimeZone")>
				<cfset local.qryTZ = local.objTSTZ.getTimeZones()>
				<cfquery name="local.qryTZ_selected" dbtype="query">
					select timeZoneID
					from [local].qryTZ
					where timeZone = '#local.qrySeminar.dspTZ#'
				</cfquery>
				<cfset local.data = CreateObject("component","model.system.common.ICALGenerator").getICALFile(local.rc.mc_siteinfo.siteName, 0, local.qrySeminar.dspStartDate,
					local.qrySeminar.dspEndDate, local.qryTZ_selected.timeZoneID, "DEFAULT", "", "", htmleditformat(local.title), local.description, 
					'DEFAULT', 'DEFAULT')>

				<!--- save to a file and send to browser --->
				<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=local.rc.mc_siteinfo.sitecode)>
				<cfset local.reportFileName = "#ReReplaceNoCase(local.title,'[^A-Z0-9]','','ALL')#.ics">
				<cffile file="#local.strFolder.folderPath#/#local.reportFileName#" action="write" output="#local.data#" addnewline="false">
				<cfset application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
			</cfif>
		</cfcase>

		<!--- generate GCAL link --->
		<cfcase value="downloadGCal">
			<cfif NOT len(arguments.event.getTrimValue('seminarid','')) OR NOT IsNumeric(arguments.event.getTrimValue('seminarid',''))>
				<cflocation url="/?pg=semwebCatalog" addtoken="false">
			</cfif>

			<!--- Get the seminar --->
			<cfset local.qrySWLSeminar = local.objSWL.getSeminarBySeminarID(seminarID=arguments.event.getTrimValue('seminarid'))>
			<!--- no seminar found --->
			<cfif local.qrySWLSeminar.recordcount is 0>
				<cfsavecontent variable="local.data">
					<cfoutput>That seminar does not exist.</cfoutput>
				</cfsavecontent>
			<cfelse>
				<cfset local.qrySeminar = local.objSWL.addParsedTimeZoneToSeminars(qrySeminars=local.qrySWLSeminar, catalogOrgCode=local.rc.mc_siteinfo.sitecode)>
				<cfset local.qryProgramObjectives = local.objSWCommon.getProgramObjectives(programType="SWL", programID=arguments.event.getTrimValue('seminarid'))>
				<cfset local.accessCode = arguments.event.getTrimValue('accesscode','')>
				<cfset local.frmView = arguments.event.getTrimValue('frmView',0)>
				<cfset local.title = local.qrySeminar.seminarName>
				<cfset local.linebreak = "#chr(13)##chr(10)#">
				<cfif len(local.accessCode) is 10>
					<cfset local.verifyReg = local.objSWL.verifySWLCode(local.accesscode)>
					<cfif val(local.verifyReg.isCodeValid)>
						<cfif local.verifyReg.SWLUserType eq "speaker">
							<cfset local.title = 'Webinar Speaker: '& local.title>
							<cfsavecontent variable="local.gcalDetails"><cfoutput>Thank you for choosing to present: #encodeForHTML(local.qrySeminar.seminarName)##local.linebreak##local.linebreak#
						Provided by: #local.strAssociation.qryAssociation.description##local.linebreak##local.linebreak#
						Click #local.strAssociation.qryAssociation.catalogURL#/?k=#local.accessCode# to enter the program.#local.linebreak##local.linebreak#
						NEED CUSTOMER SUPPORT?#local.linebreak#
						Call (737) 201-2059 <NAME_EMAIL></cfoutput></cfsavecontent>
						<cfelse>
							<cfsavecontent variable="local.gcalDetails"><cfoutput>You're registered for #EncodeForHTML(local.qrySeminar.seminarName)##local.linebreak##local.linebreak#
						Provided by: #local.strAssociation.qryAssociation.description##local.linebreak##local.linebreak#
						Click #local.strAssociation.qryAssociation.catalogURL#/?k=#local.accessCode# to enter the program.#local.linebreak##local.linebreak#
						<cfif local.qryProgramObjectives.recordCount>
							LEARNING OBJECTIVES:#local.linebreak#
							<cfloop query="local.qryProgramObjectives">
								-#trim(local.qryProgramObjectives.objective)##local.linebreak#
							</cfloop>
							#local.linebreak##local.linebreak#
						</cfif>
						NEED CUSTOMER SUPPORT?#local.linebreak#
						Call (737) 201-2059 <NAME_EMAIL></cfoutput></cfsavecontent>
						</cfif>
					<cfelse>
						<cfset local.gcalDetails = local.qrySeminar.SeminarDesc>	
					</cfif>	
					
				<cfelse>
					<cfif local.frmView EQ 1>
						<cfset local.title = '[REMINDER] Register for '& local.title>
						<cfsavecontent variable="local.gcalDetails"><cfoutput>This invite was added from a program registration page provided by SeminarWeb. It is not confirmation of registration.#local.linebreak##local.linebreak#
						To register for #local.qrySeminar.seminarName#, click: #local.strAssociation.qryAssociation.catalogURL#?pg=semwebcatalog&panel=reg&item=SWL-#arguments.event.getTrimValue('seminarid')##local.linebreak##local.linebreak#
						<cfif local.qryProgramObjectives.recordCount>
							LEARNING OBJECTIVES:#local.linebreak#
							<cfloop query="local.qryProgramObjectives">
								-#trim(local.qryProgramObjectives.objective)##local.linebreak#
							</cfloop>
							#local.linebreak##local.linebreak#
						</cfif>
						NEED CUSTOMER SUPPORT?#local.linebreak#
						Call (737) 201-2059 <NAME_EMAIL></cfoutput></cfsavecontent>
					
					<cfelse>
						<cfset local.gcalDetails = local.qrySeminar.SeminarDesc>
					</cfif>
				</cfif>

				<!--- Google descript max length 8000 --->
				<cfif len(local.gcalDetails) GTE 8000>
					<cfset local.gcalDetails = LEFT(local.gcalDetails, 7900) & "...">
				</cfif>
				<cfset local.googleCalendarLink = application.objCommon.getGoogleCalendarLink(text=local.title, details=local.gcalDetails,
					location='', website='', startDate=local.qrySeminar.dspStartDate, endDate=local.qrySeminar.dspEndDate, isAllDayEvent=false, timeZone=local.qrySeminar.dspTZ)>

				<cflocation url="#local.googleCalendarLink#" addtoken="false">
			</cfif>
		</cfcase>
		
		<cfcase value="downloadMaterialsDoc">
			<cfset local.data = downloadMaterialsDoc(event=arguments.event)>
		</cfcase>
		<cfcase value="emailMaterialsDoc">
			<cfset local.data = emailMaterialsDoc(event=arguments.event)>
		</cfcase>

		<cfcase value="My">
			<cfif local.memberID EQ 0>
				<cfset application.objWebsite.forceLogin(event=arguments.event)>
			</cfif>

			<cfset local.strArgs = { 
				siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
				catalogOrgCode=local.rc.mc_siteinfo.sitecode, 
				memberID=local.memberID, 
				depoMemberDataID=local.depomemberdataid, 
				qrySWP=local.qrySWP, 
				strAssociation=local.strAssociation, 
				swRegCart=local.objSWReg.swRegCartToQuery(), 
				strFilters=getSWProgramFilters(event=arguments.event, qrySWP=local.qrySWP),
				mode="myFeatured"
			}>

			<cfset local.data = {
				"panel":local.semWeb.catalogPanel,
				"qrySWP":local.qrySWP,
				"semWeb":local.semWeb,
				"heading":local.qrySWP.brandMyCLETab,
				"swRegCart":local.strArgs.swRegCart,
				"strHistory":getMyHistory(siteID=local.strArgs.siteID, catalogOrgCode=local.strArgs.catalogOrgCode,
								memberID=local.memberID, depoMemberDataID=local.depomemberdataid, qrySWP=local.qrySWP,
								fStartDate=arguments.event.getValue('fStartDate',''), fEndDate=arguments.event.getValue('fEndDate',''),
								fStartDateCompleted='', fEndDateCompleted=''),
				"strFeaturedPrograms":local.objSWBrowse.getSWPrograms(argumentCollection=local.strArgs),
				"savedProgramsCount":local.objSWBrowse.getSWProgramsSavedForLater().recordCount,
				"objSWBrowse":local.objSWBrowse
			}>

			<cfif arguments.event.valueExists('exportPDF')>
				<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
				<cfset local.reportFileName = reReplaceNoCase(local.data.heading,"[^A-Z0-9]","","ALL") & ".pdf">

				<cfsavecontent variable="local.data">
					<cfoutput>
					<cfinclude template="viewMyHistoryPDFContent.cfm">
					</cfoutput>
				</cfsavecontent>

				<cfdocument filename="#local.strFolder.folderPath#/#local.reportFileName#" pagetype="letter" margintop="0.5" marginbottom="0.5" marginright="0.5" format="PDF" marginleft="0.5" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
					<cfoutput>
					<cfdocumentsection>
						#local.data#
					</cfdocumentsection>
					</cfoutput>
				</cfdocument>

				<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
				<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
				<cfif not local.docResult>
					<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="false">
				</cfif>
			<cfelse>
				<cfset local.viewToUse = 'semWebCatalog/#local.viewDirectory#/myHistoryContent'>
			</cfif>
		</cfcase>
		
		<cfcase value="showFAQ">
			<cfquery name="local.qryContactInfo" datasource="#application.dsn.TLASITES_TRIALSMITH.dsn#">
				SELECT TOP 1 [Description], phone
				FROM dbo.depoTLA
				WHERE [state] = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.rc.mc_siteinfo.sitecode#">
			</cfquery>

			<cfquery name="local.qryCatalogSummary" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @participantID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qrySWP.participantID#">;

				SELECT (
						SELECT count(s.seminarID)
						FROM dbo.tblSeminars AS s
						INNER JOIN dbo.tblSeminarsSWLive AS swl ON s.seminarID = swl.seminarID
						WHERE s.participantID = @participantID
						AND s.isDeleted = 0
						AND s.offerCertificate = 1
					) AS swlOffersCertificateCount,
					(
						SELECT count(s.seminarID)
						FROM dbo.tblSeminars AS s
						INNER JOIN dbo.tblSeminarsSWOD AS swl ON s.seminarID = swl.seminarID
						WHERE s.participantID = @participantID
						AND s.isDeleted = 0
						AND s.offerCertificate = 1
					) AS swodOffersCertificateCount;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset local.data = {
				"panel":local.semWeb.catalogPanel,
				"qrySWP":local.qrySWP,
				"semWeb":local.semWeb,
				"heading":"FAQ",
				"qryContactInfo":local.qryContactInfo,
				"qryCatalogSummary":local.qryCatalogSummary,
				"swRegCart":local.objSWReg.swRegCartToQuery(),
				"savedProgramsCount":local.objSWBrowse.getSWProgramsSavedForLater().recordCount
			}>

			<cfset local.viewToUse = 'semWebCatalog/#local.viewDirectory#/showFAQContent'>
		</cfcase>

		<cfcase value="showCart">
			<cfscript>
				// make sure there are items in the cart
				local.objSWReg.paramRegSession(item=0);

				local.data = {
					"panel":local.semWeb.catalogPanel,
					"qrySWP":local.qrySWP,
					"semWeb":local.semWeb,
					"heading":"Pending Registrations",
					"hasSavedProgramsNotInCart":false,
					"swRegCart":local.objSWReg.swRegCartToQuery(),
					"savedProgramsCount":local.objSWBrowse.getSWProgramsSavedForLater().recordCount
				};

				local.data.swReg = application.mcCacheManager.sessionGetValue(keyname="swReg", defaultValue={});
				local.data.swRegCartTotals = local.objSWReg.getRegCartTotals(qryRegCart=local.data.swRegCart);
				local.data.displayedCurrencyType = local.qrySWP.showUSD ? " USD" : "";
				local.qrySavedPrograms = local.objSWBrowse.getSWProgramsSavedForLater();
				
				if (local.data.swRegCart.recordCount AND local.qrySavedPrograms.recordCount) {
					local.cartItemList = valueList(local.data.swRegCart.item);
					cfloop(query = local.qrySavedPrograms) {
						if (NOT listFindNoCase(local.cartItemList,"#local.qrySavedPrograms.programType#-#local.qrySavedPrograms.programID#")) {
							local.data.hasSavedProgramsNotInCart = true;
							break;
						}
					}
				}

				if (local.data.swRegCart.recordCount) {
					query dbtype="query" name="local.data.qrySWRegCartSorted" {echo("
						SELECT *
						FROM [local].data.swRegCart
						ORDER BY memberName, programName
					")};
	
					// members in the regcart
					local.data.qryMembersInCart = local.objSWReg.getMembersFromCart(qryRegCart=local.data.swRegCart);
	
					local.data.offerCoupon = local.objSWReg.offerCouponForSWReg(siteID=arguments.event.getValue('mc_siteinfo.siteID'), swReg=local.data.swReg, totalDiscount=local.data.swRegCartTotals.totalDiscount);
					local.data.noPaymentStatement = "No payment is due for these registrations.";

					// merchant profiles allowed
					local.data.paymentGateways = local.objSWReg.swRegCartProfiles(orgID=arguments.event.getValue('mc_siteinfo.orgID'), participantID=local.qrySWP.participantID, 
													handlesOwnPayment=local.qrySWP.handlesOwnPayment, swReg=local.data.swReg);

					// pricing overrides
					local.data.hasAmountToCharge = local.data.swRegCartTotals.actualTotal GT 0;
					local.data.showPaymentArea = true;
					if (NOT local.data.hasAmountToCharge) {
						if (local.qrySWP.handlesOwnPayment EQ 1) {
							query dbtype="query" name="local.data.qryRegCartItems" {echo("
								SELECT DISTINCT item
								FROM [local].data.swRegCart
							")};
		
							// hide payment area if one program or one payment gateway and no amount to charge
							local.data.showPaymentArea = NOT (local.data.qryRegCartItems.recordCount EQ 1 OR local.data.paymentGateways.recordCount EQ 1);
						} else {
							local.data.showPaymentArea = false;
						}
					}
	
					local.data.useMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteinfo.orgID'));
					// If NotLoggedIn First Registrant considered as Main Registrant
					if (NOT local.data.useMemberID)  {
						local.data.useMemberID = listFirst(valueList(local.data.qryMembersInCart.memberid));
						application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=local.data.useMemberID, setCookie=false);
					}

					local.payerDepoMemberID = 0;
					if(application.objUser.isLoggedIn(cfcuser=session.cfcuser))
						local.payerDepoMemberID = val(session.cfcuser.memberdata.depomemberdataid);
					if(not local.payerDepoMemberID){
						query dbtype="query" name="local.qryUseDepoMemberData" {echo("
							SELECT depomemberdataid
							FROM [local].data.swRegCart
							WHERE memberid = #int(val(local.data.useMemberID))#
						")};

						local.payerDepoMemberID = local.qryUseDepoMemberData.recordCount ? val(local.qryUseDepoMemberData.depomemberdataid) : local.data.swRegCart.depomemberdataid;
					}
					local.data.qryMemberData = application.objMember.getTSMemberData(depoMemberDataID=local.payerDepoMemberID);
				}

				local.viewToUse = 'semWebCatalog/#local.viewDirectory#/showCartContent';
			</cfscript>
		</cfcase>

		<cfdefaultcase>
			<cfoutput>
				<cfset local.dataFormat.qrySWP = local.qrySWP>
				<cfset local.dataFormat.semWeb = local.semWeb>
				<cfset local.dataFormat.swRegCart = local.objSWReg.swRegCartToQuery()>

				<cfif local.semWeb.catalogPanel eq 'showLive'>
					<!--- Validate seminarID --->
					<cfset local.dataFormat.seminarID = val(arguments.event.getValue("seminarID",0))>
					<cfif (NOT IsNumeric(local.dataFormat.seminarID) OR NOT isValid('integer',local.dataFormat.seminarID))>
						<cfset local.dataFormat.seminarID = 0>
					</cfif>
					<!--- check for valid participation --->
					<cfif NOT local.qrySWP.isSWL>
						<cflocation url = "#arguments.event.getValue('mainurl')#"  addtoken="no">
					</cfif>
					<!--- Get the seminar --->
					<!--- pass in blank billingstate .. dont need it for pricing on this page. --->
					<cfset local.dataFormat.strSeminar = local.objSWL.getSeminarForCatalog(seminarID=local.dataFormat.seminarID, catalogOrgCode=local.rc.mc_siteinfo.sitecode, billingState='', billingZIP='', depoMemberDataID=local.depomemberdataid, memberID=local.memberID)>
					<cfif local.dataFormat.strSeminar.qrySeminar.recordcount is 0 or (NOT local.dataFormat.strSeminar.qrySeminar.isPublished and event.getValue("adminpreview") is not 1)>
						<cflocation url = "#arguments.event.getValue('mainurl')#"  addtoken="no">
					</cfif>
					<cfset local.dataFormat.publisherOrgCode = local.dataFormat.strSeminar.qrySeminar.publisherOrgCode>
					<cfif len(local.dataFormat.strSeminar.qrySeminar.seminarSubTitle)>
						<cfset local.rc.mc_pageDefinition.pageTitle = "#encodeForHTML(local.dataFormat.strSeminar.qrySeminar.seminarName)#: #encodeForHTML(local.dataFormat.strSeminar.qrySeminar.seminarSubTitle)# | #local.rc.mc_pageDefinition.pageTitle#">
					<cfelse>
						<cfset local.rc.mc_pageDefinition.pageTitle = "#encodeForHTML(local.dataFormat.strSeminar.qrySeminar.seminarName)# | #local.rc.mc_pageDefinition.pageTitle#">
					</cfif>
					<!--- is registration closed? reg closes when event starts or when allowregistrants is 0--->
					<cfset local.dataFormat.isRegOpen = true>
					<cfif now() gte local.dataFormat.strSeminar.qrySeminar.dateStart OR NOT local.dataFormat.strSeminar.qrySeminar.allowRegistrants>
						<cfset local.dataFormat.isRegOpen = false>
					</cfif>
					
					<!--- Get credit grid for display--->
					<cfset local.dataFormat.strCredit = local.objCredit.getCreditsforSeminar(seminarID=local.dataFormat.strSeminar.qrySeminar.seminarID, siteCode=local.rc.mc_siteinfo.sitecode)>
					<cfset local.dataFormat.JSStructCreditInfo = local.objCredit.getCreditInfoForCatalog(local.dataFormat.strCredit.qryCredit)>
					<!--- get bundles containing seminar--->
					<cfset local.dataFormat.qryBundles = local.objSWBundle.getBundlesContainingSeminar(local.dataFormat.strSeminar.qrySeminar.seminarID,local.rc.mc_siteinfo.sitecode)>
					<!--- Get "You May Also Be Interested In" List--->
					<cfset local.dataFormat.seminarSuggestions = local.objSWOD.getSeminarSuggestions(local.dataFormat.seminarID,local.rc.mc_siteinfo.orgcode)>
					<!--- get author info--->
					<cfset local.dataFormat.speakerBio = local.objAuthor.getSpeakersInfoByProgramIDForCatalog(programID=local.dataFormat.strSeminar.qrySeminar.seminarID, programType="SWL", mode=local.viewDirectory)>

					<cfset local.dataFormat.programID = local.dataFormat.seminarID>
					<cfset local.dataFormat.programType = 'swl'>

					<cfset local.strSeminarHost = {
						"sitecode":arguments.event.getValue('mc_siteInfo.sitecode'),
						"mainurl":arguments.event.getValue('mainurl'),
						"sitename":arguments.event.getValue('mc_siteInfo.sitename'),
						"mainhostname":arguments.event.getValue('mc_siteInfo.mainhostname')
					}>
					<cfset local.dataStruct.actionStruct.structuredSWLJSON_LD = local.objSWL.getStructuredSWLData(strSeminar=local.dataFormat.strSeminar, strSeminarHost=local.strSeminarHost)>

					<cfsavecontent variable="local.swlJSON_LD">
						<cfoutput>
							<script type="application/ld+json">
								#serializeJSON(local.dataStruct.actionStruct.structuredSWLJSON_LD)#
							</script>
						</cfoutput>
					</cfsavecontent>
					<cfhtmlhead text="#local.swlJSON_LD#">
					
				<cfelseif local.semWeb.catalogPanel eq 'showBundle'>
					<!--- Validate bundleID--->
					<cfset local.dataFormat.bundleID = arguments.event.getValue("bundleID",0)>
					<cfif NOT IsNumeric(local.dataFormat.bundleID) OR NOT isValid('integer',local.dataFormat.bundleID)>
						<cfset local.dataFormat.bundleID = 0>
					</cfif>

					<!--- check for valid participation--->
					<cfif NOT local.qrySWP.isSWL AND NOT local.qrySWP.isSWOD>
						<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="no">
					</cfif>

					<!--- Get the bundle. pass in blank billingstate.. dont need it for pricing on this page.--->
					<cfset local.dataFormat.strBundle = local.objSWBundle.getBundleForCatalog(bundleID=local.dataFormat.bundleID, 
						catalogOrgCode=local.rc.mc_siteinfo.sitecode, billingState='', billingZip='', MCMemberID=local.memberID)>
					<cfif local.dataFormat.strBundle.qryBundle.recordcount is 0 or local.dataFormat.strBundle.qryBundle.status neq 'A'>
						<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="no">
					</cfif>
					<cfset local.dataFormat.publisherOrgCode = local.dataFormat.strBundle.qryBundle.publisherOrgCode>
					<cfif len(local.dataFormat.strBundle.qryBundle.bundleSubTitle)>
						<cfset local.rc.mc_pageDefinition.pageTitle = "#local.dataFormat.strBundle.qryBundle.bundleName#: #local.dataFormat.strBundle.qryBundle.bundleSubTitle# | #local.rc.mc_pageDefinition.pageTitle#">
					<cfelse>
						<cfset local.rc.mc_pageDefinition.pageTitle = "#local.dataFormat.strBundle.qryBundle.bundleName# | #local.rc.mc_pageDefinition.pageTitle#">
					</cfif>

					<!--- get items within bundle--->
					<cfset local.dataFormat.qryItems = local.objSWBundle.getBundledItemsForCatalogDeep(local.dataFormat.strBundle.qryBundle.bundleID,local.memberID)>
					<!--- get fontawesome icons for file extentions --->
					<cfset local.dataFormat.faClassesFiles = getFAClassesForFileIcons()>
					<cfset local.dataFormat.programID = local.dataFormat.bundleID>
					<cfset local.dataFormat.programType = 'swb'>
					
				<cfelseif local.semWeb.catalogPanel eq 'showSWOD'>
					<!--- Validate seminarID--->
					<cfset local.dataFormat.seminarID = arguments.event.getValue("seminarID",0)>
					<cfif NOT IsNumeric(local.dataFormat.seminarID) OR NOT isValid('integer',local.dataFormat.seminarID)>
						<cfset local.dataFormat.seminarID = 0>
					</cfif>
					<!--- check for valid participation--->
					<cfif NOT local.qrySWP.isSWOD>
						<cflocation url = "#arguments.event.getValue('mainurl')#"  addtoken="no">
					</cfif>
					<!--- Get the seminar--->
					<!--- pass in blank billingstate .. dont need it for pricing on this page.--->
					<cfset local.dataFormat.strSeminar = local.objSWOD.getSeminarForCatalog(seminarID=local.dataFormat.seminarID, catalogOrgCode=local.rc.mc_siteinfo.sitecode, billingState='', billingZip='', depoMemberDataID=local.depomemberdataid, memberID=local.memberID)>
					<cfif local.dataFormat.strSeminar.qrySeminar.recordcount is 0 or (NOT local.dataFormat.strSeminar.qrySeminar.isPublished and event.getValue("adminpreview") is not 1)>
						<cflocation url = "#arguments.event.getValue('mainurl')#"  addtoken="no">
					</cfif>
					<cfset local.dataFormat.publisherOrgCode = local.dataFormat.strSeminar.qrySeminar.publisherOrgCode>
					<cfif len(local.dataFormat.strSeminar.qrySeminar.seminarSubTitle)>
						<cfset local.rc.mc_pageDefinition.pageTitle = "#encodeForHTML(local.dataFormat.strSeminar.qrySeminar.seminarName)#: #encodeForHTML(local.dataFormat.strSeminar.qrySeminar.seminarSubTitle)# | #local.rc.mc_pageDefinition.pageTitle#">
					<cfelse>
						<cfset local.rc.mc_pageDefinition.pageTitle = "#encodeForHTML(local.dataFormat.strSeminar.qrySeminar.seminarName)# | #local.rc.mc_pageDefinition.pageTitle#">
					</cfif>
					<!--- is registration closed? reg closes when allowregistrants is 0--->
					<cfset local.dataFormat.isRegOpen = true>
					<cfif NOT local.dataFormat.strSeminar.qrySeminar.allowRegistrants>
						<cfset local.dataFormat.isRegOpen = false>
					</cfif>
					<!--- prereqs--->
					<cfset local.dataFormat.qryPreReqs = local.objSWOD.getSeminarPreReqs(local.dataFormat.strSeminar.qrySeminar.seminarID,local.depomemberdataid)>
					<cfset local.dataFormat.preReqFulfilled = 1>
					<cfif local.dataFormat.qryPreReqs.recordcount is 1 and NOT local.dataFormat.qryPreReqs.preReqFulfilled>
						<cfset local.dataFormat.preReqFulfilled = 0>
					</cfif>
					<!--- Get credit grid for display--->
					<cfset local.dataFormat.strCredit = local.objCredit.getCreditsforSeminar(seminarID=local.dataFormat.strSeminar.qrySeminar.seminarID, siteCode=local.rc.mc_siteinfo.sitecode)>
					<cfset local.dataFormat.JSStructCreditInfo = local.objCredit.getCreditInfoForCatalog(local.dataFormat.strCredit.qryCredit)>
					<!--- get titles within seminar--->
					<cfset local.dataFormat.qryOutline = local.objSWTL.getTitlesAndFilesWithinSeminar(local.dataFormat.strSeminar.qrySeminar.seminarID,local.rc.mc_siteinfo.sitecode)>
					<!--- get bundles containing seminar--->
					<cfset local.dataFormat.qryBundles = local.objSWBundle.getBundlesContainingSeminar(local.dataFormat.strSeminar.qrySeminar.seminarID,local.rc.mc_siteinfo.sitecode)>
					<!--- Get "You May Also Be Interested In" List--->
					<cfset local.dataFormat.seminarSuggestions = local.objSWOD.getSeminarSuggestions(local.dataFormat.seminarID,local.rc.mc_siteinfo.orgcode)>
					<!--- get author info--->
					<cfset local.dataFormat.speakerBio = local.objAuthor.getSpeakersInfoByProgramIDForCatalog(programID=local.dataFormat.strSeminar.qrySeminar.seminarID, programType="SWOD", mode=local.viewDirectory)>
					<!--- get video preview link --->
					<cfset local.dataFormat.videoPreviewInfo = local.objSWCommon.getVideoPreviewLinkInfoByProgramID(programType="SWOD",programID=local.dataFormat.strSeminar.qrySeminar.seminarID)>
					<!--- get fontawesome icons for file extentions --->
					<cfset local.dataFormat.faClassesFiles = getFAClassesForFileIcons()>
					<cfset local.dataFormat.programID = local.dataFormat.seminarID>
					<cfset local.dataFormat.programType = 'swod'>

				<cfelseif local.semWeb.catalogPanel eq 'showSWODQA'>
					<cfif len(arguments.event.getValue('swodkey',''))>
						<cfset local.tmpDecryptedString = local.objSWODQA.getDecryptedString(strEncrypted=arguments.event.getValue('swodkey',''))>
						<cfif listLen(local.tmpDecryptedString) is 3>
							<cfset local.dataFormat.swodQAStruct = StructNew()>
							<cfset local.dataFormat.swodQAStruct.seminarID = listGetAt(local.tmpDecryptedString, 1)>
							<cfset local.dataFormat.swodQAStruct.questionID = listGetAt(local.tmpDecryptedString, 2)>
							<cfset local.dataFormat.swodQAStruct.authorID = listGetAt(local.tmpDecryptedString, 3)>
							<cfif local.objSWODQA.isQACodeExists(local.dataFormat.swodQAStruct.seminarID,local.dataFormat.swodQAStruct.questionID,local.dataFormat.swodQAStruct.authorID)>
								<cfset session.swodQAStruct = duplicate(local.dataFormat.swodQAStruct)>
								<cfset session.swodQAStruct.authorName = local.objSWODQA.getAuthorName(local.dataFormat.swodQAStruct.authorID)>
								<cflocation url="/?pg=semwebCatalog&panel=showSWODQAQuestionDetails&questionID=#local.dataFormat.swodQAStruct.questionID#" addtoken="no">
							<cfelse>
								<cfset local.semWeb.catalogPanel = "showSWODQABadKey">
							</cfif>
						<cfelse>
							<cfset local.semWeb.catalogPanel = "showSWODQABadKey">
						</cfif>
					<cfelse>
						<cfset local.semWeb.catalogPanel = "showSWODQABadKey">
					</cfif>

				<cfelseif local.semWeb.catalogPanel eq 'showSWODQAQuestionDetails'>
					<cfif not isDefined("session.swodQAStruct")>
						<cfset local.semWeb.catalogPanel = "showSWODQABadKey">
					<cfelse>
						<cfset local.dataFormat.questionID = arguments.event.getValue('questionID','0')>
						<cfset local.dataFormat.swodQAStruct = session.swodQAStruct>
						<cfset local.dataFormat.qryQuestion = local.objSWODQA.getQuestion(local.dataFormat.questionID)>
						<cfset local.dataFormat.qryAnswers = local.objSWODQA.getAnswersFromQuestion(local.dataFormat.questionID)>
						<cfset local.dataFormat.qryQuestioner = local.objSWODQA.getQAQuestionerByQuestionID(local.dataFormat.questionID)>
						<cfset local.dataFormat.swodQAObj = local.objSWODQA>
					</cfif>

				<cfelseif local.semWeb.catalogPanel eq 'showSWODQAQuestions'>
					<cfif not isDefined("session.swodQAStruct")>
						<cfset local.semWeb.catalogPanel = "showSWODQABadKey">
					<cfelse>
						<cfset local.dataFormat.qryQuestions = local.objSWODQA.getAuthorQuestions(session.swodQAStruct.authorID)>
						<cfset local.dataFormat.swodQAObj = local.objSWODQA>
					</cfif>

				<cfelseif local.semWeb.catalogPanel eq 'saveQ'>
					<cfset local.questionID = arguments.event.getValue('questionID','0')>
					<cfif arguments.event.getValue('qaEditType','') EQ "save">
						<cfset local.messageUpdate = arguments.event.getValue('messageUpdate','')>
						<cfset local.answerID = arguments.event.getValue('answerID','0')>
						<cfset local.objSWODQA.updateAnswer(local.answerID, local.messageUpdate)>
						<cfset local.objSWODQA.replyToQuestioner(local.questionID, local.answerID)>
					<cfelseif arguments.event.getValue('qaEditType','') EQ "add">
						<cfset local.messageAdd = arguments.event.getValue('messageAdd','')>
						<cfset local.answerID = local.objSWODQA.addAnswer(local.questionID, session.swodQAStruct.authorID, local.messageAdd)>
						<cfset local.objSWODQA.replyToQuestioner(local.questionID, local.answerID)>
					</cfif>
					<cflocation url="/?pg=semwebCatalog&panel=showSWODQAQuestionDetails&questionID=#local.questionID#" addtoken="no">

				<cfelseif local.semWeb.catalogPanel eq 'joinLive'>
					<cfif val(arguments.event.getValue('joinsubmitted',0)) and len(arguments.event.getValue('uniqueCode',''))>

						<cfset local.swlcode = reReplaceNoCase(arguments.event.getValue('uniqueCode'),"[^A-Z]","","ALL")>
						<cfset local.tabset = arguments.event.getValue('tab',0)>
						<cfset local.verifyReg = local.objSWL.verifySWLCode(local.swlcode)>
						<cfset local.errMesg = "">
						<cfset local.dataFormat.strHighlightMessage = "">
						<cfset local.dataFormat.tabset = local.tabset>

						 <!---  is code valid for something? --->
						<cfif val(local.verifyReg.isCodeValid)>
							<cfif local.verifyReg.SWLUserType eq "speaker">
								<!---  is seminar open, just redirect to SWL Provider. otherwise, show done. --->
								<cfif val(local.verifyReg.seminarIsOpen) and val(local.verifyReg.providerID) gt 0 and len(local.verifyReg.joinURL)>
									<cfswitch expression="#local.verifyReg.providerID#">
										<!--- Zoom --->
										<cfcase value="3">
											<cflocation url="#local.verifyReg.joinURL#" addtoken="false">
										</cfcase>
									</cfswitch>
								</cfif>
							</cfif>
							
							<cfset local.dataFormat.swlcode = local.swlcode>
							<cfset local.objSWP = CreateObject("component","model.seminarweb.SWParticipants")>
							<cfset local.strAssociation = local.objSWP.getAssociationDetails(arguments.event.getValue('mc_siteInfo.siteCode'))>
							<cfset local.semData = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarBySeminarID(seminarID=local.verifyReg.seminarID)>
							<cfset local.dataFormat.strAssociation = local.strAssociation>
							<cfset local.dataFormat.seminarType = 'swl'>
							<cfset local.dataFormat.orgCode = local.semData.publisherorgcode>
							<cfset local.dataFormat.seminarID = local.verifyReg.seminarID>
							<cfset local.dataFormat.enrollmentID = local.verifyReg.enrollmentID>
							<cfset local.dataFormat.materialDownloadLink = "#application.objPlatform.isRequestSecure() ? 'https' : 'http'#://#arguments.event.getValue('mc_siteInfo.mainhostname')##arguments.event.getValue('mainurl')#&panel=downloadMaterialsDoc&uniqueCode=#local.swlcode#&documentID=">
							<cfset local.dataFormat.materialDownloadLinkSingle = "#local.semWeb.mainurl#&panel=downloadMaterialsDoc&uniqueCode=#local.swlcode#&documentID=">
						
							<cfinclude template="joinLiveSWL.cfm">
							<cfabort>
						<cfelse>
							<cfset local.dataFormat.isInvalid = 1>
							<cfset local.semWeb.catalogPanel = "joinLiveShowEntryForm">	
						</cfif>
					<cfelse>
						<cfset local.dataFormat.isInvalid = 0>
						<cfset local.semWeb.catalogPanel = "joinLiveShowEntryForm">
					</cfif>
				
				<cfelseif local.semWeb.catalogPanel eq 'gateway'>
					<cfif val(local.qrySWP.landingCarouselID)>
						<cfset local.dataFormat.strCarousel = createObject("component","model.carousel.carousel").renderWebsiteCarousel(siteID=arguments.event.getValue('mc_siteinfo.siteid'), carouselID=local.qrySWP.landingCarouselID)>
					</cfif>
					<cfset local.dataFormat.strFeaturedPrograms = local.objSWBrowse.getSWPrograms(
						siteID=arguments.event.getValue('mc_siteinfo.siteid'), catalogOrgCode=local.rc.mc_siteinfo.sitecode, 
						memberID=local.memberID, depoMemberDataID=local.depomemberdataid, qrySWP=local.qrySWP, 
						strAssociation=local.strAssociation, 
						swRegCart=local.dataFormat.swRegCart, strFilters=getSWProgramFilters(event=arguments.event, qrySWP=local.qrySWP), 
						mode="featured")>
					<cfset local.dataFormat.savedProgramsCount = local.objSWBrowse.getSWProgramsSavedForLater().recordCount>
					<cfset local.dataFormat.hasActiveBundles = local.objSWBrowse.hasActiveBundles(catalogOrgCode=local.rc.mc_siteinfo.sitecode)>
					<cfset local.dataFormat.objSWBrowse = local.objSWBrowse>
				</cfif>
				
				<cfset local.data = local.dataFormat>

				<cfif listFindNoCase("viewCert,viewCertPDF,viewCPCert,viewCPCertPDF",local.semWeb.catalogPanel)>
					<cfinclude template="#local.semWeb.catalogPanel#Content.cfm">
					<cfabort>
				<cfelse>
					<cfif listFindNoCase("showLive,showSWOD,showBundle",local.semWeb.catalogPanel)>
						<cfset local.qrySavedPrograms = local.objSWBrowse.getSWProgramsSavedForLater()>
						<cfset local.dataFormat.savedProgramsCount = local.qrySavedPrograms.recordCount>

						<cfif local.qrySavedPrograms.recordCount>
							<cfquery name="local.qryThisProgramSaved" dbtype="query">
								select programID
								from [local].qrySavedPrograms
								where programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.dataFormat.programID#">
								and programType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.dataFormat.programType#">
							</cfquery>
							<cfset local.dataFormat.isSavedProgram = local.qryThisProgramSaved.recordCount is 1>
						<cfelse>
							<cfset local.dataFormat.isSavedProgram = false>
						</cfif>

						<cfset local.dataFormat.strAssociation = local.strAssociation>
						<cfset local.dataFormat.mc_siteinfo = local.rc.mc_siteinfo>

						<!--- link to edit program page from toolbar --->
						<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
							<cfset local.appPageCPLink = { linkText="Edit Program", link="/?pg=admin&jumpToTool=SeminarWebAdmin%7Clist#UCase(local.dataFormat.programType)#%7Cedit#UCase(local.dataFormat.programType)#Program&pid=#local.dataFormat.programID#" }>
							<cfset arguments.event.setValue('mc_appPageCPLink', local.appPageCPLink) />
						</cfif>

						<cfset local.swReg = application.mcCacheManager.sessionGetValue(keyname="swReg", defaultValue={})>
						<cfif isStruct(local.swReg) AND local.swReg.count() AND arrayLen(local.swReg.regCart)>
							<cfset local.dataFormat.hasPendingRegistrations = true>
						<cfelse>
							<cfset local.dataFormat.hasPendingRegistrations = false>
						</cfif>

						<!--- featured image paths --->
						<cfset local.publisherSiteInfo = application.objSiteInfo.getSiteInfo(local.dataFormat.publisherOrgCode)>
						<cfset local.dataFormat.featuredThumbImageFullRootPath = "#application.paths.RAIDUserAssetRoot.path##LCASE(local.publisherSiteInfo.orgcode)#/#LCASE(local.publisherSiteInfo.sitecode)#/featuredimages/thumbnails/">
						<cfset local.dataFormat.featuredThumbImageRootPath = "/userassets/#LCASE(local.publisherSiteInfo.orgcode)#/#LCASE(local.publisherSiteInfo.sitecode)#/featuredimages/thumbnails/">

						<cfset local.dataFormat.defaultFeaturedImagePathsStr = local.objSWBrowse.getDefaultProgramFeaturedImagePaths(strAssociation=local.strAssociation, mode="detail")>
						<cfset local.dataFormat.defaultFeaturedImagePathsOtherProgramsStr = local.objSWBrowse.getDefaultProgramFeaturedImagePaths(strAssociation=local.strAssociation, mode="otherprograms")>

						<cfset local.viewToUse = 'semWebCatalog/#local.viewDirectory#/showSWProgramContentLayout'>
					<cfelse>
						<cfset local.viewToUse = 'semWebCatalog/#local.viewDirectory#/#local.semWeb.catalogPanel#Content'>
					</cfif>
				</cfif>
			</cfoutput>
		</cfdefaultcase>
		</cfswitch>
		<cfif IsStruct(local.data)>
			<cfset local.data.memberid = local.memberid>
			<cfset local.data.depomemberdataid = local.depomemberdataid>
		</cfif>
		
		<cfreturn returnAppStruct(local.data,local.viewToUse)>
	</cffunction>

	<cffunction name="getFAClassesForFileIcons" access="private" output="false" returntype="struct">
		<cfset var strClasses = {
			"mp3" : "bi bi-volume-down",
			"mp4" : "bi bi-camera-reels",
			"pdf" : "bi bi-file-earmark-text",
			"doc" : "bi bi-file-earmark-word",
			"docx" : "bi bi-file-earmark-word",
			"ppt" : "bi bi-file-earmark-ppt",
			"pptx" : "bi bi-file-earmark-ppt",
			"xls" : "bi bi-file-earmark-spreadsheet",
			"xlsx" : "bi bi-file-earmark-spreadsheet",
			"rtf" : "bi bi-file-earmark-richtext",
			"txt" : "bi bi-file-earmark-text",
			"htm" : "bi bi-file-earmark-code",
			"html" : "bi bi-file-earmark-code",
			"zip" : "bi bi-file-earmark-zip",
			"jpg" : "bi bi-file-earmark-image",
			"jpeg" : "bi bi-file-earmark-image",
			"gif" : "bi bi-file-earmark-image",
			"tif" : "bi bi-file-earmark-image",
			"tiff" : "bi bi-file-earmark-image",
			"png" : "bi bi-file-earmark-image"
		}>

		<cfreturn strClasses>
	</cffunction>

	<cffunction name="getMyHistory" access="public" output="false" returntype="struct" hint="Also called from MyCE">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="catalogOrgCode" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="depoMemberDataID" type="numeric" required="true">
		<cfargument name="qrySWP" type="query" required="true">
		<cfargument name="fStartDate" type="string" required="true">
		<cfargument name="fEndDate" type="string" required="true">
		<cfargument name="fStartDateCompleted" type="string" required="true">
		<cfargument name="fEndDateCompleted" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>

		<cfset local.strHistory = { 
			"arrSWL":[], 
			"arrSWOD":[], 
			"qryCertPrograms":CreateObject("component","model.seminarweb.SWCertPrograms").getProgramsByOrgCode(orgcode=arguments.catalogOrgCode), 
			"arrCertPrograms":[],
			"strSelfReportedCredits":{ "arrSelfReportedCredits":[], "strCredits":{} },
			"fStartDate":arguments.fStartDate, 
			"fEndDate":arguments.fEndDate, 
			"fStartDateCompleted":arguments.fStartDateCompleted, 
			"fEndDateCompleted":arguments.fEndDateCompleted
		}>

		<cfset local.strEnrollments = CreateObject("component","model.seminarweb.SWCommon").getEnrollmentHistory(MCMemberID=arguments.memberID, catalogOrgCode=arguments.catalogOrgCode)>
		<cfset local.strEnrollments.qrySWL = local.objSWL.addParsedTimeZoneToSeminars(qrySeminars=local.strEnrollments.qrySWL, catalogOrgCode=arguments.catalogOrgCode)>
		<cfset local.strAvailableProgramIDsForBrowse = CreateObject("component","browse").getSWProgramIDsAvailableForBrowse(catalogOrgCode=arguments.catalogOrgCode, memberID=arguments.memberID)>
		<!--- get credits --->
		<cfif local.strEnrollments.qrySWL.recordcount OR local.strEnrollments.qrySWOD.recordcount>
			<cfquery name="local.qryEnrollmentCredits" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @depoMemberDataID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.depoMemberDataID#">;

				SELECT DISTINCT e.seminarID, e.enrollmentID, ca.code as authorityCode, ca.authorityName, 
					CRDA.credittype.value('(var[@name="value"]/string)[1]','numeric(9,2)') AS creditValueAwarded,
					CRDT.credittype.value('(var[@name="displayname"]/string)[1]','varchar(200)') AS creditType,
					datepart(yyyy,e.dateCompleted) as CLEYear,
					convert(varchar,csa.CSALinkID,30)+'_'+CRDT.credittype.value('(var[@name="displayname"]/string)[1]','varchar(200)') as creditedTypes,
					eac.earnedCertificate,
					sac.creditOfferedEndDate, e.passed
				FROM dbo.tblEnrollments AS e
				INNER JOIN dbo.tblUsers AS u ON u.userID = e.userID 
					AND u.depomemberdataID = @depoMemberDataID
				INNER JOIN dbo.tblEnrollmentsAndCredit AS eac ON eac.enrollmentID = e.enrollmentID 
					
				INNER JOIN dbo.tblSeminarsAndCredit AS sac ON sac.seminarCreditID = eac.seminarCreditID 
				INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON csa.CSALinkID = sac.CSALinkID 
				INNER JOIN dbo.tblCreditAuthorities AS ca ON ca.authorityID = csa.authorityID 
				CROSS APPLY sac.wddxCreditsAvailable.nodes('/wddxPacket/data/array/struct') AS CRDA(credittype)
				CROSS APPLY ca.wddxCreditTypes.nodes('/wddxPacket/data/array/struct') AS CRDT(credittype)
				WHERE  e.isActive = 1
				AND CRDA.credittype.value('(var[@name="value"]/string)[1]','numeric(9,2)') > 0
				AND CRDA.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)') = CRDT.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)');

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<!--- This is used by the MyCE app --->
			<cfset local.strHistory.qryAllEnrollmentCredits = QueryFilter(local.qryEnrollmentCredits, function(thisRow) { return arguments.thisRow.earnedCertificate eq 1 AND arguments.thisRow.passed EQ 1; })>
		</cfif>

		
		<!--- swl --->
		<cfif local.strEnrollments.qrySWL.recordcount>
			<cfif len(arguments.fStartDate) OR len(arguments.fEndDate)>
				<cfquery name="local.qrySWLEnrollments" dbtype="query">
					select enrollmentID, seminarID, dateStart, dateEnd, offerCertificate, seminarName,
						seminarSubTitle, dateCompleted, passed, preReqFulfilled, CreditCount, dspStartDate, dspEndDate,
						showVideoReplay, CatalogURL, SWLCode, attended, replayAvailability, isPublished, isOpen, offerCredit
					from [local].strEnrollments.qrySWL
					where 
					<cfif len(arguments.fStartDate) AND len(arguments.fEndDate)>
						dateStart between <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.fStartDate#"> and <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.fEndDate# 23:59">
					<cfelseif len(arguments.fStartDate)>
						dateStart >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.fStartDate#">
					<cfelseif len(arguments.fEndDate)>
						dateStart <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.fEndDate# 23:59">
					</cfif>
				</cfquery>
			<cfelse>
				<cfset local.qrySWLEnrollments = local.strEnrollments.qrySWL>
			</cfif>
			<cfloop query="local.qrySWLEnrollments">
				<cfset local.progress = local.objSWL.getSeminarProgress(enrollmentID=local.qrySWLEnrollments.enrollmentID)>
				<cfset local.materialsDoc = local.objSWL.getMaterialsDocument(seminarID=local.qrySWLEnrollments.seminarId, enrollmentID=val(local.qrySWLEnrollments.enrollmentID), userType='user', sitecode=arguments.catalogOrgCode)>
				
				<cfset local.tmpStr = { "enrollmentID":local.qrySWLEnrollments.enrollmentID,
										"seminarID": local.qrySWLEnrollments.seminarID, "date":DateFormat(local.qrySWLEnrollments.dspStartDate,"m/d/yyyy"), 
										"title":local.qrySWLEnrollments.seminarName, "status":"", "dspCredits":"", "creditDetails":"",
										"completedDate":DateFormat(local.qrySWLEnrollments.dateCompleted,"m/d/yyyy"), "canViewCertificate":false, "encryptedEID":"", 
										"enterProgramLink":"", "materialsDoc":local.materialsDoc.recordcount,
										"linkToDetailPage":arrayFind(local.strAvailableProgramIDsForBrowse.arrSWLProgramIDs, local.qrySWLEnrollments.seminarID) gt 0,
										"replayVideoLink":"","SWLCode": local.qrySWLEnrollments.SWLCode,"SWLPlayerLink": "#local.qrySWLEnrollments.CatalogURL#/?k=#local.qrySWLEnrollments.SWLCode#"}>

				<cfif len(local.qrySWLEnrollments.dateCompleted) 
					and local.qrySWLEnrollments.offerCertificate
					and local.qrySWLEnrollments.passed is 1 
						and (
							local.qrySWLEnrollments.creditCount eq 0 
								OR 
							(local.qrySWLEnrollments.creditCount gt 0 and local.progress.qrySeminarProgress.hasCertificate is 1)
								OR
							(local.qrySWLEnrollments.creditCount GT 0 AND local.progress.qrySeminarProgress.allPostTestCompleted gt 0 AND local.progress.qrySeminarProgress.allEvaluationCompleted gt 0)
							)>
					<cfset local.tmpStr.status = "Completed">
					<cfset local.tmpStr.encryptedEID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qrySWLEnrollments.enrollmentID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
					<cfset local.tmpStr.canViewCertificate = true>

				<cfelseif len(local.qrySWLEnrollments.dateCompleted) and local.qrySWLEnrollments.passed is 1 and local.qrySWLEnrollments.offerCertificate>
					<cfif local.progress.qrySeminarProgress.allPostTestCompleted gt 0 AND local.progress.qrySeminarProgress.allEvaluationCompleted gt 0>
						<cfset local.tmpStr.status = "Pending Exams/Evaluations">
					<cfelseif local.progress.qrySeminarProgress.allPostTestCompleted gt 0 AND local.progress.qrySeminarProgress.allEvaluationCompleted is 0>
						<cfset local.tmpStr.status = "Pending Evaluations">
					<cfelseif local.progress.qrySeminarProgress.allPostTestCompleted is 0 AND local.progress.qrySeminarProgress.allEvaluationCompleted gt 0>
						<cfset local.tmpStr.status = "Pending Exams">
					</cfif>
				<cfelseif len(local.qrySWLEnrollments.dateCompleted) and local.qrySWLEnrollments.passed is 1 and not local.qrySWLEnrollments.offerCertificate>
					<cfset local.tmpStr.status = "Completed">
				<cfelseif len(local.qrySWLEnrollments.dateCompleted) and local.qrySWLEnrollments.passed is 0>
					<cfset local.tmpStr.status = "Failed">
				<cfelseif now() lt dateAdd("n", -15, local.qrySWLEnrollments.dateStart) and local.qrySWLEnrollments.isPublished is 1 and local.qrySWLEnrollments.isOpen is 1>
					<cfset local.tmpStr.status = "Not yet begun">
				<cfelseif now() gte dateAdd("n", -15, local.qrySWLEnrollments.dateStart) and local.qrySWLEnrollments.isPublished is 1 and local.qrySWLEnrollments.isOpen is 1>
					<cfset local.tmpStr.status = "Enter Program">
					<cfset local.tmpStr.enterProgramLink = "#local.qrySWLEnrollments.CatalogURL#/?k=#local.qrySWLEnrollments.SWLCode#">
				<cfelse>
					<cfset local.tmpStr.status = "Did not attend">
				</cfif>
				<cfset local.tmpStr.canViewReplayLink = false>
				<cfif local.qrySWLEnrollments.replayAvailability EQ "registrants">
					<cfset local.tmpStr.canViewReplayLink = true>
				<cfelseif local.qrySWLEnrollments.replayAvailability EQ "attendees" AND local.qrySWLEnrollments.attended EQ 1>
					<cfset local.tmpStr.canViewReplayLink = true>
				<cfelseif local.qrySWLEnrollments.replayAvailability EQ "nonattendees" AND  local.qrySWLEnrollments.attended EQ 0>
					<cfset local.tmpStr.canViewReplayLink = true>
				</cfif>

				<cfif local.qryEnrollmentCredits.recordCount>
					<cfquery name="local.qryThisSWLEnrollmentCredit" dbtype="query">
						select authorityCode, authorityName, creditValueAwarded, creditType
						from [local].qryEnrollmentCredits
						where seminarID = #val(local.qrySWLEnrollments.seminarID)# AND earnedCertificate = 1 AND passed = 1
						order by authorityName, creditType
					</cfquery>
					<cfif local.qryThisSWLEnrollmentCredit.recordCount>
						<cfoutput query="local.qryThisSWLEnrollmentCredit" group="authorityCode">
							<cfset local.tmpStr.dspCredits = local.tmpStr.dspCredits & "#local.qryThisSWLEnrollmentCredit.authorityName#: ">
							<cfoutput>
								<cfset local.tmpStr.dspCredits = local.tmpStr.dspCredits & "#local.qryThisSWLEnrollmentCredit.creditValueAwarded# #local.qryThisSWLEnrollmentCredit.creditType#, ">
							</cfoutput>
							<cfset local.tmpStr.dspCredits = mid(local.tmpStr.dspCredits, 1, len(local.tmpStr.dspCredits) - 2)>
							<cfset local.tmpStr.dspCredits = local.tmpStr.dspCredits & " ">
						</cfoutput>
					</cfif>
				</cfif>
				<cfif local.qrySWLEnrollments.offerCredit AND  local.qrySWLEnrollments.creditCount AND local.tmpStr.status NEQ 'Completed'>
					<cfquery name="local.qryThisNonExpiredCredit" dbtype="query">
						select authorityCode, authorityName, creditValueAwarded, creditType
						from [local].qryEnrollmentCredits
						where seminarID = #val(local.qrySWLEnrollments.seminarID)# AND creditOfferedEndDate >= now()
						order by authorityName, creditType
					</cfquery>
					
					<!--- non expired credits ---> 
					<cfif local.qryThisNonExpiredCredit.recordCount>
						<cfset local.tmpStr.creditDetails = local.tmpStr.creditDetails & "Credit Requested: ">
						<cfoutput query="local.qryThisNonExpiredCredit" group="authorityCode">
							<cfset local.tmpStr.creditDetails = local.tmpStr.creditDetails & "#local.qryThisNonExpiredCredit.authorityName#: ">
							<cfoutput>
								<cfset local.tmpStr.creditDetails = local.tmpStr.creditDetails & "#local.qryThisNonExpiredCredit.creditValueAwarded# #local.qryThisNonExpiredCredit.creditType#, ">
							</cfoutput>
							<cfset local.tmpStr.creditDetails = mid(local.tmpStr.creditDetails, 1, len(local.tmpStr.creditDetails) - 2)>
							<cfset local.tmpStr.creditDetails = local.tmpStr.creditDetails & " ">
						</cfoutput>
						<cfset local.tmpStr.creditDetails = "<span  class='text-light-grey'>#local.tmpStr.creditDetails#</span>">
					</cfif>
				</cfif>
				<cfif local.qrySWLEnrollments.offerCredit AND  local.qrySWLEnrollments.creditCount AND local.tmpStr.status EQ 'Completed'>
					<!--- both ear certifict = 0 and =1 ---> 
					<cfquery name="local.qryThisEarnedCredit" dbtype="query">
						select authorityCode, authorityName, creditValueAwarded, creditType
						from [local].qryEnrollmentCredits
						where seminarID = #val(local.qrySWLEnrollments.seminarID)# AND earnedCertificate = 1
						order by authorityName, creditType
					</cfquery>
					<cfif local.qryThisEarnedCredit.recordCount>
						<cfset local.tmpStr.creditDetails = local.tmpStr.creditDetails & "Credit Awarded: ">
						<cfoutput query="local.qryThisEarnedCredit" group="authorityCode">
							<cfset local.tmpStr.creditDetails = local.tmpStr.creditDetails & "#local.qryThisEarnedCredit.authorityName#: ">
							<cfoutput>
								<cfset local.tmpStr.creditDetails = local.tmpStr.creditDetails & "#local.qryThisEarnedCredit.creditValueAwarded# #local.qryThisEarnedCredit.creditType#, ">
							</cfoutput>
							<cfset local.tmpStr.creditDetails = mid(local.tmpStr.creditDetails, 1, len(local.tmpStr.creditDetails) - 2)>
							<cfset local.tmpStr.creditDetails = local.tmpStr.creditDetails & " ">
						</cfoutput>
					</cfif>
					<cfquery name="local.qryThisNonEarnedCredit" dbtype="query">
						select authorityCode, authorityName, creditValueAwarded, creditType
						from [local].qryEnrollmentCredits
						where seminarID = #val(local.qrySWLEnrollments.seminarID)# AND earnedCertificate = 0
						order by authorityName, creditType
					</cfquery>
					<cfif local.qryThisNonEarnedCredit.recordCount>
						<cfset local.NonEarnedcreditDetails = "Credit Denied: ">
						<cfoutput query="local.qryThisNonEarnedCredit" group="authorityCode">
							<cfset local.NonEarnedcreditDetails = local.NonEarnedcreditDetails & "#local.qryThisNonEarnedCredit.authorityName#: ">
							<cfoutput>
								<cfset local.NonEarnedcreditDetails = local.NonEarnedcreditDetails & "#local.qryThisNonEarnedCredit.creditValueAwarded# #local.qryThisNonEarnedCredit.creditType#, ">
							</cfoutput>
							<cfset local.NonEarnedcreditDetails = mid(local.NonEarnedcreditDetails, 1, len(local.NonEarnedcreditDetails) - 2)>
							<cfset local.NonEarnedcreditDetails = local.NonEarnedcreditDetails & " ">
						</cfoutput>
						<cfset local.NonEarnedcreditDetails = "<div  class='text-light-grey'>#local.NonEarnedcreditDetails#</div>">
						<cfset local.tmpStr.creditDetails = local.tmpStr.creditDetails & local.NonEarnedcreditDetails>
					</cfif>
					
				</cfif>
				<cfif local.qrySWLEnrollments.offerCredit AND  local.qrySWLEnrollments.creditCount EQ 0 AND local.tmpStr.status NEQ 'Completed'>
					<cfset local.tmpStr.creditDetails = local.tmpStr.creditDetails & "<span  class='text-light-grey'>Credit Requested: No credit selected during registration	</span>">
				</cfif>
				<cfif local.qrySWLEnrollments.offerCredit AND  local.qrySWLEnrollments.creditCount EQ 0 AND local.tmpStr.status EQ 'Completed'>
					<cfset local.tmpStr.creditDetails = local.tmpStr.creditDetails & "Credit Awarded: No credit selected during registration" >
				</cfif>
				
				<cfif local.qrySWLEnrollments.showVideoReplay is 1>
					<cfif NOT val(local.progress.qrySeminarSettings.isOpen)>
						<cfset local.tmpStr.replayVideoLink = "/?pg=semwebCatalog&panel=joinLive&joinsubmitted=1&uniqueCode=#local.qrySWLEnrollments.SWLCode#">
					</cfif>
				</cfif>
				<cfset local.strHistory.arrSWL.append(local.tmpStr)>
			</cfloop>
		</cfif>

		<!--- swod --->
		<cfif local.strEnrollments.qrySWOD.recordcount>
			<cfif len(arguments.fStartDate) OR len(arguments.fEndDate) OR len(arguments.fStartDateCompleted) OR len(arguments.fEndDateCompleted)>
				<cfquery name="local.qrySWODEnrollments" dbtype="query">
					select enrollmentID, seminarID, seminarName, seminarSubTitle, dateEnrolled, dateCompleted,
						passed, isPublished, offerCertificate, preReqFulfilled, CreditCompleteByDate, offerCredit, CreditCount
					from [local].strEnrollments.qrySWOD
					where 
					<cfif len(arguments.fStartDate) AND len(arguments.fEndDate)>
						dateEnrolled between <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.fStartDate#"> and <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.fEndDate# 23:59">
					<cfelseif len(arguments.fStartDate)>
						dateEnrolled >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.fStartDate#">
					<cfelseif len(arguments.fEndDate)>
						dateEnrolled <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.fEndDate# 23:59">
					</cfif>
					<cfif len(arguments.fStartDateCompleted) AND len(arguments.fEndDateCompleted)>
						dateCompleted between <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.fStartDateCompleted#"> and <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.fEndDateCompleted# 23:59">
					<cfelseif len(arguments.fStartDateCompleted)>
						dateCompleted >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.fStartDateCompleted#">
					<cfelseif len(arguments.fEndDateCompleted)>
						dateCompleted <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.fEndDateCompleted# 23:59">
					</cfif>
				</cfquery>
			<cfelse>
				<cfset local.qrySWODEnrollments = local.strEnrollments.qrySWOD>
			</cfif>

			<cfloop query="local.qrySWODEnrollments">
				<cfset local.tmpStr = { "enrollmentID":local.qrySWODEnrollments.enrollmentID, "seminarID": local.qrySWODEnrollments.seminarID, 
										"dateEnrolled":DateFormat(local.qrySWODEnrollments.dateEnrolled,"m/d/yyyy"), 
										"creditCompleteByDate":"", "creditCompleteByDateTime":"", "title":local.qrySWODEnrollments.seminarName, "status":"", "dspCredits":"", "creditDetails":"",
										"completedDate":DateFormat(local.qrySWODEnrollments.dateCompleted,"m/d/yyyy"), 
										"canViewCertificate":false, "encryptedEID":"", "enterProgramLink":"", "reviewProgramLink":"",
										"linkToDetailPage":arrayFind(local.strAvailableProgramIDsForBrowse.arrSWODProgramIDs, local.qrySWODEnrollments.seminarID) gt 0 }>

				<cfif len(local.qrySWODEnrollments.CreditCompleteByDate) AND dateCompare(now(),local.qrySWODEnrollments.CreditCompleteByDate, "n") LT 0>
					<cfset local.tmpStr.creditCompleteByDate = DateFormat(local.qrySWODEnrollments.CreditCompleteByDate,"m/d/yyyy")>
					<cfset local.tmpStr.creditCompleteByDateTime = timeFormat(local.qrySWODEnrollments.CreditCompleteByDate,"hh:mm TT")>
				</cfif>

				<cfif len(local.qrySWODEnrollments.dateCompleted) is 0>
					<cfif local.qrySWODEnrollments.isPublished and local.qrySWODEnrollments.preReqFulfilled>
						<cfset local.tmpStr.status = "Not Completed">
						<cfset local.tmpStr.enterProgramLink = "/?pg=swOnDemandPlayer&seminarID=#local.qrySWODEnrollments.seminarID#&enrollmentID=#local.qrySWODEnrollments.enrollmentID#&orgCode=#arguments.catalogOrgCode#">
					<cfelseif local.qrySWODEnrollments.isPublished>
						<cfset local.tmpStr.status = "Awaiting Prereqs">
					<cfelse>
						<cfset local.tmpStr.status = "Not available">
					</cfif>
				<cfelse>
					<cfif local.qrySWODEnrollments.isPublished>
						<cfset local.tmpStr.reviewProgramLink = "/?pg=swOnDemandPlayer&seminarID=#local.qrySWODEnrollments.seminarID#&enrollmentID=#local.qrySWODEnrollments.enrollmentID#&orgCode=#arguments.catalogOrgCode#">
					</cfif>
					
					<cfif local.qrySWODEnrollments.passed and local.qrySWODEnrollments.offerCertificate>
						<cfset local.tmpStr.status = "Completed">
						<cfset local.tmpStr.encryptedEID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qrySWODEnrollments.enrollmentID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
						<cfset local.tmpStr.canViewCertificate = true>
					<cfelseif local.qrySWODEnrollments.passed and not local.qrySWODEnrollments.offerCertificate>
						<cfset local.tmpStr.status = "Completed">
					<cfelseif not local.qrySWODEnrollments.passed>
						<cfset local.tmpStr.status = "Failed">
					</cfif>
				</cfif>

				<cfif local.qryEnrollmentCredits.recordCount>
					<cfquery name="local.qryThisSWLEnrollmentCredit" dbtype="query">
						select authorityCode, authorityName, creditValueAwarded, creditType
						from [local].qryEnrollmentCredits
						where seminarID = #val(local.qrySWODEnrollments.seminarID)# AND earnedCertificate = 1 AND passed = 1
						order by authorityName, creditType
					</cfquery>
					<cfif local.qryThisSWLEnrollmentCredit.recordCount>
						<cfoutput query="local.qryThisSWLEnrollmentCredit" group="authorityCode">
							<cfset local.tmpStr.dspCredits = local.tmpStr.dspCredits & "#local.qryThisSWLEnrollmentCredit.authorityName#: ">
							<cfoutput>
								<cfset local.tmpStr.dspCredits = local.tmpStr.dspCredits & "#local.qryThisSWLEnrollmentCredit.creditValueAwarded# #local.qryThisSWLEnrollmentCredit.creditType#, ">
							</cfoutput>
							<cfset local.tmpStr.dspCredits = mid(local.tmpStr.dspCredits, 1, len(local.tmpStr.dspCredits) - 2)>
							<cfset local.tmpStr.dspCredits = local.tmpStr.dspCredits & " ">
						</cfoutput>
					</cfif>
					

				</cfif>
				
				<cfif local.qrySWODEnrollments.offerCredit AND  local.qrySWODEnrollments.CreditCount AND local.tmpStr.status NEQ 'Completed'>
					<cfquery name="local.qryThisNonExpiredCredit" dbtype="query">
						select authorityCode, authorityName, creditValueAwarded, creditType
						from [local].qryEnrollmentCredits
						where seminarID = #val(local.qrySWODEnrollments.seminarID)# AND creditOfferedEndDate >= now()
						order by authorityName, creditType
					</cfquery>
					
					<!--- non expired credits ---> 
					<cfif local.qryThisNonExpiredCredit.recordCount>
						<cfset local.tmpStr.creditDetails = local.tmpStr.creditDetails & "Credit Requested: ">
						<cfoutput query="local.qryThisNonExpiredCredit" group="authorityCode">
							<cfset local.tmpStr.creditDetails = local.tmpStr.creditDetails & "#local.qryThisNonExpiredCredit.authorityName#: ">
							<cfoutput>
								<cfset local.tmpStr.creditDetails = local.tmpStr.creditDetails & "#local.qryThisNonExpiredCredit.creditValueAwarded# #local.qryThisNonExpiredCredit.creditType#, ">
							</cfoutput>
							<cfset local.tmpStr.creditDetails = mid(local.tmpStr.creditDetails, 1, len(local.tmpStr.creditDetails) - 2)>
							<cfset local.tmpStr.creditDetails = local.tmpStr.creditDetails & " ">
						</cfoutput>
						<cfset local.tmpStr.creditDetails = "<span  class='text-light-grey'>#local.tmpStr.creditDetails#</span>">
					</cfif>
				</cfif>
				<cfif local.qrySWODEnrollments.offerCredit AND  local.qrySWODEnrollments.CreditCount AND local.tmpStr.status EQ 'Completed'>
					<!--- both ear certifict = 0 and =1 ---> 
					<cfquery name="local.qryThisEarnedCredit" dbtype="query">
						select authorityCode, authorityName, creditValueAwarded, creditType
						from [local].qryEnrollmentCredits
						where seminarID = #val(local.qrySWODEnrollments.seminarID)# AND earnedCertificate = 1
						order by authorityName, creditType
					</cfquery>
					<cfif local.qryThisEarnedCredit.recordCount>
						<cfset local.tmpStr.creditDetails = local.tmpStr.creditDetails & "Credit Awarded: ">
						<cfoutput query="local.qryThisEarnedCredit" group="authorityCode">
							<cfset local.tmpStr.creditDetails = local.tmpStr.creditDetails & "#local.qryThisEarnedCredit.authorityName#: ">
							<cfoutput>
								<cfset local.tmpStr.creditDetails = local.tmpStr.creditDetails & "#local.qryThisEarnedCredit.creditValueAwarded# #local.qryThisEarnedCredit.creditType#, ">
							</cfoutput>
							<cfset local.tmpStr.creditDetails = mid(local.tmpStr.creditDetails, 1, len(local.tmpStr.creditDetails) - 2)>
							<cfset local.tmpStr.creditDetails = local.tmpStr.creditDetails & " ">
						</cfoutput>
					</cfif>
					<cfquery name="local.qryThisNonEarnedCredit" dbtype="query">
						select authorityCode, authorityName, creditValueAwarded, creditType
						from [local].qryEnrollmentCredits
						where seminarID = #val(local.qrySWODEnrollments.seminarID)# AND earnedCertificate = 0
						order by authorityName, creditType
					</cfquery>
					<cfif local.qryThisNonEarnedCredit.recordCount>
						<cfset local.NonEarnedcreditDetails = "Credit Denied: ">
						<cfoutput query="local.qryThisNonEarnedCredit" group="authorityCode">
							<cfset local.NonEarnedcreditDetails = local.NonEarnedcreditDetails & "#local.qryThisNonEarnedCredit.authorityName#: ">
							<cfoutput>
								<cfset local.NonEarnedcreditDetails = local.NonEarnedcreditDetails & "#local.qryThisNonEarnedCredit.creditValueAwarded# #local.qryThisNonEarnedCredit.creditType#, ">
							</cfoutput>
							<cfset local.NonEarnedcreditDetails = mid(local.NonEarnedcreditDetails, 1, len(local.NonEarnedcreditDetails) - 2)>
							<cfset local.NonEarnedcreditDetails = local.NonEarnedcreditDetails & " ">
						</cfoutput>
						<cfset local.NonEarnedcreditDetails = "<div  class='text-light-grey'>#local.NonEarnedcreditDetails#</div>">
						<cfset local.tmpStr.creditDetails = local.tmpStr.creditDetails & local.NonEarnedcreditDetails>
					</cfif>
					
				</cfif>
				<cfif local.qrySWODEnrollments.offerCredit AND  local.qrySWODEnrollments.CreditCount EQ 0 AND local.tmpStr.status NEQ 'Completed'>
					<cfset local.tmpStr.creditDetails = local.tmpStr.creditDetails & "<span  class='text-light-grey'>Credit Requested: No credit selected during registration	</span>">
				</cfif>
				<cfif local.qrySWODEnrollments.offerCredit AND  local.qrySWODEnrollments.CreditCount EQ 0 AND local.tmpStr.status EQ 'Completed'>
					<cfset local.tmpStr.creditDetails = local.tmpStr.creditDetails & "Credit Awarded: No credit selected during registration" >
				</cfif>

				<cfset local.strHistory.arrSWOD.append(local.tmpStr)>
			</cfloop>
		</cfif>

		<!--- certificate programs --->
		<cfif local.strHistory.qryCertPrograms.recordCount AND local.strEnrollments.qryCertPrograms.recordCount>
			<cfif len(arguments.fStartDate) OR len(arguments.fEndDate)>
				<cfquery name="local.qrySWCPEnrollments" dbtype="query">
					select programID, programName, semstatusstring, seminarID, seminarName, seminarSubTitle,
						isPublished, offerCertificate, dateCompleted, dateenrolled, passed, enrollmentID, 
						SWODID, semStatus, SWLID, dateStart, preReqFulfilled
					from [local].strEnrollments.qryCertPrograms
					where SWLID is not null
					<cfif len(arguments.fStartDate) AND len(arguments.fEndDate)>
						and dateStart between <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.fStartDate#"> and <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.fEndDate# 23:59">
					<cfelseif len(arguments.fStartDate)>
						and dateStart >= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.fStartDate#">
					<cfelseif len(arguments.fEndDate)>
						and dateStart <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.fEndDate# 23:59">
					</cfif>
						union
					select programID, programName, semstatusstring, seminarID, seminarName, seminarSubTitle,
						isPublished, offerCertificate, dateCompleted, dateenrolled, passed, enrollmentID, 
						SWODID, semStatus, SWLID, dateStart, preReqFulfilled
					from [local].strEnrollments.qryCertPrograms
					where SWODID is not null
					<cfif len(arguments.fStartDate) AND len(arguments.fEndDate)>
						and dateEnrolled between <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.fStartDate#"> and <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.fEndDate# 23:59">
					<cfelseif len(arguments.fStartDate)>
						and dateEnrolled >= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.fStartDate#">
					<cfelseif len(arguments.fEndDate)>
						and dateEnrolled <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.fEndDate# 23:59">
					</cfif>
				</cfquery>
			<cfelse>
				<cfset local.qrySWCPEnrollments = local.strEnrollments.qryCertPrograms>
			</cfif>

			<cfoutput query="local.qrySWCPEnrollments" group="programID">
				<cfset local.tmpStr = { "programID":local.qrySWCPEnrollments.programID, "programName":local.qrySWCPEnrollments.programName, "status":"", 
										"canViewCertificate":false, "arrEnrollments":[], "row":arrayLen(local.strHistory.arrCertPrograms)+1 }>

				<cfif listValueCount(local.qrySWCPEnrollments.semStatusString,1) gt 0>
					<cfset local.tmpStr.status = "In progress">
				<cfelseif listValueCount(local.qrySWCPEnrollments.semStatusString,3) gt 0>
					<cfset local.tmpStr.status = "Did not earn certificate">
				<cfelse>
					<cfset local.tmpStr.status = "Completed">
					<cfset local.tmpStr.canViewCertificate = true>
				</cfif>

				<cfoutput>
					<cfset local.tmpEnrollStr = { "enrollmentID":local.qrySWCPEnrollments.enrollmentID, "status":"",
													"dateEnrolled":DateFormat(local.qrySWCPEnrollments.dateEnrolled,"m/d/yyyy"),
													"dateCompleted":DateFormat(local.qrySWCPEnrollments.dateCompleted,"m/d/yyyy"),
													"seminarname":local.qrySWCPEnrollments.seminarname,
													"seminarLink":"", "seminarLinkText":"", "swodReviewLink":"" }>

					<cfif len(local.qrySWCPEnrollments.dateEnrolled) is 0>
						<cfif len(local.qrySWCPEnrollments.SWODID)>
							<cfset local.tmpEnrollStr.seminarLink = "/?pg=semwebcatalog&panel=showSWOD&seminarID=#local.qrySWCPEnrollments.seminarID#">
							<cfset local.tmpEnrollStr.seminarLinkText = "Enroll Now">
						<cfelseif len(local.qrySWCPEnrollments.SWLID)>
							<cfset local.tmpEnrollStr.seminarLink = "/?pg=semwebcatalog&panel=showLive&seminarID=#local.qrySWCPEnrollments.seminarID#">
							<cfset local.tmpEnrollStr.seminarLinkText = "Enroll Now">
						<cfelse>
							<cfset local.tmpEnrollStr.status = "Not enrolled">
						</cfif>
					<cfelseif len(local.qrySWCPEnrollments.dateCompleted) is 0>
						<cfif len(local.qrySWCPEnrollments.SWODID) and local.qrySWCPEnrollments.isPublished and local.qrySWCPEnrollments.preReqFulfilled>
							<cfset local.tmpEnrollStr.seminarLink = "/?pg=swOnDemandPlayer&seminarID=#local.qrySWCPEnrollments.seminarID#&enrollmentID=#local.qrySWCPEnrollments.enrollmentID#&orgCode=#arguments.catalogOrgCode#">
							<cfset local.tmpEnrollStr.seminarLinkText = "Enter">
						<cfelseif len(local.qrySWCPEnrollments.SWODID) and local.qrySWCPEnrollments.isPublished>
							<cfset local.tmpEnrollStr.status = "Awaiting Prereqs">
						<cfelseif len(local.qrySWCPEnrollments.SWLID) and local.qrySWCPEnrollments.isPublished and now() lt local.qrySWCPEnrollments.dateStart>
							<cfset local.tmpEnrollStr.status = "Not yet begun">
						<cfelseif len(local.qrySWCPEnrollments.SWLID) and local.qrySWCPEnrollments.isPublished>
							<cfset local.tmpEnrollStr.status = "Did not attend">
						<cfelse>
							<cfset local.tmpEnrollStr.status = "Not available">
						</cfif>
					<cfelse>
						<cfif len(local.qrySWCPEnrollments.SWODID) and local.qrySWCPEnrollments.isPublished>
							<cfset local.tmpEnrollStr.swodReviewLink = "?pg=swOnDemandPlayer&seminarID=#local.qrySWCPEnrollments.seminarID#&enrollmentID=#local.qrySWCPEnrollments.enrollmentID#&orgCode=#arguments.catalogOrgCode#">
						</cfif>
						<cfif local.qrySWCPEnrollments.semStatus is 2>
							<cfset local.tmpEnrollStr.status = "Completed">
						<cfelseif local.qrySWCPEnrollments.isPublished>
							<cfset local.tmpEnrollStr.status = "Does not qualify">
						<cfelse>
							<cfset local.tmpEnrollStr.status = "Not available">
						</cfif>
					</cfif>

					<cfset local.tmpStr.arrEnrollments.append(local.tmpEnrollStr)>
				</cfoutput>
				<cfset local.strHistory.arrCertPrograms.append(local.tmpStr)>
			</cfoutput>
		</cfif>

		<!--- self reported credits --->
		<cfif arguments.qrySWP.isSelfReportCredit AND local.strEnrollments.qrySelfReported.recordCount>
			<cfif len(arguments.fStartDate) OR len(arguments.fEndDate)>
				<cfquery name="local.qrySelfReportedCredits" dbtype="query">
					select selfID, eventName, eventDate, userID, courseNumber, creditXML, wddxcredittypes
					from [local].strEnrollments.qrySelfReported
					where 
					<cfif len(arguments.fStartDate) AND len(arguments.fEndDate)>
						eventDate between <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.fStartDate#"> and <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.fEndDate# 23:59">
					<cfelseif len(arguments.fStartDate)>
						eventDate >= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.fStartDate#">
					<cfelseif len(arguments.fEndDate)>
						eventDate <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.fEndDate# 23:59">
					</cfif>
				</cfquery>
			<cfelse>
				<cfset local.qrySelfReportedCredits = local.strEnrollments.qrySelfReported>
			</cfif>
			
			<cfloop query="local.qrySelfReportedCredits">
				<cfset local.tmpStr = { "selfID":local.qrySelfReportedCredits.selfID, "creditTypes":"",
										"eventDate":dateformat(local.qrySelfReportedCredits.eventDate, "mm/dd/yyyy"),
										"eventName":local.qrySelfReportedCredits.eventName,
										"courseNumber":local.qrySelfReportedCredits.courseNumber,
										"arrCreditTypes":[] }>

				<cfwddx action="wddx2cfml" input="#local.qrySelfReportedCredits.creditXML#" output="local.tmpStr.creditTypes">
				<cfloop array="#local.tmpStr.creditTypes#" index="local.i">
					<!--- Use XPath to get the proper description for credit type --->
					<cfset local.displayname = xmlsearch(local.qrySelfReportedCredits.wddxcredittypes,'string(//struct/var[@name=''displayname'']/string[../../var[@name=''fieldname'' and translate(string,''ABCDEFGHIJKLMNOPQRSTUVWXYZ'',''abcdefghijklmnopqrstuvwxyz'')=''#lcase(local.i.fieldname)#'']])')>
					<cfset local.tmpStr.arrCreditTypes.append({ "value":local.i["value"], "displayname":local.displayname })>

					<!--- Add Credits to structure so total credits can be displayed --->
					<cfif structKeyExists(local.strHistory.strSelfReportedCredits.strCredits, local.i["fieldname"])>
						<!--- if exists, add credit value --->
						<cfset local.strHistory.strSelfReportedCredits.strCredits[local.i["fieldname"]].value = local.i["value"] + local.strHistory.strSelfReportedCredits.strCredits[local.i["fieldname"]].value>
					<cfelse>
						<!--- otherwise credit key and assign credit value --->
						<cfset local.strHistory.strSelfReportedCredits.strCredits[local.i["fieldname"]] = { displayname=local.displayname, value=local.i["value"] }>
					</cfif>
				</cfloop>
				
				<cfset local.strHistory.strSelfReportedCredits.arrSelfReportedCredits.append(local.tmpStr)>
			</cfloop>
		</cfif>

		<cfreturn local.strHistory>
	</cffunction>

	<cffunction name="getSWProgramFilters" access="private" output="false" returntype="struct">
		<cfargument name="event" type="any" required="true">
		<cfargument name="qrySWP" type="query" required="true">

		<cfset var local = structNew()>

		<!--- valid filters --->
		<cfset local.keywordList = arguments.event.getTrimValue('_swkwl','')>
		<cfset local.formats = arguments.event.getTrimValue('_swft','')>
		<cfset local.isRegistered = arguments.event.getTrimValue('_swir',0)>
		<cfset local.isSaved = arguments.event.getTrimValue('_swis',0)>
		<cfset local.creditAuthorities = arguments.event.getTrimValue('_swca','')>
		<cfset local.creditAuthorityTypes = arguments.event.getTrimValue('_swcat','')>
		<cfset local.creditAmounts = arguments.event.getTrimValue('_swcam','')>
		<cfset local.subjects = arguments.event.getTrimValue('_sws','')>
		<cfset local.authors = arguments.event.getTrimValue('_swa','')>
		<cfset local.publishers = arguments.event.getTrimValue('_swp','')>
		<cfset local.startPos = arguments.event.getTrimValue('_sw_sp',1)>
		<cfset local.sortOption = arguments.event.getTrimValue('_sw_so','')>

		<cfset local.strFilters = {
			"keywordsList":"",
			"formats":"",
			"isRegistered":0,
			"isSaved":0,
			"creditAuthorityList":"",
			"creditTypes":"",
			"creditAmountList":"",
			"subjects":"",
			"authorIDList":"",
			"participantIDList":"",
			"hasSearchParams":false,
			"startPos":1,
			"sortoption":"date",
			"URLQueryString":""
		}>

		<cfif isSimpleValue(local.keywordList) AND len(local.keywordList)>
			<cfset local.strFilters.keywordsList = local.keywordList>
			<cfif listLen(local.strFilters.keywordsList,'~') gt 1>
				<cfset local.strFilters.keywordsList = local.strFilters.keywordsList.listRemoveDuplicates('~')>
			</cfif>
		</cfif>

		<cfif isSimpleValue(local.formats) AND len(local.formats)>
			<cfset local.strFilters.formats = local.formats.listFilter(
				function(ft) {
					return listFindNoCase('conf,swl,swod,swb',arguments.ft);
				}
			)>
		</cfif>
		<cfif NOT len(local.strFilters.formats)>
			<cfif arguments.qrySWP.isConf>
				<cfset local.strFilters.formats = listAppend(local.strFilters.formats, 'conf')>
			</cfif>
			<cfif arguments.qrySWP.isSWL>
				<cfset local.strFilters.formats = listAppend(local.strFilters.formats, 'swl')>
			</cfif>
			<cfif arguments.qrySWP.isSWOD>
				<cfset local.strFilters.formats = listAppend(local.strFilters.formats, 'swod')>
			</cfif>
			<cfset local.hasActiveBundles = CreateObject("component","browse").hasActiveBundles(catalogOrgCode=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cfif (arguments.qrySWP.isSWL OR arguments.qrySWP.isSWOD) and local.hasActiveBundles>
				<cfset local.strFilters.formats = listAppend(local.strFilters.formats, 'swb')>
			</cfif>
		</cfif>

		<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser) AND isSimpleValue(local.isRegistered) AND local.isRegistered is 1>
			<cfset local.strFilters.isRegistered = 1>
		</cfif>

		<cfif isSimpleValue(local.isSaved) AND local.isSaved is 1>
			<cfset local.strFilters.isSaved = 1>
		</cfif>

		<cfif isSimpleValue(local.creditAuthorities) and len(local.creditAuthorities)>
			<cfset local.strFilters.creditAuthorityList = local.creditAuthorities.listFilter(
				function(caid) {
					return IsNumeric(arguments.caid);
				}
			)>
			<cfif isSimpleValue(local.creditAuthorityTypes) AND len(local.creditAuthorityTypes)>
				<cfset local.strFilters.creditTypes = local.creditAuthorityTypes>
			</cfif>
			<cfif isSimpleValue(local.creditAmounts) AND len(local.creditAmounts)>
				<cfset local.strFilters.creditAmountList = local.creditAmounts>
			</cfif>
		</cfif>

		<cfif isSimpleValue(local.subjects) AND len(local.subjects)>
			<!--- allow comma because this is a list --->
			<cfset local.strFilters.subjects = ReReplaceNoCase(local.subjects,"[^A-Za-z0-9\,]","","ALL")>
		</cfif>

		<cfif isSimpleValue(local.authors) AND len(local.authors)>
			<cfset local.strFilters.authorIDList = local.authors.listFilter(
				function(aid) {
					return IsNumeric(arguments.aid);
				}
			)>
		</cfif>

		<cfif isSimpleValue(local.publishers) AND len(local.publishers)>
			<cfset local.strFilters.participantIDList = local.publishers.listFilter(
				function(pid) {
					return IsNumeric(arguments.pid);
				}
			)>
		</cfif>

		<cfif isSimpleValue(local.startPos) AND IsNumeric(local.startPos) and local.startPos eq abs(int(local.startPos))>
			<cfset local.strFilters.startPos = local.startPos>
		</cfif>

		<cfif isSimpleValue(local.sortOption) AND len(local.sortOption) AND listFindNoCase("date,nameasc,namedesc,priceasc,pricedesc,rank",local.sortOption)>
			<cfset local.strFilters.sortoption = local.sortOption>
		</cfif>

		<cfset local.filterKey = ''>
		<cfloop collection="#local.strFilters#" item="local.filterKey">
			<cfif NOT listFindNoCase('hasSearchParams,startPos,sortoption',local.filterKey) AND len(local.strFilters[local.filterKey]) AND local.strFilters[local.filterKey] gt 0>
				<cfset local.strFilters.hasSearchParams = true>
				<cfbreak>
			</cfif>
		</cfloop>

		<cfset local.filterKeysStruct = structNew()>
		<cfset local.filterKeysStruct["_swkwl"] = local.strFilters.keywordsList>
		<cfset local.filterKeysStruct["_swft"] = listToArray(local.strFilters.formats)>
		<cfif local.strFilters.isRegistered eq 1>
			<cfset local.filterKeysStruct["_swir"] = 1>
		</cfif>
		<cfif local.strFilters.isSaved eq 1>
			<cfset local.filterKeysStruct["_swis"] = 1>
		</cfif>
		<cfset local.filterKeysStruct["_swca"] = listToArray(local.strFilters.creditAuthorityList)>
		<cfset local.filterKeysStruct["_swcat"] = listToArray(local.strFilters.creditTypes)>
		<cfset local.filterKeysStruct["_swcam"] = listToArray(local.strFilters.creditAmountList)>
		<cfset local.filterKeysStruct["_sws"] = listToArray(local.strFilters.subjects)>
		<cfset local.filterKeysStruct["_swa"] = listToArray(local.strFilters.authorIDList)>
		<cfset local.filterKeysStruct["_swp"] = listToArray(local.strFilters.participantIDList)>
		<cfset local.filterKeysStruct["_sw_so"] = local.strFilters.sortoption>
		<cfset local.strFilters.URLQueryString = structToURLQueryString(filterStruct=local.filterKeysStruct)>
		<cfset application.mcCacheManager.sessionSetValue(keyname='swCatalogBrowseFilters', value=local.strFilters)>
		
		<cfreturn local.strFilters>
	</cffunction>

	<cffunction name="structToURLQueryString" access="public" output="true" returntype="string">
		<cfargument name="filterStruct" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.queryString = "">
		<cfset local.delim1 = "=">
		<cfset local.delim2 = "&">

		<cfloop collection="#arguments.filterStruct#" item="local.key" >
			<cfif IsArray(arguments.filterStruct[local.key])>
				<cfloop array="#arguments.filterStruct[local.key]#" item="local.itm">
					<cfset local.queryString = ListAppend(local.queryString, LCase(local.key) & local.delim1 & URLEncodedFormat(local.itm), local.delim2)>
				</cfloop>
			<cfelseif len(arguments.filterStruct[local.key])>
				<cfset local.queryString = ListAppend(local.queryString, LCase(local.key) & local.delim1 & URLEncodedFormat(arguments.filterStruct[local.key]), local.delim2)>
			</cfif>
		</cfloop>

		<cfreturn local.queryString>
	</cffunction>

	<cffunction name="createAppInstance" access="public" output="true" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="baseLink" type="string">
		<cfargument name="appInfo" type="query">
		
		<cfscript>
			var local = structNew();
			// SET SPECIFICATION ----------------------------------------------
			local.appInfo = arguments.appInfo;
			variables.isCommunityReady = XMLSearch(local.appInfo.settingsXML,"string(//setting[@name='isCommunityReady']/@value)");
			variables.isMultiInstanceReady = XMLSearch(local.appInfo.settingsXML,"string(//setting[@name='isMultiInstanceReady']/@value)");
			arguments.event.paramValue('appTypeID','0');
			// LOAD OBJECTS ---------------------------------------------------------
			local.objAppCreation = CreateObject("component","model.admin.pages.appCreationProcess");
			// call the contruct to do all the page validation and form params ------
			contructAppInstanceForm(arguments.event,local.appInfo);
		</cfscript>

		<cfif cgi.request_method eq "POST" AND NOT arguments.event.getValue('error.formErrors')>
			<cftry>
				<cfset local.SeminarWebAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteID'))>

				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.createApp">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						DECLARE @siteID	int, @languageID int, @sectionID int, @applicationTypeID int, @isVisible bit, @pageName varchar(50),
							@pageTitle varchar(200), @pagedesc varchar(400), @zoneID int, @pageTemplateID int, @pageModeID int,
							@pgResourceTypeID int, @pgParentResourceID int, @allowReturnAfterLogin bit, @applicationInstanceName varchar(100),
							@applicationInstanceDesc varchar(200), @applicationInstanceID int, @siteResourceID int, @pageID int, @publicGID int,
							@siteCode varchar(10), @searchResultFSID int, @newAccountFSID int, @SWAdminSRID int, @useID int, @minAutoID int, 
							@fieldsetID int, @area varchar(20);
						DECLARE @tmpFieldSetUsages TABLE (autoID int PRIMARY KEY IDENTITY(1,1), fieldsetID int, area varchar(20), useID int);
						
						SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;
						SET @languageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('lid')#">;
						SET @applicationTypeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('appTypeID')#">;
						SET @isVisible = 1;
						SET @pageName = dbo.fn_regexReplace(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('pageName')#">,'[^A-Z0-9\-]+','');
						SET @pageTitle = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('pageTitle')#">;
						SET @pagedesc = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('pageDesc')#">;
						SET @zoneID = dbo.fn_getZoneID('Main');
						SET @pageTemplateID = NULL;
						SET @pageModeID = <cfif arguments.event.getValue('pageModeID','0') EQ 0>NULL<cfelse><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('pageModeID')#"></cfif>;
						SET @allowReturnAfterLogin = 1;
						SET @applicationInstanceName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('appInstanceName')#">;
						SET @applicationInstanceDesc = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('appInstanceDesc')#">;
						SET @sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('sectionID')#">;
						SET @pgResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedPage');
						SET @pgParentResourceID = NULL;
						SET @SWAdminSRID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.SeminarWebAdminSRID#">;

						select @publicGID = g.groupID, @siteCode = s.siteCode
						from dbo.ams_groups as g 
						inner join dbo.sites as s on s.orgid = g.orgid 
						where s.siteid = @siteid
						and g.isSystemGroup = 1
						and g.groupName = 'Public'
						AND g.status <> 'D';

						select top 1 @searchResultFSID = fieldsetID from dbo.ams_memberFieldSets where siteId = @siteID and fieldsetName = 'Account Locator Search Results';
						select top 1 @newAccountFSID = fieldsetID from dbo.ams_memberFieldSets where siteId = @siteID and fieldsetName = 'Account Locator New Account Form';

						-- SWReg fieldset usages
						INSERT INTO @tmpFieldSetUsages (fieldsetID, area)
						SELECT @newAccountFSID, 'SWRegNewAcct'
							UNION
						SELECT @searchResultFSID, 'SWRegResults';

						-- update useID
						UPDATE tmp
						SET tmp.useID = mfu.useID
						FROM @tmpFieldSetUsages AS tmp
						INNER JOIN dbo.ams_memberFieldUsage AS mfu ON mfu.siteResourceID = @SWAdminSRID
							AND mfu.area = tmp.area;

						BEGIN TRAN;
							-- add semwebcatalog instance (public permissions by default)
							EXEC dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID,
								@isVisible=@isVisible, @pageName=@pageName, @pageTitle=@pageTitle, @pagedesc=@pagedesc, @zoneID=@zoneID, @pageTemplateID=@pageTemplateID,
								@pageModeID=@pageModeID, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=@pgParentResourceID, @allowReturnAfterLogin=@allowReturnAfterLogin,
								@applicationInstanceName=@applicationInstanceName, @applicationInstanceDesc=@applicationInstanceDesc, @applicationInstanceID=@applicationInstanceID OUTPUT,
								@siteResourceID=@siteResourceID OUTPUT, @pageID=@pageID OUTPUT;
							EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, @functionIDList=4, @roleID=null, @groupID=@publicGID,
								@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null;

							-- add swOnDemandPlayer instance (visible=0, allowReturnAfterLogin=0, public permissions)
							select @applicationInstanceID = null, @siteResourceID = null, @pageID = null, @pageModeID = null, @pageTemplateID = null, @applicationTypeID = null;
							set @pageModeID = dbo.fn_getModeID('Stream');
							SELECT @pageTemplateID = templateID
								from dbo.cms_pageTemplates
								where siteID is null
								and templateName = 'SeminarWeb OnDemand Player'
								and status = 'A';
							SELECT @applicationTypeID=applicationTypeID, @pageName=suggestedPageName, @pageTitle=applicationTypeDesc, @pagedesc=null
								from dbo.cms_applicationTypes
								where applicationTypeName = 'swOnDemandPlayer';
							EXEC dbo.cms_createApplicationInstance @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, @applicationTypeID=@applicationTypeID,
								@isVisible=0, @pageName=@pageName, @pageTitle=@pageTitle, @pagedesc=@pagedesc, @zoneID=@zoneID, @pageTemplateID=@pageTemplateID,
								@pageModeID=@pageModeID, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=@pgParentResourceID, @allowReturnAfterLogin=0,
								@applicationInstanceName=@pageTitle, @applicationInstanceDesc=@pageTitle, @applicationInstanceID=@applicationInstanceID OUTPUT,
								@siteResourceID=@siteResourceID OUTPUT, @pageID=@pageID OUTPUT;
							EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteresourceID, @include=1, @functionIDList=4, @roleID=null, @groupID=@publicGID, 
								@inheritedRightsResourceID=null, @inheritedRightsFunctionID=null;
							update dbo.cms_pages 
							set inheritPlacements = 0 
							where pageID = @pageID;

							-- new fieldset usages
							SELECT @minAutoID = MIN(autoID) FROM @tmpFieldSetUsages WHERE useID IS NULL;
							WHILE @minAutoID IS NOT NULL BEGIN
								SELECT @fieldsetID = NULL, @area = NULL, @useID = NULL;

								SELECT @fieldsetID = fieldsetID, @area = area
								FROM @tmpFieldSetUsages
								WHERE autoID = @minAutoID;
								
								EXEC dbo.ams_createMemberFieldUsage @siteResourceID=@SWAdminSRID, @fieldsetID=@fieldsetID, @area=@area,
									@createSiteResourceID=0, @useID=@useID OUTPUT;

								SELECT @minAutoID = MIN(autoID) FROM @tmpFieldSetUsages WHERE useID IS NULL AND autoID > @minAutoID;
							END
						COMMIT TRAN;

						-- add as seminarweb participant (pass in siteCode to semweb)
						EXEC seminarWeb.dbo.sw_addParticipant @orgcode=@siteCode;

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>

				<cfset local.message = 1>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch)>
					<cfset local.message = 2>
				</cfcatch>
			</cftry>
			<cfset application.objSiteInfo.triggerClusterWideReload()>
			<cfset local.pageAdminTool = CreateObject("component","model.admin.admin").buildLinkToTool(toolType='PageAdmin',mca_ta='list') & '&refreshAdminNavData=1'>
			<cflocation url="#local.pageAdminTool#" addtoken="no">
		<cfelse>
			<cfoutput>
				<cfset showAppInstanceForm(arguments.event,local.appInfo)>
			</cfoutput>
		</cfif>
	</cffunction>
	
	<cffunction name="downloadMaterialsDoc" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
		<cfset local.documentID = int(val(arguments.event.getValue('documentID',0)))>
		
		<!--- get document and rights --->
		<!--- stop if the documentID doesnt belong to the site --->
		<cfset local.getDocument = local.objDocument.getDocumentData(local.documentID,0,0,'en')>

		<cfif local.getDocument.siteResourceID gt 0>
			
			<cfset local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>
			<cfset local.swlcode = reReplaceNoCase(arguments.event.getValue('uniqueCode'),"[^A-Z]","","ALL")>
			<cfset local.verifyReg = local.objSWL.verifySWLCode(local.swlcode)>
			<cfif val(local.verifyReg.isCodeValid)>
				<cfset local.dataFormat.materialsDoc = local.objSWL.getMaterialsDocument(seminarID=local.verifyReg.seminarID, enrollmentID=val(local.verifyReg.enrollmentID), userType=local.verifyReg.SWLUserType, sitecode=arguments.event.getValue('mc_siteInfo.sitecode'), documentID=local.documentID)>
						
				<cfif local.dataFormat.materialsDoc.recordCount>
					<cfset local.allowed = 1>
				<cfelse>
					<cfset local.allowed = 0>
				</cfif>
			<cfelse>
				<cfset local.allowed = 0>
			</cfif>
		<cfelse>
			<cfset local.allowed = 0>
		</cfif>

		<cfif not local.allowed>
			<cflocation url="/?pg=404" addtoken="false">
		<cfelse>
			<cftry>
				<cfset application.objPlatformStats.recordDocHit(documentVersionID=local.getdocument.documentVersionID,allowed=local.allowed)>
				<cfcatch type="any">
					<cfset application.objError.sendError(event=arguments.event,cfcatch=cfcatch,objectToDump=local)>
				</cfcatch>
			</cftry>

			<cfset local.theFile = "#application.paths.RAIDSiteDocuments.path##local.getDocument.orgCode#/#local.getDocument.siteCode#/#local.getdocument.documentVersionID#.#local.getDocument.fileExt#">
			<cfset local.keyMod = numberFormat(local.getdocument.documentVersionID mod 1000,"0000")>
			<cfset local.objectKey = lcase("sitedocuments/#local.getDocument.orgCode#/#local.getDocument.siteCode#/#local.keyMod#/#local.getdocument.documentVersionID#.#local.getDocument.fileExt#")>

			<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath=local.theFile, displayName=local.getDocument.fileName,
				forceDownload=1, s3bucket="membercentralcdn", s3objectKey=local.objectKey, checkLocalFirst=1, s3expire=1, s3requesttype="vhost")>
			<cfif not local.docResult>
				<cflocation url="/?pg=404" addtoken="false">
			</cfif>
		</cfif>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="emailMaterialsDoc" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>
		<cfset local.objSWP = CreateObject("component","model.seminarweb.SWParticipants")>
		<cfset local.swlcode = reReplaceNoCase(arguments.event.getValue('uniqueCode'),"[^A-Z]","","ALL")>
		<cfset local.verifyReg = local.objSWL.verifySWLCode(local.swlcode)>
		<cfset local.emailStruct = structNew()>
		<cfset local.strSeminarEnrolled = local.objSWL.getEnrollmentByEnrollmentID(local.verifyReg.enrollmentID)>						
		<cfset local.materialsDoc = local.objSWL.getMaterialsDocument(seminarID=local.verifyReg.seminarID, enrollmentID=val(local.verifyReg.enrollmentID), userType=local.verifyReg.SWLUserType, sitecode=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.strAssociation = local.objSWP.getAssociationDetails(arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(local.strSeminarEnrolled.signUpOrgCode)>
		
		<cfset local.emailFrom = ''>
		<cfset local.returnStruct = structnew()>
		<cfset local.returnStruct["success"] = 0>		
		<cfset local.memberID = val(local.strSeminarEnrolled.MCMemberID)>
		<cfif NOT local.memberID>
			<cfset local.memberID = arguments.event.getValue('mc_siteInfo.sysMemberID')>
		</cfif>
		<cfif len(local.strSeminarEnrolled.email)>
			<cfif NOT structKeyExists(local.strAssociation, "qryAssociation")>
				<cflocation url="/" addtoken="No">
			<cfelse>
				<cfset local.qrySWP = local.strAssociation.qryAssociation>
				<cfset local.semWeb.qrySWP = local.qrySWP>
				<cfset local.emailFrom = local.qrySWP.emailFrom>
			</cfif>
			
			<cfif local.materialsDoc.recordCount>
				<cfsavecontent variable="local.emailStruct.emailTitle">
					<cfoutput>
					<div style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding-bottom:3px;text-align:center;"><i>#arguments.event.getValue('mc_siteInfo.siteName')#</i></div>
					<div style="text-align:center;">Program Materials for #encodeForHTML('#local.strSeminarEnrolled.seminarName#')#</div>
					</cfoutput>
				</cfsavecontent>			

				<cfsavecontent variable="local.emailStruct.html">
					<cfoutput>
						<cfloop query="local.materialsDoc">
							<cfset local.eachMaterialsObj = local.materialsDoc>	
							<cfset local.LinkToProgram = "#application.objPlatform.isRequestSecure() ? 'https' : 'http'#://#local.mc_siteInfo.mainhostname##arguments.event.getValue('mainurl')#&panel=downloadMaterialsDoc&uniqueCode=#local.swlcode#&documentID=#local.eachMaterialsObj.documentID#">

							<div style="font-size:14px;padding-left:5px;margin-bottom:20px;"><a  style="font-size: 14px;  padding: 5px 5px; background: grey; color: ##fff; border-radius: 5px; text-decoration: none;" href="#local.LinkToProgram#" target="_blank">Download</a> &nbsp;#local.eachMaterialsObj.docTitle#</div>
						</cfloop>						
					</cfoutput>
				</cfsavecontent>		
			</cfif>

			<cfset local.emailStruct.templateDisp = application.objEmailWrapper.wrapMessage(emailTitle=local.emailStruct.emailTitle, emailContent=local.emailStruct.html, sitecode=local.strSeminarEnrolled.signUpOrgCode)>
				
			<cfscript>
				local.arrEmailTo = [];
				local.toEmailArr = listToArray(replace(local.strSeminarEnrolled.email,",",";","all"),';');
				for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
					local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
				}
				
				if (arrayLen(local.arrEmailTo)) {
					local.strEmailResult = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name=local.emailFrom, email='<EMAIL>' },
						emailto=local.arrEmailTo,
						emailreplyto='',
						emailsubject="Program Materials: #local.strSeminarEnrolled.seminarName#",
						emailtitle=local.emailStruct.emailTitle,
						emailhtmlcontent=local.emailStruct.html,
						siteID=arguments.event.getValue('mc_siteInfo.siteid'),
						memberID=local.memberID,
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SWLMATERIALS"),
						sendingSiteResourceID=arguments.event.getValue('mc_siteInfo.siteSiteResourceID')
					);
				}
			</cfscript>
			<cfset local.redirecto = "/?pg=semWebCatalog&panel=joinLive&joinsubmitted=1&uniqueCode=#local.swlcode#&msg=1">
			<cfset local.returnStruct["success"] = 1>
			<cflocation url="#local.redirecto#" addtoken="false">
		<cfelse>
			<cfset local.redirecto = "/?pg=semWebCatalog&panel=joinLive&joinsubmitted=1&uniqueCode=#local.swlcode#">
		</cfif>

		<cfreturn returnAppStruct(local.returnStruct,"echo")>
	</cffunction>

</cfcomponent>