----------------------------------------------------------------------------------------------------
/* Query #38: PASSED */
ALTER PROCEDURE [dbo].[sw_AlertSQLServerAgentStarted] 
(
	@MailProfileName VARCHAR(128) = NULL,
	@AlertEmail VARCHAR(128) = NULL,
	@Company VARCHAR(128) = NULL,
	@Configuration VARCHAR(128) = NULL
)
AS
BEGIN
	SET NOCOUNT ON

	IF @MailProfileName IS NULL
	BEGIN
		SELECT @MailProfileName = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'MailProfileName'
	END

	IF @AlertEmail IS NULL
	BEGIN
		SELECT @AlertEmail = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'AlertEmail'
	END

	IF @Company IS NULL
	BEGIN
		SELECT @Company = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Company'
	END

	IF @Configuration IS NULL
	BEGIN
		SELECT @Configuration = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Configuration'
	END

	DECLARE @SQLAgentStartupDateTable TABLE
	(
		[LogDate] DATETIME NULL,
		[ErrorLevel] INT NULL,
		[Text] NVARCHAR(MAX) NULL
	)

	DECLARE @SQLAgentStopReasonTable TABLE
	(
		[LogDate] DATETIME NULL,
		[ErrorLevel] INT NULL,
		[Text] NVARCHAR(MAX) NULL
	)

	INSERT INTO @SQLAgentStartupDateTable EXEC sp_readerrorlog 0,2, 'SQLSERVERAGENT starting'

	INSERT INTO @SQLAgentStopReasonTable EXEC sp_readerrorlog 1,2, 'SQLSERVERAGENT service stopping'

	IF (SELECT TOP 1 [LogDate] FROM @SQLAgentStopReasonTable) IS NULL
	BEGIN
		INSERT INTO @SQLAgentStopReasonTable EXEC sp_readerrorlog 1,2, 'SQLSERVERAGENT stopping'
	END

	DECLARE @SQLServerStartDateTable TABLE
	(
		[LogDate] DATETIME NULL,
		[ProcessInfo] NVARCHAR(MAX) NULL,
		[Text] NVARCHAR(MAX) NULL
	)

	DECLARE @SQLServerStopReasonTable TABLE
	(
		[LogDate] DATETIME NULL,
		[ProcessInfo] NVARCHAR(MAX) NULL,
		[Text] NVARCHAR(MAX) NULL
	)

	INSERT INTO @SQLServerStartDateTable EXEC sp_readerrorlog 0,1, 'Logging SQL Server messages in file'

	INSERT INTO @SQLServerStopReasonTable EXEC sp_readerrorlog 1,1, 'SQL Server is terminating'

	DECLARE @Case TINYINT = 1 /* 1: Just Agent Restarted | 2: Agent And SQL Server Restarted | 3: Agent, SQL Server, and Server Restarted */

	DECLARE @AgentStartupDateTime DATETIME = (SELECT TOP 1 [LogDate] FROM @SQLAgentStartupDateTable)
	DECLARE @AgentShutdownDateTime DATETIME = (SELECT TOP 1 [LogDate] FROM @SQLAgentStopReasonTable)
	DECLARE @AgentShutdownReason NVARCHAR(MAX) = (SELECT TOP 1 [Text] FROM @SQLAgentStopReasonTable)

	INSERT INTO StartupLog ([Service], [ShutdownTime], [ShutdownMessage], [StartupTime]) VALUES ('SQL Server Agent', @AgentShutdownDateTime, @AgentShutdownReason, @AgentStartupDateTime)

	DECLARE @SQLStartupDateTime DATETIME = (SELECT TOP 1 [LogDate] FROM @SQLServerStartDateTable)
	DECLARE @SQLShutdownDateTime DATETIME = (SELECT TOP 1 [LogDate] FROM @SQLServerStopReasonTable)
	DECLARE @SQLShutdownReason NVARCHAR(MAX) = (SELECT TOP 1 [Text] FROM @SQLServerStopReasonTable)

	IF @SQLStartupDateTime IS NOT NULL AND (SELECT TOP 1 [ID] FROM [StartupLog] WHERE [StartupTime] = @SQLStartupDateTime AND [Service] = 'SQL Server') IS NULL
	BEGIN
		DECLARE @PreviousSQLStartupDateTime DATETIME = (SELECT TOP 1 [StartupTime] FROM [StartupLog] WHERE [Service] = 'SQL Server' ORDER BY [StartupTime] DESC)
		DECLARE @LastServerStartupDateTime DATETIME = (SELECT TOP 1 [StartupTime] FROM [StartupLog] WHERE [Service] = 'Windows' ORDER BY [StartupTime] DESC)
		IF @PreviousSQLStartupDateTime > @LastServerStartupDateTime
		BEGIN
			SET @Case = 2
		END
		ELSE 
		BEGIN
			DECLARE @ServerStartupDateTime DATETIME = @LastServerStartupDateTime
			DECLARE @ServerShutdownDateTime DATETIME = (SELECT TOP 1 [ShutdownTime] FROM [SQLWatchmen].[dbo].[StartupLog] WHERE [StartupTime] = @ServerStartupDateTime AND [Service] = 'Windows')
			DECLARE @ServerShutdownMessage NVARCHAR(1000) = (SELECT TOP 1 [ShutdownMessage] FROM [SQLWatchmen].[dbo].[StartupLog] WHERE [StartupTime] = @ServerStartupDateTime AND [Service] = 'Windows')
			DECLARE @ServerUnexpectedShutdownMessage NVARCHAR(1000) = (SELECT TOP 1 [UnexpectedShutdownMessage] FROM [SQLWatchmen].[dbo].[StartupLog] WHERE [StartupTime] = @ServerStartupDateTime AND [Service] = 'Windows')
			SET @Case = 3
		END
		INSERT INTO StartupLog ([Service], [ShutdownTime], [ShutdownMessage], [StartupTime]) VALUES ('SQL Server', @SQLShutdownDateTime, @SQLShutdownReason, @SQLStartupDateTime)
	END

	DECLARE @Subject NVARCHAR(255) 
	DECLARE @Body NVARCHAR(MAX)
	
	IF @Case = 1 
	BEGIN
		SET @Subject = 'SQL Server Agent Restarted | ' + @Configuration + ' | ' + @Company
		SET @Body = 'SQL Server Agent Restarted' + CHAR(13) + CHAR(10) 
		IF @AgentStartupDateTime IS NOT NULL
		BEGIN
			SET @Body += 'Startup Date/Time: ' + CAST(@AgentStartupDateTime AS NVARCHAR) + CHAR(13) + CHAR(10) 
		END 
		ELSE
		BEGIN
			SET @Body += 'Error Retrieving SQL Server Agent Startup Time from Error Log'
		END
		IF @AgentShutdownDateTime IS NOT NULL
		BEGIN
			SET @Body += 'Shutdown Date/Time: ' + CAST(@AgentShutdownDateTime AS NVARCHAR) + CHAR(13) + CHAR(10) +
			'Shutdown Reason: ' + @AgentShutdownReason + CHAR(13) + CHAR(10)
		END
		ELSE
		BEGIN
			SET @Body += 'Error Retrieving SQL Server Agent Shutdown Information from Error Log. SQL Server Agent may have stopped unexpectedly' + CHAR(13) + CHAR(10)
		END
	END

	IF @Case = 2 
	BEGIN
		SET @Subject = 'SQL Server Agent and SQL Server Restarted | ' + @Configuration + ' | ' + @Company
		SET @Body = 'SQL Server Agent Information:' + CHAR(13) 
		IF @AgentStartupDateTime IS NOT NULL
		BEGIN
			SET @Body += CHAR(9) + 'Startup Date/Time: ' + CAST(@AgentStartupDateTime AS NVARCHAR) + CHAR(13) + CHAR(10) 
		END 
		ELSE
		BEGIN
			SET @Body += CHAR(9) + 'Error Retrieving SQL Server Agent Startup Time from Error Log' + CHAR(13) + CHAR(10)
		END
		IF @AgentShutdownDateTime IS NOT NULL
		BEGIN
			SET @Body += CHAR(9) + 'Shutdown Date/Time: ' + CAST(@AgentShutdownDateTime AS NVARCHAR) + CHAR(13) + CHAR(10) +
			CHAR(9) + 'Shutdown Reason: ' + @AgentShutdownReason + CHAR(13) + CHAR(10)
		END
		ELSE
		BEGIN
			SET @Body += CHAR(9) + 'Error Retrieving SQL Server Agent Shutdown Information from Error Log. SQL Server Agent may have stopped unexpectedly' + CHAR(13) + CHAR(10)
		END

		SET @Body += 'SQL Server Information:' + CHAR(13) + CHAR(10)
		IF @SQLStartupDateTime IS NOT NULL
		BEGIN
			SET @Body += CHAR(9) + 'Startup Date/Time: ' + CAST(@SQLStartupDateTime AS NVARCHAR) + CHAR(13) + CHAR(10)
		END
		ELSE 
		BEGIN
			SET @Body += CHAR(9) + 'Error Retrieving SQL Server Startup Time from Error Log' + CHAR(13) + CHAR(10)
		END
		IF @SQLShutdownDateTime IS NOT NULL
		BEGIN
			SET @Body += CHAR(9) + 'Shutdown Date/Time: ' + CAST(@SQLShutdownDateTime AS NVARCHAR) + CHAR(13) + CHAR(10) +
			CHAR(9) + 'Shutdown Reason: ' + @SQLShutdownReason + CHAR(13) + CHAR(10)
		END
		ELSE
		BEGIN
			SET @Body += CHAR(9) + 'Error Retrieving SQL Server Shutdown Information from Error Log. SQL Server may have stopped unexpectedly' + CHAR(13) + CHAR(10)
		END
	END

	IF @Case = 3 
	BEGIN
		SET @Subject = 'Server Restarted | ' + @Configuration + ' | ' + @Company
		SET @Body = 'SQL Server Agent Information:' + CHAR(13) + CHAR(10)
		IF @AgentStartupDateTime IS NOT NULL
		BEGIN
			SET @Body += CHAR(9) + 'Startup Date/Time: ' + CAST(@AgentStartupDateTime AS NVARCHAR) + CHAR(13) + CHAR(10)
		END 
		ELSE
		BEGIN
			SET @Body += CHAR(9) + 'Error Retrieving SQL Server Agent Startup Time from Error Log' + CHAR(13) + CHAR(10)
		END
		IF @AgentShutdownDateTime IS NOT NULL
		BEGIN
			SET @Body += CHAR(9) + 'Shutdown Date/Time: ' + CAST(@AgentShutdownDateTime AS NVARCHAR) + CHAR(13) + CHAR(10) +
			CHAR(9) + 'Shutdown Reason: ' + @AgentShutdownReason + CHAR(13) + CHAR(10)
		END
		ELSE
		BEGIN
			SET @Body += CHAR(9) + 'Error Retrieving SQL Server Agent Shutdown Information from Error Log. SQL Server Agent may have stopped unexpectedly' + CHAR(13) + CHAR(10)
		END

		SET @Body += 'SQL Server Information:' + CHAR(13) + CHAR(10)
		IF @SQLStartupDateTime IS NOT NULL
		BEGIN
			SET @Body += CHAR(9) + 'Startup Date/Time: ' + CAST(@SQLStartupDateTime AS NVARCHAR) + CHAR(13) + CHAR(10)
		END
		ELSE 
		BEGIN
			SET @Body += CHAR(9) + 'Error Retrieving SQL Server Startup Time from Error Log' + CHAR(13) + CHAR(10)
		END
		IF @SQLShutdownDateTime IS NOT NULL
		BEGIN
			SET @Body += CHAR(9) + 'Shutdown Date/Time: ' + CAST(@SQLShutdownDateTime AS NVARCHAR) + CHAR(13) + CHAR(10) +
			CHAR(9) + 'Shutdown Reason: ' + @SQLShutdownReason + CHAR(13) + CHAR(10)
		END
		ELSE
		BEGIN
			SET @Body += CHAR(9) + 'Error Retrieving SQL Server Shutdown Information from Error Log. SQL Server may have stopped unexpectedly' + CHAR(13) + CHAR(10)
		END

		SET @Body += 'Server Information:' + CHAR(13) + CHAR(10)
		IF @ServerStartupDateTime IS NOT NULL
		BEGIN
			SET @Body += CHAR(9) + 'Startup Date/Time: ' + CAST(@ServerStartupDateTime AS NVARCHAR) + CHAR(13) + CHAR(10)
		END
		ELSE 
		BEGIN
			SET @Body += CHAR(9) + 'Error Retrieving Server Startup Time' + CHAR(13) + CHAR(10)
		END
		IF @ServerShutdownDateTime IS NOT NULL
		BEGIN
			SET @Body += CHAR(9) + 'Shutdown Date/Time: ' + CAST(@ServerShutdownDateTime AS NVARCHAR) + CHAR(13) + CHAR(10) +
			CHAR(9) + 'Shutdown Reason: ' + @ServerShutdownMessage + CHAR(13) + CHAR(10)
		END
		ELSE
		BEGIN
			SET @Body += CHAR(9) + 'UNEXPECTED SHUTDOWN: ' + @ServerUnexpectedShutdownMessage + CHAR(13) + CHAR(10)
		END
	END

	EXEC msdb.dbo.sp_send_dbmail
        @profile_name = @MailProfileName,
        @recipients = @AlertEmail,
        @subject = @Subject,
        @body = @Body

	INSERT INTO [SQLWatchmen].[dbo].[AlertLog] ([Alert]) VALUES ('SQL Server Agent Restarted')

END
GO
