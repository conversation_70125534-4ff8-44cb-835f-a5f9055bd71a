<cfcomponent extends="model.admin.admin" output="false">
	<cfset variables.defaultEvent = 'controller'>
	
	<cffunction name="controller" access="public" output="false" returntype="string" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			this.objAdminEvent = CreateObject("component","model.admin.events.event");

			// RUN ASSIGNED METHOD --------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('meth')];

			if( ListFindNoCase('getRegistrants,getRSVPs',arguments.event.getValue('meth')) and val(arguments.event.getValue('eID',0))){
				this.siteResourceID = this.objAdminEvent.getSiteResourceIDByEventID(siteID=arguments.event.getValue('mc_siteInfo.siteid'), eventID=arguments.event.getValue('eID'));
			}
			// ------------------------------------------------------------------------------------------ ::
			// SET RIGHTS INTO EVENT -------------------------------------------------------------------- ::
			local.tmpRights = buildRightAssignments(
				siteResourceID=this.siteResourceID,
				memberID=session.cfcuser.memberdata.memberID,
				siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

			// PASS THE ARGUMENT COLLECTION TO THE CURRENT METHOD AND EXECUTE IT. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="getCalendars" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.searchValue = form['search[value]'] ?: '';
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"calendarName #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"pageName #arguments.event.getValue('orderDir')#")>
		<cfset local.orderBy = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<cfquery name="local.qryCalendars" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpCalendars') IS NOT NULL
				DROP TABLE ##tmpCalendars;
			IF OBJECT_ID('tempdb..##tmpCalendarsFinal') IS NOT NULL
				DROP TABLE ##tmpCalendarsFinal;
			CREATE TABLE ##tmpCalendars (calendarID int PRIMARY KEY, calendarName varchar(200), siteResourceID int,
				applicationInstanceID int);
			CREATE TABLE ##tmpCalendarsFinal (calendarID int PRIMARY KEY, calendarName varchar(200), siteResourceID int,
				applicationInstanceID int, pageName varchar(50), rightsXML xml, row int);

			DECLARE @siteID int, @totalCount int, @posStart int, @posStartAndCount int, @searchValue varchar(300), @memberID int;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;
			SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>

			INSERT INTO ##tmpCalendars (calendarID, siteResourceID, applicationInstanceID, calendarName)
			SELECT calendarID, siteResourceID, applicationInstanceID, calendarName
			FROM dbo.fn_ev_getCalendarsOnSite(@siteID);

			INSERT INTO ##tmpCalendarsFinal (calendarID, calendarName, siteResourceID, applicationInstanceID, pageName, rightsXML, row)
			SELECT calendarID, calendarName, siteResourceID, applicationInstanceID, pageName, rightsXML,
				ROW_NUMBER() OVER (ORDER BY #local.orderBy#)
			FROM (
				SELECT cals.calendarID, cals.siteResourceID, cals.applicationInstanceID, cals.calendarName, aip.pageName,
					dbo.fn_cache_perms_getResourceRightsXML(cals.siteResourceID,@memberID,@siteID) as rightsXML
				FROM ##tmpCalendars as cals
				CROSS APPLY dbo.fn_cms_getApplicationInstancePagePath(@siteID,cals.applicationInstanceID) as aip
				WHERE aip.applicationSiteResourceID = cals.siteResourceID
				AND aip.applicationSiteResourceType = 'Events'
				<cfif len(local.searchValue)>
					AND (cals.calendarName LIKE @searchValue OR aip.pageName LIKE @searchValue)
				</cfif>
			) tmp2
			WHERE rightsXML.exist('(/rights/right[@allowed="1"])[1]') = 1;

			SET @totalCount = @@ROWCOUNT;

			SELECT calendarID, calendarName, siteResourceID, applicationInstanceID, pageName, rightsXML, @totalCount as totalCount
			FROM ##tmpCalendarsFinal
			WHERE row > @posStart
			AND row <= @posStartAndCount
			ORDER BY row;

			IF OBJECT_ID('tempdb..##tmpCalendars') IS NOT NULL
				DROP TABLE ##tmpCalendars;
			IF OBJECT_ID('tempdb..##tmpCalendarsFinal') IS NOT NULL
				DROP TABLE ##tmpCalendarsFinal;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfif local.qryCalendars.recordCount>
			<cfloop query="local.qryCalendars">
				<cfset local.xmlRights = xmlParse(local.qryCalendars.rightsXML)>
				<cfset local.tmpStr = {
					"calendarid": local.qryCalendars.calendarID,
					"calendarname": local.qryCalendars.calendarName,
					"siteresourceid": local.qryCalendars.siteResourceID,
					"pagename": local.qryCalendars.pageName,
					"applicationinstanceid": local.qryCalendars.applicationInstanceID,
					"addeventrights": XMLSearch(local.xmlRights,"string(//right[@functionName='AddEvent']/@allowed)") is 1,
					"deleteeventrights": XMLSearch(local.xmlRights,"string(//right[@functionName='Delete']/@allowed)") is 1,
					"DT_RowId": "evcal_#local.qryCalendars.calendarID#"
				}>

				<cfset arrayAppend(local.data, local.tmpStr)>
			</cfloop>
		</cfif>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryCalendars.totalCount),
			"recordsFiltered": val(local.qryCalendars.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getRegistrationSchedules" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.registrationID = int(val(arguments.event.getValue('registrationID',0)));
			local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
			local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
		</cfscript>

		<cfquery name="local.qryRegistrationSchedules" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;

			SELECT ep.scheduleID, ep.registrationID, ep.rangeName, ep.startDate, ep.endDate, 
				CASE 
				WHEN EXISTS(select top 1 priceID from dbo.ev_ticketPackageAvailable where scheduleID = ep.scheduleID and isActive = 1) THEN 1
				ELSE 0 END AS hasPackagesTied
			FROM dbo.ev_priceSchedule ep
			INNER JOIN dbo.ev_registration as er ON er.siteID = @siteID and er.registrationID = ep.registrationID AND er.registrationTypeID = 1
			INNER JOIN dbo.ev_events e ON e.siteID = @siteID and e.eventID = er.eventID AND e.status <> 'D'
			WHERE ep.registrationID = <cfqueryparam value="#arguments.event.getValue('registrationID',0)#" cfsqltype="CF_SQL_INTEGER">
			ORDER BY ep.startDate ASC;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfif local.qryRegistrationSchedules.recordCount>
			<cfloop query="local.qryRegistrationSchedules">
				<cfset local.TzStartDate=local.objTZ.convertTimeZone(dateToConvert=local.qryRegistrationSchedules.startDate,fromTimeZone='US/Central',toTimeZone=local.regTimeZone)>
				<cfset local.TzEndDate=local.objTZ.convertTimeZone(dateToConvert=local.qryRegistrationSchedules.endDate,fromTimeZone='US/Central',toTimeZone=local.regTimeZone)>

				<cfset local.tmpStr = {
					"scheduleid": local.qryRegistrationSchedules.scheduleID,
					"registrationid": local.qryRegistrationSchedules.registrationID,
					"rangename": local.qryRegistrationSchedules.rangeName,
					"startdate": "#DateTimeFormat(local.TzStartDate, 'm/d/yyyy hh:nn tt', local.regTimeZone)#",
					"enddate": "#DateTimeFormat(local.TzEndDate, 'm/d/yyyy hh:nn tt', local.regTimeZone)#",
					"haspackagestied": local.qryRegistrationSchedules.hasPackagesTied,
					"DT_RowId": "row_#local.qryRegistrationSchedules.scheduleID#"
				}>

				<cfset arrayAppend(local.data, local.tmpStr)>
			</cfloop>
		</cfif>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryRegistrationSchedules.recordCount,
			"recordsFiltered": local.qryRegistrationSchedules.recordCount,
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getEventDocuments" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.arreventDocuments = []>
		<cfset local.reorderData = DeserializeJSON(arguments.event.getValue('reorderData',''))>
		<cfif isArray(local.reorderData) and arrayLen(local.reorderData) GT 1>
			<cfloop array="#local.reorderData#" index="local.thisData">
				<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryUpdateData">
					UPDATE dbo.ev_eventDocuments
					SET fileOrder = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.newOrder#">
					WHERE eventDocumentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.id#">
					and ISNULL(eventDocumentGroupingID,0) = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.eventDocumentGroupingID#">
					AND eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('eID',0)#">
				</cfquery>
			</cfloop>
		</cfif>
		<cfquery name="local.qryDocuments" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">,
				@eventID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('eID',0)#">;

			WITH DocGroupings AS (
				SELECT eventDocumentGroupingID, eventDocumentGroupingOrder, eventDocumentGrouping
				FROM dbo.ev_eventDocumentGrouping
				WHERE eventID = @eventID
					UNION ALL
				SELECT 0, 0, 'Default - No Grouping'
			)
			SELECT ed.eventDocumentID, ed.documentID, ed.eventID, dl.docTitle, dv.documentVersionID, dv.fileName, 
				dv.author, l.languageCode, dg.eventDocumentGroupingID, dg.eventDocumentGrouping, 
				dg.eventDocumentGroupingOrder, ed.fileOrder
			FROM DocGroupings AS dg
			LEFT OUTER JOIN dbo.ev_eventDocuments AS ed
				INNER JOIN dbo.cms_documents AS d ON ed.documentID = d.documentID and d.siteID=@siteID
				INNER JOIN dbo.cms_documentLanguages AS dl ON dl.documentID = d.documentID
				INNER JOIN dbo.cms_documentVersions AS dv ON dv.documentLanguageID = dl.documentLanguageID AND dv.isActive = 1
				INNER JOIN dbo.cms_languages AS l ON l.languageID = dl.languageID
				INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID AND sr.siteResourceID = d.siteResourceID
				INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID AND srs.siteResourceStatusDesc = 'Active'
				ON ISNULL(ed.eventDocumentGroupingID,0) = dg.eventDocumentGroupingID
				AND ed.eventID = @eventID
			ORDER BY dg.eventDocumentGroupingOrder, ed.fileOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		
		<cfquery name="local.totalEventDocumentGroupingsNotZero" dbtype="query">
			SELECT DISTINCT eventDocumentGroupingID
			FROM [local].qryDocuments
			WHERE eventDocumentGroupingID > 0
		</cfquery>
		
		<cfset local.RGIndex = 0>

		<cfoutput query="local.qryDocuments" group="eventDocumentGroupingID">
			<cfset local.eventDocumentGroupRowID = "rg#val(local.qryDocuments.eventDocumentGroupingID)#">

			<cfif local.totalEventDocumentGroupingsNotZero.recordcount>
				<cfif local.qryDocuments.eventDocumentGroupingID gt 0>
					<cfset local.RGIndex++>
				</cfif>

				<cfset local.objeventDocumentGrouping = {
					"level": 1,
					"rowType": "eventDocumentGroup",
					"displayName": local.qryDocuments.eventDocumentGrouping,
					"displayNameEncoded": encodeForJavascript(local.qryDocuments.eventDocumentGrouping),
					"eventDocumentGroupingID": val(local.qryDocuments.eventDocumentGroupingID),
					"eventDocumentID": 0,
					"hasChildren": 0,
					"canMoveUp": local.RGIndex gt 1,
					"canMoveDown": local.RGIndex lt local.totalEventDocumentGroupingsNotZero.recordCount,
					"parentRowID": "gridRoot",
					"DT_RowId": local.eventDocumentGroupRowID,
					"DT_RowClass": "child-of-gridRoot"
				}>
				<cfset local.objeventDocumentGrouping["DT_RowClass"] &= val(local.qryDocuments.eventDocumentGroupingID) eq 0 ? " default-nogrouping" : "">
				<cfset local.arreventDocuments.append(local.objeventDocumentGrouping)>
			</cfif>

			<cfset local.eventDocumentIndex = 0>
			<cfset local.eventDocumentParentRowID = local.totalEventDocumentGroupingsNotZero.recordcount ? local.eventDocumentGroupRowID : "gridRoot">
				
			<cfoutput group="eventDocumentID">
				<cfif val(local.qryDocuments.eventDocumentID) gt 0>
					<cfset local.eventDocumentIndex++>

					<cfquery name="local.totaleventDocumentCount" dbtype="query">
						SELECT DISTINCT eventDocumentID
						FROM [local].qryDocuments
						WHERE eventDocumentGroupingID = #local.qryDocuments.eventDocumentGroupingID#
					</cfquery>

					<cfset local.eventDocumentRowID = "rg#local.qryDocuments.eventDocumentGroupingID#-#local.qryDocuments.eventDocumentID#">
					<cfset local.objeventDocument = {
						"level": local.totalEventDocumentGroupingsNotZero.recordcount ? 2 : 1,
						"rowType": "eventDocument",
						"displayName": local.qryDocuments.docTitle,
						"displayNameEncoded": encodeForJavascript(local.qryDocuments.docTitle),
						"eventDocumentGroupingID": val(local.qryDocuments.eventDocumentGroupingID),
						"eventDocumentID": val(local.qryDocuments.eventDocumentID),
						"documentID": local.qryDocuments.documentID,
						"eventID": local.qryDocuments.eventID,
						"docTitle": local.qryDocuments.docTitle,
						"fileName": local.qryDocuments.fileName,
						"author": local.qryDocuments.author,
						"hasChildren": 0,
						"canMoveUp": local.eventDocumentIndex gt 1,
						"canMoveDown": local.eventDocumentIndex lt local.totaleventDocumentCount.recordCount,
						"previewLink": "/docDownload/#local.qryDocuments.documentID#&vid=#local.qryDocuments.documentVersionID#&lang=#local.qryDocuments.languageCode#",
						"parentRowID": local.eventDocumentParentRowID,
						"DT_RowId": local.eventDocumentRowID,
						"DT_RowClass": "child-of-#local.eventDocumentParentRowID#"
					}>
					<cfset local.arreventDocuments.append(local.objeventDocument)>

					

				</cfif>
			</cfoutput>

			<cfset local.objeventDocumentGrouping.hasChildren = local.eventDocumentIndex gt 0>
		</cfoutput>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": arrayLen(local.arreventDocuments),
			"recordsFiltered": arrayLen(local.arreventDocuments),
			"data": local.arreventDocuments
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getRegistrants" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.objTransAdmin = CreateObject('component','model.admin.transactions.transactionAdmin');
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			arguments.event.setValue('eID',int(val(arguments.event.getValue('eID',0))));
			
			local.gridMode = arguments.event.getValue('gridMode','regTabGrid');		
			local.qryRegistrants = this.objAdminEvent.getRegistrantsFromFilters(event=arguments.event, mode=local.gridMode);

			if (local.gridMode EQ 'regTabGrid')
				local.hasEventDocs = this.objAdminEvent.hasEventDocumentsForAnEvent(eventID=arguments.event.getValue('eID'));			
		</cfscript>

		<cfset local.editmemberlink = createObject("component","model.admin.admin").buildLinkToTool(toolType='MemberAdmin',mca_ta='edit')>

		<cfset local.data = []>
		<cfloop query="local.qryRegistrants">
			<cfset local.tmpCredits = xmlParse(local.qryRegistrants.creditsXML)>
			<cfset local.tmpCredits = local.tmpCredits.xmlRoot>
			
			<cfif local.gridMode EQ 'regSearchGrid'>
				<cfset local.tmpRights = xmlParse(local.qryRegistrants.rightsXML)>
				<cfset local.hasEditRegRights = XMLSearch(local.tmpRights,"string(//right[@functionName='EditRegistrants']/@allowed)") is 1>
			<cfelse>
				<cfset local.hasEditRegRights = arguments.event.getValue('mc_adminToolInfo.myRights.EditRegistrants',0)>
			</cfif>

			<cfset local.tmpStr = {
				"registrantid": local.qryRegistrants.registrantID,
				"memberid": local.qryRegistrants.memberid,
				"membernumber": local.qryRegistrants.memberNumber,
				"status": local.qryRegistrants.status,
				"isflagged": local.qryRegistrants.isFlagged,
				"lastname": local.qryRegistrants.lastname,
				"firstname": local.qryRegistrants.firstname,
				"company": local.qryRegistrants.company,
				"regroles": local.gridMode EQ 'regTabGrid' ? replace(local.qryRegistrants.regRoles,"|",", ","ALL") : "",
				"regrate": local.gridMode EQ 'regTabGrid' OR local.gridMode EQ 'regSearchGrid' ? local.qryRegistrants.regrate: "",
				"registereddate": dateFormat(local.qryRegistrants.dateRegistered,"m/d/yyyy"),
				"registereddatetime": "#dateFormat(local.qryRegistrants.dateRegistered,"m/d/yyyy")# #timeFormat(local.qryRegistrants.dateRegistered,"h:mm tt")#",
				"attendedinfo": "",
				"totalregfee": dollarFormat(local.qryRegistrants.totalRegFee),
				"amountdue": local.qryRegistrants.amountDue,
				"amountduedisplay": dollarFormat(local.qryRegistrants.amountDue),
				"canedit": local.qryRegistrants.status EQ 'A' and local.hasEditRegRights,
				"addpaymentencstring": "",
				"eventstatus": local.qryRegistrants.eventStatus,
				"showcert": local.gridMode EQ 'regTabGrid' ? local.qryRegistrants.showCert : false,
				"showeval": local.gridMode EQ 'regTabGrid' ? local.qryRegistrants.showEval : false,
				"cancleanupinvoices": local.qryRegistrants.status EQ 'D' AND local.hasEditRegRights AND val(local.qryRegistrants.totalRegFee),
				"showemailmaterials": local.gridMode EQ 'regTabGrid' ? local.hasEventDocs : false,
				"editmemberlink": "#local.editmemberlink#&memberID=#local.qryRegistrants.memberid#",
				"DT_RowId": "row_#local.qryRegistrants.registrantID#"
			}>

			<cfif local.gridMode EQ 'regSearchGrid'>
				<cfset StructAppend(local.tmpStr, {
					"eventid": local.qryRegistrants.eventID,
					"calendarid": local.qryRegistrants.calendarID,
					"eventtitle": local.qryRegistrants.eventTitle
				}, true)>
			</cfif>

			<cfif local.tmpStr.status eq "A">				
				<cfset local.tmpStr.attendedinfo = YesNoFormat(local.qryRegistrants.attended)>
				<cfif arrayLen(local.tmpCredits.xmlChildren)>
					<cfset local.tmpAwarded = xmlsearch(local.tmpCredits,"count(//credit[@creditAwarded = '1'])")>
					<cfset local.tmpStr.attendedinfo &= " - #local.tmpAwarded# credit">
					<cfif local.tmpAwarded gt 1>
						<cfset local.tmpStr.attendedinfo &= "s">
					</cfif>
				</cfif>
			</cfif>

			<cfif local.tmpStr.canedit and local.tmpStr.amountDue gt 0>
				<cfset local.tmpStr.addPaymentEncString = local.objTransAdmin.generatePOForAddPayment(pmid=local.qryRegistrants.memberID, t="Registration of #local.qryRegistrants.lastname#, #local.qryRegistrants.firstname# (#local.qryRegistrants.memberNumber#)", ta=local.qryRegistrants.amountDue, tmid=local.qryRegistrants.memberID, ad="v|#local.qryRegistrants.invoicesDue#")>
			</cfif>

			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryRegistrants.totalCount),
			"recordsFiltered": val(local.qryRegistrants.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getMemberEvents" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.objTransAdmin = CreateObject('component','model.admin.transactions.transactionAdmin');
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			arguments.event.setValue('memberID',int(val(arguments.event.getValue('memberID',0))));
		</cfscript>

		<!--- handle ordering --->
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"eventTitle")>
		<cfset arrayAppend(local.arrCols,"dateRegistered")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>

		<!--- get registrants --->
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRegistrants" result="local.qryRegistrantsResult">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int, @orgID int, @memberID int, @loggedMemberID int, @totalCount int, @defaultTimeZoneID int;
			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
			set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
			set @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('memberID')#">;
			set @loggedMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">;
			set @defaultTimeZoneID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.defaultTimeZoneID')#">;

			IF OBJECT_ID('tempdb..##tblCertParentEvents') IS NOT NULL 
				DROP TABLE ##tblCertParentEvents;
			IF OBJECT_ID('tempdb..##tblCertThisEvent') IS NOT NULL 
				DROP TABLE ##tblCertThisEvent;
			IF OBJECT_ID('tempdb..##tblRegistrations') IS NOT NULL 
				DROP TABLE ##tblRegistrations;
			IF OBJECT_ID('tempdb..##tblRegistrantInvoices') IS NOT NULL 
				DROP TABLE ##tblRegistrantInvoices;
			CREATE TABLE ##tblCertParentEvents (eventID int, certificateID int);
			CREATE TABLE ##tblCertThisEvent (registrantID int, certificateID int);
			CREATE TABLE ##tblRegistrantInvoices (registrantID int, invoiceID int);
			
			insert into ##tblCertParentEvents
			select subEvent.parentEventID, max(c.certificateID) as certificateID
			from dbo.ev_registrants as r
			inner join dbo.ev_registration rn on rn.siteID = @siteID and rn.registrationID = r.registrationID
				and rn.status = 'A'
			inner join dbo.crd_requests as rc on rc.registrantID = r.registrantID
				and rc.creditAwarded = 1
			inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
			inner join dbo.crd_authoritySponsorTypes as ecast on ecast.astid = ect.astid
			inner join dbo.crd_certificates as c on c.certificateID = ecast.LiveApprovedCertificateID
			inner join dbo.ev_subEvents as subEvent on subEvent.eventID = rn.eventID
			inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = r.memberID and m.activeMemberID = @memberID
			where r.recordedOnSiteID = @siteID
			and r.status = 'A'
			and r.attended = 1
			group by subEvent.parentEventID;
			
			insert into ##tblCertThisEvent
			select r.registrantID, max(c.certificateID) as certificateID
			from dbo.ev_registrants as r
			inner join dbo.ev_registration rn on rn.siteID = @siteID and rn.registrationID = r.registrationID
				and rn.status = 'A'
			inner join dbo.crd_requests as rc on rc.registrantID = r.registrantID
				and rc.creditAwarded = 1
			inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
			inner join dbo.crd_authoritySponsorTypes as ecast on ecast.astid = ect.astid
			inner join dbo.crd_certificates as c on c.certificateID = ecast.LiveApprovedCertificateID
			inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = r.memberID and m.activeMemberID = @memberID
			where r.recordedOnSiteID = @siteID
			and r.status = 'A'
			and r.attended = 1
			group by r.registrantID;

			select *, ROW_NUMBER() OVER (ORDER BY [status], #preserveSingleQuotes(local.orderby)# #arguments.event.getValue('orderDir')#) as row
			into ##tblRegistrations
			from (
				select e.eventID, ce.calendarID, m.activeMemberID as memberID, r.registrantID, r.attended, 
					cl.contentTitle as eventTitle, e.eventSubTitle, r.dateRegistered, r.status, r.isFlagged, 
					case when certs.certificateID is null then 0 else 1 end as showCert,
					case when certs2.certificateID is null then 0 else 1 end as showCert2,
					dbo.fn_cache_perms_getResourceRightsXML(ai.siteResourceID,@loggedMemberID,e.siteID) as calRightsXML,
					times.startTime as eventStartDate, r.rateID
				FROM dbo.ev_registrants as r
				INNER JOIN dbo.ev_registration as rn ON rn.siteID = @siteID and rn.registrationID = r.registrationID AND rn.status = 'A'
				INNER JOIN dbo.ams_members as m ON m.memberID = r.memberID and m.activeMemberID = @memberID
				INNER JOIN dbo.ev_events as e on e.siteID = @siteID and e.eventID = rn.eventID and e.status <> 'D'
				INNER JOIN dbo.ev_calendarEvents as ce on e.eventID = ce.sourceEventID and ce.calendarID = ce.sourceCalendarID
				INNER JOIN dbo.ev_calendars as cal on cal.siteID = @siteID and cal.calendarID = ce.calendarID
				INNER JOIN dbo.cms_applicationInstances as ai on ai.siteID = @siteID and ai.applicationInstanceID = cal.applicationInstanceID
				INNER JOIN dbo.cms_contentLanguages as cl on cl.contentID = e.eventContentID and cl.languageID = 1
				INNER JOIN dbo.ev_times as times on times.eventID = e.eventID and times.timeZoneID = @defaultTimeZoneID
				LEFT OUTER JOIN ##tblCertThisEvent as certs on certs.registrantID = r.registrantID					
				LEFT OUTER JOIN ##tblCertParentEvents certs2 on certs2.eventID = rn.eventID	
				WHERE r.recordedOnSiteID = @siteID
				<cfif arguments.event.getValue('fCalendar',0) gt 0>
					AND ce.calendarID = <cfqueryparam value="#arguments.event.getValue('fCalendar')#" cfsqltype="CF_SQL_INTEGER">
				</cfif>	
				<cfif len(arguments.event.getTrimValue('fKeyword',''))>
					AND cl.contentTitle + isnull(' '+e.eventSubTitle,'') like <cfqueryparam value="%#replace(arguments.event.getValue('fKeyword'),'_','\_','ALL')#%" cfsqltype="cf_sql_varchar"> ESCAPE('\')
				</cfif>
				<cfif arguments.event.getValue('fEventType','') eq 'main'>
					AND NOT EXISTS (select se.eventID from dbo.ev_subevents se where se.eventID = e.eventID)		
				<cfelseif arguments.event.getValue('fEventType','') eq 'sub'>
					AND EXISTS (select se.eventID from dbo.ev_subevents se where se.eventID = e.eventID)			
				</cfif>
				<cfif arguments.event.getValue('fCategory',0) gt 0>
					AND EXISTS 
					(SELECT ec.categoryID FROM dbo.ev_eventCategories ec WHERE ec.eventID = e.eventID 
					AND ec.categoryID=<cfqueryparam value="#arguments.event.getValue('fCategory',0)#" cfsqltype="cf_sql_integer">
					)	
				</cfif>
				<cfif arguments.event.getValue('fDateFrom','') NEQ ''>
					AND r.dateRegistered >= '#arguments.event.getValue('fDateFrom','')#'
				</cfif>
				<cfif arguments.event.getValue('fDateTo','') NEQ ''>
					AND r.dateRegistered <= '#arguments.event.getValue('fDateTo','')# 23:59:59.997'
				</cfif>
				<cfif arguments.event.getValue('fEvDateFrom','') NEQ '' and arguments.event.getValue('fEvDateTo','') NEQ ''>
					AND times.startTime between '#arguments.event.getValue('fEvDateFrom','')#' and '#arguments.event.getValue('fEvDateTo','')# 23:59:59.997'
				<cfelseif arguments.event.getValue('fEvDateFrom','') NEQ ''>
					AND times.startTime >= '#arguments.event.getValue('fEvDateFrom','')#'
				<cfelseif arguments.event.getValue('fEvDateTo','') NEQ ''>
					AND times.startTime <= '#arguments.event.getValue('fEvDateTo','')# 23:59:59.997'
				</cfif>
				<cfif arguments.event.getValue('fRegStatus',0) is 1>
					AND r.status = 'A'
				<cfelseif arguments.event.getValue('fRegStatus',0) is 2>
					AND r.status = 'D'
				</cfif>	
			) as innerTmp;

			select @totalCount = @@ROWCOUNT;

			INSERT INTO ##tblRegistrantInvoices (registrantID, invoiceID)
			select tmp.registrantID, it.invoiceID
			from ##tblRegistrations as tmp
			cross apply dbo.fn_ev_registrantTransactions(tmp.registrantID) as rt
			inner join dbo.tr_invoiceTransactions as it on it.orgID = rt.ownedByOrgID and it.transactionID = rt.transactionID
			inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
			inner join dbo.tr_invoiceStatuses as invs on invs.statusID = i.statusID
			where invs.status in ('Open','Closed','Delinquent')
			group by tmp.registrantID, it.invoiceID
			having sum(it.cache_invoiceAmountAfterAdjustment - it.cache_activePaymentAllocatedAmount - it.cache_pendingPaymentAllocatedAmount) > 0;

			select tmp.*, @totalCount as totalCount,
				regFee.totalRegFee, regFee.totalRegFee-regFee.regFeePaid as amountDue, regFee.TransactionIDForRateAdjustment as rateTID,
				invoicesDue = case 
								when regFee.totalRegFee-regFee.regFeePaid > 0 
									then (select substring((
									  select ','+ cast(invoiceID as varchar(10)) AS [text()]
										from ##tblRegistrantInvoices
										where registrantID = tmp.registrantID
										For XML PATH ('')
										), 2, 2000)) 
								else '' end,
				cast(isnull((select credit.offeringTypeID, credit.creditAwarded
					from dbo.crd_requests as credit
					where registrantID = tmp.registrantID
					for XML AUTO, ROOT('credits')
					),'<credits/>') as xml) as creditsXML,
				cast(isnull((select offeringTypeID, creditAwarded from (
					select credit.offeringTypeID, credit.creditAwarded
					from dbo.crd_requests as credit
					where registrantID = tmp.registrantID
						union
					select cr.offeringTypeID, cr.creditAwarded
					from dbo.crd_requests as cr
					inner join dbo.ev_registrants as er on er.registrantID = cr.registrantID and er.memberID = tmp.memberID										
					inner join dbo.ev_registration as reg on reg.siteID = @siteID and reg.registrationID = er.registrationID
					inner join dbo.ev_subEvents as subEvent on subEvent.eventID = reg.eventID and subEvent.parentEventID = tmp.eventID
					) credit
					for XML AUTO, ROOT('credits')
					),'<credits/>') as xml) as masterEvCreditsXML,
				(select STRING_AGG(c.categoryName,'|')
				from dbo.ev_registrantCategories as rc 
				inner join dbo.cms_categories as c on c.categoryID = rc.categoryID
				where rc.registrantID = tmp.registrantID
				and c.isActive = 1) as regRoles,
				r.rateName as regrate
			from ##tblRegistrations as tmp
			LEFT OUTER JOIN dbo.ev_rates as r on r.rateID = tmp.rateID
			OUTER APPLY dbo.fn_ev_totalRegFeeAndPaid(@orgID, tmp.registrantID) as regFee
			WHERE tmp.row > <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">
			AND tmp.row <= <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart') + arguments.event.getValue('count')#">
			ORDER by tmp.row;

			IF OBJECT_ID('tempdb..##tblCertParentEvents') IS NOT NULL 
				DROP TABLE ##tblCertParentEvents;
			IF OBJECT_ID('tempdb..##tblCertThisEvent') IS NOT NULL 
				DROP TABLE ##tblCertThisEvent;
			IF OBJECT_ID('tempdb..##tblRegistrations') IS NOT NULL 
				DROP TABLE ##tblRegistrations;
			IF OBJECT_ID('tempdb..##tblRegistrantInvoices') IS NOT NULL 
				DROP TABLE ##tblRegistrantInvoices;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<!--- show email materials --->
		<cfset local.showEmailMaterialsCol = true>
		<cfif local.qryRegistrants.recordCount>
			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryDocuments">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT ed.eventID, COUNT(ed.eventDocumentID) AS evDocCount
				FROM dbo.ev_eventDocuments AS ed
				INNER JOIN dbo.cms_documents AS d ON ed.documentID = d.documentID
				INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = d.siteResourceID
				INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID 
					AND srs.siteResourceStatusDesc = 'Active'
				WHERE ed.eventID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#valueList(local.qryRegistrants.eventID)#">)
				GROUP BY ed.eventID;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfif local.qryRegistrants.totalCount LTE arguments.event.getValue('count') AND NOT local.qryDocuments.recordCount>
				<cfset local.showEmailMaterialsCol = false>
			</cfif>
		</cfif>
		<cfset local.badgePrinterEnabled = arguments.event.getValue('mc_siteinfo.SF_BADGEPRINTERS')>
		<cfset local.hasBadgeDevices = createObject("component","model.admin.badges.device").hasBadgeDeviceForSite(siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfset local.qryBadgeTemplates = CreateObject("component","model.admin.badges.device").getCategoriesAndTemplatesForTree(siteID=arguments.event.getValue('mc_siteinfo.siteid'), treeCode="BTEVENTS")>

		<cfset local.data = []>
		<cfloop query="local.qryRegistrants">
			<cfset var thisRegEventID = local.qryRegistrants.eventID>
			<cfset local.tmpCredits = xmlParse(local.qryRegistrants.creditsXML)>
			<cfset local.tmpCredits = local.tmpCredits.xmlRoot>
			<cfset local.tmpMasterCredits = xmlParse(local.qryRegistrants.masterEvCreditsXML)>
			<cfset local.tmpMasterCredits = local.tmpMasterCredits.xmlRoot>
			<cfset local.XMLCalRights = xmlParse(local.qryRegistrants.calRightsXML)>
			<cfset local.hasEventDocs = QueryFilter(local.qryDocuments,function(thisRow) { return arguments.thisRow.eventID EQ thisRegEventID; }).recordCount GT 0>
			<cfset local.hasEditRegRights = XMLSearch(local.XMLCalRights,"string(//right[@functionName='EditRegistrantsByDefault']/@allowed)") is 1>
			
			<cfset local.tmpStr = {
				"registrantid": local.qryRegistrants.registrantID,
				"eventid": local.qryRegistrants.eventID,
				"eventtitle": encodeForHTML(local.qryRegistrants.eventTitle),
				"eventsubtitle": encodeForHTML(local.qryRegistrants.eventSubTitle),
				"memberid": local.qryRegistrants.memberid,
				"status": local.qryRegistrants.status,
				"isflagged": local.qryRegistrants.isFlagged,
				"regroles": replace(local.qryRegistrants.regRoles,"|",", ","ALL"),
				"regrate": local.qryRegistrants.regrate,
				"registereddate": dateFormat(local.qryRegistrants.dateRegistered,"m/d/yyyy"),
				"eventdate": dateFormat(local.qryRegistrants.eventStartDate,"m/d/yyyy"),
				"registereddatetime": "#dateFormat(local.qryRegistrants.dateRegistered,"m/d/yyyy")# #timeFormat(local.qryRegistrants.dateRegistered,"h:mm tt")#",
				"attendedinfo": "",
				"totalregfee": local.qryRegistrants.totalRegFee,
				"totalregfeedisplay": dollarFormat(local.qryRegistrants.totalRegFee),
				"amountdue": local.qryRegistrants.amountDue,
				"amountduedisplay": dollarFormat(local.qryRegistrants.amountDue),
				"canedit": local.qryRegistrants.status EQ 'A' and local.hasEditRegRights,
				"editeventlink": createObject("component","model.admin.admin").buildLinkToTool(toolType='EventAdmin',mca_ta='editEvent') & "&bc=e&eID=#local.qryRegistrants.eventID#",
				"addpaymentencstring": "",
				"showcert": local.qryRegistrants.showCert,
				"showcert2": local.qryRegistrants.showCert2,
				"showemailmaterials": local.showEmailMaterialsCol,
				"haseventdocs": local.hasEventDocs,
				"badgeprinterenabled": local.badgePrinterEnabled,
				"hasbadgedevices": local.hasBadgeDevices,
				"hasbadgetemplates": local.qryBadgeTemplates.recordcount,
				"cancleanupinvoices": local.qryRegistrants.status EQ 'D' AND local.hasEditRegRights AND val(local.qryRegistrants.totalRegFee),
				"DT_RowId": "row_#local.qryRegistrants.registrantID#"
			}>

			<cfif local.tmpStr.status eq "A">				
				<cfset local.tmpStr.attendedinfo = YesNoFormat(local.qryRegistrants.attended)>
				<cfif arrayLen(local.tmpCredits.xmlChildren)>
					<cfset local.tmpAwarded = xmlsearch(local.tmpCredits,"count(//credit[@creditAwarded = '1'])")>
					<cfset local.tmpStr.attendedinfo &= " - #local.tmpAwarded# credit">
					<cfif local.tmpAwarded gt 1>
						<cfset local.tmpStr.attendedinfo &= "s">
					</cfif>
				<cfelseif arrayLen(local.tmpMasterCredits.xmlChildren)>
					<cfset local.tmpAwarded = xmlsearch(local.tmpMasterCredits,"count(//credit[@creditAwarded = '1'])")>
					<cfset local.tmpStr.attendedinfo &= " - #local.tmpAwarded# credit">
					<cfif local.tmpAwarded gt 1>
						<cfset local.tmpStr.attendedinfo &= "s">
					</cfif>
				</cfif>
			</cfif>

			<cfif local.tmpStr.canedit and local.tmpStr.amountDue gt 0>
				<cfset local.tmpStr.addPaymentEncString = local.objTransAdmin.generatePOForAddPayment(pmid=local.qryRegistrants.memberID, t="Registration of #local.qryRegistrants.eventTitle#", ta=local.qryRegistrants.amountDue, tmid=local.qryRegistrants.memberID, ad="v|#local.qryRegistrants.invoicesDue#")>
			</cfif>

			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryRegistrants.totalCount),
			"recordsFiltered": val(local.qryRegistrants.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getRSVPs" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"lastname")>
		<cfset arrayAppend(local.arrCols,"email")>
		<cfset arrayAppend(local.arrCols,"phone")>
		<cfset arrayAppend(local.arrCols,"dateEntered")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRegistrants" result="local.qryRegistrantsResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @posStart int, @posStartAndCount int, @totalCount int;
				SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
				SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;

				IF OBJECT_ID('tempdb..##tblRegistrants') IS NOT NULL
					DROP TABLE ##tblRegistrants;

				CREATE TABLE ##tblRegistrants (rsvpID int, salutation varchar(50), firstname varchar(70), lastname varchar(70),
					email varchar(200), phone varchar(30), company varchar(200), dateEntered datetime, row int);
				CREATE TABLE ##tblMembersWithCerts (memberID int PRIMARY KEY, certificateID int);

				INSERT INTO ##tblRegistrants
				SELECT rsvp.rsvpID, rsvp.salutation, rsvp.firstname, rsvp.lastname, rsvp.email, rsvp.phone, rsvp.company, rsvp.dateEntered,
					ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)# #arguments.event.getValue('orderDir')#) as row
				FROM dbo.ev_rsvp as rsvp
				INNER JOIN dbo.ev_registration as reg ON reg.registrationID = rsvp.registrationID and reg.status = 'A'
				AND reg.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
				AND reg.eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('eID')#">;

				SELECT @totalCount = @@ROWCOUNT;

				SELECT rsvpID, salutation, firstname, lastname,	email, phone, company, dateEntered, row, @totalCount as totalCount
				FROM ##tblRegistrants
				WHERE row > @posStart AND row <= @posStartAndCount
				ORDER BY row;

				IF OBJECT_ID('tempdb..##tblRegistrants') IS NOT NULL
					DROP TABLE ##tblRegistrants;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryRegistrants">
			<cfset local.tmpStr = {
				"rsvpid": local.qryRegistrants.rsvpID,
				"salutation": local.qryRegistrants.salutation,
				"firstname": local.qryRegistrants.firstname,
				"lastname": local.qryRegistrants.lastname,
				"email": local.qryRegistrants.email,
				"phone": local.qryRegistrants.phone,
				"company": local.qryRegistrants.company,
				"dateentered": "#dateFormat(local.qryRegistrants.dateEntered,"m/d/yyyy")# #timeFormat(local.qryRegistrants.dateEntered,"h:mm tt")#",
				"canedit": arguments.event.getValue('mc_adminToolInfo.myRights.EditRegistrants',0),
				"DT_RowId": "row_#local.qryRegistrants.rsvpID#"
			}>

			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryRegistrants.totalCount),
			"recordsFiltered": val(local.qryRegistrants.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getEventsOnSite" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.objEventAdmin = CreateObject("component","eventAdmin");
			local.objEvent = CreateObject("component","event");

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.strEventsBaseLinks = {};
			local.editLink = createObject("component","model.admin.admin").buildLinkToTool(toolType='EventAdmin',mca_ta='editEvent');
			local.mainhostname = arguments.event.getValue('mc_siteInfo.mainhostname');
			local.scheme = arguments.event.getValue('mc_siteInfo.scheme');

			if(val(arguments.event.getValue('peID',0))) local.gridMode = 'subEventsGrid';
			else local.gridMode = 'eventsGrid';
		
			if (local.gridMode EQ 'eventsGrid')
				local.objEvent.saveEventsFilter(arguments.event);
			
			local.qryEvents = local.objEvent.getEventsOnSiteFromFilters(event=arguments.event, mode=local.gridMode);
			local.showSWFeaturedCol = val(local.qryEvents.showSWFeaturedCol);
		</cfscript>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.strEventCategories" returntype="struct" columnkey="categoryID">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select distinct evCat.categoryID, evCat.category
				from dbo.fn_IntListToTable(<cfqueryparam cfsqltype="cf_sql_varchar" value="0#ValueList(local.qryEvents.categoryIDList)#">,',') as tmpCat
				INNER JOIN dbo.ev_categories as evCat on evCat.categoryID = tmpCat.listItem;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryEvents">
			<cfif NOT StructKeyExists(local.strEventsBaseLinks,local.qryEvents.applicationInstanceId)>
				<cfset structInsert(local.strEventsBaseLinks, local.qryEvents.applicationInstanceId, local.objEventAdmin.getAppBaseLink(applicationInstanceID=local.qryEvents.applicationInstanceId))>
			</cfif>
			<cfset local.eventLinkForClipboard = ToBase64("#local.scheme#://#local.mainhostname#/?#local.strEventsBaseLinks[local.qryEvents.applicationInstanceId]#&evAction=showDetail&eid=#local.qryEvents.eventID#")>
			<cfset local.eventRegistrationLink = ToBase64("#local.scheme#://#local.mainhostname#/?#local.strEventsBaseLinks[local.qryEvents.applicationInstanceId]#&evAction=regV2&eid=#local.qryEvents.eventID#")>

			<cfset local.xmlRights = xmlParse(local.qryEvents.rightsXML)>
			<cfset local.XMLCalRights = xmlParse(local.qryEvents.calRightsXML)>
			<cfset local.evTitle = encodeForHTML(local.qryEvents.eventTitle)>

			<cfset local.tmpStr = {
				"eventid": local.qryEvents.eventID,
				"status": local.qryEvents.status,
				"evtitle": local.evTitle,
				"starttime": dateFormat(local.qryEvents.startTime,"m/d/yyyy"),
				"regtype": local.qryEvents.regType,
				"eventsubtitle": local.qryEvents.eventSubTitle,
				"calendarnamehtmlencoded": encodeForHTML(local.qryEvents.calendarName),
				"categorylist": "",
				"forceselect": val(arguments.event.getValue('peID',0)) ? YesNoFormat(local.qryEvents.forceSelect) : "No",
				"eventsrid": local.qryEvents.eventSRID,
				"canadd": XMLSearch(local.XMLCalRights,"string(//right[@functionName='AddEvent']/@allowed)") is 1,
				"canedit": XMLSearch(local.xmlRights,"string(//right[@functionName='EditEvent']/@allowed)") is 1,
				"candelete":  XMLSearch(local.xmlRights,"string(//right[@functionName='DeleteEvent']/@allowed)") is 1,
				"numsubevents": local.qryEvents.numSubEvents,
				"issubevent": local.qryEvents.isSubEvent,
				"parenteventid": val(arguments.event.getValue('peID',0)),
				"editlink": "#local.editLink#&eid=#local.qryEvents.eventID#&bc=e",
				"eventlinkforclipboard": local.eventLinkForClipboard,
				"eventregistrationlink": local.eventRegistrationLink,
				"isswevent": local.qryEvents.isSWEvent,
				"isswfeatured": local.qryEvents.isSWFeatured,
				"isrecurringevent": arguments.event.getValue('mc_siteInfo.sf_recurringEvents') EQ 1 AND val(local.qryEvents.recurringSeriesID) GT 0,
				"editsubeventperms":  XMLSearch(local.xmlRights,"string(//right[@functionName='EditEvent']/@allowed)") is 1 and val(arguments.event.getValue('peID',0)) and local.qryEvents.isSubEvent,
				"viewregistrantsperms": local.qryEvents.hasRegistration is 1 and XMLSearch(local.xmlRights,"string(//right[@functionName='ViewRegistrants']/@allowed)") is 1,
				"viewregistrantslink": "#local.editLink#&eid=#local.qryEvents.eventID#&bc=e&tab=registrants",
				"numregistrants": local.qryEvents.numRegistrants,
				"totalfeesdisplay": "$#numberformat(val(local.qryEvents.totalFees),"9,999")#",
				"showswfeaturedcol":local.qryEvents.showSWFeaturedCol,
				"DT_RowId": "row_#local.qryEvents.eventID#"
			}>

			<cfset local.tmpStr.categorylist = "">
			<cfloop list="#local.qryEvents.categoryIDList#" index="local.catID">
				<cfset local.tmpStr.categorylist &= "#htmlEditFormat(local.strEventCategories[local.catID].category)#<br/>">
			</cfloop>

			<cfif not local.qryEvents.isSubEvent>
				<cfset local.tmpStr.editlink &= "&peid=#arguments.event.getValue('peID',0)#">
			</cfif>

			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryEvents.totalCount),
			"recordsFiltered": val(local.qryEvents.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getEventsOnCalendar" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.objEventAdmin = CreateObject("component","eventAdmin");
			local.objEvent = CreateObject("component","event");
			local.strEventsBaseLinks = {};
			local.editLink = createObject("component","model.admin.admin").buildLinkToTool(toolType='EventAdmin',mca_ta='editEvent');
			local.mainhostname = arguments.event.getValue('mc_siteInfo.mainhostname');
			local.scheme = arguments.event.getValue('mc_siteInfo.scheme');

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			
			CreateObject("component","model.admin.events.event").saveCalendarEventsFilter(arguments.event);
			local.qryEvents = local.objEvent.getEventsOnSiteFromFilters(event=arguments.event, mode='eventsGrid');
			local.showSWFeaturedCol = val(local.qryEvents.showSWFeaturedCol);
		</cfscript>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.strEventCategories" returntype="struct" columnkey="categoryID">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select distinct evCat.categoryID, evCat.category
				from dbo.fn_IntListToTable(<cfqueryparam cfsqltype="cf_sql_varchar" value="0#ValueList(local.qryEvents.categoryIDList)#">,',') as tmpCat
				INNER JOIN dbo.ev_categories as evCat on evCat.categoryID = tmpCat.listItem;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryEvents">
			<cfif NOT StructKeyExists(local.strEventsBaseLinks,local.qryEvents.applicationInstanceId)>
				<cfset structInsert(local.strEventsBaseLinks, local.qryEvents.applicationInstanceId, local.objEventAdmin.getAppBaseLink(applicationInstanceID=local.qryEvents.applicationInstanceId))>
			</cfif>

			<cfset local.eventLinkForClipboard = ToBase64("#local.scheme#://#local.mainhostname#/?#local.strEventsBaseLinks[local.qryEvents.applicationInstanceId]#&evAction=showDetail&eid=#local.qryEvents.eventID#")>
			<cfset local.eventRegistrationLink = ToBase64("#local.scheme#://#local.mainhostname#/?#local.strEventsBaseLinks[local.qryEvents.applicationInstanceId]#&evAction=reg&eid=#local.qryEvents.eventID#")>

			<cfset local.xmlRights = xmlParse(local.qryEvents.rightsXML)>
			<cfset local.XMLCalRights = xmlParse(local.qryEvents.calRightsXML)>
			<cfset local.evTitle = encodeForHTML(local.qryEvents.eventTitle)>

			<cfset local.tmpStr = {
				"eventid": local.qryEvents.eventID,
				"status": local.qryEvents.status,
				"evtitle": local.evTitle,
				"starttime": dateFormat(local.qryEvents.startTime,"m/d/yyyy"),
				"regtype": local.qryEvents.regType,
				"eventsubtitle": local.qryEvents.eventSubTitle,
				"calendarnamehtmlencoded": encodeForHTML(local.qryEvents.calendarName),
				"categorylist": "",
				"forceselect": val(arguments.event.getValue('peID',0)) ? YesNoFormat(local.qryEvents.forceSelect) : "No",
				"eventsrid": local.qryEvents.eventSRID,
				"canadd": XMLSearch(local.XMLCalRights,"string(//right[@functionName='AddEvent']/@allowed)") is 1,
				"canedit": XMLSearch(local.xmlRights,"string(//right[@functionName='EditEvent']/@allowed)") is 1,
				"candelete":  XMLSearch(local.xmlRights,"string(//right[@functionName='DeleteEvent']/@allowed)") is 1,
				"numsubevents": local.qryEvents.numSubEvents,
				"issubevent": local.qryEvents.isSubEvent,
				"parenteventid": val(arguments.event.getValue('peID',0)),
				"editlink": "#local.editLink#&eid=#local.qryEvents.eventID#&bc=e",
				"eventlinkforclipboard": local.eventLinkForClipboard,
				"eventregistrationlink": local.eventRegistrationLink,
				"isswevent": local.qryEvents.isSWEvent,
				"isswfeatured": local.qryEvents.isSWFeatured,
				"isrecurringevent": arguments.event.getValue('mc_siteInfo.sf_recurringEvents') EQ 1 AND val(local.qryEvents.recurringSeriesID) GT 0,
				"editsubeventperms":  XMLSearch(local.xmlRights,"string(//right[@functionName='EditEvent']/@allowed)") is 1 and val(arguments.event.getValue('peID',0)) and local.qryEvents.isSubEvent,
				"viewregistrantsperms": local.qryEvents.hasRegistration is 1 and XMLSearch(local.xmlRights,"string(//right[@functionName='ViewRegistrants']/@allowed)") is 1,
				"viewregistrantslink": "#local.editLink#&eid=#local.qryEvents.eventID#&bc=e&tab=registrants",
				"numregistrants": local.qryEvents.numRegistrants,
				"totalfeesdisplay": "$#numberformat(val(local.qryEvents.totalFees),"9,999")#",
				"showswfeaturedcol":local.qryEvents.showSWFeaturedCol,
				"DT_RowId": "row_#local.qryEvents.eventID#"
			}>

			<cfset local.tmpStr.categorylist = "">
			<cfloop list="#local.qryEvents.categoryIDList#" index="local.catID">
				<cfset local.tmpStr.categorylist &= "#htmlEditFormat(local.strEventCategories[local.catID].category)#<br/>">
			</cfloop>

			<cfif local.showSWFeaturedCol>
				<cfset StructAppend(local.tmpStr,{"isSWFeatured": local.qryEvents.isSWFeatured,"isSWEvent": local.qryEvents.isSWEvent})>
			</cfif>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryEvents.totalCount),
			"recordsFiltered": val(local.qryEvents.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getCategories" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"cat.category")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCategories" result="local.qryCategoriesResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tblCategories') IS NOT NULL
					DROP TABLE ##tblCategories;
				CREATE TABLE ##tblCategories (categoryID int, category varchar(50), calColor varchar(70), visibility char(1), eventsInCategory int, deletedEventsInCategory int, row int);

				DECLARE @posStart int, @posStartAndCount int, @totalCount int, @searchValue varchar(max);
				SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
				SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;
				<cfif len(local.searchValue)>
					SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
				</cfif>

				INSERT INTO ##tblCategories
				SELECT cat.categoryID, cat.category, cat.calColor, cat.visibility, 
					sum(case when e.status <> 'D' then 1 else 0 end) as eventsInCategory,
					sum(case when e.status = 'D' then 1 else 0 end) as deletedEventsInCategory,
					ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)# #arguments.event.getValue('orderDir')#) as row
				FROM dbo.ev_categories as cat
				LEFT OUTER JOIN dbo.cache_calendarEvents as ecActive
					INNER JOIN dbo.ev_events as e on e.eventid = ecActive.eventid and e.siteid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
					on ecActive.categoryID = cat.categoryID and ecActive.calendarID = cat.calendarID
				WHERE cat.calendarID = <cfqueryparam value="#arguments.event.getValue('cid')#" cfsqltype="CF_SQL_INTEGER">
				<cfif len(local.searchValue)>
					AND cat.category LIKE @searchValue
				</cfif>
				GROUP BY cat.categoryID, cat.category, cat.calColor, cat.visibility;

				SELECT @totalCount = @@ROWCOUNT;

				SELECT categoryID, category, calColor, visibility, eventsInCategory, deletedEventsInCategory, row, @totalCount as totalCount
				FROM ##tblCategories
				WHERE row > @posStart AND row <= @posStartAndCount
				ORDER BY row;

				IF OBJECT_ID('tempdb..##tblCategories') IS NOT NULL
					DROP TABLE ##tblCategories;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryCategories">
			<cfset local.tmpStr = {
				"categoryid": local.qryCategories.categoryID,
				"calendarid": arguments.event.getValue('cid'),
				"category":  encodeForHTML(local.qryCategories.category) ,
				"noofevents": local.qryCategories.eventsInCategory + local.qryCategories.deletedEventsInCategory,
				"eventsincategory": local.qryCategories.eventsInCategory,
				"deletedeventsincategory": local.qryCategories.deletedEventsInCategory,
				"calcolor": local.qryCategories.calColor,
				"visibility": local.qryCategories.visibility,
				"DT_RowId": "row_#local.qryCategories.categoryID#"
			}>

			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryCategories.totalCount),
			"recordsFiltered": val(local.qryCategories.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getCurrentEventsOnSite" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
		</cfscript>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryEvents">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int, @memberID int, @loggedMemberID int, @posStart int, @posStartAndCount int, 
				@totalCount int, @searchValue varchar(300), @nowDate datetime;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
			SET @memberID = <cfqueryparam value="#arguments.event.getValue('mid',0)#" cfsqltype="CF_SQL_INTEGER">;
			SET @loggedMemberID = <cfqueryparam value="#session.cfcuser.memberdata.memberid#" cfsqltype="CF_SQL_INTEGER">;
			SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
			SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;
			SET @nowDate = getdate();
			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>

			-- get events on site
			IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
				DROP TABLE ##tmpEventsOnSite;
			CREATE TABLE ##tmpEventsOnSite (calendarID int, eventID int, [status] char(1), isPastEvent bit,
				startTime datetime, endTime datetime, timeZoneID int, timeZoneCode varchar(25), timeZoneAbbr varchar(4),
				displayStartTime datetime, displayEndTime datetime, displayTimeZoneID int, displayTimeZoneCode varchar(25),
				displayTimeZoneAbbr varchar(4), siteResourceID int, isAllDayEvent bit, altRegistrationURL varchar(300),
				eventTitle varchar(200), eventSubTitle varchar(200), locationTitle varchar(200), 
				categoryIDList varchar(max));
			EXEC dbo.ev_getEventsOnSite @siteID=@siteID, @startDate=@nowDate, @endDate=null, @categoryIDList='';

			-- filter events
			IF OBJECT_ID('tempdb..##tmpEvents') IS NOT NULL
				DROP TABLE ##tmpEvents;
			SELECT e.eventid, tmp.eventTitle, tmp.eventSubTitle, tmp.calendarID, tmp.startTime, tmp.categoryIDList, ai.siteResourceID
			INTO ##tmpEvents
			FROM dbo.ev_events as e
			INNER JOIN ##tmpEventsOnSite as tmp on tmp.eventID = e.eventID
			INNER JOIN dbo.ev_registration as er on er.eventID = e.eventID and er.siteID = @siteID and er.status = 'A'
				AND er.registrationID is not null
			INNER JOIN dbo.ev_registrationTypes as ert on er.registrationTypeID = ert.registrationTypeID
				AND ert.registrationType = 'Reg'
			INNER JOIN dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID 
			INNER JOIN dbo.ev_calendars as cal on cal.siteID = @siteID and cal.calendarID = ce.sourceCalendarID
			INNER JOIN dbo.cms_applicationInstances as ai on ai.siteID = @siteID and ai.applicationInstanceID = cal.applicationInstanceID			
			WHERE e.siteID = @siteID 
			AND e.status = 'A'
			<cfif len(local.searchValue)>
				AND (tmp.eventTitle LIKE @searchValue OR tmp.eventSubTitle LIKE @searchValue)
			</cfif>;

			select @totalCount = count(*) from ##tmpEvents;

			SELECT eventid, eventTitle, eventSubTitle, calendarID, startTime, categoryIDList, @totalCount as totalCount,
				dbo.fn_ev_isUserRegisteredForEvent(@memberID,eventid) as isRegistered,
				dbo.fn_cache_perms_getResourceRightsXML(siteResourceID,@loggedMemberID,@siteID) as rightsXML 
			FROM (
				select *, ROW_NUMBER() OVER (ORDER BY startTime, eventTitle) as row
				from ##tmpEvents
			) AS tmp
			WHERE row > @posStart AND row <= @posStartAndCount
			ORDER by row;

			IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
				DROP TABLE ##tmpEventsOnSite;
			IF OBJECT_ID('tempdb..##tmpEvents') IS NOT NULL
				DROP TABLE ##tmpEvents;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.strEventCategories" returntype="struct" columnkey="categoryID">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select distinct evCat.categoryID, evCat.category
			from dbo.fn_IntListToTable(<cfqueryparam cfsqltype="cf_sql_varchar" value="0#ValueList(local.qryEvents.categoryIDList)#">,',') as tmpCat
			INNER JOIN dbo.ev_categories as evCat on evCat.categoryID = tmpCat.listItem;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryEvents">
			<cfset local.xmlRights = xmlParse(local.qryEvents.rightsXML)>
			<cfset local.IsEditRegistrationAllowed = XMLSearch(local.xmlRights,"string(//right[@functionName='EditRegistrantsByDefault']/@allowed)") is 1>

			<cfsavecontent variable="local.categoryNameList">
				<cfoutput><cfloop list="#local.qryEvents.categoryIDList#" index="local.catID">#encodeForHTML(local.strEventCategories[local.catID].category)#<br/></cfloop></cfoutput>
			</cfsavecontent>

			<cfset arrayAppend(local.data, {
				"calendarid": local.qryEvents.calendarid,
				"eventid": local.qryEvents.eventID,
				"eventtitle": encodeForHTML(local.qryEvents.eventTitle),
				"eventsubtitle": encodeForHTML(local.qryEvents.eventSubTitle),
				"starttime": dateFormat(local.qryEvents.startTime,"m/d/yyyy"),
				"categorynamelist": local.categoryNameList,
				"isregistered": local.qryEvents.isRegistered,
				"iseditregistrationallowed": local.IsEditRegistrationAllowed,
				"DT_RowId": "evRow_#local.qryEvents.eventID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryEvents.totalCount),
			"recordsFiltered": val(local.qryEvents.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getLinkEventsOnSite" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfset local.taskType = arguments.event.getValue('taskType','link')>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryEvents">			
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int, @posStart int, @posStartAndCount int, @totalCount int;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
			SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
			SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;

			-- get events on site
			IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
				DROP TABLE ##tmpEventsOnSite;
			CREATE TABLE ##tmpEventsOnSite (calendarID int, eventID int, [status] char(1), isPastEvent bit,
				startTime datetime, endTime datetime, timeZoneID int, timeZoneCode varchar(25), timeZoneAbbr varchar(4),
				displayStartTime datetime, displayEndTime datetime, displayTimeZoneID int, displayTimeZoneCode varchar(25),
				displayTimeZoneAbbr varchar(4), siteResourceID int, isAllDayEvent bit, 
				altRegistrationURL varchar(300), eventTitle varchar(200), eventSubTitle varchar(200), locationTitle varchar(200), 
				categoryIDList varchar(max));
			EXEC dbo.ev_getEventsOnSite @siteID=@siteID, 
				@startDate='#dateFormat(arguments.event.getValue('fDateFrom',DateAdd("yyyy",-5,now())),"m/d/yyyy")#', 
				@endDate='#dateFormat(arguments.event.getValue('fDateTo',DateAdd("yyyy",2,now())),"mm/dd/yyyy")# 23:59:59.997', 
				@categoryIDList='<cfif arguments.event.getValue('fCategory',0) gt 0>#int(val(arguments.event.getValue('fCategory')))#</cfif>';

			-- filter events
			IF OBJECT_ID('tempdb..##tmpEvents') IS NOT NULL
				DROP TABLE ##tmpEvents;

			SELECT e.eventid, tmp.status, tmp.eventTitle, tmp.eventSubTitle, tmp.startTime, tmp.categoryIDList
			INTO ##tmpEvents
			FROM dbo.ev_events as e
			INNER JOIN ##tmpEventsOnSite as tmp on tmp.eventID = e.eventID
			<cfif arguments.event.getValue('fRegType',0) gt 0>
				INNER JOIN dbo.ev_registration as er on er.siteID = @siteID and er.eventID = e.eventID and er.status = 'A'
				INNER JOIN dbo.ev_registrationTypes as ert on er.registrationTypeID = ert.registrationTypeID
					and ert.registrationTypeID = <cfqueryparam value="#arguments.event.getValue('fRegType')#" cfsqltype="CF_SQL_INTEGER">
			</cfif>							
			WHERE e.siteID = @siteID 
			and tmp.calendarID = <cfqueryparam value="#arguments.event.getValue('cid')#" cfsqltype="CF_SQL_INTEGER">
			and tmp.status in ('A','I')
			and e.eventID <> <cfqueryparam value="#arguments.event.getValue('eid')#" cfsqltype="CF_SQL_INTEGER">
			and e.altRegistrationURL is null
			<cfif len(arguments.event.getTrimValue('fKeyword',''))>
				and tmp.eventTitle like <cfqueryparam value="%#replace(arguments.event.getValue('fKeyword'),'_','\_','ALL')#%" cfsqltype="cf_sql_varchar"> ESCAPE('\')
			</cfif>															
			<cfif local.taskType eq "link">
				and NOT EXISTS (select parentEventID from dbo.ev_subevents where parentEventID = e.eventID)
				and NOT EXISTS (select se.eventID from dbo.ev_subevents se where se.eventID = e.eventID and se.parentEventID = <cfqueryparam value="#arguments.event.getValue('eid')#" cfsqltype="CF_SQL_INTEGER">)
				and NOT EXISTS (
					select er.registrationID 
					from dbo.ev_registration as er
					inner join dbo.ev_registrationTypes as ert on er.registrationTypeID = ert.registrationTypeID
					where er.siteID = @siteID 
					and er.status = 'A' 
					and ert.registrationType = 'RSVP'
					and er.eventID = e.eventID)						
			</cfif>
			<cfif arguments.event.getValue('fEventType','') eq 'main'>
				and NOT EXISTS (select se.eventID from dbo.ev_subevents se where se.eventID = e.eventID)		
			</cfif>		
			<cfif arguments.event.getValue('fEventType','') eq 'sub'>
				and EXISTS (select se.eventID from dbo.ev_subevents se where se.eventID = e.eventID)			
			</cfif>
			<cfif len(arguments.event.getTrimValue('fReportCode',''))>
				and e.ReportCode = <cfqueryparam value="#arguments.event.getTrimValue('fReportCode')#" cfsqltype="cf_sql_varchar">
			</cfif>;

			select @totalCount = count(*) from ##tmpEvents;

			SELECT tmp.eventid, tmp.status, tmp.eventTitle, tmp.eventSubTitle, tmp.startTime, tmp.categoryIDList, tmp.row, @totalCount as totalCount
			FROM (
				select *, ROW_NUMBER() OVER (order by startTime desc, eventTitle) as row
				from ##tmpEvents
			) AS tmp 
			WHERE row > @posStart AND row <= @posStartAndCount
			ORDER by tmp.row;

			IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
				DROP TABLE ##tmpEventsOnSite;
			IF OBJECT_ID('tempdb..##tmpEvents') IS NOT NULL
				DROP TABLE ##tmpEvents;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.strEventCategories" returntype="struct" columnkey="categoryID">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select distinct evCat.categoryID, evCat.category
			from dbo.fn_IntListToTable(<cfqueryparam cfsqltype="cf_sql_varchar" value="0#ValueList(local.qryEvents.categoryIDList)#">,',') as tmpCat
			INNER JOIN dbo.ev_categories as evCat on evCat.categoryID = tmpCat.listItem;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryEvents">
			<cfsavecontent variable="local.categoryNameList">
				<cfoutput><cfloop list="#local.qryEvents.categoryIDList#" index="local.catID">#encodeForHTML(local.strEventCategories[local.catID].category)#<br/></cfloop></cfoutput>
			</cfsavecontent>

			<cfset arrayAppend(local.data, {
				"eventid": local.qryEvents.eventID,
				"eventtitle": encodeForHTML(local.qryEvents.eventTitle),
				"eventsubtitle": encodeForHTML(local.qryEvents.eventSubTitle),
				"status": local.qryEvents.status,
				"starttime": dateFormat(local.qryEvents.startTime,"m/d/yyyy"),
				"categorynamelist": local.categoryNameList,
				"DT_RowId": "eventRow_#local.qryEvents.eventID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryEvents.totalCount),
			"recordsFiltered": val(local.qryEvents.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSelectedEventsOfReport" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfif arguments.event.getValue('dtMode','') EQ 'selectEvents'>
			<cfset local.eventIDList = arguments.event.getValue('eidlist','')>
		<cfelse>
			<cfset local.otherXML = createObject("component","model.admin.common.modules.eventWidget.eventWidget").getReportOtherXML(rptid=arguments.event.getValue('_evrptid',0), 
									csrid=arguments.event.getValue('_evcsrid',0))>
			<cfset local.eventIDList = XMLSearch(local.otherXML,"string(/report/extra/eidlist/text())")>
		</cfif>
		
		<cfquery name="local.qryEvents" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int, @RTID int;
				SET @siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteID')#" cfsqltype="CF_SQL_INTEGER">;
				SELECT @RTID = dbo.fn_getResourceTypeID('Community');

				IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
					DROP TABLE ##tmpEventsOnSite;
				CREATE TABLE ##tmpEventsOnSite (calendarID int, eventID int, [status] char(1), isPastEvent bit,
					startTime datetime, endTime datetime, timeZoneID int, timeZoneCode varchar(25), timeZoneAbbr varchar(4),
					displayStartTime datetime, displayEndTime datetime, displayTimeZoneID int, displayTimeZoneCode varchar(25),
					displayTimeZoneAbbr varchar(4), siteResourceID int, isAllDayEvent bit, altRegistrationURL varchar(300), 
					eventTitle varchar(200), eventSubTitle varchar(200), locationTitle varchar(200), 
					categoryIDList varchar(max));
				EXEC dbo.ev_getEventsOnSite @siteID=@siteID, @startDate=null, @endDate=null, @categoryIDList='';

				SELECT tmp.eventid, tmp.status, tmp.eventTitle, tmp.eventSubTitle, tmp.startTime,
					CASE WHEN e.altRegistrationURL IS NULL AND ert.registrationType  = '' THEN ''
						WHEN e.altRegistrationURL IS NOT NULL THEN 'Alt'
					ELSE ert.registrationType END as regType,
					ai.applicationInstanceName + 
					CASE WHEN communityInstances.applicationInstanceName is not null THEN ' (' + communityInstances.applicationInstanceName + ')'
					ELSE '' END as calendarName, tmp.categoryIDList
				FROM ##tmpEventsOnSite as tmp
				INNER JOIN dbo.fn_intListToTable(<cfqueryparam value="0#local.eventIDList#" cfsqltype="CF_SQL_VARCHAR">,',') dg on dg.listitem = tmp.eventID
				INNER JOIN dbo.ev_events AS e ON e.siteID = @siteID and e.eventID = tmp.eventID
				INNER JOIN dbo.ev_calendars as c on c.siteID = @siteID and c.calendarid = tmp.calendarID
				INNER JOIN dbo.cms_applicationInstances as ai on c.applicationInstanceID = ai.applicationInstanceID and ai.siteID = @siteID
				INNER JOIN dbo.cms_siteResources as sr on sr.siteID = @siteID and ai.siteResourceID = sr.siteResourceID
				INNER JOIN dbo.cms_siteResources as parentResource on parentResource.siteID = @siteID and parentResource.siteResourceID = sr.parentSiteResourceID
				LEFT OUTER JOIN dbo.cms_siteResources as grandparentResource
					INNER JOIN dbo.cms_applicationInstances as CommunityInstances on CommunityInstances.siteID = @siteID 
						and communityInstances.siteResourceID = grandParentResource.siteResourceID
						ON grandparentResource.siteID = @siteID
						AND grandparentResource.siteResourceID = parentResource.parentSiteResourceID
						AND grandparentResource.resourceTypeID = @RTID
				LEFT OUTER JOIN dbo.ev_registration as er
					INNER JOIN dbo.ev_registrationTypes as ert on er.registrationTypeID = ert.registrationTypeID
					on er.siteID = @siteID 
					and er.eventID = e.eventID 
					and er.status = 'A'
				order by tmp.startTime;

				IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
					DROP TABLE ##tmpEventsOnSite;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.strEventCategories" returntype="struct" columnkey="categoryID">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select distinct evCat.categoryID, evCat.category
			from dbo.fn_IntListToTable(<cfqueryparam cfsqltype="cf_sql_varchar" value="0#ValueList(local.qryEvents.categoryIDList)#">,',') as tmpCat
			INNER JOIN dbo.ev_categories as evCat on evCat.categoryID = tmpCat.listItem;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryEvents">
			<cfsavecontent variable="local.categoryNameList">
				<cfoutput><cfloop list="#local.qryEvents.categoryIDList#" index="local.catID">#encodeForHTML(local.strEventCategories[local.catID].category)#<br/></cfloop></cfoutput>
			</cfsavecontent>

			<cfset arrayAppend(local.data, {
				"eventid": local.qryEvents.eventID,
				"status": local.qryEvents.status,
				"eventtitle": encodeForHTML(local.qryEvents.eventTitle),
				"eventsubtitle": encodeForHTML(local.qryEvents.eventSubTitle),
				"starttime": dateFormat(local.qryEvents.startTime,"m/d/yyyy"),
				"regtype": local.qryEvents.regType,
				"calendarname": encodeForHTML(local.qryEvents.calendarName),
				"categorynamelist": local.categoryNameList,
				"DT_RowId": "selEvRow_#local.qryEvents.eventID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryEvents.recordCount,
			"recordsFiltered": local.qryEvents.recordCount,
			"data": local.data
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getEventsOnSiteForReports" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.eventIDList = arguments.event.getValue('eidlist','');
			local.qryEvents = createObject("component","model.admin.common.modules.eventWidget.eventWidget").getEventsOnSiteFromFilters(
				siteID=arguments.event.getValue('mc_siteinfo.siteID'), fRegType=arguments.event.getValue('fRegType',0), 
				fCalendar=arguments.event.getTrimValue('fCalendar',''), fDateFrom=arguments.event.getTrimValue('fDateFrom',''), 
				fDateTo=arguments.event.getTrimValue('fDateTo',''), fCategory=arguments.event.getValue('fCategory',''), 
				fReportCode=arguments.event.getTrimValue('fReportCode',''), fEventType=arguments.event.getValue('fEventType',''), 
				fKeyword=arguments.event.getTrimValue('fKeyword',''), direct=arguments.event.getValue('orderDir'), 
				orderby=arguments.event.getValue('orderby'), posStart=arguments.event.getValue('posStart'), 
				count=arguments.event.getValue('count'), eventIDList=local.eventIDList);
		</cfscript>

		<cfset local.data = []>
		<cfloop query="local.qryEvents">
			<cfset arrayAppend(local.data, {
				"eventid": local.qryEvents.eventID,
				"status": local.qryEvents.status,
				"eventtitle": encodeForHTML(local.qryEvents.eventTitle),
				"eventsubtitle": encodeForHTML(local.qryEvents.eventSubTitle),
				"starttime": dateFormat(local.qryEvents.startTime,"m/d/yyyy"),
				"regtype": local.qryEvents.regType,
				"calendarname": encodeForHTML(local.qryEvents.calendarName),
				"categorynamelist": replace(encodeForHTML(local.qryEvents.categoryNameList),"|","<br/>","ALL"),
				"DT_RowId": "selEvForRptRow_#local.qryEvents.eventID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryEvents.totalCount),
			"recordsFiltered": val(local.qryEvents.totalCount),
			"data": local.data
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getRates" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.arrRates = []>
		<cfset local.reorderData = DeserializeJSON(arguments.event.getValue('reorderData',''))>
		<cfif isArray(local.reorderData) and arrayLen(local.reorderData) GT 1>
			<cfloop array="#local.reorderData#" index="local.thisData">
				<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryUpdateData">
					UPDATE dbo.ev_rates
					SET rateOrder = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.newOrder#">
					WHERE rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.id#">
					and ISNULL(rateGroupingID,0) = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.rateGroupingID#">
					and registrationID = #arguments.event.getValue('registrationID',0)# ;
				</cfquery>
			</cfloop>
		</cfif>
		<cfset arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))))>
		<cfset local.qryRates = this.objAdminEvent.getRatesByRegistrationID(arguments.event.getValue('registrationID'))>

		<cfquery name="local.qryGroups" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			SELECT groupID, groupPathExpanded AS thePathexpanded
			FROM dbo.ams_groups 
			WHERE orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
			AND status <> 'D'
			AND hideOnGroupLists = 0
			ORDER BY groupPathSortOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="local.totalRateGroupingsNotZero" dbtype="query">
			SELECT DISTINCT rateGroupingID
			FROM [local].qryRates
			WHERE rateGroupingID > 0
		</cfquery>
		
		<cfset local.RGIndex = 0>

		<cfoutput query="local.qryRates" group="rateGroupingID">
			<cfset local.rateGroupRowID = "rg#val(local.qryRates.rateGroupingID)#">

			<cfif local.totalRateGroupingsNotZero.recordcount>
				<cfif local.qryRates.rateGroupingID gt 0>
					<cfset local.RGIndex++>
				</cfif>

				<cfset local.objRateGrouping = {
					"level": 1,
					"rowType": "rateGroup",
					"displayName": local.qryRates.rateGrouping,
					"displayNameEncoded": encodeForJavascript(local.qryRates.rateGrouping),
					"rateGroupingID": val(local.qryRates.rateGroupingID),
					"rateID": 0,
					"groupID": 0,
					"hasChildren": 0,
					"canMoveUp": local.RGIndex gt 1,
					"canMoveDown": local.RGIndex lt local.totalRateGroupingsNotZero.recordCount,
					"parentRowID": "gridRoot",
					"DT_RowId": local.rateGroupRowID,
					"DT_RowClass": "child-of-gridRoot"
				}>
				<cfset local.objRateGrouping["DT_RowClass"] &= val(local.qryRates.rateGroupingID) eq 0 ? " default-nogrouping" : "">
				<cfset local.arrRates.append(local.objRateGrouping)>
			</cfif>

			<cfset local.rateIndex = 0>
			<cfset local.rateParentRowID = local.totalRateGroupingsNotZero.recordcount ? local.rateGroupRowID : "gridRoot">
				
			<cfoutput group="rateID">
				<cfif val(local.qryRates.rateID) gt 0>
					<cfset local.rateIndex++>

					<cfquery name="local.totalRateCount" dbtype="query">
						SELECT DISTINCT rateID
						FROM [local].qryRates
						WHERE rateGroupingID = #local.qryRates.rateGroupingID#
					</cfquery>

					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryChildIDs">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						SELECT r.siteResourceID
						FROM dbo.ev_rates r
						WHERE r.parentRateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryRates.rateID#">;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					<cfset local.dupeIDs = valueList(local.qryChildIDs.siteResourceID)>

					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRangeNames">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						SELECT ep.rangeName, ep.startDate, ep.endDate
						FROM dbo.ev_priceSchedule ep
						INNER JOIN dbo.ev_ratesAvailable ra on ep.scheduleID = ra.scheduleID
						WHERE ra.rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryRates.rateID#">
						ORDER BY ep.startDate;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>

					<cfset local.rangeNamesList = replace(valueList(local.qryRangeNames.rangeName,'|'),'|', ', ',"all")>
					<cfset local.rangeNamesListHover = "">
					<cfloop query="local.qryRangeNames">
						<cfset local.rangeNamesListHover = listAppend(local.rangeNamesListHover,"#local.qryRangeNames.rangeName# (#dateformat(local.qryRangeNames.startDate,"m/d/yyyy")# - #dateformat(local.qryRangeNames.endDate,"m/d/yyyy")#)","|")>
					</cfloop>
					<cfset local.rangeNamesListHover = replace(local.rangeNamesListHover,'|', ', ',"all")>
					
					<cfset local.rateRowID = "rg#local.qryRates.rateGroupingID#-#local.qryRates.rateID#">

					<cfset local.objRate = {
						"level": local.totalRateGroupingsNotZero.recordcount ? 2 : 1,
						"rowType": "rate",
						"displayName": local.qryRates.rateName & (local.qryRates.isHidden is 1 ? " (hidden rate)" : ""),
						"displayNameEncoded": encodeForJavascript(local.qryRates.rateName),
						"rateGroupingID": val(local.qryRates.rateGroupingID),
						"rateID": val(local.qryRates.rateID),
						"rate": local.qryRates.rate,
						"rateFormatted": dollarFormat(local.qryRates.rate),
						"rateSRID": local.qryRates.siteResourceID,
						"childRateSRIDs": local.dupeIDs,
						"schedules": local.rangeNamesList,
						"schedulesWithDates": local.rangeNamesListHover,
						"groupID": 0,
						"hasChildren": 0,
						"canMoveUp": local.rateIndex gt 1,
						"canMoveDown": local.rateIndex lt local.totalRateCount.recordCount,
						"parentRowID": local.rateParentRowID,
						"DT_RowId": local.rateRowID,
						"DT_RowClass": "child-of-#local.rateParentRowID#"
					}>
					<cfset local.arrRates.append(local.objRate)>

					<cfset local.groupIndex = 0>
					<cfoutput>
						<cfif local.qryRates.groupID gt 0>
							<cfset local.groupIndex++>

							<cfquery dbtype="query" name="local.qryGroupFullPath">
								SELECT thePathExpanded
								FROM [local].qryGroups
								WHERE groupID = #local.qryRates.groupID#
							</cfquery>

							<cfif len(local.qryGroupFullPath.thePathExpanded)>
								<cfset local.displayGroupAs = local.qryGroupFullPath.thePathExpanded>
							<cfelse>
								<cfset local.displayGroupAs = local.qryRates.groupName>
							</cfif>

							<cfset local.arrRates.append({
								"level": local.totalRateGroupingsNotZero.recordcount ? 3 : 2,
								"rowType": "group",
								"displayName": (local.qryRates.include eq 0 ? "Denied: " : "") & local.displayGroupAs,
								"displayNameEncoded": encodeForJavascript(local.displayGroupAs),
								"rateGroupingID": val(local.qryRates.rateGroupingID),
								"rateID": val(local.qryRates.rateID),
								"groupID": local.qryRates.groupID,
								"include": local.qryRates.include,
								"hasChildren": 0,
								"canMoveUp": 0,
								"canMoveDown": 0,
								"parentRowID": "#local.rateRowID#",
								"DT_RowId": "rg#local.qryRates.rateGroupingID#-#local.qryRates.rateID#-#local.qryRates.groupid#-#local.qryRates.include#",
								"DT_RowClass": "child-of-#local.rateRowID#"
							})>
						</cfif>
					</cfoutput>

					<cfset local.objRate.hasChildren = local.groupIndex gt 0>
				</cfif>
			</cfoutput>

			<cfset local.objRateGrouping.hasChildren = local.rateIndex gt 0>
		</cfoutput>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": arrayLen(local.arrRates),
			"recordsFiltered": arrayLen(local.arrRates),
			"data": local.arrRates
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getRegistrantsForPrintingBadges" access="public" output="false" returntype="string">
 		<cfargument name="Event" type="any">

 		<cfscript>
 			var local = structNew();

 			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
 			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
 			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
 			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
 			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

 			local.arrData = [];
 		</cfscript>

		<cfset local.qryRegistrants = this.objAdminEvent.getRegistrantsFromFilters(event=arguments.event, mode="regTabBadgeGrid")>
		<cfloop query="local.qryRegistrants">
			<cfset arrayAppend(local.arrData, {
				"registrantID": local.qryRegistrants.registrantID,
				"memberName": "#local.qryRegistrants.lastname#, #local.qryRegistrants.firstname# (#local.qryRegistrants.memberNumber#)",
				"memberCompany": local.qryRegistrants.company,
				"eventID": arguments.event.getValue('eID',0),
				"registrantRowID": "#local.qryRegistrants.registrantID#",
				"DT_RowClass": "cursor-pointer"
			})>
		</cfloop>
 		
 	   <cfset local.returnStruct = {
 			"success": true,
 			"draw": arguments.event.getValue('draw'),
 			"recordsTotal": val(local.qryRegistrants.totalCount),
 			"recordsFiltered": val(local.qryRegistrants.totalCount),
 			"data": local.arrData
 		}>

 		<cfreturn serializejson(local.returnStruct)>
 	</cffunction>

	 <cffunction name="getEventForms" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfset local.qryForms = this.objAdminEvent.getEventForms(eventID=arguments.event.getValue('eid',0))>

		<cfset local.arrData = []>
		<cfloop query="local.qryForms">
			<cfquery name="local.qryThisLoadPointForms" dbtype="query">
				select count(*) as formCount
				from [local].qryForms
				where loadpoint = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryForms.loadpoint#">
			</cfquery>
			<cfset local.arrData.append({
				"eventFormID": local.qryForms.eventFormID,
				"formTitle": encodeForHTML(local.qryForms.formTitle),
				"numResponses" : local.qryForms.NumResponses,
				"orderby" : local.qryForms.orderby,
				"canmoveup" : local.qryForms.orderBy NEQ 1,
				"canmovedown" : local.qryForms.orderby neq local.qryThisLoadPointForms.formCount,
				"DT_RowId": "eventFormRow_#local.qryForms.eventFormID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryForms.recordCount,
			"recordsFiltered": local.qryForms.recordCount,
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>
</cfcomponent>