CREATE PROC dbo.queue_invNumPrefix_sendMessage
@xmlMessage xml

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @DialogHandleFinal uniqueidentifier;
	set @DialogHandleFinal = NEWID();

	BEGIN DIALOG CONVERSATION @DialogHandleFinal
		FROM SERVICE [PlatformQueue/InvNumPrefixService]
		TO SERVICE N'PlatformQueue/InvNumPrefixService'
		ON CONTRACT [PlatformQueue/GeneralXMLContract]
		WITH ENCRYPTION = OFF;
	SEND ON CONVERSATION @DialogHandleFinal
		MESSAGE TYPE [PlatformQueue/GeneralXMLRequest] (@xmlMessage);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO