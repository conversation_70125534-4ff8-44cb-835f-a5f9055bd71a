----------------------------------------------------------------------------------------------------
/* Query #30: PASSED */
ALTER PROCEDURE [dbo].[sw_AlertDatabaseBackupPreCheck]
(
    @PrimaryBackupDrive VARCHAR(128) = NULL,
    @SecondaryBackupDrive VARCHAR(128) = NULL,
    @MailProfileName VARCHAR(128) = NULL,
    @AlertEmail varchar(128) = NULL,
	@Company varchar(128) = NULL,
	@Configuration varchar(128) = NULL
)
AS
BEGIN

    SET NOCOUNT ON

    IF @PrimaryBackupDrive IS NULL
    BEGIN  
        SET @PrimaryBackupDrive = (SELECT LEFT([Value], 1) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'PrimaryBackupLocation')
    END

    IF @SecondaryBackupDrive IS NULL
    BEGIN
        SET @SecondaryBackupDrive = (SELECT LEFT([Value], 1) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'SecondaryBackupLocation')
    END

    DECLARE @LastBackupSize DECIMAL (19,4) =
    (SELECT 
	CASE WHEN CAST(SUM([compressed_backup_size]) / 1073741824 AS DECIMAL (19,4)) IS NULL THEN 0 
        ELSE CAST(SUM([compressed_backup_size]) / 1073741824 AS DECIMAL (19,4)) END
    FROM [msdb].[dbo].[backupset] as b
    JOIN [msdb].[dbo].[backupmediafamily] as bmf on bmf.[media_set_id] = b.[media_set_id]
    WHERE 
    [is_snapshot] = 0
    AND [is_force_offline] = 0
    AND [is_copy_only] = 0
    AND [type] = 'D'
    AND [backup_start_date] > 
    (SELECT MAX([msdb].[dbo].[agent_datetime](sjh.[run_date], sjh.[run_time]))
    FROM [msdb].[dbo].[sysjobs] sj
    JOIN [msdb].[dbo].[sysjobhistory] as sjh on sj.[job_id] = sjh.[job_id]
    WHERE sj.[name] = 'SW - Maintenance - Backup Databases'))

	DECLARE @PrimaryBackupDriveFreeGB DECIMAL (19,4) =
	(SELECT [Free_GB] 
	FROM DriveSpaceLog 
	WHERE [DateTime] = (SELECT MAX([DateTime]) FROM [SQLWatchmen].[dbo].[DriveSpaceLog] WHERE [Drive] = @PrimaryBackupDrive) 
	AND [Drive] = @PrimaryBackupDrive)

	DECLARE @SecondaryBackupDriveFreeGB DECIMAL (19,4) =
	(SELECT [Free_GB] 
	FROM DriveSpaceLog 
	WHERE [DateTime] = (SELECT MAX([DateTime]) FROM [SQLWatchmen].[dbo].[DriveSpaceLog] WHERE [Drive] = @SecondaryBackupDrive) 
	AND [Drive] = @SecondaryBackupDrive)


	DECLARE @PrimaryBackupMayFail BIT
	DECLARE @SecondaryBackupMayFail BIT

	IF @PrimaryBackupDriveFreeGB < @LastBackupSize
	BEGIN
		SET @PrimaryBackupMayFail = 1
	END

	IF @SecondaryBackupDriveFreeGB < @LastBackupSize
	BEGIN
		SET @SecondaryBackupMayFail = 1
	END
	
    IF @PrimaryBackupMayFail = 1 OR @SecondaryBackupMayFail = 1
    BEGIN
        IF @MailProfileName IS NULL
        BEGIN
            SET @MailProfileName = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'MailProfileName')
        END

        IF @AlertEmail IS NULL
        BEGIN
            SET @AlertEmail = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'AlertEmail')
        END

        IF @Company IS NULL
        BEGIN
            SET @Company = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Company')
        END

        IF @Configuration IS NULL
        BEGIN
            SET @Configuration = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Configuration')
        END

        DECLARE @Subject NVARCHAR(255)
        DECLARE @Body NVARCHAR(MAX)

        IF @PrimaryBackupMayFail = 1
        BEGIN
            SET @Subject = 'Primary Database Backup May Fail | ' + @Configuration + ' | ' + @Company 
            SET @Body = 'The primary database backup drive may not have enough space to complete the backup. Please check the drive space on the primary backup drive.' + CHAR(13) + CHAR(10) +
                'Primary Backup Drive: ' + @PrimaryBackupDrive + CHAR(13) + CHAR(10) +
                'Last Backup Size (GB): ' + CAST(@LastBackupSize AS VARCHAR) + CHAR(13) + CHAR(10) +
                'Free Space (GB): ' + CAST(@PrimaryBackupDriveFreeGB AS VARCHAR)
            
            EXEC msdb.dbo.sp_send_dbmail
                @profile_name = @MailProfileName,
                @recipients = @AlertEmail,
                @subject = @Subject,
                @body = @Body

            INSERT INTO [SQLWatchmen].[dbo].[AlertLog] ([Alert]) VALUES ('Primary Database Backup May Fail')

        END

        IF @SecondaryBackupMayFail = 1
        BEGIN
            SET @Subject = 'Secondary Database Backup May Fail | ' + @Configuration + ' | ' + @Company 
            SET @Body = 'The secondary database backup drive may not have enough space to complete the backup. Please check the drive space on the secondary backup drive.' + CHAR(13) + CHAR(10) +
                'Secondary Backup Drive: ' + @SecondaryBackupDrive + CHAR(13) + CHAR(10) +
                'Last Backup Size (GB): ' + CAST(@LastBackupSize AS VARCHAR(MAX)) + CHAR(13) + CHAR(10) +
                'Free Space (GB): ' + CAST(@SecondaryBackupDriveFreeGB AS VARCHAR(MAX))
            
            EXEC msdb.dbo.sp_send_dbmail
                @profile_name = @MailProfileName,
                @recipients = @AlertEmail,
                @subject = @Subject,
                @body = @Body

            INSERT INTO [SQLWatchmen].[dbo].[AlertLog] ([Alert]) VALUES ('Secondary Database Backup May Fail')

        END

    END

END
GO
