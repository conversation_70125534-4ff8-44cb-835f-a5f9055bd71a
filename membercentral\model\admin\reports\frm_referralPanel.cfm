<cfsavecontent variable="local.dataHead">
	<cfoutput>
	<script language="javascript">
		let referralPanelsTable;

		function initReferralPanelsTable() {
			let domString = "<'row'<'col-sm-3 col-md-3'l><'col-sm-9 col-md-9'f>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";
			referralPanelsTable = $('##referralPanelsTable').DataTable({
				"processing": true,
				"serverSide": true,
				"pageLength": 10,
				"lengthMenu": [ 10, 25, 50, 100 ],
				"language": {
					"lengthMenu": "_MENU_"
				},
				"dom": domString,
				"ajax": { 
					"url": "#local.referralPanelList#",
					"type": "post"
				},
				"autoWidth": false,
				"columns": [
					{ "data": "thePathExpanded", "width": "85%" },
					{ "data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								if(data.isSelected) renderData += '<a href="javascript:removeReferralPanelFromFilter('+ data.panelID+')" id="btnRemRptPanel'+data.panelID+'" class="btn btn-xs p-1 m-1 btn-outline-danger"><i class="fa-solid fa-trash-can"></i></a>';
								else renderData += '<a href="javascript:addToFilter('+ data.panelID+')" id="btnAddRptPanel'+data.panelID+'" class="btn btn-xs p-1 m-1 btn-outline-primary"><i class="fa-solid fa-plus"></i></a>';
							}
							return type === 'display' ? renderData : data;
						},
						"width": "15%",
						"className": "text-center",
						"orderable": false
					}
				],
				"order": [[0, 'asc']]
			});
		}
		function addToFilter(i) {
			var cv = $('##rplist').val().split(',');
				cv.push(i);
				cv = cv.join(',').replace(/(^\s*,)|(,\s*$)/g,'');
			$('##rplist').val(cv);
			doAddPanelToRptFilter(i);
		}
		function doAddPanelToRptFilter(rpid){
			var addPanelToRptFilterResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					reloadReferralPanelGrids();
				} else {
					alert('Unable to add referral panel to filter.');
					addRptPanelElem.removeClass('disabled').html('<i class="fa-solid fa-plus"></i>');
				}	
			};
		
			let addRptPanelElem = $('##btnAddRptPanel'+rpid);
			addRptPanelElem.addClass('disabled').html('Adding...');

			var objParams = { rptID:$('##rptID').val(), rplist:$('##rplist').val() , csrID:#local.siteResourceID#, rptTT:'#local.reportTT#'};
			TS_AJX('ADMREPORTS','saveReferralPanelFilter',objParams,addPanelToRptFilterResult,addPanelToRptFilterResult,10000,addPanelToRptFilterResult);
		}
		function reloadReferralPanelGrids(){
			referralPanelsTable.draw(false);
			top.reloadReferralPanelFilter();
		}
		function removeReferralPanelFromFilter(rpid){
			var removeRptPanelResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					reloadReferralPanelGrids();
				} else {
					delCFElement.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
					alert('Unable to remove referral panel.');
				}	
			};

			let delCFElement = $('##btnRemRptPanel'+rpid);
			mca_initConfirmButton(delCFElement, function(){
				var objParams = { rptId:#local.reportID#, rptTT:'#local.reportTT#', rpid:rpid, csrID:#this.siteResourceID# };
				TS_AJX('ADMREPORTS','removeReferralPanelFilter',objParams,removeRptPanelResult,removeRptPanelResult,10000,removeRptPanelResult);
			});
		}
		$(function() {
			initReferralPanelsTable();
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.dataHead)#">

<cfoutput>
<div class="p-3">
	<table id="referralPanelsTable" class="table table-sm table-striped table-bordered" style="width:100%">
		<thead>
			<tr>
				<th>Referral Panel</th>
				<th>Tools</th>
			</tr>
		</thead>
	</table>
	<form name="frmFilter" id="frmFilter">
		<input type="hidden" name="rptID" id="rptID" value="#local.reportID#">
		<input type="hidden" name="rplist" id="rplist" value="#XMLSearch(local.otherXML,'string(/report/extra/rplist/text())')#">
	</form>
</div>
</cfoutput>
