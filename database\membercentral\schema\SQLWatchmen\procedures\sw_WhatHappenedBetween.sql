----------------------------------------------------------------------------------------------------
/* Query #57: PASSED */
ALTER PROCEDURE [dbo].[sw_WhatHappenedBetween]
(
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @Help BIT = 0
)
AS
BEGIN

    IF @Help = 1
    BEGIN
        PRINT 
            'Stored Procedure sw_WhatHappenedBetween' + CHAR(13) +
            '-------------------------------------------------------------------------------' + CHAR(13) +
            'Purpose: ' + CHAR(13) +
            CHAR(9) + 'To provide information about what was going on with the server between two times' + CHAR(13) +
            'Parameters: ' + CHAR(13) +
            CHAR(9) + '@StartDate DATETIME - The oldest time you want to look back to.' + CHAR(13) +
            CHAR(9) + '@EndDate DATETIME - The most recent time you want to look at.' + CHAR(13) +
            CHAR(9) + '@Help BIT - Set to 1 to display this help message.' + CHAR(13) +
            'Note: ' + CHAR(13) +
            CHAR(9) + 'By default @EndDate will be the current Date/Time and @StartDate will be 30 minutes prior to @EndDate.' + CHAR(13) +
            CHAR(9) + 'This means you can specify @EndDate without specifying @StartDate and it will default to 30 minutes prior to @EndDate.'
        RETURN
    END 

    IF @EndDate IS NULL
    BEGIN
        SET @EndDate = GETDATE()
    END
    IF @StartDate IS NULL
    BEGIN
        SET @StartDate = DATEADD(minute, -30, GETDATE())
    END

    SELECT @StartDate AS [Start Date], @EndDate AS [End Date]

    SELECT 'Alert Log'
    SELECT * FROM [SQLWatchmen].[dbo].[AlertLog] WHERE [DateTime] BETWEEN @StartDate AND @EndDate ORDER BY [DateTime] ASC

    SELECT 'CPU Usage Log'
    SELECT * FROM [SQLWatchmen].[dbo].[CPULog] WHERE [DateTime] BETWEEN @StartDate AND @EndDate ORDER BY [DateTime] ASC

    SELECT 'File Stats Log'
    SELECT * FROM [SQLWatchmen].[dbo].[FileStatsLog] WHERE [DateTime] BETWEEN @StartDate AND @EndDate ORDER BY [DateTime] ASC

    SELECT 'Blocking Log'
    SELECT * FROM [SQLWatchmen].[dbo].[BlockingLog] WHERE [DateTime] BETWEEN @StartDate AND @EndDate ORDER BY [DateTime] ASC

    SELECT 'Blitz Who Log'
    SELECT * 
    FROM [SQLWatchmen].[dbo].[BlitzWhoLog] 
    WHERE CAST([CheckDate] AS DATETIME)
    BETWEEN @StartDate AND @EndDate 
    ORDER BY [CheckDate] ASC

    SELECT 'Performance Counters Log'
    SELECT * FROM [SQLWatchmen].[dbo].[PerformanceCounterLog] WHERE [DateTime] BETWEEN @StartDate AND @EndDate ORDER BY [DateTime] ASC

    SELECT 'Performance - Core Metrics - Pivot'
    SELECT * FROM (
        SELECT [DateTime], CounterName, [Value]
        FROM [SQLWatchmen].[dbo].[PerformanceCounterLog]
        WHERE CounterName IN ('Batch Requests/sec', 'SQL Compilations/sec', 'SQL Re-Compilations/sec', 'Page life expectancy', 'Page reads/sec', 'Page writes/sec', 'Page Splits/sec')
    ) AS PerResults
    PIVOT (
        SUM([Value])
        FOR CounterName
        IN (
            [Batch Requests/sec], [SQL Compilations/sec], [SQL Re-Compilations/sec], [Page life expectancy], [Page reads/sec], [Page writes/sec], [Page Splits/sec]
        )
    ) AS pt
    WHERE [DateTime] BETWEEN @StartDate AND @EndDate
    ORDER BY [DateTime] ASC

    SELECT 'Running Jobs'
    SELECT 
    sj.[name] AS [Job Name],
    sjh.[step_name] [Step Name],
    [msdb].[dbo].agent_datetime(sjh.run_date, sjh.run_time) AS [DateTime],
    STUFF(STUFF(STUFF(RIGHT(REPLICATE('0', 8) + CAST(sjh.[run_duration] as varchar(8)), 8), 3, 0, ':'), 6, 0, ':'), 9, 0, ':') AS [Run Duration],
    sjh.[step_id] AS [Step ID]
    FROM [msdb].[dbo].[sysjobs] AS sj
    JOIN [msdb].[dbo].[sysjobhistory] AS sjh 
    ON sjh.[job_id] = sj.[job_id]
    WHERE [msdb].[dbo].agent_datetime(sjh.[run_date], sjh.[run_time]) BETWEEN @StartDate AND @EndDate
    ORDER BY [msdb].[dbo].agent_datetime(sjh.[run_date], sjh.[run_time]) ASC
    
END
GO
