<cfoutput>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>#event.getValue('mc_pageDefinition.pagetitle',event.getValue('mc_siteInfo.sitename'))#</title>	
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon">
    <link rel="icon" href="/images/favicon.ico" type="image/x-icon">
    #application.objCMS.getBootstrapHeadHTML()#
    #application.objCMS.getResponsiveHeadHTML()#
    <link href="/css/main.css" rel="stylesheet" type="text/css">
	<link href="/css/font.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="/assets/common/javascript/owlCarousel/234/owl.carousel.min.css">
    <link rel="stylesheet" href="/assets/common/javascript/owlCarousel/234/owl.theme.default.min.css">
    #application.objCMS.getFontAwesomeHTML(includeVersion4Support=true)#
    <!-- custom css files -->
    <link href="/css/jquery.mCustomScrollbar.min.css" rel="stylesheet" type="text/css">
    <link href="/css/stylesheet.css" rel="stylesheet" type="text/css">
    <link href="/css/responsive.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="/css/print.css" media="print">
    <script src="/javascript/widget.min.js"></script>
    <link href="/css/jquery.multiselect.filter.css" rel="stylesheet" type="text/css">
    #application.objCMS.getSiteCustomCSS(siteID=event.getValue('mc_siteInfo.siteID'))#
    <!-- Font Awesome -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
</cfoutput>