----------------------------------------------------------------------------------------------------
/* Query #45: PASSED */
ALTER PROCEDURE [dbo].[sw_Cleanup]
AS
BEGIN
    SET NOCOUNT ON

    IF OBJECT_ID('tempdb..#Cleanup') IS NOT NULL
    BEGIN
        DROP TABLE #Cleanup
    END 

    CREATE TABLE #Cleanup
    (
        [Row] SMALLINT IDENTITY PRIMARY KEY CLUSTERED NOT NULL,
        [Table] NVARCHAR(128) NOT NULL,
        [Days] SMALLINT NOT NULL
    )

    INSERT INTO #Cleanup
    SELECT 
    OBJECT_NAME([major_id]),
    CAST([value] AS SMALLINT)
    FROM [SQLWatchmen].[sys].[tables] AS t 
    JOIN [SQLWatchmen].[sys].[extended_properties] AS ep ON ep.[major_id] = t.[object_id] 
    WHERE ep.[name] = N'Retention Policy'

    DECLARE @Row SMALLINT = 1
    DECLARE @RowCount SMALLINT = (SELECT COUNT(*) FROM #Cleanup)
    DECLARE @Table NVARCHAR(128)
    DECLARE @Days SMALLINT
    DECLARE @DateTime DATETIME 
    DECLARE @SQL NVARCHAR(500)

    WHILE @Row <= @RowCount
    BEGIN
        SELECT @Table = [Table], @Days = [Days] FROM #Cleanup WHERE [Row] = @Row
        SET @DateTime = GETDATE() - @Days
        SET @SQL = 'DELETE FROM [' + @Table + '] WHERE [DateTime] < ' + '''' + CAST(@DateTime AS NVARCHAR) + ''''
        EXEC (@SQL)
        SET @Row += 1
    END 

    IF OBJECT_ID('tempdb..#Cleanup') IS NOT NULL
    BEGIN
        DROP TABLE #Cleanup
    END

    DECLARE @CommandLogRetention SMALLINT = (SELECT CAST([Value] AS SMALLINT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'CommandLogRetention')
    SET @DateTime = GETDATE() - @CommandLogRetention
    DELETE FROM [SQLWatchmen].[dbo].[CommandLog] WHERE [StartTime] < @DateTime

    DECLARE @BackupHistoryRetention SMALLINT = (SELECT CAST([Value] AS SMALLINT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'BackupHistoryRetention')
    SET @DateTime = GETDATE() - @BackupHistoryRetention
    EXEC msdb.dbo.sp_delete_backuphistory @oldest_date = @DateTime

    DECLARE @JobHistoryRetention SMALLINT = (SELECT CAST([Value] AS SMALLINT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'JobHistoryRetention')
    SET @DateTime = GETDATE() - @JobHistoryRetention
    EXEC msdb.dbo.sp_purge_jobhistory @oldest_date = @DateTime

    DECLARE @MailItemsRetention SMALLINT = (SELECT CAST([Value] AS SMALLINT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'MailItemsRetention')
    SET @DateTime = GETDATE() - @MailItemsRetention
    EXEC msdb.dbo.sysmail_delete_mailitems_sp @sent_before = @DateTime

END
GO
