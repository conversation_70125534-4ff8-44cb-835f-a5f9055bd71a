----------------------------------------------------------------------------------------------------
/* Query #51: PASSED */
ALTER PROCEDURE sw_LogPerformanceCounters
(
	@LogPerformanceCountersDelay VARCHAR(128) = NULL
)
AS
BEGIN
	IF @LogPerformanceCountersDelay IS NULL
	BEGIN
		SET @LogPerformanceCountersDelay = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'LogPerformanceCountersDelay')
	END

	IF OBJECT_ID('tempdb..#PerfCounter1') IS NOT NULL 
	BEGIN	
		DROP TABLE #PerfCounter1
	END

	IF OBJECT_ID('tempdb..#PerfCounter2') IS NOT NULL 
	BEGIN	
		DROP TABLE #PerfCounter2
	END

	CREATE TABLE #PerfCounter1 
	(
		[ID] SMALLINT NOT NULL PRIMARY KEY CLUSTERED,
		[CounterValue] BIGINT NOT NULL
	)

	CREATE TABLE #PerfCounter2 
	(
		[ID] SMALLINT NOT NULL PRIMARY KEY CLUSTERED,
		[CounterValue] BIGINT NOT NULL
	)

	DECLARE @Collection1 datetime2 = SYSDATETIME()

	INSERT INTO #PerfCounter1
	SELECT pc.[ID], d.[cntr_value] 
	FROM [SQLWatchmen].[dbo].[PerformanceCounters] AS pc
	JOIN [sys].[dm_os_performance_counters] AS d 
	ON d.[object_name] = pc.[ObjectName] 
	AND d.[counter_name] = pc.[CounterName] 
	AND d.[instance_name] = pc.[InstanceName]

	WAITFOR DELAY @LogPerformanceCountersDelay

	DECLARE @Collection2 datetime2 = SYSDATETIME()

	INSERT INTO #PerfCounter2
	SELECT pc.[ID], d.[cntr_value] 
	FROM [SQLWatchmen].[dbo].[PerformanceCounters] AS pc
	JOIN [sys].[dm_os_performance_counters] AS d 
	ON d.[object_name] = pc.[ObjectName] 
	AND d.[counter_name] = pc.[CounterName] 
	AND d.[instance_name] = pc.[InstanceName]
	WHERE pc.CounterType <> 65792

	DECLARE @TimeDiff DECIMAL(8) = DATEDIFF(MILLISECOND, @Collection1, @Collection2) / 1000.0

	INSERT INTO [SQLWatchmen].[dbo].[PerformanceCounterLog] ([ObjectName], [CounterName], [InstanceName], [Value])
	SELECT pc.ObjectName, pc.CounterName, pc.InstanceName, one.CounterValue
	FROM #PerfCounter1 as one
	JOIN [SQLWatchmen].[dbo].[PerformanceCounters] as pc ON pc.[ID] = one.[ID]
	WHERE pc.[CounterType] = 65792

	INSERT INTO [SQLWatchmen].[dbo].[PerformanceCounterLog] ([ObjectName], [CounterName], [InstanceName], [Value])
	SELECT 
	pc.ObjectName, 
	pc.CounterName, 
	pc.InstanceName, 
	ROUND(((two.CounterValue - one.CounterValue) / @TimeDiff), 0)
	FROM [SQLWatchmen].[dbo].[PerformanceCounters] as pc 
	JOIN #PerfCounter1 as one on pc.[ID] = one.[ID]
	JOIN #PerfCounter2 as two on pc.[ID] = two.[ID]
	WHERE pc.[CounterType] = 272696320 
	OR pc.[CounterType] = 272696576

	DECLARE @BufferCacheHitRatio DECIMAL(19) = CAST((
	SELECT two.CounterValue - one.CounterValue FROM #PerfCounter1 AS one JOIN #PerfCounter2 AS two on one.ID = two.ID WHERE one.ID = 
	(SELECT [ID] FROM [SQLWatchmen].[dbo].[PerformanceCounters] WHERE [ObjectName] = N'SQLServer:Buffer Manager' AND [CounterName] = N'Buffer cache hit ratio' AND [InstanceName] = N'' AND [CounterType] = 537003264)) 
	AS DECIMAL(19))
	DECLARE @BufferCacheHitRatioBase DECIMAL(19) = CAST((
	SELECT two.CounterValue - one.CounterValue FROM #PerfCounter1 as one JOIN #PerfCounter2 as two ON one.ID = two.ID WHERE one.ID = 
	(SELECT [ID] FROM [SQLWatchmen].[dbo].[PerformanceCounters] WHERE [ObjectName] = N'SQLServer:Buffer Manager' AND [CounterName] = N'Buffer cache hit ratio base' AND [InstanceName] = N'' AND [CounterType] = 1073939712)) 
	AS DECIMAL(19))

	INSERT INTO [SQLWatchmen].[dbo].[PerformanceCounterLog] ([ObjectName], [CounterName], [InstanceName], [Value])
	SELECT 
	N'SQLServer:Buffer Manager',
	N'Buffer cache hit ratio',
	N'',
	CASE WHEN (@BufferCacheHitRatioBase = 0) 
		THEN NULL ELSE ROUND((@BufferCacheHitRatio / @BufferCacheHitRatioBase) * 100, 0) END

	DECLARE @WorktablesFromCacheRatio DECIMAL(19) = CAST((
	SELECT two.CounterValue - one.CounterValue FROM #PerfCounter1 AS one JOIN #PerfCounter2 AS two on one.ID = two.ID WHERE one.ID = 
	(SELECT [ID] FROM [SQLWatchmen].[dbo].[PerformanceCounters] WHERE [ObjectName] = N'SQLServer:Access Methods' AND [CounterName] = N'Worktables From Cache Ratio' AND [InstanceName] = N'' AND [CounterType] = 537003264)) 
	AS DECIMAL(19))
	DECLARE @WorktablesFromCacheBase DECIMAL(19) = CAST((
	SELECT two.CounterValue - one.CounterValue FROM #PerfCounter1 as one JOIN #PerfCounter2 as two ON one.ID = two.ID WHERE one.ID = 
	(SELECT [ID] FROM [SQLWatchmen].[dbo].[PerformanceCounters] WHERE [ObjectName] = N'SQLServer:Access Methods' AND [CounterName] = N'Worktables From Cache Base' AND [InstanceName] = N'' AND [CounterType] = 1073939712)) 
	AS DECIMAL(19))

	INSERT INTO [SQLWatchmen].[dbo].[PerformanceCounterLog] ([ObjectName], [CounterName], [InstanceName], [Value])
	SELECT 
	N'SQLServer:Access Methods',
	N'Worktables From Cache Ratio',
	N'',
	CASE WHEN (@WorktablesFromCacheBase = 0) 
		THEN NULL ELSE ROUND((@WorktablesFromCacheRatio / @WorktablesFromCacheBase) * 100, 0) END

	DECLARE @CacheHitRatio DECIMAL(19) = CAST((
	SELECT two.CounterValue - one.CounterValue FROM #PerfCounter1 AS one JOIN #PerfCounter2 AS two on one.ID = two.ID WHERE one.ID = 
	(SELECT [ID] FROM [SQLWatchmen].[dbo].[PerformanceCounters] WHERE [ObjectName] = N'SQLServer:Plan Cache' AND [CounterName] = N'Cache Hit Ratio' AND [InstanceName] = N'_Total' AND [CounterType] = 537003264)) 
	AS DECIMAL(19))
	DECLARE @CacheHitRatioBase DECIMAL(19) = CAST((
	SELECT two.CounterValue - one.CounterValue FROM #PerfCounter1 as one JOIN #PerfCounter2 as two ON one.ID = two.ID WHERE one.ID = 
	(SELECT [ID] FROM [SQLWatchmen].[dbo].[PerformanceCounters] WHERE [ObjectName] = N'SQLServer:Plan Cache' AND [CounterName] = N'Cache Hit Ratio Base' AND [InstanceName] = N'_Total' AND [CounterType] = 1073939712)) 
	AS DECIMAL(19))

	INSERT INTO [SQLWatchmen].[dbo].[PerformanceCounterLog] ([ObjectName], [CounterName], [InstanceName], [Value])
	SELECT 
	N'SQLServer:Plan Cache',
	N'Cache Hit Ratio',
	N'',
	CASE WHEN (@CacheHitRatioBase = 0)
		THEN NULL ELSE ROUND((@CacheHitRatio / @CacheHitRatioBase) * 100, 0) END

	DECLARE @AvgLockWaitTime DECIMAL(19) = CAST((
	SELECT two.CounterValue - one.CounterValue FROM #PerfCounter1 AS one JOIN #PerfCounter2 AS two on one.ID = two.ID WHERE one.ID = 
	(SELECT [ID] FROM [SQLWatchmen].[dbo].[PerformanceCounters] WHERE [ObjectName] = N'SQLServer:Locks' AND [CounterName] = N'Average Wait Time (ms)' AND [InstanceName] = N'_Total' AND [CounterType] = 1073874176)) 
	AS DECIMAL(19))
	DECLARE @AvgLockWaitTimeBase DECIMAL(19) = CAST((
	SELECT two.CounterValue - one.CounterValue FROM #PerfCounter1 as one JOIN #PerfCounter2 as two ON one.ID = two.ID WHERE one.ID = 
	(SELECT [ID] FROM [SQLWatchmen].[dbo].[PerformanceCounters] WHERE [ObjectName] = N'SQLServer:Locks' AND [CounterName] = N'Average Wait Time Base' AND [InstanceName] = N'_Total' AND [CounterType] = 1073939712)) 
	AS DECIMAL(19))

	INSERT INTO [SQLWatchmen].[dbo].[PerformanceCounterLog] ([ObjectName], [CounterName], [InstanceName], [Value])
	SELECT 
	N'SQLServer:Locks',
	N'Average Wait Time (ms)',
	N'_Total',
	CASE WHEN (@AvgLockWaitTimeBase = 0)
		THEN NULL ELSE ROUND((@AvgLockWaitTime / @AvgLockWaitTimeBase) * 1000.0, 0) END

	DECLARE @AvgLatchWaitTime DECIMAL(19) = CAST((
	SELECT two.CounterValue - one.CounterValue FROM #PerfCounter1 AS one JOIN #PerfCounter2 AS two on one.ID = two.ID WHERE one.ID = 
	(SELECT [ID] FROM [SQLWatchmen].[dbo].[PerformanceCounters] WHERE [ObjectName] = N'SQLServer:Latches' AND [CounterName] = N'Average Latch Wait Time (ms)' AND [InstanceName] = N'' AND [CounterType] = 1073874176)) 
	AS DECIMAL(19))
	DECLARE @AvgLatchWaitTimeBase DECIMAL(19) = CAST((
	SELECT two.CounterValue - one.CounterValue FROM #PerfCounter1 as one JOIN #PerfCounter2 as two ON one.ID = two.ID WHERE one.ID = 
	(SELECT [ID] FROM [SQLWatchmen].[dbo].[PerformanceCounters] WHERE [ObjectName] = N'SQLServer:Latches' AND [CounterName] = N'Average Latch Wait Time Base' AND [InstanceName] = N'' AND [CounterType] = 1073939712)) 
	AS DECIMAL(19))

	INSERT INTO [SQLWatchmen].[dbo].[PerformanceCounterLog] ([ObjectName], [CounterName], [InstanceName], [Value])
	SELECT 
	N'SQLServer:Latches',
	N'Average Latch Wait Time (ms)',
	N'',
	CASE WHEN (@AvgLatchWaitTimeBase = 0)
		THEN NULL ELSE ROUND((@AvgLatchWaitTime / @AvgLatchWaitTimeBase) * 1000.0, 0) END

	IF OBJECT_ID('tempdb..#PerfCounter1') IS NOT NULL 
	BEGIN	
		DROP TABLE #PerfCounter1
	END

	IF OBJECT_ID('tempdb..#PerfCounter2') IS NOT NULL 
	BEGIN	
		DROP TABLE #PerfCounter2
	END

END
GO
