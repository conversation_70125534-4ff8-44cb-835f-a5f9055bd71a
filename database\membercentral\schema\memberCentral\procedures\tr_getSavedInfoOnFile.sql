ALTER PROC dbo.tr_getSavedInfoOnFile 
@payProfileID int,
@profileID int,
@memberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @orgID int, @cofMemberID int, @tokenStore varchar(30);
	SELECT @cofMemberID = dbo.fn_getActiveMemberID(@memberID);

	SELECT @orgID = s.orgID, @tokenStore = g.tokenStore
	FROM dbo.mp_profiles AS mp
	INNER JOIN dbo.mp_gateways AS g ON g.gatewayID = mp.gatewayID
	INNER JOIN dbo.sites AS s ON s.siteID = mp.siteID
	WHERE mp.profileID = @profileID;

	-- bank draft ignores the profileID
	IF @tokenStore = 'BankDraft'
		SELECT mpp.payProfileID, mpp.memberID, mpp.detail, mpp.nickname, b.routingNumber, b.accountNumber, b.acctType, 
			mpp.dateAdded, 0 AS surchargeEligible
		FROM dbo.tr_bankAccounts AS b
		INNER JOIN dbo.ams_memberPaymentProfiles AS mpp ON mpp.payProfileID = b.MPPPayProfileID
			AND mpp.payProfileID = @payProfileID
			AND mpp.memberID = @cofMemberID
			AND mpp.[status] = 'A'
		WHERE b.orgID = @orgID;

	ELSE 
		SELECT payProfileID, memberID, detail, nickname, customerProfileID, paymentProfileID, dateAdded, otherFields, surchargeEligible
		FROM dbo.ams_memberPaymentProfiles
		WHERE payProfileID = @payProfileID
		AND memberID = @cofMemberID
		AND profileID = @profileID
		AND [status] = 'A';

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;
END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO