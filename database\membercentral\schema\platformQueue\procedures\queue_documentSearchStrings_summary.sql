CREATE PROC dbo.queue_documentSearchStrings_summary

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	insert into #tmpQueueDataCount (queueType, queueStatus, numItems, minDateAdded, offerDelete)
	select 'documentSearchStrings', qs.queueStatus, count(qi.itemID), min(qi.dateUpdated), 1
	from dbo.queue_documentSearchStrings as qi
	inner join dbo.tblQueueStatuses as qs on qs.queuestatusID = qi.statusID
	group by qs.queueStatus;	


	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO