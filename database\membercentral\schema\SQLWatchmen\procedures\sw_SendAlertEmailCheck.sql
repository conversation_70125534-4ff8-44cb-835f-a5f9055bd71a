----------------------------------------------------------------------------------------------------
/* Query #53: PASSED */
ALTER PROCEDURE [dbo].[sw_SendAlertEmailCheck] 
(
	@MailProfileName varchar(128) = NULL,
	@AlertEmail varchar(128) = NULL,
	@Company varchar(128) = NULL,
	@Configuration varchar(128) = NULL
)
AS
BEGIN
    SET NOCOUNT ON

    IF @MailProfileName IS NULL
    BEGIN
        SET @MailProfileName = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'MailProfileName')
    END

    IF @AlertEmail IS NULL
    BEGIN
        SET @AlertEmail = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'AlertEmail')
    END

    IF @Company IS NULL
    BEGIN
        SET @Company = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Company')
    END

    IF @Configuration IS NULL
    BEGIN
        SET @Configuration = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Configuration')
    END

    DECLARE @Subject NVARCHAR(255)
    DECLARE @Body NVARCHAR(MAX)

    SET @Subject = N'Alert Email Check | ' + @Configuration + N' | ' + @Company
    SET @Body = 'Alert Email Check' + CHAR(13) + CHAR(10) + 
        'Configuration: ' + @Configuration + CHAR(13) + CHAR(10) +
        'Company: ' + @Company + CHAR(13) + CHAR(10) +
        'Mail Profile Used: ' + @MailProfileName + CHAR(13) + CHAR(10) +
        'Alert Email Address: ' + @AlertEmail 

    EXEC msdb.dbo.sp_send_dbmail
        @profile_name = @MailProfileName,
        @recipients = @AlertEmail,
        @subject = @Subject,
        @body = @Body

    INSERT INTO [SQLWatchmen].[dbo].[AlertLog] ([Alert]) VALUES ('Email Check')

END
GO
