----------------------------------------------------------------------------------------------------
/* Query #32: PASSED */
ALTER PROCEDURE [dbo].[sw_AlertHighCPUUtilization]
(
    @AverageCPUUtilizationThreshold TINYINT = NULL,
    @HighCPUUtilizationAlertInterval SMALLINT = NULL,
    @MailProfileName varchar(128) = NULL,
	@AlertEmail varchar(128) = NULL,
	@Company varchar(128) = NULL,
	@Configuration varchar(128) = NULL
)
AS
BEGIN

    SET NOCOUNT ON

    IF @HighCPUUtilizationAlertInterval IS NULL
    BEGIN
        SET @HighCPUUtilizationAlertInterval = (SELECT CAST([Value] AS SMALLINT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'HighCPUUtilizationAlertInterval')
    END

    DECLARE @DateTimeCutoff datetime = DATEADD(minute, -@HighCPUUtilizationAlertInterval, GETDATE())
    IF (SELECT TOP 1 [DateTime] FROM [SQLWatchmen].[dbo].[AlertLog] WHERE [Alert] = 'High CPU Utilization' AND [DateTime] > @DateTimeCutoff) IS NOT NULL
    BEGIN
        RETURN
    END

    IF @AverageCPUUtilizationThreshold IS NULL
    BEGIN
        SET @AverageCPUUtilizationThreshold = (SELECT CAST([Value] AS TINYINT) FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'AverageCPUUtilizationThreshold')
    END

    DECLARE @AverageCPUUtilization TINYINT = (SELECT AVG([SQL] + [Other]) FROM [SQLWatchmen].[dbo].[CPULog] WHERE [DateTime] > @DateTimeCutoff)
    
    IF @AverageCPUUtilization > @AverageCPUUtilizationThreshold
    BEGIN

        IF @MailProfileName IS NULL
        BEGIN
            SET @MailProfileName = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'MailProfileName')
        END

        IF @AlertEmail IS NULL
        BEGIN
            SET @AlertEmail = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'AlertEmail')
        END

        IF @Company IS NULL
        BEGIN
            SET @Company = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Company')
        END

        IF @Configuration IS NULL
        BEGIN
            SET @Configuration = (SELECT [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Configuration')
        END

        DECLARE @Subject NVARCHAR(255)
        DECLARE @Body NVARCHAR(MAX)

        SET @Subject = 'High CPU Utilization Detected | ' + @Configuration + ' | ' + @Company
        SET @Body = 'The average CPU utilization for the last ' + CAST(@HighCPUUtilizationAlertInterval AS NVARCHAR) + ' minutes is ' + CAST(@AverageCPUUtilization AS NVARCHAR) + '%. This is above the threshold of ' + CAST(@AverageCPUUtilizationThreshold AS NVARCHAR) + '%.' + CHAR(13) + CHAR(10)
        SET @Body += 'Please refer to the details listed below:' + CHAR(13) + CHAR(10)
        SET @Body += 'Log Date/Time | SQL CPU Utilization | Other CPU Utilization | Idle CPU Utilization' + CHAR(13) + CHAR(10)
        SELECT @Body += CAST([DateTime] AS NVARCHAR) + ' | ' + CAST([SQL] AS NVARCHAR) + ' | ' + CAST([Other] AS NVARCHAR) + ' | ' + CAST([Idle] AS NVARCHAR) + CHAR(13) + CHAR(10) 
            FROM [SQLWatchmen].[dbo].[CPULog] WHERE [DateTime] > @DateTimeCutoff

        EXEC msdb.dbo.sp_send_dbmail
			@profile_name = @MailProfileName,
			@recipients = @AlertEmail,
			@subject = @Subject,
			@body = @Body

		INSERT INTO [SQLWatchmen].[dbo].[AlertLog] ([Alert]) VALUES ('High CPU Utilization')

    END

END
GO
