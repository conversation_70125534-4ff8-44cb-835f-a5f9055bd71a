ALTER PROC dbo.queue_paperStatements_clear
@status varchar(60)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	BEGIN TRAN;
		DELETE qi
		FROM dbo.queue_paperStatementsDetail as qi
		inner join dbo.tblQueueStatuses qs on qs.queueStatusID = qi.queueStatusID and qs.queueStatus = @status;

		DELETE from dbo.queue_paperStatements 
		where itemID not in (select itemID from dbo.queue_paperStatementsDetail);
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO