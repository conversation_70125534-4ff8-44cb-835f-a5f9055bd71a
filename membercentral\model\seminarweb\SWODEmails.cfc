<cfcomponent extends="SWODSeminars">
	<cffunction name="sendConfirmation" access="public" returntype="boolean" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="performedBy" type="numeric" required="yes">
		<cfargument name="outgoingType" type="string" required="yes">
		<cfargument name="orgcode" type="string" required="yes" hint="really is the sitecode">
		<cfargument name="emailOverride" type="string" required="no" default="">
		<cfargument name="customText" type="string" required="no" default="">
		<cfargument name="isImportantCustomText" type="boolean" required="no" default="0">

		<cfset var local = structnew()>
		
		<!--- construct email messages --->
		<cfset local.strEmailContent = generateConfirmationEmail(enrollmentID=arguments.enrollmentID, customText=arguments.customtext, isImportantCustomText=arguments.isImportantCustomText)>

		<!--- handle email override --->
		<cfset local.arrEmailTo = []>
		<cfset local.emailOverride = len(arguments.emailOverride) GT 0 ? replace(replace(arguments.emailOverride,',',';','ALL'),' ','','ALL') : "">
		<cfif len(local.emailOverride)>
			<cfset local.emailOverrideArr = listToArray(local.emailOverride,';')>
			<cfloop array="#local.emailOverrideArr#" item="local.thisEmail">
				<cfif isValid("regex",local.thisEmail,application.regEx.email)>
					<cfset local.arrEmailTo.append({ name=local.strEmailContent.emailToName, email=local.thisEmail })>
				</cfif>
			</cfloop>
		</cfif>

		<cfif len(local.strEmailContent.emailTo) and isValid("regex",local.strEmailContent.emailTo,application.regEx.email) AND NOT listFindNoCase(local.emailOverride,local.strEmailContent.emailTo,';')>
			<cfset local.arrEmailTo.append({ name=local.strEmailContent.emailToName, email=local.strEmailContent.emailTo })>
		</cfif>
		<cfif len(local.strEmailContent.overrideEmail) AND NOT listFindNoCase(local.emailOverride,local.strEmailContent.overrideEmail,';')>
			<cfset local.arrEmailTo.append({ name=local.strEmailContent.emailToName, email=local.strEmailContent.overrideEmail })>
		</cfif>
		
		<!--- add to email queue--->
		<cftry>
			<cfif arrayLen(local.arrEmailTo) gt 0>
				<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.orgCode)>
				<cfset local.seminarWebSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SemWebCatalog',siteID=local.mc_siteInfo.siteID)>
				<cfset local.emailhtmlcontent = application.objEmailWrapper.wrapMessage(emailTitle=local.strEmailContent.emailTitle, emailContent=local.strEmailContent.html, emailFooter=local.strEmailContent.emailFooter , sitecode=application.objSiteInfo.getSiteCodeFromSiteID(local.mc_siteInfo.siteID))>        
				<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name=local.strEmailContent.emailFromName, email=local.mc_siteInfo.networkEmailFrom },
						emailto=local.arrEmailTo,
						emailreplyto=local.mc_siteInfo.supportProviderEmail,
						emailsubject=local.strEmailContent.subject,
						emailtitle=local.strEmailContent.emailTitle,
						emailhtmlcontent=local.emailhtmlcontent,
						siteID=local.mc_siteInfo.siteID,
						memberID=local.strEmailContent.memberID,
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SEMWEBCONF"),
						sendingSiteResourceID=local.seminarWebSiteResourceID,
						doWrapEmail=false
				)>
				<cfif NOT local.strResult.success>
					<cfthrow message="#local.strResult.err#">
				<cfelse>
					<cfloop array="#local.arrEmailTo#" index="local.thisEmail">
						<cfset logAction(arguments.seminarID,arguments.outgoingType,arguments.performedBy,local.thisEmail.email,arguments.enrollmentID)>
					</cfloop>
					<cfset local.emailSent = true>
				</cfif>
			<cfelse>
				<cfset local.emailSent = false>
			</cfif>
		<cfcatch type="Any">
			<cfset local.emailSent = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>
		
		<cfreturn local.emailSent>
	</cffunction>

	<cffunction name="sendSaveAndExitEmail" access="public" returntype="void" output="no">
		<cfargument name="logAccessID" type="numeric" required="yes">
		<cfargument name="includeLoginDetails" type="boolean" required="yes">

		<cfset var local = structnew()>
		
		<!--- get info for email --->
		<cfquery name="local.qryInfo" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT TOP 1 e.seminarID, s.seminarName, s.seminarSubTitle, ISNULL(nullif(me.email,''),d.email) AS Email,
				p.orgcode as signUpOrgCode, d.depomemberdataID, e.enrollmentID, 
				ISNULL(m2.firstname,d.firstName) + ' ' + ISNULL(m2.lastname,d.lastname) AS fullname, 
				ISNULL(eo.email,'') AS overrideEmail
			FROM dbo.tblLogAccessSWOD AS laswod 
			INNER JOIN dbo.tblEnrollments AS e ON laswod.enrollmentID = e.enrollmentID 
			INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
			INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
			INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
			INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID
			LEFT OUTER JOIN membercentral.dbo.ams_members AS m
				INNER JOIN membercentral.dbo.ams_members as m2 on m2.orgID = m.orgID and m2.memberID = m.activeMemberID
				INNER JOIN memberCentral.dbo.ams_memberEmails AS me ON me.orgID = m2.orgID and me.memberID = m2.memberID
				INNER JOIN memberCentral.dbo.ams_memberEmailTags AS metag ON metag.orgID = m2.orgID and metag.memberID = me.memberID AND metag.emailTypeID = me.emailTypeID
				INNER JOIN memberCentral.dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID = m2.orgID and metagt.emailTagTypeID = metag.emailTagTypeID AND metagt.emailTagType = 'Primary'
			ON m.memberID = e.MCMemberID
			LEFT OUTER JOIN memberCentral.dbo.ams_emailAppOverrides AS eo ON eo.itemID = e.enrollmentID AND eo.itemType = 'semwebreg'
			WHERE laswod.logAccessID = <cfqueryparam value="#arguments.logAccessID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		
		<cfif local.qryInfo.recordcount>
			<!--- construct email messages --->
			<cfset local.arrEmailTo = []>
			<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(local.qryInfo.signUpOrgCode)>
			<cfset local.memberID = CreateObject("component","SWCommon").getMemberIDByDepoMemberDataID(siteCode=local.qryInfo.signUpOrgCode, depoMemberDataID=local.qryInfo.depomemberdataID)>
			<cfset local.strEmailContent = generateSaveAndExitEmail(seminarID=local.qryInfo.seminarID, seminarName=local.qryInfo.seminarName,
				email=local.qryInfo.email, signuporgcode=local.qryInfo.signUpOrgCode, depoMemberDataID=local.qryInfo.depomemberdataid, toName = '#local.qryInfo.fullname#', memberId = local.memberID,enrollmentID = local.qryInfo.enrollmentID)>

			<cfif IsValid("regex",local.strEmailContent.emailTo,application.regEx.email)>
				<cfset local.arrEmailTo.append({ name=local.qryInfo.fullname, email=local.strEmailContent.emailTo })>
			</cfif>
			<cfif len(local.qryInfo.overrideEmail)>
				<cfset local.arrEmailTo.append({ name=local.qryInfo.fullname, email=local.qryInfo.overrideEmail })>
			</cfif>
			
			<!--- add to email queue--->
			<cftry>
				<cfif arrayLen(local.arrEmailTo)>
					<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(local.qryInfo.signUpOrgCode)>
					<cfset local.seminarWebSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SemWebCatalog',siteID=local.mc_siteInfo.siteID)>
					<cfset local.memberID = CreateObject("component","SWCommon").getMemberIDByDepoMemberDataID(siteCode=local.qryInfo.signUpOrgCode, depoMemberDataID=local.qryInfo.depomemberdataID)>

					<cfif local.memberID>
						<cfset local.emailhtmlcontent = application.objEmailWrapper.wrapMessage(emailTitle=local.strEmailContent.emailTitle, emailContent=local.strEmailContent.html, emailFooter=local.strEmailContent.emailFooter , sitecode=application.objSiteInfo.getSiteCodeFromSiteID(local.mc_siteInfo.siteID))>		
						<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
								emailfrom={ name=local.strEmailContent.emailFromName, email="<EMAIL>" },
								emailto=local.arrEmailTo,
								emailreplyto=local.mc_siteInfo.supportProviderEmail,
								emailsubject="Continue Your Seminar: #local.strEmailContent.subject#",
								emailtitle=local.strEmailContent.emailTitle,
								emailhtmlcontent=local.emailhtmlcontent,
								siteID=local.mc_siteInfo.siteID,
								memberID=local.memberID,
								messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SEMWEBSWODEXIT"),
								sendingSiteResourceID=local.seminarWebSiteResourceID,
								doWrapEmail=false
						)>
						<cfif NOT local.strResult.success>
							<cfthrow message="#local.strResult.err#">
						<cfelse>
							<cfset logAction(local.qryInfo.seminarID,"manualResendInstructions",local.qryInfo.depomemberdataid,local.strEmailContent.emailTo,local.qryInfo.enrollmentID)>
						</cfif>
					<cfelse>
						<cfthrow message="Unable to send Save and Exit email because no member record found.">
					</cfif>
				</cfif>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfcatch>
			</cftry>
		</cfif>
	</cffunction>

	<cffunction name="generateSaveAndExitEmail" access="private" returntype="struct" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="seminarName" type="string" required="yes">
		<cfargument name="email" type="string" required="yes">
		<cfargument name="signuporgcode" type="string" required="yes">
		<cfargument name="depoMemberDataID" type="numeric" required="yes">
		<cfargument name="toName" type="string" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		
		<cfset var local = structnew()>
		<cfset local.qryAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(arguments.signuporgcode).qryAssociation>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.signUpOrgCode)>
		<cfset local.CreditCompleteByDate = CreateObject("component","model.seminarweb.SWODSeminars").getCreditCompleteByDateForSeminar(enrollmentID=arguments.enrollmentID)>

		<!--- set subject/to --->
		<cfset local.strEmailContent.subject = arguments.seminarName>
		<cfset local.strEmailContent.emailTo = arguments.email>
		<cfset local.strEmailContent.emailFromName = local.qryAssociation.emailFrom>
		
		<cfsavecontent variable="local.strEmailContent.emailTitle">
			<cfoutput>
			<div style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding-bottom:5px;text-align:center;"><i>#local.mc_siteInfo.siteName#</i></div>
			<div style="text-align:center;">Continue Your Seminar</div>
			<cfif len(local.CreditCompleteByDate) AND dateCompare(now(),local.CreditCompleteByDate, "n") LT 0>
				<div style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;color:##F00;text-align:center;padding-top:8px;">Complete by: #dateformat(local.CreditCompleteByDate, "m/d/yyyy")# at 11:59 PM CT</div>
			</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfset local.memberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.memberID,orgID=local.mc_siteInfo.orgID)>
		<cfset local.memberKey = application.objMergeCodes.generateMemberKey(orgcode=arguments.signUpOrgCode, membernumber=local.memberNumber)>
		<cfset local.enterProgramLink = "#local.mc_siteInfo.scheme#://#local.mc_siteInfo.mainhostname#?pg=swOnDemandPlayer&seminarID=#arguments.seminarID#&enrollmentID=#arguments.enrollmentID#&orgCode=#arguments.signuporgcode#&mk=#local.memberKey#">

		<!--- html version --->
		<cfsavecontent variable="local.strEmailContent.html">
			<cfoutput>
			#arguments.toName#,
			<br/><br/>
			You can continue <b>#encodeForHTML(arguments.seminarName)#</b> using the instructions below.
			<br/><br/>
			<table cellspacing="0" cellpadding="0" width="100%" style="background-color:##fff;font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;border:1px black solid;padding:10px">
			<tr>
				<td>
					<div align="center">
						<a href="#local.enterProgramLink#" target="_blank" style="-webkit-border-radius: 5px; -moz-border-radius: 5px; border-radius: 5px; color: ##ffff; display: block;width: 180px; background: ##2c78e4;font-size: 16px;font-weight: bold;font-family: sans-serif;text-decoration: none;line-height: 40px;">
							Continue Seminar!
						</a>
					</div>
					<div style="width: 100%; border-top: 1px black solid;margin-top: 13px;"></div>
					<div align="center">
						<span style="display: block;font-size: 16px;font-weight: bold;font-family: sans-serif;text-decoration: none;line-height: 40px;">
							Review All Programs
						</span>
	
						<span style="font-size: 12px;"><a href="#local.qryAssociation.CatalogURL#/?panel=My&_swft=SWOD&mk=#local.memberKey#">Click here</a> to review your purchase history, access materials, and download certificates.</span>
					</div>
				</td>
			</tr>						
			</table><br/><br/>
			#local.qryAssociation.emailFrom#<br/>
			<a href="mailto:#local.qryAssociation.supportEmail#">#local.qryAssociation.supportEmail#</a><br/>
			#local.qryAssociation.supportPhone#
			</cfoutput>
		</cfsavecontent>
		
		<cfset local.qryOrgIdentity = application.objOrgInfo.getOrgIdentity(orgIdentityID=local.qryAssociation.orgIdentityID)>
		
		<cfsavecontent variable="local.strEmailContent.emailFooter">
			<cfoutput>
                This message was sent by SeminarWeb on behalf of <a href='#local.qryOrgIdentity.website#'>#local.qryOrgIdentity.organizationName#</a><br/>
                <cfif len(trim(local.qryOrgIdentity.email))>
                    <a href='mailto:#local.qryOrgIdentity.email#' target="_blank">#local.qryOrgIdentity.email#</a>
                </cfif>
                <cfif len(trim(local.qryOrgIdentity.phone))>
                    | #local.qryOrgIdentity.phone#
                </cfif>
            </cfoutput>
		</cfsavecontent>

		<cfreturn local.strEmailContent>
	</cffunction>

	<cffunction name="generateConfirmationEmail" access="public" returntype="struct" output="no">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="customText" type="string" required="no" default="">
		<cfargument name="isImportantCustomText" type="boolean" required="no" default="0">

		<cfset var local = structnew()>
		<cfset local.qryEnrollment = getEnrollmentByEnrollmentID(arguments.enrollmentID)>
		<cfset local.qryAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(local.qryEnrollment.signUpOrgCode).qryAssociation>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(local.qryEnrollment.signUpOrgCode)>
		
		<!--- get transaction data --->
		<cfif local.qryEnrollment.handlesOwnPayment is 1>
			<!--- transactions for confirmation --->
			<cfstoredproc procedure="sw_enrollmentTransactionsForConfirmation" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryEnrollment.enrollmentID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="SWOD">
				<cfprocresult name="local.qryTotals" resultset="1">
				<cfprocresult name="local.qryEnrollmentTransactions" resultset="2">
				<cfprocresult name="local.qryPaymentAllocations" resultset="3">
			</cfstoredproc>
			<cfif val(local.qryTotals.total) gt 0>
				<cfstoredproc procedure="sw_getEnrollmentPaymentDetail" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryEnrollment.enrollmentID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="SWOD">
					<cfprocresult name="local.qryEnrollmentPaymentDetail" resultset="1">
				</cfstoredproc>
			</cfif>
		<cfelse>
			<cfset local.SWChargeDetail = application.mcCacheManager.sessionGetValue(keyname='SWChargeDetail', defaultValue="")>

			<!--- get transaction data --->
			<cfquery name="local.qryGetTransactionData" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				select sum(dt.amountBilled) as total, sum(dt.salesTaxAmount) as tax, max(dt.datePurchased) as datePurchased
				from dbo.tblEnrollments as e
				inner join dbo.tblEnrollmentsSWOD as eswod ON eswod.enrollmentID = e.enrollmentID
				inner join dbo.tblSeminars as s on s.seminarID = e.seminarID
				inner join dbo.tblUsers as u on u.userID = e.userID 
				inner join trialsmith.dbo.depomemberdata as d on d.depomemberdataID = u.depoMemberDataID
				inner join trialsmith.dbo.depoTransactionsApplications as dta on dta.itemID = e.enrollmentID
					and dta.itemType = 'SWE'
				inner join trialsmith.dbo.depoTransactions as dt on dt.TransactionID = dta.transactionID
				where e.enrollmentID = <cfqueryparam value="#local.qryEnrollment.enrollmentID#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>
		</cfif>
		
		<!--- get username of registrant --->
		<cfset local.username = application.objUser.login_getUsername(memberid=local.qryEnrollment.MCMemberID, siteID=local.mc_siteInfo.siteID)>
		<cfset local.memberNumber = application.objMember.getMemberNumberByMemberID(memberID=local.qryEnrollment.MCMemberID,orgID=local.mc_siteInfo.orgID)>
		<cfset local.memberKey = application.objMergeCodes.generateMemberKey(orgcode=local.mc_siteInfo.orgcode, membernumber=local.memberNumber)>

		<!--- set subject/to --->
		<cfset local.strEmailContent.subject = "Connect to Your Seminar: #local.qryEnrollment.seminarName#">
		<cfset local.strEmailContent.emailToName = "#local.qryEnrollment.firstname# #local.qryEnrollment.lastname#">
		<cfset local.strEmailContent.emailTo = local.qryEnrollment.email>
		<cfset local.strEmailContent.overrideEmail = local.qryEnrollment.overrideEmail>
		<cfset local.strEmailContent.emailFromName = local.qryAssociation.emailFrom>
		<cfset local.strEmailContent.enrollmentID = arguments.enrollmentID>
		<cfset local.strEmailContent.memberID = local.qryEnrollment.MCMemberID>

		<cfif len(local.mc_siteInfo.alternateForgotPasswordLink)>
			<cfset local.strEmailContent.alternateForgotPasswordLink = local.mc_siteInfo.alternateForgotPasswordLink>
		<cfelse>
			<cfset local.strEmailContent.alternateForgotPasswordLink = "#local.mc_siteInfo.scheme#://#local.mc_siteInfo.mainhostname#/?pg=login&logact=requestReset">
		</cfif>

		<cfset local.strEmailContent.LinkToProgram = "#local.qryEnrollment.CatalogURL#/?d=SWOD-#local.qryEnrollment.seminarID#">
		<cfset local.strEmailContent.myProgramsLink = "#local.mc_siteInfo.scheme#://#local.mc_siteInfo.mainhostname#?pg=semwebcatalog&panel=My&_swft=SWOD">
		<cfset local.strEmailContent.otherProgramsLink = "#local.mc_siteInfo.scheme#://#local.mc_siteInfo.mainhostname#?pg=semwebcatalog&panel=browse&_swft=SWOD">
		<cfset local.enterProgramLink = "#local.mc_siteInfo.scheme#://#local.mc_siteInfo.mainhostname#?pg=swOnDemandPlayer&seminarID=#local.qryEnrollment.seminarID#&enrollmentID=#local.qryEnrollment.enrollmentID#&orgCode=#local.mc_siteInfo.orgcode#&mk=#local.memberKey#">
		<cfset local.CreditCompleteByDate = CreateObject("component","SWODSeminars").getCreditCompleteByDateForSeminar(enrollmentID=local.strEmailContent.enrollmentID)>

		<cfsavecontent variable="local.strEmailContent.emailTitle">
			<cfoutput>
			<div style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding-bottom:5px;text-align:center;"><i>#local.mc_siteInfo.siteName#</i></div>
			<div style="text-align:center;">Seminar Connection Instructions</div>
			<cfif len(local.CreditCompleteByDate) AND dateCompare(now(),local.CreditCompleteByDate, "n") LT 0>
				<div style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;color:##F00;text-align:center;padding-top:8px;">Complete by: #dateformat(local.CreditCompleteByDate, "m/d/yyyy")# at 11:59 PM CT</div>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<!--- replace line breaks with brs and escape html --->
		<cfset local.customText = len(arguments.customText) GT 0 ? replace(htmlEditFormat(arguments.customText),chr(10),"<br/>","ALL") : "">
		
		<!--- html / email version --->
		<cfsavecontent variable="local.strEmailContent.html">
			<cfoutput>
			<span id="customtextimportant" style="font-weight:bold;color:red;">
				<cfif arguments.isImportantCustomText and len(local.customText)>
					#trim(local.customText)#
					<br/><br/>
				</cfif>
			</span>
			#local.qryEnrollment.firstname# #local.qryEnrollment.lastname#,
			<br/><br/>
			You're registered for <b>#encodeForHTML(local.qryEnrollment.seminarName)#</b>.
			<br/><br/>
			<cfif local.qryEnrollment.handlesOwnPayment is 0 and local.qryGetTransactionData.recordCount>
				<cfif val(local.qryGetTransactionData.total)>
					Amount: <b>#dollarFormat(local.qryGetTransactionData.total)# <cfif local.qryGetTransactionData.tax gt 0>+ #dollarformat(local.qryGetTransactionData.tax)# tax</cfif></b><br/>
				<cfelseif len(local.qryEnrollment.freeRateDisplay)>
					Amount: <b>#local.qryEnrollment.freeRateDisplay#</b><br/>
				</cfif>
				<cfif len(local.SWChargeDetail)>
					Paid by <b>#local.SWChargeDetail# <span style="padding-left:2em;">#DateFormat(local.qryGetTransactionData.datePurchased,"m/d/yyyy")#</span></b><br/>
					<div style="margin-top:3px;">PLEASE KEEP A COPY OF THIS RECEIPT FOR YOUR RECORDS</div>
				</cfif>
			<cfelseif local.qryEnrollment.handlesOwnPayment is 1>
				<cfif val(local.qryTotals.total) gt 0>
					Amount: <b>#dollarFormat(local.qryTotals.subTotal)# <cfif local.qryTotals.tax gt 0>+ #dollarformat(local.qryTotals.tax)# tax</cfif></b><br/>
				<cfelseif len(local.qryEnrollment.freeRateDisplay)>
					Amount: <b>#local.qryEnrollment.freeRateDisplay#</b><br/>
				</cfif>
				<cfif val(local.qryTotals.total) gt 0 and local.qryEnrollmentPaymentDetail.recordcount>
					Payment Details:<br/>
					<cfloop query="local.qryEnrollmentPaymentDetail">
						<div style="margin-left:20px;">
							<b>#dollarformat(local.qryEnrollmentPaymentDetail.allocSum)#<cfif val(local.qryEnrollmentPaymentDetail.processingFees)> (+ #dollarFormat(local.qryEnrollmentPaymentDetail.processingFees)# #local.qryEnrollmentPaymentDetail.processingFeeLabel#)</cfif></b> paid by:<br/>
							#local.qryEnrollmentPaymentDetail.firstname# #local.qryEnrollmentPaymentDetail.lastname#<cfif len(local.qryEnrollmentPaymentDetail.company)> - #local.qryEnrollmentPaymentDetail.company#</cfif><br/>
							#dollarFormat(local.qryEnrollmentPaymentDetail.payAmount)# #local.qryEnrollmentPaymentDetail.detail# made on #DateFormat(local.qryEnrollmentPaymentDetail.transactionDate,"m/d/yyyy")#
							<cfif local.qryEnrollmentPaymentDetail.statusID is 3><span class="red">(Pending Payment)</span></cfif>
						</div>
					</cfloop>
				</cfif>
			</cfif>
			<br/><br/>
			<table cellspacing="0" cellpadding="0" width="100%" style="background-color:##fff;font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;border:1px black solid;padding:10px">
				<tr>
					<td>
						<div align="center">
							<a href="#local.enterProgramLink#" target="_blank" style="-webkit-border-radius: 5px; -moz-border-radius: 5px; border-radius: 5px; color: ##ffff; display: block;width: 180px; background: ##2c78e4;font-size: 16px;font-weight: bold;font-family: sans-serif;text-decoration: none;line-height: 40px;">
								Launch Seminar!
							</a>
							<br>
							<span style="font-size:12px;">Your program is self-paced, accessible 24/7, and compatible with Chrome, Firefox or Safari for best results.</span>
						</div>
						<div style="width: 100%; border-top: 1px black solid;margin-top: 13px;"></div>
						<div align="center">
							<span style="display: block;font-size: 16px;font-weight: bold;font-family: sans-serif;text-decoration: none;line-height: 40px;">
								Review All Programs
							</span>
							
							<span style="font-size: 12px;"><a href="#local.strEmailContent.myProgramsLink#&mk=#local.memberKey#">Click here</a> to review your purchase history, access materials, and download certificates.</span>
							<br><br>
							<span style="font-size: 12px;"><a href="#local.strEmailContent.otherProgramsLink#&mk=#local.memberKey#">+ Register for Other Programs</a> </span>
						</div>
					</td>
				</tr>
			</table>

			<br/><br/>
			<span id="customtext">
			<cfif not arguments.isImportantCustomText and len(local.customText)>
				#trim(local.customText)#
				<br/><br/>
			</cfif>
			</span>
			#local.qryAssociation.emailFrom#<br/>
			<a href="mailto:#local.qryAssociation.supportEmail#">#local.qryAssociation.supportEmail#</a><br/>
			#local.qryAssociation.supportPhone#
			</cfoutput>
		</cfsavecontent>
		
		<!--- html / receipt version --->
		<cfsavecontent variable="local.strEmailContent.receipt">
			<cfoutput>
			#local.qryEnrollment.firstname# #local.qryEnrollment.lastname#,
			<br/><br/>
			You're registered for <b>#encodeForHTML(local.qryEnrollment.seminarName)#</b>.
			<br/><br/>
			<cfif local.qryEnrollment.handlesOwnPayment is 0 and local.qryGetTransactionData.recordCount>
				<cfif val(local.qryGetTransactionData.total)>
					Amount: <b>#dollarFormat(local.qryGetTransactionData.total)# <cfif local.qryGetTransactionData.tax gt 0>+ #dollarformat(local.qryGetTransactionData.tax)# tax</cfif></b><br/>
				<cfelseif len(local.qryEnrollment.freeRateDisplay)>
					Amount: <b>#local.qryEnrollment.freeRateDisplay#</b><br/>
				</cfif>
				<cfif len(local.SWChargeDetail)>
					Paid by <b>#local.SWChargeDetail# <span style="padding-left:2em;">#DateFormat(local.qryGetTransactionData.datePurchased,"m/d/yyyy")#</span></b><br/>
					<div style="margin-top:3px;">PLEASE KEEP A COPY OF THIS RECEIPT FOR YOUR RECORDS</div>
				</cfif>
			<cfelseif local.qryEnrollment.handlesOwnPayment is 1>
				<cfif val(local.qryTotals.total) gt 0>
					Amount: <b>#dollarFormat(local.qryTotals.subTotal)# <cfif local.qryTotals.tax gt 0>+ #dollarformat(local.qryTotals.tax)# tax</cfif></b><br/>
				<cfelseif len(local.qryEnrollment.freeRateDisplay)>
					Amount: <b>#local.qryEnrollment.freeRateDisplay#</b><br/>
				</cfif>
				<cfif val(local.qryTotals.total) gt 0 and local.qryEnrollmentPaymentDetail.recordcount>
					Payment Details:<br/>
					<cfloop query="local.qryEnrollmentPaymentDetail">
						<div style="margin-left:20px;">
							<b>#dollarformat(local.qryEnrollmentPaymentDetail.allocSum)#<cfif val(local.qryEnrollmentPaymentDetail.processingFees)> (+ #dollarFormat(local.qryEnrollmentPaymentDetail.processingFees)# #local.qryEnrollmentPaymentDetail.processingFeeLabel#)</cfif></b> paid by:<br/>
							#local.qryEnrollmentPaymentDetail.firstname# #local.qryEnrollmentPaymentDetail.lastname#<cfif len(local.qryEnrollmentPaymentDetail.company)> - #local.qryEnrollmentPaymentDetail.company#</cfif><br/>
							#dollarFormat(local.qryEnrollmentPaymentDetail.payAmount)# #local.qryEnrollmentPaymentDetail.detail# made on #DateFormat(local.qryEnrollmentPaymentDetail.transactionDate,"m/d/yyyy")#
							<cfif local.qryEnrollmentPaymentDetail.statusID is 3><span class="red">(Pending Payment)</span></cfif>
						</div>
					</cfloop>
				</cfif>
			</cfif>
			<br/><br/>
			<table cellspacing="0" cellpadding="0" width="100%" style="background-color:##fff;font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;border:1px black solid;padding:10px">
				<tr>
					<td>
						<b>Start Your Seminar</b>
						<br><br>
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b>&##10003;</b>&nbsp;<a href="#local.strEmailContent.myProgramsLink#&mk=#local.memberKey#" target="_blank">Go here</a> and click "<b>Enter Program</b>" button.
						<br/><br/>
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b>&##10003;</b>&nbsp; Your program is Self-paced, accessible 24/7 and compatible with Chrome, Firefox or Safari for best results.
						<br><br>
					</td>
				</tr>
			</table>							

			<br/><br/>
			<span id="customtext">
			<cfif len(local.customText)>
				#trim(local.customText)#
				<br/><br/>
			</cfif>
			</span>
			#local.qryAssociation.emailFrom#<br/>
			<a href="mailto:#local.qryAssociation.supportEmail#">#local.qryAssociation.supportEmail#</a><br/>
			#local.qryAssociation.supportPhone#
			</cfoutput>
		</cfsavecontent>
		<cfset local.strEmailContent.receipt = replace(replace(replace(local.strEmailContent.receipt,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL")>

		<!--- payment receipt --->
		<cfif local.qryEnrollment.handlesOwnPayment is 1 and val(local.qryTotals.total) gt 0>
			<cfsavecontent variable="local.strEmailContent.paymentReceipt">
				<cfoutput>
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Registration</td>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;text-align:right;">Total</td>
				</tr>

				<tr>
					<td colspan="2" class="tsAppBodyText" style="padding:6px;">
						<div style="margin:10px 0 5px 0;padding-bottom:5px;" class="tsAppBB20">
							<b>#encodeForHTML(local.qryEnrollment.seminarName)#</b><br/>
							<cfif len(local.qryEnrollment.seminarSubTitle)>
								<b>#encodeForHTML(local.qryEnrollment.seminarSubTitle)#</b><br/>
							</cfif>
							Registrant: #local.qryEnrollment.firstname# #local.qryEnrollment.lastname#:<br/>
							<cfif len(local.CreditCompleteByDate) AND dateCompare(now(),local.CreditCompleteByDate, "n") LT 0>
								<p><i>Must be completed by #local.CreditCompleteByDate# to earn credit.</i></p>
							</cfif>
						</div>
					</td>
				</tr>

				<cfquery name="local.qrySWODRate" dbtype="query">
					select detail, amount
					from [local].qryEnrollmentTransactions
					where itemType = 'SWODRate'
				</cfquery>

				<tr>
					<td class="tsAppBodyText" style="padding:6px">
						#local.qrySWODRate.detail#
					</td>
					<td class="tsAppBodyText" align="right" style="padding: 6px" valign="top">
						<cfif local.qrySWODRate.amount gt 0>
							#dollarFormat(local.qrySWODRate.amount)#
						<cfelse>
							#local.qryEnrollment.freeRateDisplay#
						</cfif>
					</td>
				</tr>
				</table>
				<br/><br/>
				<table width="100%" border="0" cellspacing="0" cellpadding="4" style="border:1px solid ##999;border-collapse:collapse;">
				<tr bgcolor="##DEDEDE">
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;"><b>Payment</b></td>
				</tr>
				<tr valign="top">
					<td class="tsAppBodyText tsAppBB20" style="padding: 6px">
						<cfif local.qryEnrollmentPaymentDetail.recordcount gt 0>
							<cfloop query="local.qryEnrollmentPaymentDetail">
								<div style="margin-bottom:12px;">
									<b>#dollarformat(local.qryEnrollmentPaymentDetail.allocSum)#<cfif val(local.qryEnrollmentPaymentDetail.processingFees)> (+ #dollarFormat(local.qryEnrollmentPaymentDetail.processingFees)# #local.qryEnrollmentPaymentDetail.processingFeeLabel#)</cfif></b> 
									paid by:
									<div style="margin-left:20px;margin-top:6px;">
										#local.qryEnrollmentPaymentDetail.firstname# #local.qryEnrollmentPaymentDetail.lastname#<cfif len(local.qryEnrollmentPaymentDetail.company)> - #local.qryEnrollmentPaymentDetail.company#</cfif><br/>
										#dollarFormat(local.qryEnrollmentPaymentDetail.payAmount)# #local.qryEnrollmentPaymentDetail.detail# made on #DateFormat(local.qryEnrollmentPaymentDetail.transactionDate,"m/d/yyyy")#
										<cfif local.qryEnrollmentPaymentDetail.statusID is 3><span class="red">(Pending Payment)</span></cfif>
									</div>
								</div>
							</cfloop>
						<cfelseif local.qryEnrollmentPaymentDetail.recordcount is 0>
							No payment was made.
						<cfelse>
							No payment was due.
						</cfif>
						<br/>
					</td>
				</tr>
				</table>
				</cfoutput>
			</cfsavecontent>
			<cfset local.strEmailContent.paymentReceipt = trim(replace(replace(replace(local.strEmailContent.paymentReceipt,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL"))>
		</cfif>
		
		<cfset local.qryOrgIdentity = application.objOrgInfo.getOrgIdentity(orgIdentityID=local.qryAssociation.orgIdentityID)>
		
		<cfsavecontent variable="local.strEmailContent.emailFooter">
            <cfoutput>
                This message was sent by SeminarWeb on behalf of <a href='#local.qryOrgIdentity.website#'>#local.qryOrgIdentity.organizationName#</a><br/>
                <cfif len(trim(local.qryOrgIdentity.email))>
                    <a href='mailto:#local.qryOrgIdentity.email#' target="_blank">#local.qryOrgIdentity.email#</a>
                </cfif>
                <cfif len(trim(local.qryOrgIdentity.phone))>
                    | #local.qryOrgIdentity.phone#
                </cfif>
            </cfoutput>
        </cfsavecontent>
		
		<cfset local.strEmailContent.templateDisp = application.objEmailWrapper.wrapMessage(emailTitle=local.strEmailContent.emailTitle, emailContent=local.strEmailContent.html, emailFooter=local.strEmailContent.emailFooter, sitecode=local.qryAssociation.sitecode)>

		<cfreturn local.strEmailContent>
	</cffunction>
</cfcomponent>