CREATE PROC dbo.queue_addMCPayECheckNSFBatches_sendMessage
@xmlMessage xml

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @DialogHandleFinal uniqueidentifier;
	SET @DialogHandleFinal = NEWID();

	BEGIN DIALOG CONVERSATION @DialogHandleFinal
		FROM SERVICE [PlatformQueue/MCPayECheckNSFBatchService]
		TO SERVICE N'PlatformQueue/MCPayECheckNSFBatchService'
		ON CONTRACT [PlatformQueue/GeneralXMLContract]
		WITH ENCRYPTION = OFF;
	SEND ON CONVERSATION @DialogHandleFinal 
		MESSAGE TYPE [PlatformQueue/GeneralXMLRequest] (@xmlMessage);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO