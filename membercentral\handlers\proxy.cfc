<cfcomponent name="proxy" extends="coldbox.system.eventhandler" output="false">

	<cffunction name="preHandler" access="public" returntype="void" output="false" hint="A preHandler action">
		<cfargument name="Event" type="any">
		<cfsetting showdebugoutput="false">
	</cffunction>

	<cffunction name="ts_json" access="public" returntype="void" output="false">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cftry>
			<cfset local.serializeQueryByColumns = (arguments.event.getvalue('serializeQueryByColumns',1) eq 1)>
			<cfset local.requestData = structNew()>
			<cfset local.requestData.componentName = arguments.event.getTrimValue('c')>
			<cfset local.requestData.methodName = arguments.event.getTrimValue('m')>

			<cfset local.requestData["mcproxy_orgID"] = arguments.event.getValue('mc_siteInfo.orgID')>
			<cfset local.requestData["mcproxy_orgCode"] = arguments.event.getValue('mc_siteInfo.orgCode')>
			<cfset local.requestData["mcproxy_siteID"] = arguments.event.getValue('mc_siteInfo.siteID')>
			<cfset local.requestData["mcproxy_siteCode"] = arguments.event.getValue('mc_siteInfo.siteCode')>
			<cfset local.requestData["mcproxy_memberID"] = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=local.requestData["mcproxy_orgID"])>

			<cfset local.requestData.argumentCollection = structNew()>

			<cfif NOT isSimpleValue(local.requestData.componentName) OR ReFindNoCase("[^A-Z0-9\_]", local.requestData.componentName) OR local.requestData.componentName eq "">
				<cfthrow message="Invalid componentName">
			</cfif>
			<cfif NOT isSimpleValue(local.requestData.methodName) OR ReFindNoCase("[^A-Z0-9\_]", local.requestData.methodName) OR local.requestData.methodName eq "">
				<cfthrow message="Invalid methodName">
			</cfif>

			<cfset local.systemURLVariables = "c,m,pg,resID,event,mode,logout,serializeQueryByColumns">
			<cfloop list="#structKeyList(form)#" index="local.thisKey">
				<cfif not listFindNoCase(local.systemURLVariables,local.thisKey) and isSimpleValue(form[local.thisKey])>
					<cfset local.requestData.argumentCollection[local.thisKey] = form[local.thisKey]>
				</cfif>
			</cfloop>
			<cfloop list="#structKeyList(url)#" index="local.thisKey">
				<cfif not listFindNoCase(local.systemURLVariables,local.thisKey)>
					<cfset local.requestData.argumentCollection[local.thisKey] = url[local.thisKey]>
				</cfif>
			</cfloop>

			<!--- check for array encoded variables --->
			<cfloop collection="#local.requestData.argumentCollection#" item="local.key">
				<cfif right(local.key,4) eq '~[]~'>
					<cfset local.cleanKey = replace(local.key,"~[]~","","one")>
					<cfset local.requestData.argumentCollection[local.cleanKey] = listToArray(local.requestData.argumentCollection[local.key],'^~~~^')>
					<cfset structDelete(local.requestData.argumentCollection,local.key)>
				</cfif>
			</cfloop>

			<!--- inject common variables into the argument collection for all methods to use as needed --->
			<cfset local.requestData.argumentCollection['mcproxy_orgID'] = local.requestData["mcproxy_orgID"]>
			<cfset local.requestData.argumentCollection['mcproxy_orgCode'] = local.requestData["mcproxy_orgCode"]>
			<cfset local.requestData.argumentCollection['mcproxy_siteID'] = local.requestData["mcproxy_siteID"]>
			<cfset local.requestData.argumentCollection['mcproxy_siteCode'] = local.requestData["mcproxy_siteCode"]>
			<cfset local.requestData.argumentCollection['mcproxy_memberID'] = local.requestData["mcproxy_memberID"]>

			<cfset local.proxyResponse = application.objProxyService.processRequest(local.requestData)>
		<cfcatch type="Any">
			<!--- Send back error --->
			<cfset local.proxyResponse = StructNew()>
			<cfset local.proxyResponse.success = false>
			
			<!--- trigger master error handler. some cases should not trigger exception. --->
			<cfif cfcatch.message eq "Invalid methodName"
				OR cfcatch.message eq "Invalid componentName"
				OR findNoCase("has no function with name", cfcatch.message)
				OR NOT IsDefined("local.requestData.componentName")
				OR NOT IsDefined("local.requestData.methodName")
			>
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			</cfif>

			<!--- send back error header to trigger error callback --->
			<cfheader statuscode="500" statustext="Error processing request">
		</cfcatch>
		</cftry>
		
		<cfset local.returnJsonString = SerializeJSON(local.proxyResponse,local.serializeQueryByColumns)>
		<cfheader name="X-Robots-Tag" value="noindex">
		<cfset arguments.event.renderData(data=local.returnJsonString,Type="plain",contentType="application/json; charset=UTF-8")>
	</cffunction>

</cfcomponent>