CREATE TRIGGER [dbo].[trg_ref_clientsUpdate] ON [dbo].[ref_clients]
AFTER UPDATE
AS 

SET NOCOUNT ON
BEGIN TRY

	IF NOT EXISTS (SELECT * FROM inserted) RETURN

	IF UPDATE(homePhone) OR UPDATE(cellPhone) OR UPDATE(alternatePhone) BEGIN
	    UPDATE c
	    SET c.phoneForSearch = rtrim(ltrim(
			dbo.fn_RegExReplace(isnull(c.homePhone,''),'[^0-9]','') + ' ' + 
			dbo.fn_RegExReplace(isnull(c.cellPhone,''),'[^0-9]','') + ' ' +
			dbo.fn_RegExReplace(isnull(c.alternatePhone,''),'[^0-9]','')
			))
		FROM dbo.ref_clients as c 
		INNER JOIN Inserted as I ON c.clientID = I.clientID
		INNER JOIN Deleted as D ON c.clientID = D.clientID
		WHERE (
			isnull(D.homePhone,'') <> isnull(I.homePhone,'')
			OR isnull(D.cellPhone,'') <> isnull(I.cellPhone,'')
			OR isnull(D.alternatePhone,'') <> isnull(I.alternatePhone,'')
		)
	END

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO
