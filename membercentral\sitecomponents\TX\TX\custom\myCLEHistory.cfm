<cfscript>
	local.orgCode	= event.getValue('mc_siteInfo.orgCode');
</cfscript>

<!--- default to show list of events --->
<cfset arguments.event.paramValue('panel','showList')>

<cfif arguments.event.getValue('panel') eq "viewCert">
	
	<cfscript>
	// get encrypted registrantid
	local.encryptedRID = arguments.event.getValue('rid','');
	
	// change xPcmKx to % (case sensitive), decode, fromBase64, and decrypt
	try { local.decryptedRID = val(decrypt(toString(toBinary(URLDecode(replace(local.encryptedRID,"xPcmKx","%","ALL")))),"TRiaL_SMiTH")); } 
	catch (any e) { local.decryptedRID = 0; }
	
	// generate certificate for registrantID
	local.strCertificate = CreateObject("component","model.admin.events.certificate").generateCertificate(registrantID=local.decryptedRID);
	</cfscript>
	
	<!--- redirect to pdf --->
	<cfif len(local.strCertificate.certificateURL)>
		<cflocation url="#local.strCertificate.certificateURL#" addtoken="no">
	<cfelse>
		<cflocation url="/?pg=MyCLEHistory&panel=certErr&mode=direct" addtoken="no">
	</cfif>
	
<cfelseif arguments.event.getValue('panel') eq "certErr">
	<cfoutput>
	<div><span class="HeaderText">My CLE History</span></div>
	<br/>
	<div class="tsAppBodyText">
		<b>Sorry...</b>, we were unable to generate a certificate at this time.<br/><br/>
		If you continue to see this message, please contact #event.getValue('mc_siteInfo.ORGShortName')# for assistance.
	</div>
	</cfoutput>

<cfelse>
			
	<!--- CLE history based on event registration with credit selections --->
	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCLE">
		SET NOCOUNT ON;

		DECLARE @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;

		select e.eventid, r.registrantID, r.dateRegistered, c.contentTitle, rc.creditValueAwarded, isnull(ast.ovTypeName,cat.typeName) as creditType,
			datepart(year,r.dateRegistered) as CLEYear
		from dbo.ev_registrants as r
		inner join dbo.ev_registration as evr on evr.registrationID = r.registrationID AND evr.siteID = @siteID
		inner join dbo.ev_events as e on e.eventid = evr.eventid AND e.siteID = @siteID
		inner join dbo.ev_calendarEvents ce on ce.sourceEventID = e.eventID
		inner join dbo.ev_categories ec on ec.calendarid = ce.calendarID and categoryShort = 'CLE-Conf'
		inner join dbo.ev_eventCategories ecat on ecat.eventid = e.eventID and ecat.categoryid = ec.categoryID		
		inner join dbo.cms_contentLanguages as c on c.contentID = e.eventContentID and c.languageID = 1
		inner join dbo.crd_requests as rc on rc.registrantID = r.registrantID and rc.creditAwarded = 1 and r.status='A'
		inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID
		inner join dbo.crd_authoritySponsorTypes as ast on ast.astid = ect.astid
		inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
		inner join dbo.ams_members as m on m.memberID = <cfqueryparam value="#session.cfcUser.memberData.memberID#" cfsqltype="CF_SQL_INTEGER">
		inner join dbo.ams_members as mMerged on mMerged.activeMemberID = m.activeMemberID
		where r.memberid = mMerged.memberID
		order by r.dateRegistered desc, e.eventid;
	</cfquery>
	<cfquery name="local.qryCLETotals" dbtype="query">
		select CLEYear, creditType, sum(creditValueAwarded) as totalCLE
		from [local].qryCLE
		group by CLEYear, creditType
		order by CLEYear, totalCLE
	</cfquery>
	
	<!--- seminarweb history --->
	<cfset local.qrySWP = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(local.orgCode).qryAssociation>
	<cfset local.showSW = false>
	<cfif val(session.cfcUser.memberData.depomemberdataid) gt 0>
		<cfstoredproc procedure="sw_getEnrollmentHistory" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.orgcode#">
			<cfprocresult name="local.qrySWL" resultset="1">
			<cfprocresult name="local.qrySWOD" resultset="2">
			<cfprocresult name="local.qryCertPrograms" resultset="3">
		</cfstoredproc>
		<cfset local.showSW = true>
		
		<cfquery dbtype="query" name="local.qrySWODOrderByDate">
			select *
			from [local].qrySWOD
			order by dateEnrolled
		</cfquery>
		
		<cfquery dbtype="query" name="local.qrySWLOrderByDate">
			select *
			from [local].qrySWL
			order by dateStart desc
		</cfquery>
		
	</cfif>
	
	<cfsavecontent variable="local.cleCSS">
		<cfoutput>
		<style type="text/css">
		##clehistory th, ##swlhistory th, ##swodhistory th { text-align:left; border-bottom:1px solid ##666; }
		</style>
		<script language="JavaScript">
			function viewEVCert(rid) {
				var certURL = '/?pg=myCLEHistory&panel=viewCert&mode=stream&rid=' + rid;
				window.open(certURL,'ViewCertificate','width=990,height=500');
			}
		</script>
		<cfif local.showSW>
			<script language="JavaScript">
			function viewCert(eId) {
				var certURL = '/?pg=semWebCatalog&panel=viewCert&mode=direct&eId=' + eId;
				window.open(certURL,'ViewCertificate','width=990,height=500');
			}
			</script>
		</cfif>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#local.cleCSS#">
	
	<cfoutput>
		<div><span class="TitleText">My CLE History</span></div>
		<br/>
	</cfoutput>

	<cfoutput><div><span class="HeaderText">#local.qrySWP.brandConfTab#</span></div></br></cfoutput>
		
	<cfif local.qryCLE.recordcount>
	
		<cfoutput>
		<table class ="table no-border-top" cellpadding="2" cellspacing="0" border="0" id="myLinks">
			<tr><td colspan="2" class="bodyText tsAppBodyText"><b>Credit Totals</b></td></tr>
			<cfset local.totalYear = "">
			<cfloop query="local.qryCLETotals">
				<tr valign="top">
					<td class="bodyText tsAppBodyText"><cfif local.qryCLETotals.cleYear neq local.totalYear>#local.qryCLETotals.CLEYear#<cfset local.totalYear =local.qryCLETotals.cleYear><cfelse>&nbsp;</cfif></td>
					<td class="bodyText tsAppBodyText">#local.qryCLETotals.totalCLE# #local.qryCLETotals.creditType#</td>
				</tr>
			</cfloop>
		</table>
	
		<table class ="table border-bottom no-border-top collapse-to-row" width="98%" cellpadding="2" cellspacing="0" border="0" id="clehistory">
			<thead>
				<tr class="tsAppBodyText">
					<th >Date</th>
					<th>Title</th>
					<th colspan="2">Credit Awarded</th>
				</tr>
			</thead>
			<tbody>
			</cfoutput>
				<cfset local.oddeven = 0>
				<cfoutput query="local.qryCLE" group="eventid">
					<cfset local.oddeven = local.oddeven + 1>
					<cfset local.rID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qryCLE.registrantID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
			
					<tr valign="top" <cfif local.oddeven mod 2 is 0>class="rowcolor0"</cfif>>
						<td class="bodyText tsAppBodyText">#dateformat(local.qryCLE.dateRegistered,"mm/d/yyyy")#</td>
						<td class="bodyText tsAppBodyText">#local.qryCLE.contentTitle#</td>
						<td class="bodyText tsAppBodyText" nowrap>
							<div>
								<div class="inline-div cert-img">
									<a href="javascript:viewEVCert('#local.rID#');" title="Print certificate"><i class="icon-print"></i></a>
									<br>
								</div>
								<div class="inline-div"><cfoutput>#local.qryCLE.creditValueAwarded# #local.qryCLE.creditType#<br/></cfoutput></div>
							</div>
						</td>
					</tr>	
				</cfoutput>
			
			<cfoutput>
			</tbody>
		</table>
		</cfoutput>
	<cfelse>
		<cfoutput>
		<div class="tsAppBodyText">
			There are no Live Conferences & Events to display.
		</div>
		<br />
		</cfoutput>
	</cfif>
	
	<cfif local.showSW>
		<cfoutput>
			<div><span class="HeaderText">On-Demand and Live Webinars</span></div>
	
		<!--- SWL --->
		<cfif local.qrySWL.recordcount>
			<br>
			<div class="subHeadingBlue"><b>#local.qrySWP.brandSWLTab#</b></div><br/>
			<table class ="table border-bottom no-border-top collapse-to-row" width="98%" cellpadding="2" cellspacing="0" border="0" id="swlhistory">
				<thead>
					<tr class="tsAppBodyText">
						<th>Date</th>
						<th>Title</th>
						<th>Status</th>
					</tr>
				</thead>
				<tbody>	
					<cfloop query="local.qrySWLOrderByDate">
						<cfset local.eID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qrySWLOrderByDate.enrollmentID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
						<tr valign="top">
							<td class="tsAppBodyText">#DateFormat(local.qrySWLOrderByDate.dateStart,'m/d/yyyy')#</td>
							<td class="tsAppBodyText">#encodeForHTML(local.qrySWLOrderByDate.seminarName)#</td>
							<td class="tsAppBodyText" width="120">
								<cfif len(local.qrySWLOrderByDate.dateCompleted) and local.qrySWLOrderByDate.passed is 1 and local.qrySWLOrderByDate.offerCertificate>
									<a href="javascript:viewCert('#local.eID#')">View certificate(s)</a>
								<cfelseif len(local.qrySWLOrderByDate.dateCompleted) and local.qrySWLOrderByDate.passed is 1 and not local.qrySWLOrderByDate.offerCertificate>
									Completed
								<cfelseif len(local.qrySWLOrderByDate.dateCompleted) and local.qrySWLOrderByDate.passed is 0>
									Failed
								<cfelseif now() lt local.qrySWLOrderByDate.dateStart>
									Not yet begun
								<cfelse>
									Did not attend
								</cfif>
							</td>
							<td class="visible-phone blank-column">&nbsp;</td>
						</tr>
					</cfloop>
				</tbody>
			</table>
			<br/><br/>
		</cfif>
	
		<!--- SWOD --->
		<cfif local.qrySWODOrderByDate.recordcount>
			<br>
			<div class="subHeadingBlue"><b>#local.qrySWP.brandSWODTab#</b></div><br/>
			<table class="table border-bottom no-border-top collapse-to-row" width="98%" cellpadding="2" cellspacing="0" border="0" id="swodhistory">
			<tr class="tsAppBodyText">
				<th>Title</th>
				<th>Status</th>
			</tr>
			<cfloop query="local.qrySWODOrderByDate">
				<cfset local.eID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qrySWODOrderByDate.enrollmentID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
				<tr valign="top">
					<td class="tsAppBodyText">#encodeForHTML(local.qrySWODOrderByDate.seminarName)#</td>
					<td class="tsAppBodyText" width="140">
						<cfif len(local.qrySWODOrderByDate.dateCompleted) is 0>
							<cfif local.qrySWODOrderByDate.isPublished and local.qrySWODOrderByDate.preReqFulfilled>
								<a href="/?pg=swOnDemandPlayer&seminarID=#local.qrySWODOrderByDate.seminarID#&enrollmentID=#local.qrySWODOrderByDate.enrollmentID#&orgCode=#arguments.event.getValue('mc_siteinfo.orgCode')#" target="_blank">Begin</a>
							<cfelseif local.qrySWODOrderByDate.isPublished>
								Awaiting Prereqs
							<cfelse>
								Not available
							</cfif>
						<cfelse>
							<cfif local.qrySWODOrderByDate.isPublished>
								<a href="/?pg=swOnDemandPlayer&seminarID=#local.qrySWODOrderByDate.seminarID#&enrollmentID=#local.qrySWODOrderByDate.enrollmentID#&orgCode=#arguments.event.getValue('mc_siteinfo.orgCode')#" target="_blank">Review</a> |
							</cfif>
							<cfif local.qrySWODOrderByDate.passed and local.qrySWODOrderByDate.offerCertificate>
								<a href="javascript:viewCert('#local.eID#');">Certificate</a>
							<cfelseif local.qrySWODOrderByDate.passed and not local.qrySWODOrderByDate.offerCertificate>
								Completed
							<cfelseif not local.qrySWODOrderByDate.passed>
								Failed
							</cfif>
						</cfif>
					</td>
				</tr>
			</cfloop>
			</table>
			<br/><br/>
		</cfif>	
	
		</cfoutput>
	</cfif>	

</cfif>

<cffunction name="getMyStorePurchases" returntype="query">
	<cfargument name="siteID" type="numeric" required="yes">
		
	<cfset var local = structNew()>
	
	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.data">
		SET NOCOUNT ON;

		declare @orgID int, @siteID int;
		set @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteId#">;
		select @orgID = orgID from dbo.sites where siteID = @siteID;

		SELECT o.orderID, o.DateOfOrder, o.orderNumber, isnull(ttl.totalFee,0) as totalFee
		FROM dbo.store_orders as o
		cross apply (
			select sum(ts.cache_amountAfterAdjustment) as totalFee
			from dbo.fn_store_orderTransactions(@orgID,o.orderid) as rt
			inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = rt.transactionID
		) as ttl
		INNER JOIN dbo.store as s on s.storeID = o.storeID and s.siteID = @siteID
		INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberid = o.memberID 
		INNER JOIN dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
		WHERE o.orderCompleted = 1	
		and mActive.memberID = <cfqueryparam value="#session.cfcUser.memberData.memberID#" cfsqltype="CF_SQL_INTEGER">
		ORDER BY DateOfOrder desc;
	</cfquery>
	
	<cfreturn local.data>
</cffunction>
