----------------------------------------------------------------------------------------------------
/* Query #34: PASSED */
ALTER PROCEDURE [dbo].[sw_AlertJobFailed]
(
    @JobName NVARCHAR(128) = NULL,
    @Page BIT = 0,
    @MailProfileName varchar(128) = NULL,
	@AlertEmail varchar(128) = NULL,
	@PageEmail varchar(128) = NULL,
	@Company varchar(128) = NULL,
	@Configuration varchar(128) = NULL
)
AS
BEGIN
    SET NOCOUNT ON

    IF @JobName IS NULL
    BEGIN
        RAISERROR ('JobName is required', 0, 1)
    END

    IF @MailProfileName IS NULL
    BEGIN
        SELECT @MailProfileName = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'MailProfileName'
    END

    IF @AlertEmail IS NULL
    BEGIN
        SELECT @AlertEmail = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'AlertEmail'
    END

    IF @PageEmail IS NULL
    BEGIN
        SELECT @PageEmail = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'PageEmail'
    END

    IF @Company IS NULL
    BEGIN
        SELECT @Company = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Company'
    END

    IF @Configuration IS NULL
    BEGIN
        SELECT @Configuration = [Value] FROM [SQLWatchmen].[dbo].[Parameters] WHERE [Parameter] = 'Configuration'
    END

    DECLARE @Subject NVARCHAR(255) = 'Job "' + @JobName + '" Failed' + ' | ' + @Configuration + ' | ' + @Company
    DECLARE @Body NVARCHAR(MAX) = 'The job "' + @JobName + '" failed.  Please review the job history for details.'

    IF @Page = 1
    BEGIN
        EXEC msdb.dbo.sp_send_dbmail
			@profile_name = @MailProfileName,
			@recipients = @AlertEmail,
            @blind_copy_recipients = @PageEmail,
			@subject = @Subject,
			@body = @Body
    END
    ELSE
    BEGIN
        EXEC msdb.dbo.sp_send_dbmail
            @profile_name = @MailProfileName,
            @recipients = @AlertEmail,
            @subject = @Subject,
            @body = @Body
    END

    INSERT INTO [SQLWatchmen].[dbo].[AlertLog] ([Alert]) VALUES (('Job "' + @JobName + '" Failed'))

END
GO
